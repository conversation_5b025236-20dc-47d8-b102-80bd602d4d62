/**
 * 不是真实的 webpack 配置，仅为兼容 webstorm 和 intellij idea 代码跳转
 * ref: http://git.ponshine.fe/ponshinejs/ponshine/issues/1109#issuecomment-423380125
 */

module.exports = {
  resolve: {
    alias: {
      // eslint-disable-next-line global-require
      '@': require('path').resolve(__dirname, 'src'),
      // eslint-disable-next-line global-require
      '@@': require('path').resolve(__dirname, 'src/pages/.ponshine'),
    },
  },
};
