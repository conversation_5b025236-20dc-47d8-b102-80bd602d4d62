{"name": "demasia-pro", "version": "4.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 ponshine build", "build": "ponshine build", "dev": "npm run start:dev", "fetch:blocks": "npm run fetch:blocks:demasia-pro-blocks && npm run fetch:blocks:system-manage", "fetch:blocks:demasia-pro-blocks": "pro fetch-blocks && npm run prettier", "fetch:blocks:system-manage": "block fetch-blocks http://git.ponshine.fe/blocks/system-manage && npm run prettier", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"**/*\"", "serve": "cross-env PORT=8888 ponshine-serve", "start": "cross-env PONSHINE_UI=none  ponshine dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none PONSHINE_UI=none ponshine dev", "start:no-mock": "cross-env MOCK=none ponshine dev", "start:no-ui": "cross-env PONSHINE_UI=none MOCK=none ponshine dev", "start:no-ui-with-mock": "cross-env PONSHINE_UI=none ponshine dev", "start:pre": "cross-env REACT_APP_ENV=pre ponshine dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none ponshine dev", "test": "ponshine test --passWithNoTests", "test:all": "node ./tests/run-tests.js --passWithNoTests", "test:component": "ponshine test ./src/components --passWithNoTests", "ui": "ponshine ui"}, "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 9"], "dependencies": {"@demasia-rc/captcha": "^1.0.17", "@jiaminghi/data-view-react": "^1.2.5", "antd": "^3.23.6", "array-move": "^3.0.1", "axios": "^1.7.7", "browserify-aes": "^1.2.0", "classnames": "^2.2.6", "crypto-js": "^3.3.0", "demasia-pro-layout": "^5.0.1", "demasia-pro-table": "^1.0.47", "docx-preview": "^0.1.15", "dryad": "^2.6.3", "echarts": "^5.4.0", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "html2canvas": "^1.4.1", "jsencryptNew": "^3.0.0", "lodash": "^4.17.11", "moment": "^2.24.0", "omit.js": "^2.0.2", "path-to-regexp": "2.4.0", "phooks": "^2.9.6", "ponshine": "^2.13.28", "ponshine-plugin-auth": "^4.3.3", "ponshine-request": "^1.8.6", "qs": "^6.9.0", "react": "^16.13.1", "react-contexify": "^4.1.1", "react-copy-to-clipboard": "^5.0.1", "react-dom": "^16.13.1", "react-file-viewer": "^1.2.1", "react-helmet": "^5.2.1", "react-highlight-words": "^0.18.0", "react-pdf-js": "^5.1.0", "redux": "^4.0.1", "string-natural-compare": "^3.0.1", "use-resize-observer": "^7.0.0", "uuid": "^9.0.1", "virtualizedtableforantd": "^0.7.9", "wd-domsize-monitor": "^1.1.0"}, "devDependencies": {"@ponshinejs/fabric": "^2.4.6", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^25.1.0", "@types/lodash": "^4.14.144", "@types/qs": "^6.5.3", "@types/react": "^16.9.35", "@types/react-dom": "^16.9.8", "@types/react-helmet": "^5.0.13", "chalk": "^3.0.0", "cookie": "^0.4.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "demasia-pro-cli": "^1.0.30", "enzyme": "^3.9.0", "eslint": "^7.11.0", "express": "^4.17.1", "husky": "^4.0.7", "jest-puppeteer": "^4.2.0", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "node-fetch": "^2.6.0", "ponshine-block-cli": "^1.1.4", "ponshine-plugin-antd-theme": "^2.0.5", "ponshine-plugin-pro-block": "^1.4.1", "ponshine-plugin-react": "^1.14.5", "ponshine-serve": "^1.9.2", "ponshine-types": "^0.8.2", "prettier": "2.6.2", "stylelint": "^13.7.0"}, "optionalDependencies": {"puppeteer": "^2.0.0"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}