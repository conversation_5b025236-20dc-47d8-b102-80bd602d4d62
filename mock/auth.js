// @ts-ignore
import cookie from 'cookie';
import Routes from '../config/routes';

const getMenu = (route, pid = 0) => {
  let arr = [];
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < route.length; i++) {
    const item = route[i];
    const id = (Math.random() * 100000).toFixed(0);
    if (item.path) {
      arr.push({ pid, nodeId: id, pageUrl: item.path });
    }

    if (item.routes) {
      arr = arr.concat(getMenu(item.routes, id));
    }
  }

  return arr;
};

const getAuth = (req, res) => {
  const cookies = cookie.parse(req.headers.cookie || '');
  const { userName } = cookies;

  // if (!['admin', 'user'].includes(userName)) {
  //   res.status(200).json({
  //     status: 'invalid',
  //   });
  //   return;
  // }

  const authorities = [
    { authority: '/api/user/findUserDetail' },
    { authority: '/api/user/updateRoleByUserId' },
    { authority: '/api/user/resetPassword' },
    { authority: '/api/audit/list' },
    { authority: '/api/user/editUserInfo' },
    { authority: '/api/user/delUser' },
    { authority: '/api/user/findUserPager' },
    { authority: 'ROLE_admin' },
    { authority: '/api/user/mergeUser' },
    { authority: '/api/user/saveUser' },
  ];

  const buttons = [];

  const authData = {
    principal: {
      id: 1,
      avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
      username: userName,
      password: null,
      nickName: userName === 'admin' ? '管理员' : '普通用户',
      phonenum: null,
      email: null,
      createTime: null,
      passwordChangeTime: null,
      lastLoginTime: null,
      lastLoginIp: null,
      roleId: 1,
      updateUserId: null,
      roleName: 'admin',
      authorities,
      accountNonExpired: true,
      accountNonLocked: true,
      credentialsNonExpired: true,
      enabled: true,
    },
    permission: {
      buttons,
      menus: getMenu(Routes),
    },
  };

  if (userName === 'admin') {
    authData.permission.menus = authData.permission.menus.concat([
      {
        pid: 0,
        nodeId: 109,
        pageUrl: '/admin',
        nodeText: '~管理页~',
        imageClassName: 'crown',
      },
      {
        pid: 109,
        nodeId: 10900,
        pageUrl: '/admin/sub-page',
        nodeText: '~管理页~',
        imageClassName: 'line',
      },
    ]); // @ts-ignore

    authData.permission.buttons.push({
      // @ts-ignore
      pid: 108,
      // @ts-ignore
      nodeId: 10800,
      // @ts-ignore
      url: 'admin',
      // @ts-ignore
      nodeText: '管理员',
    });
  }

  const removeIcon = true;
  const removeName = true;

  if (removeIcon || removeName) {
    // @ts-ignore
    authData.permission.menus = authData.permission.menus.map((val) => {
      const { imageClassName, nodeText, ...rest } = val;
      return {
        ...rest,
        ...{
          imageClassName: !removeIcon ? imageClassName : '',
          nodeText: !removeName ? nodeText : '',
        },
      };
    });
  }

  res.json(authData);
};

export default {
  'GET /api/auth': getAuth,
  'GET /api/user/getSessionValues': getAuth,
};
