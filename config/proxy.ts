// const loginApi = '192.168.66.26:8084'; //张术
// const apiApi = `192.168.66.26:8084`; //

const loginApi = '**********:8084'; //测试环境
const apiApi = '**********:8084';

// const loginApi = '*************:8084'; //林俊
// const apiApi = '*************:8084';

// const loginApi = '*************:8084'; //林俊无线
// const apiApi = '*************:8084';

// const loginApi = '*************:8084'; //长乐
// const apiApi = '*************:8084';

// const loginApi = '*************:8084'; //徐文俊
// const apiApi = '*************:8084';

export default {
  dev: {
    // '/cmdb/': {
    //   target: `http://${ip}:8089/`,
    //   changeOrigin: true,
    // },
    // '/collect/': {
    //   target: `http://${ip}:8090/`,
    //   changeOrigin: true,
    // },
    // '/alarm/': {
    //   target: `http://${ip}:8096/`,
    //   changeOrigin: true,
    // },
    // '/order/': {
    //   target: `http://${ip}:8098/`,
    //   changeOrigin: true,
    // },
    '/api/': {
      target: `http://${apiApi}`,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      headers: {
        Connection: 'keep-alive',
      },
      bypass: function (
        req: { headers: { origin: string; host: string; referer: string } },
        res: any,
        proxyOptions: any,
      ) {
        req.headers.origin = `http://${apiApi}`;
        req.headers.host = apiApi;
        req.headers.referer = `http://${apiApi}/`;
      },
    },
    // '/api/cmdChannel/editSlaverUser': {
    //   target: `http://${apiApi1}`,
    //   changeOrigin: true,
    //   pathRewrite: { '^': '' },
    //   bypass: function(
    //     req: { headers: { origin: string; host: string; referer: string } },
    //     res: any,
    //     proxyOptions: any,
    //   ) {
    //     req.headers.origin = `http://${apiApi1}`;
    //     req.headers.host = apiApi1;
    //     req.headers.referer = `http://${apiApi1}/`;
    //   },
    // },
    '/biz/': {
      target: `http://${apiApi}`,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      headers: {
        Connection: 'keep-alive',
      },
      bypass: function (
        req: { headers: { origin: string; host: string; referer: string } },
        res: any,
        proxyOptions: any,
      ) {
        req.headers.origin = `http://${apiApi}`;
        req.headers.host = apiApi;
        req.headers.referer = `http://${apiApi}/`;
      },
    },
    // 以下3个规则是前后端分离开发环境中的单点登录使用：
    '/sso/login': {
      target: `http://${loginApi}`,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      headers: {
        Connection: 'keep-alive',
      },
      bypass: function (
        req: {
          originalUrl: string;
          query: { [key: string]: any };
          headers: { origin: string; host: string; referer: string };
        },
        res: any,
        proxyOptions: any,
      ) {
        req.headers.origin = `http://${loginApi}`;
        req.headers.host = loginApi;
        req.headers.referer = `http://${loginApi}/`;
      },
    },
    '/oauth2/authorization/ponshine': {
      target: `http://${loginApi}`,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      headers: {
        Connection: 'keep-alive',
      },
      bypass: function (
        req: {
          originalUrl: string;
          query: { [key: string]: any };
          headers: { origin: string; host: string; referer: string };
        },
        res: any,
        proxyOptions: any,
      ) {
        req.headers.origin = `http://${loginApi}`;
        req.headers.host = loginApi;
        req.headers.referer = `http://${loginApi}/`;
      },
    },
    '/login/oauth2/code/ponshine': {
      target: `http://${loginApi}`,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      headers: {
        Connection: 'keep-alive',
      },
      bypass: function (
        req: {
          originalUrl: string;
          query: { [key: string]: any };
          headers: { origin: string; host: string; referer: string };
        },
        res: any,
        proxyOptions: any,
      ) {
        req.headers.origin = `http://${loginApi}`;
        req.headers.host = loginApi;
        req.headers.referer = `http://${loginApi}/`;
      },
    },
  },
  test: {
    '/api/': {
      target: 'http://demasia-pro.preview.ponshine.fe',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      headers: {
        Connection: 'keep-alive',
      },
      bypass: function (
        req: { headers: { origin: string; host: string; referer: string } },
        res: any,
        proxyOptions: any,
      ) {
        req.headers.origin = 'http://demasia-pro.preview.ponshine.fe';
        req.headers.host = 'demasia-pro.preview.ponshine.fe';
        req.headers.referer = 'http://demasia-pro.preview.ponshine.fe/';
      },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};

// const ip = '************';
//
// const loginApi = `${ip}:8090`;
// const apiApi = `${ip}:8090`;
// export default {
//   dev: {
//     '/cmdb/': {
//       target: `http://${ip}:8090/`,
//       changeOrigin: true,
//     },
//     '/collect/': {
//       target: `http://${ip}:8090/`,
//       changeOrigin: true,
//     },
//     '/alarm/': {
//       target: `http://${ip}:8090/`,
//       changeOrigin: true,
//     },
//     '/api/': {
//       target: `http://${apiApi}`,
//       changeOrigin: true,
//       pathRewrite: { '^': '' },
//       bypass: function(
//         req: { headers: { origin: string; host: string; referer: string } },
//         res: any,
//         proxyOptions: any,
//       ) {
//         req.headers.origin = `http://${apiApi}`;
//         req.headers.host = apiApi;
//         req.headers.referer = `http://${apiApi}/`;
//       },
//     },
//     // 以下3个规则是前后端分离开发环境中的单点登录使用：
//     '/sso/login': {
//       target: `http://${loginApi}`,
//       changeOrigin: true,
//       pathRewrite: { '^': '' },
//       bypass: function(
//         req: {
//           originalUrl: string;
//           query: { [key: string]: any };
//           headers: { origin: string; host: string; referer: string };
//         },
//         res: any,
//         proxyOptions: any,
//       ) {
//         req.headers.origin = `http://${loginApi}`;
//         req.headers.host = loginApi;
//         req.headers.referer = `http://${loginApi}/`;
//       },
//     },
//     '/oauth2/authorization/ponshine': {
//       target: `http://${loginApi}`,
//       changeOrigin: true,
//       pathRewrite: { '^': '' },
//       bypass: function(
//         req: {
//           originalUrl: string;
//           query: { [key: string]: any };
//           headers: { origin: string; host: string; referer: string };
//         },
//         res: any,
//         proxyOptions: any,
//       ) {
//         req.headers.origin = `http://${loginApi}`;
//         req.headers.host = loginApi;
//         req.headers.referer = `http://${loginApi}/`;
//       },
//     },
//     '/login/oauth2/code/ponshine': {
//       target: `http://${loginApi}`,
//       changeOrigin: true,
//       pathRewrite: { '^': '' },
//       bypass: function(
//         req: {
//           originalUrl: string;
//           query: { [key: string]: any };
//           headers: { origin: string; host: string; referer: string };
//         },
//         res: any,
//         proxyOptions: any,
//       ) {
//         req.headers.origin = `http://${loginApi}`;
//         req.headers.host = loginApi;
//         req.headers.referer = `http://${loginApi}/`;
//       },
//     },
//   },
//   test: {
//     '/api/': {
//       target: 'http://demasia-pro.preview.ponshine.fe',
//       changeOrigin: true,
//       pathRewrite: { '^': '' },
//       bypass: function(
//         req: { headers: { origin: string; host: string; referer: string } },
//         res: any,
//         proxyOptions: any,
//       ) {
//         req.headers.origin = 'http://demasia-pro.preview.ponshine.fe';
//         req.headers.host = 'demasia-pro.preview.ponshine.fe';
//         req.headers.referer = 'http://demasia-pro.preview.ponshine.fe/';
//       },
//     },
//   },
//   pre: {
//     '/api/': {
//       target: 'your pre url',
//       changeOrigin: true,
//       pathRewrite: { '^': '' },
//     },
//   },
// };
//
