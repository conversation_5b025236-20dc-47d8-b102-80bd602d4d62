import theme from './theme';
export default [
  [
    'ponshine-plugin-react',
    {
      antd: true,
      dryad: {
        hmr: true,
      },
      locale: {
        // default false
        enable: true,
        // default zh-CN
        default: 'zh-CN',
        // default true, when it is true, will use `navigator.language` overwrite default
        baseNavigator: true,
      },
      dynamicImport: {
        loadingComponent: './components/PageLoading/index',
        webpackChunkName: true,
        level: 3,
      },
    },
  ],
  [
    'ponshine-plugin-pro-block',
    {
      moveMock: false,
      moveService: false,
      modifyRequest: true,
      autoAddMenu: true,
      modifyRouteException: true,
    },
  ],
  ['ponshine-plugin-auth', null],
  // ['ponshine-plugin-antd-theme', theme],
];
