/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * http://pro.demasia.org/docs/deploy
 */
export default {
  dev: {
    '/api/': {
      // target: 'http://**********:8084',
      target: 'http://*************:8084', //张术本地外网

      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
      headers: {
        // windows 操作系统中使用 sdp 连接需要 'Connection': 'keep-alive'
        Connection: 'keep-alive',
      },
      bypass: function (req, res, proxyOptions) {
        req.headers.origin = 'http://demasia-pro.preview.ponshine.fe';
        req.headers.host = 'demasia-pro.preview.ponshine.fe';
        req.headers.referer = 'http://demasia-pro.preview.ponshine.fe/';
      },
    },
    // 以下3个规则是前后端分离开发环境中的单点登录使用：
    '/sso/login': {
      target: 'http://host:port',
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
      headers: {
        // windows 操作系统中使用 sdp 连接需要 'Connection': 'keep-alive'
        Connection: 'keep-alive',
      },
      bypass: function (req, res, proxyOptions) {
        req.headers.origin = 'http://host:port';
        req.headers.host = 'host:port';
        req.headers.referer = 'http://host:port/';
      },
    },
    '/oauth2/authorization/ponshine': {
      target: 'http://host:port',
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
      headers: {
        // windows 操作系统中使用 sdp 连接需要 'Connection': 'keep-alive'
        Connection: 'keep-alive',
      },
      bypass: function (req, res, proxyOptions) {
        req.headers.origin = 'http://host:port';
        req.headers.host = 'host:port';
        req.headers.referer = 'http://host:port/';
      },
    },
    '/login/oauth2/code/ponshine': {
      target: 'http://host:port',
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
      headers: {
        // windows 操作系统中使用 sdp 连接需要 'Connection': 'keep-alive'
        Connection: 'keep-alive',
      },
      bypass: function (req, res, proxyOptions) {
        req.headers.origin = 'http://host:port';
        req.headers.host = 'host:port';
        req.headers.referer = 'http://host:port/';
      },
    },
  },
  test: {
    '/api/': {
      target: 'http://demasia-pro.preview.ponshine.fe',
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
      headers: {
        // windows 操作系统中使用 sdp 连接需要 'Connection': 'keep-alive'
        Connection: 'keep-alive',
      },
      bypass: function (req, res, proxyOptions) {
        req.headers.origin = 'http://demasia-pro.preview.ponshine.fe';
        req.headers.host = 'demasia-pro.preview.ponshine.fe';
        req.headers.referer = 'http://demasia-pro.preview.ponshine.fe/';
      },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
      headers: {
        // windows 操作系统中使用 sdp 连接需要 'Connection': 'keep-alive'
        Connection: 'keep-alive',
      },
    },
  },
};
