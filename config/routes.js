/*
 * @Description:
 * @Author: ss
 * @Date: 2022-09-16 14:43:24
 * @LastEditTime: 2025-07-14 16:37:41
 * @LastEditors: ss
 * @Reference:
 */
// ponshine routes: http://doc.ponshine.fe/ponshinejs/ponshine/V2.x/zh/guide/router.html
export default [
  {
    path: '/user',
    component: '../layouts/UserLayout',
    routes: [
      {
        name: '登录',
        icon: 'smile',
        path: '/user/login',
        component: './user/login',
      },
      {
        name: '修改密码',
        icon: 'smile',
        path: '/user/changePassword',
        component: './ChangePassword',
      },
    ],
  },
  {
    path: '/',
    component: '../layouts/BasicLayout',
    routes: [
      {
        path: '/',
        redirect: '/situationAnalysis/situationAnalysis',
      },
      // {
      //   path: '/welcome',
      //   name: '欢迎',
      //   icon: 'smile',
      //   component: './Welcome',
      // },
      // {
      //   path: '/admin',
      //   name: 'admin',
      //   icon: 'crown',
      //   component: './Admin',
      //   routes: [
      //     {
      //       path: '/admin/sub-page',
      //       name: 'sub-page',
      //       icon: 'smile',
      //       component: './Welcome',
      //     },
      //   ],
      // },
      {
        path: '/situationAnalysis',
        name: '态势分析',
        icon: 'fund',
        routes: [
          {
            name: '态势分析',
            path: '/situationAnalysis/situationAnalysis',
            component: './SituationAnalysis',
          },
          {
            name: '地市态势分析',
            path: '/situationAnalysis/citySituationAnalysis',
            component: './SituationAnalysis/CitySituationAnalysis',
          },
          {
            name: '地市关停及白名单统计',
            path: '/situationAnalysis/cityAndWhiteListStatistics',
            component: './SituationAnalysis/CityAndWhiteListStatistics',
          },
          {
            name: '复机统计',
            path: '/situationAnalysis/RepeatedMachineStatistics',
            component: './SituationAnalysis/RepeatedMachineStatistics',
          },
          {
            name: '关停标签统计',
            path: '/situationAnalysis/ShutdownLabelStatistics',
            component: './SituationAnalysis/ShutdownLabelStatistics',
          },
          {
            path: '/situationAnalysis/OperationsMonitoringCenter',
            name: '运营监控中心',
            component: './OperationsMonitoringCenterNew',
          },
          {
            path: '/situationAnalysis/antiFraudCockpit',
            name: '反诈态势分析驾驶舱',
            component: './AntiFraudCockpit',
          },
        ],
      },
      {
        path: '/disposalCenter',
        name: '处置中心',
        icon: 'block',
        routes: [
          {
            name: '防欺诈关停管理',
            path: '/disposalCenter/fraudPreventionManage',
            component: './DisposalCenter/FraudPreventionManage',
          },
          {
            name: '防欺诈关停标签管理',
            path: '/disposalCenter/fraudPreventionLabelManage',
            component: './DisposalCenter/FraudPreventionLabelManage',
          },
          {
            name: '配置记录',
            path: '/disposalCenter/fraudPreventionLabelManage/configRecord',
            component: './DisposalCenter/FraudPreventionLabelManage/ConfigRecord',
            hideInMenu: true,
          },
          {
            name: '策略管理',
            path: '/disposalCenter/AnalyStrategyManage',
            component: './DisposalCenter/Strategy/AnalyStrategyManage',
          },
          {
            name: '策略历史记录',
            path: '/disposalCenter/strategyHistory',
            component: './DisposalCenter/Strategy/strategyHistory',
            hideInMenu: true,
          },
          {
            name: '涉案类号码复开审批',
            path: '/disposalCenter/involveFraudCaseApproval',
            component: './DisposalCenter/InvolveFraudCaseApproval',
            // extra: {
            //   intercepted: false,
            //   hideInMenu: true,
            // },
          },
          {
            name: '终端管理',
            path: '/disposalCenter/TerminalManagement',
            routes: [
              {
                name: '黑终端查询',
                // name: 'whiteList-manage',
                path: '/disposalCenter/TerminalManagement/BlackTerminalQuery',
                component: './DataCenter/TerminalManagement/BlackTerminalQuery',
              },
              {
                name: '白终端查询',
                // name: 'whiteList-manage',
                path: '/disposalCenter/TerminalManagement/WhiteTerminalQuery',
                component: './DataCenter/TerminalManagement/WhiteTerminalQuery',
              },
              {
                name: '终端操作记录查询',
                // name: 'whiteList-manage',
                path: '/disposalCenter/TerminalManagement/TerminalOperationRecordQuery',
                component: './DataCenter/TerminalManagement/TerminalOperationRecordQuery',
              },
              {
                name: '终端记录审批',
                // name: 'whiteList-manage',
                path: '/disposalCenter/TerminalManagement/TerminalRecordApproval',
                component: './DataCenter/TerminalManagement/TerminalRecordApproval',
              },
              {
                name: '终端信息查询',
                // name: 'whiteList-manage',
                path: '/disposalCenter/TerminalManagement/TerminalInfoSearch',
                component: './DataCenter/TerminalManagement/TerminalInfoSearch',
              },
              {
                name: '终端关联查询',
                // name: 'whiteList-manage',
                path: '/disposalCenter/TerminalManagement/TerminalAssociationSearch',
                component: './DataCenter/TerminalManagement/TerminalAssociationSearch',
              },
              {
                name: '终端批量关联查询',
                // name: 'Terminal_batch_Association_query',
                path: '/disposalCenter/TerminalManagement/TerminalAssociationquery',
                component: './DataCenter/TerminalManagement/TerminalAssociationquery',
              },
            ],
          },
          {
            name: '外呼核验工单查询',
            path: '/disposalCenter/outCallVerificationRecord',
            component: './DisposalCenter/OutCallVerification/WorkOrderRecord',
          },
          {
            name: '外呼核验工单处理',
            path: '/disposalCenter/outCallVerificationHandle',
            component: './DisposalCenter/OutCallVerification/WorkOrderHandle',
          },
          {
            name: '线下关停复机记录查询',
            path: '/disposalCenter/offlineShutdownRecords',
            component: './DisposalCenter/OfflineShutdownRecords',
          },
          {
            name: '外呼预警语音提醒',
            path: '/disposalCenter/outCallEarlyWarningVoiceReminder',
            component: './DisposalCenter/OutCallWarningReminder/VoiceReminder',
          },
          {
            name: '外呼预警短信提醒',
            path: '/disposalCenter/outCallEarlyWarningMessageReminder',
            component: './DisposalCenter/OutCallWarningReminder/MessageReminder',
          },
          {
            name: '关停审批',
            path: '/disposalCenter/shutdownApprove',
            component: './DisposalCenter/ShutdownApprove',
          },
          {
            name: '复机审批',
            path: '/disposalCenter/reviewApprove',
            component: './DisposalCenter/ReviewApprove',
          },
          {
            name: '卡断数据审批',
            path: '/disposalCenter/snapDataApprove',
            component: './DisposalCenter/SnapDataApprove',
          },
          {
            name: '模型管理',
            path: '/disposalCenter/modelDataManage',
            component: './DisposalCenter/ModelManage',
          },
          {
            name: '配置记录',
            path: '/disposalCenter/modalDataConfigRecord',
            component: './DisposalCenter/ModelManage/ModelConfigManage/ConfigRecord',
            extra: {
              intercepted: false,
            },
            hideInMenu: true,
          },
          {
            name: '处置配置中心',
            path: '/disposalCenter/disposalConfigCenter',
            component: './DisposalCenter/DisposalConfigCenter',
          },
          // 数据接入配置-配置记录
          {
            name: '数据接入配置记录',
            path: '/disposalCenter/DataAccessConfigRecord',
            component: './DisposalCenter/DisposalConfigCenter/DataAccessConfig/ConfigRecord',
            extra: {
              intercepted: false,
            },
            hideInMenu: true,
          },
          // 数据分类配置-配置记录
          {
            name: '数据分类配置记录',
            path: '/disposalCenter/DataCategoryConfigRecord',
            component: './DisposalCenter/DisposalConfigCenter/DataCategoryConfig/ConfigRecord',
            extra: {
              intercepted: false,
            },
            hideInMenu: true,
          },
          // 处置策略配置-配置记录
          {
            name: '处置策略配置记录',
            path: '/disposalCenter/HandleStrategyConfigRecord',
            component: './DisposalCenter/DisposalConfigCenter/HandleStrategyConfig/ConfigRecord',
            extra: {
              intercepted: false,
            },
            hideInMenu: true,
          },
          // AI质检人工核验
          {
            name: 'AI质检人工核验',
            path: '/disposalCenter/AIQualityInspectionManualVerification',
            component: './DisposalCenter/AIQualityInspectionManualVerification',
            extra: {
              intercepted: false,
            },
          },
        ],
      },
      {
        path: '/dataCenter',
        // name: 'data-center',
        name: '数据中心',
        icon: 'database',
        routes: [
          {
            name: '黑白灰名单管理',
            // name: 'whiteList-manage',
            path: '/dataCenter/blackWhiteGreyListManage',
            routes: [
              {
                name: '白名单信息查询',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/whiteListInfoQuery',
                component: './DataCenter/WhiteListManage/InfoQuery',
              },
              {
                name: '白名单批量变更',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/whiteListBatchChange',
                component: './DataCenter/WhiteListManage/BatchChange',
              },
              {
                name: '白名单工单管理',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/whiteListWorkOrderManage',
                component: './DataCenter/WhiteListManage/WorkOrderManage',
              },
              {
                name: '白名单工单审批',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/whiteListWorkOrderExamine',
                component: './DataCenter/WhiteListManage/WorkOrderExamine',
              },
              {
                name: '黑灰名单信息查询',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/blackListInfoQuery',
                component: './DataCenter/BlackListManage/InfoQuery',
              },
              {
                name: '黑灰名单批量变更',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/blackListBatchChange',
                component: './DataCenter/BlackListManage/BatchChange',
              },
              {
                name: '黑灰名单操作记录管理',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/blackListWorkOrderManage',
                component: './DataCenter/BlackListManage/WorkOrderManage',
              },
              {
                name: '黑灰名单工单审批',
                // name: 'whiteList-manage',
                path: '/dataCenter/blackWhiteGreyListManage/blackListWorkOrderExamine',
                component: './DataCenter/BlackListManage/WorkOrderExamine',
              },
            ],
          },

          {
            name: '用户画像',
            path: '/dataCenter/userPortrait',
            component: './DataCenter/UserPortrait',
          },

          {
            name: '用户信息查询',
            path: '/dataCenter/userInfoSearch',
            routes: [
              {
                name: '用户信息查询',
                path: '/dataCenter/userInfoSearch/list',
                component: './DataCenter/UserSearch/UserInfoSearch',
              },
              {
                name: '用户信息批量查询',
                path: '/dataCenter/userInfoSearch/batchList',
                component: './DataCenter/UserSearch/UseInfoBatchSearch',
              },
              // 所在地查询
              {
                name: '所在地查询',
                path: '/dataCenter/userInfoSearch/locationQuery',
                component: './QuerySignal/LocationQuery',
              },
              // 话单查询
              {
                name: '话单查询',
                // path: '/dataCenter/userInfoSearch/locationQuery',
                path: '/dataCenter/userInfoSearch/callTicket',
                component: './QuerySignal/CallTicket',
              },
              // 呼叫信令查询
              {
                name: '呼叫信令查询',
                path: '/dataCenter/userInfoSearch/callSignalingQuery',
                component: './QuerySignal/CallSignalingQuery',
              },
              // 日轨迹查询
              {
                name: '日轨迹查询',
                path: '/dataCenter/userInfoSearch/dailyTrackQuery',
                component: './QuerySignal/DailyTrackQuery',
              },
            ],
          },
          {
            name: '公安驻场号码信息查询',
            path: '/dataCenter/policeStationNumberInfoSearch',
            routes: [
              {
                name: '号码信息查询',
                path: '/dataCenter/policeStationNumberInfoSearch/numberInfoQuery',
                component: './DataCenter/NumberInfoQuery',
              },
              {
                name: '号码信息批量查询',
                path: '/dataCenter/policeStationNumberInfoSearch/numberInfoBatchQuery',
                component: './DataCenter/NumberInfoBatchQuery',
              },
            ],
          },

          {
            name: '集团上报数据',
            path: '/dataCenter/groupReport',
            routes: [
              {
                name: '工信部共享数据反馈',
                path: '/dataCenter/groupReport/GXBSharedDataViewFeedback',
                component: './DataCenter/GXBSharedDataViewFeedback',
              },
              {
                name: '申诉信息查询',
                path: '/dataCenter/groupReport/appealInfoSearch',
                component: './DataCenter/AppealInformationPush/AppealInfoSearch',
              },
              {
                name: '申诉信息审批',
                path: '/dataCenter/groupReport/appealInfoApprove',
                component: './DataCenter/AppealInformationPush/AppealInfoApprove',
              },
              {
                name: '申诉信息反馈',
                path: '/dataCenter/groupReport/appealInfoFeedback',
                component: './DataCenter/AppealInformationPush/AppealInfoFeedback',
              },
              {
                name: '打猫成效查询',
                // name: 'whiteList-manage',
                path: '/dataCenter/groupReport/typingCatQuery',
                component: './TypingCatInformation/TypingCatQuery',
              },
              {
                name: '打猫成效审批',
                // name: 'whiteList-manage',
                path: '/dataCenter/groupReport/typingCatApprove',
                component: './TypingCatInformation/TypingCatApprove',
              },
            ],
          },

          // {
          //   name: '信令话单查询',
          //   path: '/dataCenter/querySignaling',
          //   component: './QuerySignaling',
          // },

          {
            name: '外呼核验查询',
            path: '/dataCenter/outCallCheck',
            routes: [
              {
                name: '外呼核验统计分析',
                path: '/dataCenter/outCallCheck/statisticalAnalysis',
                component: './DataCenter/OutCallCheck/StatisticalAnalysis',
              },
              {
                name: '外呼核验明细数据查询',
                path: '/dataCenter/outCallCheck/detailDataQuery',
                component: './DataCenter/OutCallCheck/DetailDataQuery',
              },
            ],
          },

          {
            name: '反诈知识库',
            path: '/dataCenter/AntiFraudKnowledgeBase',
            component: './QuerySignal/AntiFraudKnowledgeBase',
          },

          {
            name: '涉诈短彩数据分析',
            path: '/dataCenter/fraudMessageDataAnalysis',
            routes: [
              {
                name: '涉诈短彩态势分析',
                path: '/dataCenter/fraudMessageDataAnalysis/situationAnalysis',
                component: './DataCenter/FraudMessageDataAnalysis/FraudMessageSituationAnalysis',
              },
              {
                name: '涉诈短彩数据查询',
                path: '/dataCenter/fraudMessageDataAnalysis/dataSearch',
                component: './DataCenter/FraudMessageDataAnalysis/FraudMessageDataSearch',
              },
            ],
          },
          {
            name: 'AI质检管理',
            path: '/dataCenter/AIQualityInspectionManage',
            routes: [
              {
                name: '签入签出管理',
                path: '/dataCenter/AIQualityInspectionManage/signInMoveOutManage',
                component: './DataCenter/AIQualityInspectionManage/SignInMoveOutManage',
              },
              {
                name: '关键词管理',
                path: '/dataCenter/AIQualityInspectionManage/keyWordsManage',
                component: './DataCenter/AIQualityInspectionManage/NewKeyWordsManage',
              },
              {
                name: '关键词配置记录',
                path: '/dataCenter/AIQualityInspectionManage/keyWordsManageRecord',
                component: './DataCenter/AIQualityInspectionManage/NewKeyWordsManage/ConfigRecord',
                extra: {
                  intercepted: false,
                },
                hideInMenu: true,
              },
            ],
          },
          {
            name: '黑基站管理',
            path: '/dataCenter/blackBaseStationManage',
            component: './DataCenter/BlackBaseStationManage',
          },
        ],
      },
      {
        path: '/diskInformateManage',
        name: '复盘信息管理',
        icon: 'read',
        routes: [
          // {
          //   name: '涉案通报核查复盘',
          //   path: '/diskInformateManage/caseNotifyVerify',
          //   component: './DiskInformateManage/CaseNotifyVerify',
          // },
          // {
          //   name: '诈骗电话举报复盘',
          //   path: '/diskInformateManage/fraudulentTelephoneReport',
          //   component: './DiskInformateManage/FraudulentTelephoneReport',
          // },
          // {
          //   name: '公安通报核查复盘',
          //   path: '/diskInformateManage/securityNotifyVerify',
          //   component: './DiskInformateManage/SecurityNotifyVerify',
          // },
          {
            name: '涉案涉诈号码复盘',
            path: '/diskInformateManage/mobileFraudVerificationResume',
            component: './DiskInformateManage/MobileFraudVerificationResume',
          },
          {
            name: '固话涉诈核查复盘',
            path: '/diskInformateManage/fixedLineFraudVerificationResume',
            component: './DiskInformateManage/FixedLineFraudVerificationResume',
          },
          {
            name: '每日号码详单',
            path: '/diskInformateManage/dailyNumberDetail',
            component: './DiskInformateManage/DailyNumberDetail',
          },
          {
            name: '电渠漫游异常清单',
            path: '/diskInformateManage/roamExceptionList',
            component: './DiskInformateManage/RoamExceptionList',
          },
          {
            name: '窝点信息管理',
            path: '/diskInformateManage/densInfoManage',
            component: './DiskInformateManage/DensInfoManage',
          },
          {
            name: '业务模型管理',
            path: '/diskInformateManage/businessModelManagement',
            component: './DiskInformateManage/BusinessModelManagement',
          },
          {
            name: '业务模型变更管理',
            path: '/diskInformateManage/businessModelChangeManagement',
            component: './DiskInformateManage/BusinessModelChangeManagement',
          },
        ],
      },
      {
        path: '/assessStatistics',
        name: '考核统计',
        icon: 'calculator',
        routes: [
          {
            name: '涉案举报考核数据查询', //反诈考核数据查询
            path: '/assessStatistics/antiFraudAssess',
            component: './AssessStatistics/AntiFraudAssess',
          },
          {
            name: '涉案举报考核排名查询', //集团涉案及举报考核排名查询
            path: '/assessStatistics/caseAndReportAssess',
            component: './AssessStatistics/CaseAndReportAssess',
          },
          {
            name: '省内考核指标值', // 地市考核指标查询
            path: '/assessStatistics/citiesAssess',
            component: './AssessStatistics/CitiesAssess',
          },
          {
            name: '月度考核统计查询', // 考核月度统计
            path: '/assessStatistics/assessMonthStatistics',
            component: './AssessStatistics/AssessMonthStatistics',
          },
          {
            name: '反诈攻坚日通报', // 考核日报
            path: '/assessStatistics/assessDaily',
            component: './AssessStatistics/AssessDaily',
          },
          {
            name: '集团日报统计',
            path: '/assessStatistics/groupDailyStatistic',
            component: './AssessStatistics/GroupDailyStatistic',
          },
          {
            name: '日常通报',
            path: '/assessStatistics/dailyNotice',
            component: './AssessStatistics/DailyNotice',
          },
          {
            name: '大数据模型分场景关停复机统计',
            path: '/assessStatistics/bigDataSceneShutDownStartStatistics',
            component: './AssessStatistics/BigDataSceneShutDownStartStatistics',
          },
          {
            name: '新入网外呼统计',
            path: '/assessStatistics/newOutboundCallsStatistics',
            component: './AssessStatistics/NewOutboundCallsStatistics',
          },
          {
            name: '网点分析报表',
            path: '/assessStatistics/dotAnalysisReport',
            component: './AssessStatistics/DotAnalysisReport',
          },
          {
            name: '报表制作与通报',
            path: '/assessStatistics/reportMakeReport',
            component: './AssessStatistics/ReportMakeReport',
          },
          {
            name: '报告记录管理处理',
            path: '/assessStatistics/reportMakeReport_recordManage_handle/:id',
            component: './AssessStatistics/ReportMakeReport/ReportRecordManage/HandleDetail',
            // extra: {
            //   intercepted: false,
            // },
            hideInMenu: true,
          },
          // 省联席办数据查询
          {
            name: '省联席办数据查询',
            path: '/assessStatistics/provinceJointMeetingOfficeDataQuery',
            component: './AssessStatistics/ProvinceJointMeetingOfficeDataQuery',
          },
        ],
      },
      {
        path: '/antiFraudSpecialTopic',
        name: '反诈专题分析',
        icon: 'bar-chart',
        routes: [
          {
            name: '校园卡专题',
            path: '/antiFraudSpecialTopic/campusCardTopic',
            component: './AssessStatistics/ThematicManage/CampusCardTopic',
          },
          {
            name: '固话专题',
            path: '/antiFraudSpecialTopic/fixedLineTopic',
            component: './AssessStatistics/ThematicManage/FixedLineTopic',
          },
          {
            name: '互联网专题',
            path: '/antiFraudSpecialTopic/internetTopic',
            component: './AntiFraudSpecialTopic/InternetTopic',
          },
        ],
      },
      {
        path: '/sysManage',
        name: '系统管理',
        icon: 'block',
        routes: [
          {
            name: '导出权限记录查询',
            path: '/sysManage/exportPermissionRecord',
            component: './ExportPermission/Record',
          },
          {
            name: '导出权限审批',
            path: '/sysManage/exportPermissionApprove',
            component: './ExportPermission/Approve',
          },
          {
            name: '账号延期申请',
            path: '/sysManage/accountExtensionApply',
            component: './AccountExtensionApply',
          },
          {
            name: '账号延期审批',
            path: '/sysManage/accountExtensionApprove',
            component: './AccountExtensionApprove',
          },
          {
            name: '账号绑定IP管理',
            path: '/sysManage/accountBindIPManage',
            component: './AccountBindIPManage',
          },

          {
            name: '日志管理',
            path: '/sysManage/logManage',
            component: './LogManage',
          },
          // {
          //   name: 'menu-manage',
          //   path: '/sysManage/menuManage',
          //   component: './MenuManage',
          // },
          {
            name: '用户组管理',
            path: '/sysManage/userGroupManage',
            component: './UserGroupManage',
          },
          {
            name: '账号权限管理',
            path: '/sysManage/accountPermissionManage',
            component: './AccountPermissionManage',
          },
          {
            name: '用户管理',
            path: '/sysManage/userManage',
            component: './UserManage',
          },
          {
            name: '安全配置',
            path: '/sysManage/securityConfig',
            component: './SecurityConfig',
          },
          {
            name: '菜单管理',
            //  icon: 'smile',
            path: '/sysManage/menuManage',
            component: './MenuManage',
          },

          // {
          //   name: 'personal-center',
          //   path: '/sysManage/personalCenter',
          //   component: './PersonalCenter',
          // },
        ],
      },
      {
        component: './404',
      },
    ],
  },
  {
    component: './404',
  },
];
