/*
 * @Description: 
 * @Author: ss
 * @Date: 2022-09-16 14:43:24
 * @LastEditTime: 2022-09-16 15:18:17
 * @LastEditors: ss
 * @Reference: 
 */
import SelectLang from '@/components/SelectLang';
import { DefaultFooter, getBrowserTitle, getMenuData } from 'demasia-pro-layout';
import { connect } from 'dryad'; // eslint-disable-next-line import/no-extraneous-dependencies
import { Link } from 'ponshine';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { Helmet } from 'react-helmet';
import projectSettings from '../../config/projectSettings';
import styles from './UserLayout.less';

const { logo, title } = projectSettings;

const UserLayout = (props) => {
  const {
    route = {
      routes: [],
    },
  } = props;
  const { routes = [] } = route;
  const {
    children,
    location = {
      pathname: '',
    },
  } = props;
  const { breadcrumb } = getMenuData(routes);
  const browserTitle = getBrowserTitle({
    // pathname: location.pathname,
    breadcrumb,
    formatMessage,
    title,
    ...props,
  });
  return (
    <>
      <Helmet>
        <title>{browserTitle}</title>
        <meta name="description" content={browserTitle} />
      </Helmet>
      <div className={styles.container}>
        <div className={styles.lang}>
          <SelectLang />
        </div>
        <div className={styles.content}>
          <div className={styles.top}>
            <div className={styles.header}>
              <span className={styles.title}>反诈运营管理平台</span>
              <div className={styles.minTitle}>用户个人信息保护提醒</div>
              {
                ['第一条 收集用户个人信息前须征得用户同意。',
                '第二条 不得收集使用与工作无关的用户个人信息。',
                '第三条 不得拍摄电脑终端屏幕上的用户个人信息。',
                '第四条 不在本地电脑终端存储用户个人信息。',
                '第五条 不得通过公网即时通信工具传递用户个人信息。',
                '第六条 不得将用户个人信息透露给无关人员。',
                '第七条 用户个人信息使用完毕后须遵照相关规定及时删除。',
                '第八条 批量导出用户个人信息需严格审批和记录。',
                '第九条 一人一账号，不得转让或共用。开展账号行为定期审核审计。'].map(v=>{
                  return (
                    <div className={styles.minContentText}>{v}</div>
                  )
                })
              }
              {/* <Link to="/"> */}
                {/* <img alt="logo" className={styles.logo} src={logo} /> */}
                {/* <span className={styles.title}>反诈运营管理平台</span> */}
              {/* </Link> */}
            </div>
            {/* <div className={styles.desc}>Demasia 是 Web 设计规范</div> */}
          </div>
          {children}
        </div>
        <DefaultFooter />
      </div>
    </>
  );
};

export default connect(({ settings }) => ({ ...settings }))(UserLayout);
