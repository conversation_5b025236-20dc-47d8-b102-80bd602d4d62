/*
 * @Description:
 * @Author: ss
 * @Date: 2022-09-16 14:43:24
 * @LastEditTime: 2022-09-20 10:41:27
 * @LastEditors: ss
 * @Reference:
 */

import { Icon } from 'antd';
import ProLayout, { Tooltip } from 'demasia-pro-layout';
import { connect } from 'dryad';
import { useAuth } from 'ponshine'; // eslint-disable-next-line import/no-extraneous-dependencies
import { useEffect } from 'react';
// import { formatMessage } from 'ponshine-plugin-react/locale';
import RightContent from '@/components/GlobalHeader/RightContent';
import projectSettings from '../../config/projectSettings';
import { TpWatermark } from '@/utils/TpWatermark';
import moment from 'moment';
const BasicLayout = (props) => {
  const { dispatch, children, settings, user } = props;
  /**
   * constructor
   */

  const authState = useAuth((authModel) => authModel.authState);

  useEffect(() => {
    if (typeof useAuth !== 'function' && dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      });
    }
  }, []);

  useEffect(() => {
    // if (name) {
    authState?.user?.username &&
      TpWatermark(
        `<div>${authState?.user?.username}  ${moment().format('YYYY-MM-DD HH:mm:ss')}</div>`,
      );
    // }
  }, []);

  useEffect(() => {
    document.body.scrollTop = document.documentElement.scrollTop = 0;
  }, [children]);
  return (
    <ProLayout
      logo={projectSettings.logo}
      title={projectSettings.title}
      footerContent={undefined}
      footerCopyright="2022 鹏信前端出品"
      // defaultBrowserTitle={projectSettings.title}
      browserTitleRender={() => projectSettings.title}
      // formatMessage={formatMessage}
      headerRightContentRender={() => <RightContent />}
      {...props}
      settings={settings}
      tabsBarHomeIcon={false}
      breadcrumbHomeIcon={false}
    >
      {children}
    </ProLayout>
  );
};

export default connect(({ settings, user }) => ({
  settings,
  user,
}))(BasicLayout);
