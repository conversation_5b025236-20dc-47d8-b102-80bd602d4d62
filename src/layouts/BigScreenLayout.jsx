import React, { useEffect } from 'react';

import NavBar from '@/components/NavBar';

import useWindowSize from '@/components/useWindowSize';
import useResizeObserver from 'use-resize-observer';

import './BigScreenLayout.less';

export default ({ children }) => {
  const { width: windowWidth, height: windowHeight } = useWindowSize();
  const { ref, height } = useResizeObserver();
  useEffect(() => {
    document.title = '湖南电信反诈态势分析驾驶舱';
  }, []);
  return (
    <div
      id="screenRoot"
      style={{ overflow: 'hidden', height: height ? height * (windowWidth / 1920) : 0 }}
    >
      <div
        id="screenRoot_scale"
        ref={ref}
        style={{ width: 1920, transform: `scale(${windowWidth / 1920})`, transformOrigin: '0 0' }}
      >
        <header>
          <NavBar />
        </header>
        <main>{children}</main>
      </div>
    </div>
  );
};
