@import (reference) '~antd/es/style/themes/default.less';

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: @layout-body-background;
}

.lang {
  width: 100%;
  height: 40px;
  line-height: 44px;
  text-align: right;
  :global(.ant-dropdown-trigger) {
    margin-right: 24px;
  }
}

.content {
  // flex: 1;
  padding: 32px 0;
}

@media (min-width: @screen-md-min) {
  .container {
    background-image: url('../../public/bg.jpg');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100%;
  }

  .content {
    padding:80px 50px 24px 130px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}

.top {
  text-align: center;
  margin: 0 auto;
  // padding: 10px;
  width: 450px;
}

.header {
  height: 44px;
  line-height: 44px;
  a {
    text-decoration: none;
  }
}

.logo {
  height: 44px;
  margin-right: 16px;
  vertical-align: top;
}

.title {
  position: relative;
  top: 2px;
  color: @heading-color;
  font-weight: 600;
  font-size: 33px;
  font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
  color:#fff;
  margin-bottom: 18px;
  display: inline-block;
}
.minTitle{
  font-weight: 600;
  font-size: 20px;
  font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
  color:#fff;
  text-align: left;
}
.minContentText{
  line-height: 33px;
  text-align: left;
  color:#fff;
  font-weight: 600;
} 

.desc {
  margin-top: 12px;
  margin-bottom: 40px;
  color: @text-color-secondary;
  font-size: @font-size-base;
}
