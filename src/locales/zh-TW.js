import component from './zh-TW/component';
import globalHeader from './zh-TW/globalHeader';
import menu from './zh-TW/menu';
import pwa from './zh-TW/pwa';
import settingDrawer from './zh-TW/settingDrawer';
import settings from './zh-TW/settings';
import tabs from './zh-TW/tabs';
export default {
  'navBar.lang': '語言',
  'layout.user.link.help': '幫助',
  'layout.user.link.privacy': '隱私',
  'layout.user.link.terms': '條款',
  'app.preview.down.block': '下載此頁面到本地項目',
  'app.welcome.link.fetch-blocks': '獲取全部區塊',
  'app.welcome.link.block-list': '基於 block 開發，快速構建標準頁面',
  'app.welcome.link.or': '或',
  'auth.403.title': '403',
  'auth.403.sub-title': '抱歉，您無權訪問此頁面。',
  'auth.403.go-login': '去登錄頁',
  'auth.404.title': '404',
  'auth.404.sub-title': '抱歉，您訪問的頁面不存在。',
  'auth.404.back-home': '返回首頁',
  'auth.500.title': '500',
  'auth.500.sub-title': '抱歉，服務器出錯了。',
  'auth.500.back-home': '返回首頁',
  ...globalHeader,
  ...settingDrawer,
  ...tabs,
  ...menu,
  ...settings,
  ...pwa,
  ...component,
};
