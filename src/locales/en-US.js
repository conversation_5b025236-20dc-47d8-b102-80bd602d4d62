import component from './en-US/component';
import globalHeader from './en-US/globalHeader';
import menu from './en-US/menu';
import pwa from './en-US/pwa';
import settingDrawer from './en-US/settingDrawer';
import settings from './en-US/settings';
import tabs from './en-US/tabs';
export default {
  'navBar.lang': 'Languages',
  'layout.user.link.help': 'Help',
  'layout.user.link.privacy': 'Privacy',
  'layout.user.link.terms': 'Terms',
  'app.preview.down.block': 'Download this page to your local project',
  'app.welcome.link.fetch-blocks': 'Get all block',
  'app.welcome.link.block-list': 'Quickly build standard, pages based on `block` development',
  'app.welcome.link.or': 'Or',
  'auth.403.title': '403',
  'auth.403.sub-title': 'Sorry, you are not authorized to access this page.',
  'auth.403.go-login': 'Go Login',
  'auth.404.title': '404',
  'auth.404.sub-title': 'Sorry, the page you visited does not exist.',
  'auth.404.back-home': 'Back Home',
  'auth.500.title': '500',
  'auth.500.sub-title': 'Sorry, the server is reporting an error.',
  'auth.500.back-home': 'Back Home',
  ...globalHeader,
  ...settingDrawer,
  ...tabs,
  ...menu,
  ...settings,
  ...pwa,
  ...component,
};
