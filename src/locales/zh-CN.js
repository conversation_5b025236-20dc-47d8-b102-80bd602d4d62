import component from './zh-CN/component';
import globalHeader from './zh-CN/globalHeader';
import menu from './zh-CN/menu';
import pwa from './zh-CN/pwa';
import settingDrawer from './zh-CN/settingDrawer';
import settings from './zh-CN/settings';
import tabs from './zh-CN/tabs';
export default {
  'navBar.lang': '语言',
  'layout.user.link.help': '帮助',
  'layout.user.link.privacy': '隐私',
  'layout.user.link.terms': '条款',
  'app.preview.down.block': '下载此页面到本地项目',
  'app.welcome.link.fetch-blocks': '获取全部区块',
  'app.welcome.link.block-list': '基于 block 开发，快速构建标准页面',
  'app.welcome.link.or': '或',
  'auth.403.title': '403',
  'auth.403.sub-title': '抱歉，您无权访问此页面。',
  'auth.403.go-login': '去登录页',
  'auth.404.title': '404',
  'auth.404.sub-title': '抱歉，您访问的页面不存在。',
  'auth.404.back-home': '返回首页',
  'auth.500.title': '500',
  'auth.500.sub-title': '抱歉，服务器出错了。',
  'auth.500.back-home': '返回首页',
  ...globalHeader,
  ...settingDrawer,
  ...tabs,
  ...menu,
  ...settings,
  ...pwa,
  ...component,
};
