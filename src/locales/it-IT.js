import component from './it-IT/component';
import globalHeader from './it-IT/globalHeader';
import menu from './it-IT/menu';
import pwa from './it-IT/pwa';
import settingDrawer from './it-IT/settingDrawer';
import settings from './it-IT/settings';
import tabs from './it-IT/tabs';
export default {
  'navBar.lang': 'Languages',
  'layout.user.link.help': 'Help',
  'layout.user.link.privacy': 'Privacy',
  'layout.user.link.terms': 'Terms',
  'app.preview.down.block': 'Download this page to your local project',
  'app.welcome.link.fetch-blocks': 'Get all block',
  'app.welcome.link.block-list': 'Quickly build standard, pages based on `block` development',
  'app.welcome.link.or': 'O',
  'auth.403.title': '403',
  'auth.403.sub-title': 'Spiacenti, non sei autorizzato ad accedere a questa pagina.',
  'auth.403.go-login': '<PERSON>ai Accedi',
  'auth.404.title': '404',
  'auth.404.sub-title': 'Siamo spiacenti, la pagina visitata non esiste.',
  'auth.404.back-home': 'Ritorno casa',
  'auth.500.title': '500',
  'auth.500.sub-title': 'Spiacenti, il server sta segnalando un errore.',
  'auth.500.back-home': 'Ritorno casa',
  ...globalHeader,
  ...settingDrawer,
  ...tabs,
  ...menu,
  ...settings,
  ...pwa,
  ...component,
};
