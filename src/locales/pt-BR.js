import component from './pt-BR/component';
import globalHeader from './pt-BR/globalHeader';
import menu from './pt-BR/menu';
import pwa from './pt-BR/pwa';
import settingDrawer from './pt-BR/settingDrawer';
import settings from './pt-BR/settings';
import tabs from './pt-BR/tabs';
export default {
  'navBar.lang': 'Idiomas',
  'layout.user.link.help': 'ajuda',
  'layout.user.link.privacy': 'política de privacidade',
  'layout.user.link.terms': 'termos de serviços',
  'app.preview.down.block': 'Faça o download desta página no seu projeto local',
  'app.welcome.link.fetch-blocks': 'Obter todo o bloco',
  'app.welcome.link.block-list':
    'Crie rapidamente páginas padrão e baseadas no desenvolvimento de blocos',
  'app.welcome.link.or': 'Ou',
  'auth.403.title': '403',
  'auth.403.sub-title': '<PERSON><PERSON><PERSON><PERSON>, você não tem acesso a esta página.',
  'auth.403.go-login': '<PERSON><PERSON>gar',
  'auth.404.title': '404',
  'auth.404.sub-title': 'Desculpe, a página que você visitou não existe.',
  'auth.404.back-home': 'Voltar Início',
  'auth.500.title': '500',
  'auth.500.sub-title': 'Desculpe, o servidor está relatando um erro.',
  'auth.500.back-home': 'Voltar Início',
  ...globalHeader,
  ...settingDrawer,
  ...tabs,
  ...menu,
  ...settings,
  ...pwa,
  ...component,
};
