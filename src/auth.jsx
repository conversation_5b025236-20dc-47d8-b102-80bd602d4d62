import request from '@/utils/request';
import { Button, message, notification } from 'antd';
import 'antd/es/notification/style/index.less';
import {
  getAuthLocation,
  router,
  useAuth,
  /* refreshAuth, getRefreshAuthState, getAuthState as getAuthStateSync, */
  useAuthLocation,
  /* Licensee, useLicensee, CharterLicense, authRequest, */
  checkPathname,
} from 'ponshine';
import { getResponseHeader } from 'ponshine-request';
import { useLayoutEffect, useRef, useState } from 'react'; // import { IRoute } from 'ponshine-types';
import { matchPath } from 'react-router';
import projectSettings from '../config/projectSettings'; // 单点登录，当 response 中的 status 为 302 时执行（必须将接口请求的参数 redirect 设为 'manual'，而且当状态码为 302 时，js 只能获取到状态码为 0 但此时的 type 为 opaqueredirect）
import NoPermissionPage from './pages/403';
import Loading from './pages/Loading';

import { aesDecode } from '@/services/login';

const doSSO = (redirectUri) => {
  if (typeof projectSettings.doSSOAddress === 'string' && projectSettings.doSSOAddress) {
    // TODO doSSOAddress 记得反向代理到真实的后端服务
    const jumpUrl = `${projectSettings.doSSOAddress}${
      projectSettings.doSSOAddress.indexOf('?') !== -1 ? '' : '?'
    }&redirect_uri=${window.encodeURIComponent(redirectUri || window.location.href)}`;
    window.location.href = jumpUrl;
  }
};

const getAuthApi = '/api/user/getSessionValues';
const getLoginCaptchaApis = [
  '/api/imgCode', // @demasia-rc/captcha 常规验证码的接口
  '/api/captcha/get', // @demasia-rc/captcha 滑动拼图验证码的接口
];

const verifyLoginCaptchaApis = [
  '/api/captcha/check', // @demasia-rc/captcha 滑动拼图验证码的接口
];
const forgotPasswordApis = ['/api/user/sendBackPasswordCaptcha', '/api/user/backPassword'];
const changeInitialPasswordPathname = '/user/changePassword';
let isChangeInitialPassword = true;
const changeExpiredPasswordPathname = '/user/changePassword';
let isChangeExpiredPassword = true;
const loginPathname = '/user/login'; // 为兼容单体服务和单点登录，登录接口改为 POST /api/login，登出接口改为 POST /api/logout

const loginApi = '/api/phone/login';
const logoutApi = '/api/logout';
const homePathname = '/';
const rootRoutePath = '/';
const unInterceptRoutePathPrefix = '/user'; // 为了兼容单点登录，所有接口全部改为 options.redirect = 'manual';
// const manualRedirectApis = [logoutApi]; // 阻止重定向 (并不是像它的名字描述的那样「手动」)，阻止之后才可由 flow 中的 response 时机时处理

const GetAuthStateErrorSymbol = Symbol('GetAuthStateErrorSymbol');

const getAddressOrParamsAPi = '/api/hn/systemConfig/getLoginParam';

const code = new URL(window.location.href).searchParams.get('code');
let getUnifiedCertificationAccessTokenRunned = false;
let checkUnifiedCertificationAvailableRunned = false;
let checkUnifiedCertificationAvailableError = false;
let accessToken = '';
// 是否探活成功
let checkUnifiedCertificationAvailableFlag = true;

let setUnifiedCertificationLoading;

let unifiedCertificationInfo = {
  // exploreLifeUrl: 'http://**************:30048/api/openapi/uac/code/survival',
  // obtainAuthorizationCodeUrl: 'http://**************:32207/index.html',
  // getAccessTokenUrl: 'http://**************:30048/api/openapi/hnOauth/oauth/token',
  // logoutUrl: 'http://**************:30048/api/openapi/hnOauth/token/logout',
  // appId: '6d0c1b3b95166f8e2aa239a7baaf6c81',
  // appKey: 'ff77b2019428c38339685015c06bc00f',
  // clientId: 'TYRZ_HNFZ',
  // clientSecret: 'TYRZ_HNFZ',
  // codeRedirectUri: 'http://**************:8000',
  // tokenRedirectUri: 'http://**************:8000',
};

function removeUrlParameterAndGetNewUrl(url, parameterName) {
  const urlObject = new URL(url);
  const urlParams = new URLSearchParams(urlObject.search);

  // 删除所有同名参数
  urlParams.forEach((_, name) => {
    if (name === parameterName) {
      urlParams.delete(name);
    }
  });

  // 重建 URL
  const newSearch = urlParams.toString();
  const newUrl =
    urlObject.origin + urlObject.pathname + (newSearch ? '?' + newSearch : '') + urlObject.hash;
  return newUrl;
}
function removeUnifiedCertificationLocationCodeParam() {
  const newUrl = removeUrlParameterAndGetNewUrl(location.href, 'code');
  if (location.href !== newUrl) {
    location.href = newUrl;
  }
}

// 获取统一认证相关参数和接口地址
async function getAddressOrParams(url) {
  if (getUnifiedCertificationAccessTokenRunned) {
    return;
  }
  try {
    const response = await request.get(getAddressOrParamsAPi, {
      useCSRFToken: false,
      syncInjectIntoContext: () => ({
        ignoreRouteFlow: true, // ponshine-plugin-auth V4.2.0+ 时支持通过 ignoreRouteFlow 来控制 request 流程是否被 route 流程阻塞（默认阻塞）
      }),
    });
    if (response.code === 200) {
      Object.keys(response.data).forEach((ele) => {
        unifiedCertificationInfo[ele] = aesDecode(response.data[ele]);
      });
    } else {
      message.error(response.msg);
    }
  } catch (error) {
    console.error('There was a problem with the fetch operation:', error);
  }
}

const pullMessage = (content = '', fallback) => {
  message.error(content, 2).then((res) => {
    fallback && fallback();
  });
};

// 探活，并在统一认证服务可用时跳转到统一认证的登录页
const checkUnifiedCertificationAvailableAndGotoUnifiedCertificationLogin = async (
  fallback,
  isDoSelfLogout,
) => {
  const {
    exploreLifeUrl,
    clientId,
    appId,
    appKey,
    codeRedirectUri,
    clientSecret,
    obtainAuthorizationCodeUrl,
  } = unifiedCertificationInfo;

  if (checkUnifiedCertificationAvailableRunned) {
    return;
  }
  checkUnifiedCertificationAvailableRunned = true;
  try {
    const response = await request.post(`/api/hn/autoLogin/survival`, {
      data: {
        clientId,
      },
      useCSRFToken: false,
      headers: {
        'X-APP-ID': appId,
        'X-APP-KEY': appKey,
      },
      syncInjectIntoContext: () => ({
        ignoreRouteFlow: true, // ponshine-plugin-auth V4.2.0+ 时支持通过 ignoreRouteFlow 来控制 request 流程是否被 route 流程阻塞（默认阻塞）
      }),
    });
    checkUnifiedCertificationAvailableRunned = false;

    if (response.code === 200 && response.data.code === 0) {
      // window.location.href = 'http://**************:8000?code=123';
      window.location.href = `${obtainAuthorizationCodeUrl}?client_id=${clientId}&client_secret=${clientSecret}&redirect_uri=${codeRedirectUri}`;
    } else {
      message.destroy();

      message.error(
        response.message + (fallback && isDoSelfLogout ? '，已为您自动跳转至系统原有登录页面' : ''),
      );
      // debugger;

      await fallback();
    }
  } catch (error) {
    console.log('---error', error);
  }
};

// // 用统一认证返回来的code获取accessToken
const getUnifiedCertificationAccessToken = async () => {
  // debugger;
  if (!getUnifiedCertificationAccessTokenRunned) {
    await getAddressOrParams();
  }

  if (!code) {
    return;
  }

  if (getUnifiedCertificationAccessTokenRunned) {
    return;
  }
  getUnifiedCertificationAccessTokenRunned = true;

  const { getAccessTokenUrl, appId, appKey, clientId, clientSecret, tokenRedirectUri } =
    unifiedCertificationInfo;
  // debugger;

  const response = await request(`/api/hn/autoLogin/acceptCode`, {
    method: 'POST',
    requestType: 'form',
    headers: {
      'X-APP-ID': appId,
      'X-APP-KEY': appKey,
    },
    data: {
      client_id: clientId,
      client_secret: clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: tokenRedirectUri,
    },
    syncInjectIntoContext: () => ({
      ignoreRouteFlow: true, // ponshine-plugin-auth V4.2.0+ 时支持通过 ignoreRouteFlow 来控制 request 流程是否被 route 流程阻塞（默认阻塞）
    }),
  });
  // debugger;

  if (response.code === 200 && response?.data?.code === 0) {
    accessToken = response?.data?.data;
    // debugger;

    await request('/api/hn/autoLogin/acceptAccessToken', {
      method: 'POST',
      requestType: 'form',
      data: { encryptToken: accessToken },
      syncInjectIntoContext: () => ({
        ignoreRouteFlow: true, // ponshine-plugin-auth V4.2.0+ 时支持通过 ignoreRouteFlow 来控制 request 流程是否被 route 流程阻塞（默认阻塞）
      }),
    }).then(async (res) => {
      // debugger;
      if (res.state === 'FAIL') {
        pullMessage(
          res.message + '，2s后自动为您跳转至登录',
          removeUnifiedCertificationLocationCodeParam,
        );
      } else {
        removeUnifiedCertificationLocationCodeParam();
      }
    });
  } else {
    pullMessage(response.message || '', removeUnifiedCertificationLocationCodeParam);
  }
};

export async function getAuthState() {
  return request
    .get(`${getAuthApi}?timestamp=${Date.now()}`, {
      useCSRFToken: false,
      syncInjectIntoContext: () => ({
        ignoreRouteFlow: true,
        // ponshine-plugin-auth V4.2.0+ 时支持通过 ignoreRouteFlow 来控制 request 流程是否被 route 流程阻塞（默认阻塞）
        ignoreWarning: true, // 配合 responseFeaturesHandler 使用，取消403报错提示
      }),
    }) // 此处加时间戳是为了防止缓存
    .then(function cb(rs) {
      const {
        permission: { menus = [], buttons = [] } = {},
        principal: user = null,
        status = '',
        accountStatus,
        authenticationPolicy,
        passwordPolicy,
        // TODO
        accessToken: resAccessToken,
      } = rs || {};
      accessToken = resAccessToken;

      if (status === 'invalid') {
        return undefined;
      }

      const menuPathnameJsonByNodeId = {};
      menus.forEach((menu) => {
        const { nodeId, pageUrl } = menu;
        menuPathnameJsonByNodeId[nodeId] = pageUrl;
      });
      const fmtButtons = [];
      buttons.forEach((button) => {
        const { pid, url, nodeText } = button;

        if (menuPathnameJsonByNodeId[pid]) {
          let foundIndex = fmtButtons.findIndex(
            (b) => b.menuPathname === menuPathnameJsonByNodeId[pid],
          );

          if (foundIndex === -1) {
            fmtButtons.push({
              menuPathname: menuPathnameJsonByNodeId[pid],
              data: [],
            });
            foundIndex = fmtButtons.length - 1;
          }

          fmtButtons[foundIndex].data.push({
            license: url,
            text: nodeText,
          });
        }
      });
      return {
        user,
        permission: {
          menus,
          buttons,
          fmtButtons,
        },
        accountStatus,
        authenticationPolicy,
        passwordPolicy,
        accessToken,
      };
    })
    .catch(function cb(err) {
      // eslint-disable-next-line
      console.error(err);

      if (err?.response?.status === 403 || err?.response?.status === 401) {
        return null;
      }

      return GetAuthStateErrorSymbol;
    });
}
export function Wrapper(props) {
  const { children } = props;
  const authState = useAuth((authModel) => authModel.authState);
  const location = useAuthLocation();

  // if ((code && getUnifiedCertificationAccessTokenRunned) || authState === GetAuthStateErrorSymbol) {
  //   return <Loading />;
  // }

  if (authState === GetAuthStateErrorSymbol) {
    return <Loading />;
  }

  if (
    location.pathname.indexOf(unInterceptRoutePathPrefix) !== 0 &&
    location.pathname !== rootRoutePath &&
    !authState
  ) {
    return null;
  }

  // 如果是登录页，且未探活时，不允许访问
  // if (location.pathname.indexOf(loginPathname) === 0 && checkUnifiedCertificationAvailableFlag) {
  //   console.log(window.location.host, '99999');
  //   router.replace(rootRoutePath);
  //   return null;
  // }
  return children;
}
export const RefreshingState = Loading;
export function Watch(props) {
  const { authModel } = props;
  const { loading: isLoading, authState } = authModel;
  const isLoadingRef = useRef(isLoading);
  isLoadingRef.current = isLoading;
  const timeoutRef = useRef(null);
  const [delaying, setDelaying] = useState(!isLoading);
  const [unifiedCertificationLoading, _setUnifiedCertificationLoading] = useState(false);
  setUnifiedCertificationLoading = _setUnifiedCertificationLoading;
  const authStateRef = useRef(authState);
  authStateRef.current = authState;
  useLayoutEffect(() => {
    clearTimeout(timeoutRef.current);

    if (!isLoadingRef.current) {
      setDelaying(true);
      const delayTime = authStateRef.current ? 1500 : 100;
      timeoutRef.current = setTimeout(() => {
        if (!isLoadingRef.current) {
          setDelaying(false);
        }
      }, delayTime);
    } else {
      setDelaying(false);
    }

    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [isLoading]);

  if (isLoading || delaying || unifiedCertificationLoading) {
    return <Loading />;
  }

  return null;
}
export function interceptor(authState, interceptRoutesState) {
  // @ts-ignore
  const { permission: { menus = [] } = {} } = authState || {};
  const menuPathnames = menus.map((menu) => menu.pageUrl);

  const setIntercept = (routes) => {
    routes.forEach((route) => {
      const { path, routes: childrenRoutes, extra } = route;

      if (
        !path ||
        path === rootRoutePath ||
        path.indexOf(unInterceptRoutePathPrefix) === 0 ||
        // 添加不拦截路由的extra
        (extra && extra.intercepted === false)
      ) {
        Object.assign(route, {
          intercepted: false,
        });
      } else if (path && menuPathnames.find((menuPathname) => !!matchPath(menuPathname, route))) {
        Object.assign(route, {
          intercepted: false,
        });
      }

      if (childrenRoutes) {
        setIntercept(childrenRoutes);
      }
    });
  };

  setIntercept(interceptRoutesState);
}
export const Fallback = NoPermissionPage; // export function Access(props: any) {}

export function patchRoute(intercepted, route, authState) {
  // @ts-ignore
  const { permission: { menus = [] } = {} } = authState || {};
  const { path } = route;

  if (intercepted) {
    Object.assign(route, {
      hideInMenu: true,
    });
  }

  let foundMenu = null;

  if (
    path &&
    menus.find((menu) => {
      const match = matchPath(menu.pageUrl, route);

      if (match) {
        foundMenu = menu;
      }

      return !!match;
    })
  ) {
    // @ts-ignore
    const { nodeText, imageClassName } = foundMenu; // eslint-disable-line

    Object.assign(route, {
      // name: !nodeText ? route.name : nodeText, // 取消菜单国际化后可开启通过数据库配置菜单名称
      icon: !imageClassName ? route.icon : imageClassName,
    });
  }
}
export function licensor(authState, props, licensesState) {
  // @ts-ignore
  const { permission: { fmtButtons = [] } = {} } = authState || {};
  const { route } = props;
  let foundBtnData = null;

  if (
    fmtButtons.find((btn) => {
      const match = matchPath(btn.menuPathname, route);

      if (match) {
        foundBtnData = btn.data;
      }

      return !!match;
    })
  ) {
    if (Array.isArray(foundBtnData)) {
      foundBtnData.forEach((btnData) => {
        const { license, ...rest } = btnData;
        Object.assign(licensesState, {
          [license]: rest,
        });
      });
    }
  }
}
export const requestInitOptions = (
  req, // @ts-ignore
  injected, // eslint-disable-line
) => {
  const { url, options: opt } = req;
  let parsedUrl;

  try {
    if (url.indexOf('http://') === 0 || url.indexOf('https://') === 0) {
      parsedUrl = new URL(url);
    } // eslint-disable-next-line no-empty
  } catch (e) {}

  let isSameOrigin = true;

  if (parsedUrl) {
    const { origin } = parsedUrl;
    const { origin: localOrigin } = new URL(window.location.href);
    isSameOrigin = origin === localOrigin;
  }

  const options = {
    mode: opt.mode ?? (isSameOrigin ? 'same-origin' : 'cors'),
    requestType: opt.requestType ?? 'form', // useCSRFToken: opt.useCSRFToken ?? (isSameOrigin && true), // TODO 默认同域开启 useCSRFToken
    // useCSRFToken:false
  }; // TODO 按照 2020-10-23 最新约定 getLoginCaptchaApis verifyLoginCaptchaApis forgotPasswordApis这些接口需要开启 CSRFToken，请研发人员根据实际需要自行开启
  // if (isSameOrigin && [...getLoginCaptchaApis, ...verifyLoginCaptchaApis, ...forgotPasswordApis].includes(parsedUrl?.pathname || (typeof url === 'string' && url.indexOf('?') !== -1 ? url.substring(0, url.indexOf('?')) : url))) {
  //   options.useCSRFToken = true;
  // }
  // 为了兼容单点登录，所有接口全部改为 options.redirect = 'manual';
  // 阻止重定向 (并不是像它的名字描述的那样「手动」)，阻止之后才可由 flow 中的 response 时机时处理
  // if (manualRedirectApis.includes(url)) {
  //   options.redirect = 'manual';
  // }

  if (isSameOrigin) {
    options.redirect = 'manual';
  }

  return options;
};
export function responseFeaturesHandler({
  status,
  statusText,
  url,
  // @ts-ignore
  // eslint-disable-next-line
  location,
  // @ts-ignore
  // eslint-disable-next-line
  locationAction,
  // @ts-ignore
  // eslint-disable-next-line
  authState,
  // @ts-ignore
  // eslint-disable-next-line
  refreshAuthState,
  request: requestFeature,
  parsed,
  injected,
}) {
  const statusDescription = {
    200: '服务器成功返回请求的数据。',
    201: '新建或修改数据成功。',
    202: '一个请求已经进入后台排队（异步任务）。',
    204: '删除数据成功。',
    304: '自从上次请求后，请求的网页未修改过。',
    400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
    401: '用户没有权限（令牌、用户名、密码错误）。',
    403: '用户得到授权，但是访问是被禁止的。',
    404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
    406: '请求的格式不可得。',
    410: '请求的资源被永久删除，且不会再得到的。',
    422: '当创建一个对象时，发生一个验证错误。',
    500: '服务器发生错误，请检查服务器。',
    502: '网关错误。',
    503: '服务不可用，服务器暂时过载或维护。',
    504: '网关超时。',
  };

  if (status === undefined) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
      placement: 'topRight',
    });
  } else if (
    !((status >= 200 && status < 300) || status === 304 || status === 401) &&
    (statusDescription[status] || statusText)
  ) {
    if (!(injected.ignoreWarning && status === 403)) {
      // 接口可单独配置取消报错提示，参考 getAuth 方法使用 syncInjectIntoContext 进行配置
      notification.error({
        message: `请求错误 ${status}: ${url || requestFeature.url}`,
        description: statusDescription[status] || statusText,
        placement: 'topRight',
      });
    }
  }

  if (parsed instanceof Error) {
    // eslint-disable-next-line
    console.error(parsed);
  }
}
export function dispatchLoginChecker(interceptRouteState, interceptRouteStatus) {
  if (
    !interceptRouteState.routes &&
    !interceptRouteState.intercepted &&
    !interceptRouteState.redirect &&
    interceptRouteState.path.indexOf(unInterceptRoutePathPrefix) !== 0
  ) {
    return true;
  }

  return false;
}
let isInitAuth = false;

const unifiedCertificationCommonfun = async (
  dispatch,
  isDoRefreshAuthAndUpdateMemo,
  isDoSelfLogout,
  isInterceptRoutePath,
) => {
  if (!getUnifiedCertificationAccessTokenRunned) {
    setUnifiedCertificationLoading(true);
  }

  await getUnifiedCertificationAccessToken();

  // 防止多次請求
  if (code && getUnifiedCertificationAccessTokenRunned) {
    return;
  }
  const authModelForRefreshAuth = await dispatch('refreshAuth', !isDoRefreshAuthAndUpdateMemo); // isInitAuth = true; // 应该在 await dispatch('refreshAuth') 之前赋值

  // 未登录
  if (authModelForRefreshAuth && !authModelForRefreshAuth.authState) {
    await checkUnifiedCertificationAvailableAndGotoUnifiedCertificationLogin(async () => {
      if (isDoRefreshAuthAndUpdateMemo) {
        const authModelForRefreshAuth = await dispatch('refreshAuth', true);
        if (authModelForRefreshAuth && !authModelForRefreshAuth.authState) {
          if (isDoSelfLogout) {
            await dispatch('logout', loginPathname, true);
          }
        }
      } else {
        if (isDoSelfLogout) {
          await dispatch('logout', loginPathname, true);
        }
      }
    }, isDoSelfLogout);
    if (!checkUnifiedCertificationAvailableRunned) {
      setUnifiedCertificationLoading(false);
    }
  } else if (!isInterceptRoutePath) {
    // 已登录，且路由是不拦截的路由（登录），探活
    await checkUnifiedCertificationAvailableAndGotoUnifiedCertificationLogin();
    if (!checkUnifiedCertificationAvailableRunned) {
      setUnifiedCertificationLoading(false);
    }
  }

  if (!checkUnifiedCertificationAvailableRunned) {
    setUnifiedCertificationLoading(false);
  }
};

const getNoticeContent = (value) => {
  if (value === 0) {
    return '账号今日到期，请立即申请延期';
  } else if (value > 0) {
    return '账号还有' + value + '天到期，请申请延期';
  } else if (value < 0) {
    return '账号已过期' + Math.abs(value) + '天，请申请延期';
  }
};

export async function flow(when, things, dispatch) {
  const {
    authModel: authModelThing,
    toLocation: toLocationThing,
    request: requestThing,
    response: responseThing,
    parsed: parsedThing,
  } = things;
  let requestUrl;

  switch (when) {
    case 'route':
      //是否拦截路由
      const isInterceptRoutePath =
        toLocationThing.pathname.indexOf(unInterceptRoutePathPrefix) !== 0;
      // 未登录
      if (
        !authModelThing.authState
        // &&
        // toLocationThing.pathname.indexOf(unInterceptRoutePathPrefix) !== 0
      ) {
        // isInitAuth = true; 应该在 await dispatch('refreshAuth') 之前赋值，这样可解决路由并发时多次调用 refreshAuth 的问题
        isInitAuth = true; // 防止首次打开被拦截地址跳转到登录页后重新请求权限接口
        // 统一认证方法
        await unifiedCertificationCommonfun(
          dispatch,
          isInterceptRoutePath,
          isInterceptRoutePath,
          isInterceptRoutePath,
        );
      } else if (!isInitAuth) {
        isInitAuth = true;
        // await dispatch('refreshAuth');
        await unifiedCertificationCommonfun(dispatch, true, false, isInterceptRoutePath);
      } else if (authModelThing.authState && isInitAuth && !isInterceptRoutePath) {
        // 通过tab键切换路由且切换的不是拦截页面
        await unifiedCertificationCommonfun(dispatch, false, false, isInterceptRoutePath);
      }
      if (
        (isChangeInitialPassword || isChangeExpiredPassword) &&
        toLocationThing.pathname.indexOf(unInterceptRoutePathPrefix) !== 0
      ) {
        router.push(
          isChangeInitialPassword ? changeInitialPasswordPathname : changeExpiredPasswordPathname,
        );
      } else if (
        !isChangeInitialPassword &&
        toLocationThing.pathname === changeInitialPasswordPathname &&
        !isChangeExpiredPassword &&
        toLocationThing.pathname === changeExpiredPasswordPathname
      ) {
        router.replace(rootRoutePath);
      }

      break;

    case 'request':
      requestUrl = requestThing.url
        ? requestThing.url
        : requestThing.options && (requestThing.options.url || '');

      if (requestUrl.indexOf('?') !== -1) {
        requestUrl = requestUrl.substring(0, requestUrl.indexOf('?'));
      }

      const { url } = requestThing;
      let parsedUrl;

      try {
        if (url.indexOf('http://') === 0 || url.indexOf('https://') === 0) {
          parsedUrl = new URL(url);
        } // eslint-disable-next-line no-empty
      } catch (e) {}

      let isSameOrigin = true;

      if (parsedUrl) {
        const { origin } = parsedUrl;
        const { origin: localOrigin } = new URL(window.location.href);
        isSameOrigin = origin === localOrigin;
      }
      if (!isSameOrigin) {
        return true;
      }

      if (
        !authModelThing.authState &&
        ![
          getAuthApi,
          ...getLoginCaptchaApis,
          ...verifyLoginCaptchaApis,
          ...forgotPasswordApis,
          loginApi,
          logoutApi,
          getAddressOrParamsAPi,
          '/api/login/captcha',
          '/api/hn/sendMessage/sendLoginVerificationCode',
          '/api/phone/login',
          '/unifiedCertification/api/openapi/uac/code/survival',
          '/unifiedCertification/api/openapi/hnOauth/oauth/token',
          '/api/hn/autoLogin/acceptAccessToken',
          '/api/hn/autoLogin/acceptCode',
          '/api/hn/autoLogin/survival',

          // '/api/hn/whiteLoginIp/checkLoginIp',
        ].includes(requestUrl)
      ) {
        // ponshine-plugin-auth V4.2.0 已经能做到在 route 流程时阻塞 request 流程了，所以以下代码可注释掉了，直接 return false 即可
        // const authModelForRefreshAuth = await dispatch('refreshAuth', true);
        // if (!authModelForRefreshAuth.authState) {
        //   return false;
        // }
        return false;
      }

      break;

    case 'response':
      if (responseThing) {
        const { status, type } = responseThing;

        if (status === 302 || (status === 0 && type === 'opaqueredirect')) {
          doSSO();
        }

        try {
          if (
            JSON.parse(getResponseHeader(responseThing, 'account-status') || '').firstLogin === true
          ) {
            isChangeInitialPassword = true;
            router.push(changeInitialPasswordPathname);
            return undefined;
          } // eslint-disable-next-line no-empty
        } catch (e) {}

        isChangeInitialPassword = false;

        try {
          if (
            JSON.parse(getResponseHeader(responseThing, 'account-status') || '')
              .credentialsNonExpired === false
          ) {
            isChangeExpiredPassword = true;
            router.push(changeExpiredPasswordPathname);
            return undefined;
          } // eslint-disable-next-line no-empty
        } catch (e) {}

        isChangeExpiredPassword = false;

        try {
          const { credentialsExpireAfter } = JSON.parse(
            getResponseHeader(responseThing, 'account-status') || '',
          );

          if (typeof credentialsExpireAfter !== 'undefined') {
            notification.warning({
              message: '请注意！',
              duration: null,
              description: (
                <div>
                  密码还有
                  <span
                    style={{
                      color: 'red',
                      fontSize: 18,
                    }}
                  >
                    {credentialsExpireAfter}
                  </span>
                  天到期，请及时修改！
                </div>
              ),
            });
          } // eslint-disable-next-line no-empty
        } catch (e) {}

        // 新增账号到期提醒
        try {
          const accountStatus = JSON.parse(
            getResponseHeader(responseThing, 'account-status') || '',
          );
          if (
            // delayApproving：1表示在申请中，当账号有效期小于等于15天或者已到期的账号，没有申请延期时，提示
            (accountStatus.accountExpireNote === 0 || accountStatus.accountExpireNote) &&
            accountStatus.delayApproving !== 1
          ) {
            notification.destroy();
            notification.warning({
              message: '账号到期提醒',
              description: getNoticeContent(accountStatus.accountExpireNote),
              duration: null,
              btn: (
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    router.push('/sysManage/accountExtensionApply');
                  }}
                >
                  去申请
                </Button>
              ),
            });
          }
        } catch (e) {}

        if (getResponseHeader(responseThing, 'session-status') === 'EXPIRED') {
          await dispatch('refreshAuth');
          await checkUnifiedCertificationAvailableAndGotoUnifiedCertificationLogin(async () => {
            await dispatch('logout', loginPathname);
          });

          return undefined;
        }

        const { url } = requestThing;
        let parsedUrl;
        const pathname = url.indexOf('?') !== -1 ? url.substring(0, url.indexOf('?')) : url;

        try {
          if (url.indexOf('http://') === 0 || url.indexOf('https://') === 0) {
            parsedUrl = new URL(url);
          } // eslint-disable-next-line no-empty
        } catch (e) {}

        let isSameOrigin = true;

        if (parsedUrl) {
          const { origin } = parsedUrl;
          const { origin: localOrigin } = new URL(window.location.href);
          isSameOrigin = origin === localOrigin;
        }

        const location = getAuthLocation();

        if (pathname === logoutApi) {
          // 单点登录退出
          if (typeof parsedThing?.data?.location === 'string' && parsedThing?.data?.location) {
            const doSSOLogoutAddress = parsedThing?.data?.location; // TODO 你可以先使用 router.push 或者 router.replace 进行地址栏的变更后再立即调用 window.location.href，从而获得正确的重定向地址
            const jumpUrl = `${doSSOLogoutAddress}${
              doSSOLogoutAddress.indexOf('?') !== -1 ? '' : '?'
            }&redirect_uri=${window.encodeURIComponent(window.location.href)}`;
            window.location.href = jumpUrl;
            return undefined;
          }

          if (location.pathname !== loginPathname) {
            // debugger;
            await dispatch('refreshAuth');
            await checkUnifiedCertificationAvailableAndGotoUnifiedCertificationLogin(async () => {
              await dispatch('logout', loginPathname);
            });
          }
        } else if (isSameOrigin && pathname !== getAuthApi) {
          if (status === 401 && location.pathname.indexOf(unInterceptRoutePathPrefix) !== 0) {
            if (location.pathname !== loginPathname) {
              const authModelForRefreshAuth = await dispatch('refreshAuth');
              if (authModelForRefreshAuth && !authModelForRefreshAuth.authState) {
                await checkUnifiedCertificationAvailableAndGotoUnifiedCertificationLogin(
                  async () => {
                    await dispatch('logout', loginPathname);
                  },
                );
              }
            }
          } else if ((status >= 200 && status < 300) || status === 304) {
            if (
              pathname === loginApi &&
              parsedThing &&
              ['SUCCESS', 'OK'].includes(`${parsedThing.state || parsedThing.status}`.toUpperCase())
            ) {
              if (location.pathname !== homePathname) {
                const authModelForRefreshAuth = await dispatch('refreshAuth');

                if (authModelForRefreshAuth && authModelForRefreshAuth.authState) {
                  await dispatch('login', homePathname);
                }
              }
            }
          }
        }
      }

      break;

    default:
      break;
  }

  return undefined;
}
