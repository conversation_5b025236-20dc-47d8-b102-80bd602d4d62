/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-22 09:47:39
 * @LastEditors: zhao<PERSON><PERSON>en
 * @LastEditTime: 2022-09-23 15:49:43
 * @FilePath: \hunanfanzha\src\models\DataCenter\BlackListManage\WorkOrderManage.js
 * @Description:
 */
import { findTableData } from '@/services/DataCenter/BlackListManage/WorkOrderManage';

const Model = {
  namespace: 'blackListWorkOrderManage',
  state: {
    tableData: {
      list: [],
      pagination: {},
    },
  },
  effects: {
    *findTableData({ payload, callback }, { call, put }) {
      const response = yield call(findTableData, payload);

      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      response.data.items.map((item) => {
        item.key = item.id;
        return item;
      });

      yield put({
        type: 'save',
        payload: {
          tableData: {
            list: response.data.items || [],
            pagination: {
              total: response.data.totalNum,
              pageSize: payload.pageSize,
              current: payload.currentPage,
            },
          },
        },
      });
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
export default Model;
