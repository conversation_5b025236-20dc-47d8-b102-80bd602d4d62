/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:44:43
 * @LastEditors: zhao<PERSON><PERSON>en
 * @LastEditTime: 2022-09-30 15:35:16
 * @FilePath: \hunanfanzha\src\models\DataCenter\BlackListManage\InfoQuery.js
 * @Description:
 */

import {
  findTableData,
  addBlackInfo,
  updateBlackInfo,
  deleteBlackInfo,
  exportBlackInfo,
} from '@/services/DataCenter/BlackListManage/InfoQuery';

const Model = {
  namespace: 'blackListInfoQuery',
  state: {
    tableData: {
      list: [],
      pagination: {},
    },
  },
  effects: {
    *findTableData({ payload, callback }, { call, put }) {
      const response = yield call(findTableData, payload);

      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      response.data.items.map((item) => {
        item.key = item.id;
        return item;
      });

      yield put({
        type: 'save',
        payload: {
          tableData: {
            list: response.data.items || [],
            pagination: {
              total: response.data.totalNum,
              pageSize: payload.pageSize,
              current: payload.pageNum,
            },
          },
        },
      });
    },

    *addBlackInfo({ payload, callback }, { call, put }) {
      const response = yield call(addBlackInfo, payload);
      if (callback) callback(response);
    },
    *updateBlackInfo({ payload, callback }, { call, put }) {
      const response = yield call(updateBlackInfo, payload);
      if (callback) callback(response);
    },
    *deleteBlackInfo({ payload, callback }, { call, put }) {
      const response = yield call(deleteBlackInfo, payload);
      if (callback) callback(response);
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
export default Model;
