import {
  findTableData,
  blackListApprove,
} from '@/services/DataCenter/BlackListManage/WorkOrderExamine';

const pageSizeOptions = ['10', '20', '30', '50', '200', '1000', '5000'];

const Model = {
  namespace: 'blackWorkOrderExamine',
  state: {
    tableData: {
      list: [],
      pagination: {
        total: 0,
        pageNum: 1,
        pageSize: 200,
        pageSizeOptions,
      },
    },
    searchParams: {},
  },
  effects: {
    *findTableData({ payload, callback }, { call, put }) {
      const response = yield call(findTableData, payload);

      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      response.data.items.map((item) => {
        item.key = item.id;
        return item;
      });

      yield put({
        type: 'save',
        payload: {
          tableData: {
            list: response.data.items || [],
            pagination: {
              total: response.data.totalNum,
              pageSize: payload.pageSize,
              current: payload.currentPage,
              pageSizeOptions,
            },
          },
          searchParams: payload,
        },
      });
    },
    *blackListApprove({ payload, callback }, { call, put }) {
      const response = yield call(blackListApprove, payload);
      if (callback) callback(response);
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
export default Model;
