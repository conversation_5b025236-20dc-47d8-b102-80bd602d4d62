/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:44:43
 * @LastEditors: zhao<PERSON><PERSON>en
 * @LastEditTime: 2022-09-23 15:49:53
 * @FilePath: \hunanfanzha\src\models\DataCenter\WhiteListManage\InfoQuery.js
 * @Description:
 */

import {
  findTableData,
  addWhiteInfo,
  updateWhiteInfo,
  deleteWhiteInfo,
  exportWhiteInfo,
} from '@/services/DataCenter/WhiteListManage/InfoQuery';

const Model = {
  namespace: 'whiteListInfoQuery',
  state: {
    tableData: {
      list: [],
      pagination: {},
    },
  },
  effects: {
    *findTableData({ payload, callback }, { call, put }) {
      const response = yield call(findTableData, payload);

      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      response.data.items.map((item) => {
        item.key = item.id;
        return item;
      });

      yield put({
        type: 'save',
        payload: {
          tableData: {
            list: response.data.items || [],
            pagination: {
              total: response.data.totalNum,
              pageSize: payload.pageSize,
              current: payload.currentPage,
            },
          },
        },
      });
    },

    *addWhiteInfo({ payload, callback }, { call, put }) {
      const response = yield call(addWhiteInfo, payload);
      if (callback) callback(response);
    },
    *updateWhiteInfo({ payload, callback }, { call, put }) {
      const response = yield call(updateWhiteInfo, payload);
      if (callback) callback(response);
    },
    *deleteWhiteInfo({ payload, callback }, { call, put }) {
      const response = yield call(deleteWhiteInfo, payload);
      if (callback) callback(response);
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
export default Model;
