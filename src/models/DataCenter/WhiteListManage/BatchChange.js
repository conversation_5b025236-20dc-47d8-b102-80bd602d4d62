/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-22 09:47:39
 * @LastEditors: z<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-10 12:13:38
 * @FilePath: \hunanfanzha\src\models\DataCenter\WhiteListManage\BatchChange.js
 * @Description:
 */
import { findTableData, submitFileList } from '@/services/DataCenter/WhiteListManage/BatchChange';

const Model = {
  namespace: 'batchChange',
  state: {
    tableData: {
      list: [],
      pagination: {},
    },
  },
  effects: {
    *findTableData({ payload, callback }, { call, put }) {
      const response = yield call(findTableData, payload);

      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      response.data.items.map((item) => {
        item.key = item.id;
        return item;
      });

      yield put({
        type: 'save',
        payload: {
          tableData: {
            list: response.data.items || [],
            pagination: {
              total: response.data.totalNum,
              pageSize: payload.pageSize,
              current: payload.currentPage,
            },
          },
        },
      });
    },

    *submitFileList({ payload, callback }, { call, put }) {
      const response = yield call(submitFileList, payload);
      if (callback) callback(response);
    },
    *clearTableData({ payload, callback }, { call, put }) {
      yield put({
        type: 'save',
        payload: {
          tableData: {
            list: [],
            pagination: false,
          },
        },
      });
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
export default Model;
