/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-22 09:47:39
 * @LastEditors: z<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-23 15:49:58
 * @FilePath: \hunanfanzha\src\models\DataCenter\WhiteListManage\WorkOrderExamine.js
 * @Description:
 */
import {
  findTableData,
  whiteListApprove,
} from '@/services/DataCenter/WhiteListManage/WorkOrderExamine';

const Model = {
  namespace: 'workOrderExamine',
  state: {
    tableData: {
      list: [],
      pagination: {},
    },
  },
  effects: {
    *findTableData({ payload, callback }, { call, put }) {
      const response = yield call(findTableData, payload);

      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      response.data.items.map((item) => {
        item.key = item.id;
        return item;
      });

      yield put({
        type: 'save',
        payload: {
          tableData: {
            list: response.data.items || [],
            pagination: {
              total: response.data.totalNum,
              pageSize: payload.pageSize,
              current: payload.currentPage,
              pageSizeOptions: ['10', '20', '30', '50', '200'],
            },
          },
        },
      });
    },
    *whiteListApprove({ payload, callback }, { call, put }) {
      const response = yield call(whiteListApprove, payload);
      if (callback) callback(response);
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
export default Model;
