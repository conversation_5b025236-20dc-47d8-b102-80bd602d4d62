import {
  fakeAccountLogin,
  fakeAccount<PERSON>ogout,
  getFake<PERSON><PERSON>tcha,
  getFakeNormalCaptcha,
  sendLoginVerificationCode,
} from '@/services/login';
const Model = {
  namespace: 'login',
  state: {
    status: undefined,
  },
  effects: {
    *login({ payload, callback }, { call, put }) {
      const response = yield call(fakeAccountLogin, payload);
      yield put({
        type: 'changeLoginStatus',
        payload: response,
      });
      if (callback) callback(response);
    },

    *getCaptcha({ payload }, { call }) {
      yield call(getFakeCaptcha, payload);
    },
    *sendLoginVerificationCode({ payload, callback }, { call }) {
      const response = yield call(sendLoginVerificationCode, payload);
      if (callback) callback(response);
    },

    *getNormalCaptcha(_, { call }) {
      yield call(getFakeNormalCaptcha);
    },

    *logout(_, { call }) {
      yield call(fakeAccountLogout);
    },
  },
  reducers: {
    changeLoginStatus(state, { payload }) {
      return {
        ...state,
        message: payload.message || payload.msg,
        status:
          payload.status ||
          (!payload.error &&
          (`${payload.state || ''}`.toUpperCase() === 'SUCCESS' || payload.success)
            ? 'ok'
            : 'error'),
        type: payload.type || 'account',
      };
    },
  },
};
export default Model;
