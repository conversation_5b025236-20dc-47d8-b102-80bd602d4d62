import { message } from 'antd';
// api
import {
  reqGetAllStrategy,
  reqAddStrategy,
  reqChangeStrategy,
  reqUpdateStrategyPriorityLevel,
  reqGetStrategyChangeRecord,
  getWhiteGroup,
  getStrategyIds,
  saveWhiteGroup,
  updateWhiteGroup,
} from '../services/AnalyStrategyManage';
import { v4 as uuidv4 } from 'uuid';

export default {
  namespace: 'analyStrategyManage',
  state: {
    // 策略清单
    strategyList: [],
    catchStrategy: [],
    // 变更记录
    changeRecord: [],
    // 策略组
    getWhiteGroup: [],
    // 策略ID
    strategyIds: [],
  },

  effects: {
    *update({ payload, callback }, { call }) {
      const response = yield call(reqChangeStrategy, payload);
      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
    },
    *saveWhiteGroup({ payload, callback }, { call }) {
      const response = yield call(saveWhiteGroup, payload);
      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
    },
    *updateWhiteGroup({ payload, callback }, { call }) {
      const response = yield call(updateWhiteGroup, payload);
      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
    },
    *getWhiteGroup({ payload, callback }, { call, put }) {
      const response = yield call(getWhiteGroup, payload);
      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      const items = response?.items?.map((x) => ({
        ...x,
        key: uuidv4(),
      }));
      yield put({
        type: 'save',
        payload: {
          getWhiteGroup: { ...response, items },
        },
      });
    },
    *getStrategyIds({ payload, callback }, { call, put }) {
      const response = yield call(getStrategyIds, payload);
      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: {
          strategyIds: response,
        },
      });
    },
    *add({ payload, callback }, { call }) {
      const response = yield call(reqAddStrategy, payload);
      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
    },
    *updateLevel({ payload, callback }, { call }) {
      const formData = new FormData();
      for (let i = 0; i < payload.length; i++) {
        formData.append('strategyIdList', payload[i]);
      }
      const response = yield call(reqUpdateStrategyPriorityLevel, formData);
      if (response === undefined) {
        return false;
      }
      if (callback) callback(response);
    },
    *saveStrategyList({ payload }, { call, put }) {
      yield put({
        type: 'saveStrategy',
        payload,
      });
    },
    *getAnalyStrategyList({ payload, callback }, { call, put }) {
      const response = yield call(reqGetAllStrategy);
      if (response === undefined) {
        return false;
      }
      if (response.code === 200) {
        const strategyList = response.data.map((item, idx) => ({
          ...item,
          index: idx,
        }));
        yield put({
          type: 'saveStrategy',
          payload: strategyList,
        });

        yield put({
          type: 'saveCatch',
          payload: strategyList,
        });
      } else {
        message.error(response.message);
      }
      if (callback) callback(response);
    },
    *saveCatchStrategy({ payload }, { call, put }) {
      yield put({
        type: 'saveCatch',
        payload,
      });
    },
    *getStrategyHistory({ payload, callback }, { call, put }) {
      const response = yield call(reqGetStrategyChangeRecord, payload);
      if (response === undefined) {
        return false;
      }
      yield put({
        type: 'saveRecord',
        payload: response.items,
      });
      if (callback) callback(response);
      // 因为后端返回无状态码，估做此修改 张术
      // if (response.code == 200) {
      //   yield put({
      //     type: 'saveRecord',
      //     payload: response.items,
      //   });
      //   if (callback) callback(response);
      // } else {
      //   response.message && message.error(response.message);
      // }
    },
    *resetStrategyHistory({ payload, callback }, { call, put }) {
      yield put({
        type: 'saveRecord',
        payload: [],
      });
    },
  },

  reducers: {
    // 保存策略列表
    saveStrategy(state, action) {
      return {
        ...state,
        strategyList: action.payload,
      };
    },
    // 缓存列表
    saveCatch(state, action) {
      return {
        ...state,
        catchStrategy: action.payload,
      };
    },
    // 保存缓存
    saveRecord(state, action) {
      return {
        ...state,
        changeRecord: action.payload,
      };
    },
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
