import {
  pageMobileReplay,
  batchImportMobileReplayInfo,
  batchImportReplayResult,
  updateMobileReplayDetailById,
  getMobileReplayDetailById,
  deleteMobileReplayById,
  batchDeleteById,
  getOrganizationByUser,
  getSystemConfigListByConfigType,
  getAllClassification,
  batchQueryRealtimeStatus,
} from '@/services/DiskInformateManage/mobileFraudVerificationResume';

const defaultState = {
  tableData: {},
  details: {},
  localNetworkList: [],
  caseTypeList: [],
  classificationList: [],
  searchParams: {},
};

export default {
  namespace: 'mobileFraudVerificationResume',
  state: defaultState,
  effects: {
    // 分页查询
    *pageMobileReplay({ payload, callback }, { call, put }) {
      const response = yield call(pageMobileReplay, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { tableData: response.data },
      });
    },
    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { localNetworkList: response.data },
      });
    },

    // 获取分类
    *getAllClassification({ payload, callback }, { call, put }) {
      const response = yield call(getAllClassification, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { [payload?.name ? 'caseTypeList' : 'classificationList']: response.data || [] },
      });
    },

    *getSystemConfigListByConfigType({ payload, callback }, { call, put }) {
      const response = yield call(getSystemConfigListByConfigType, payload);
      if (callback) callback(response);
    },

    // 导入
    *batchImportMobileReplayInfo({ payload, callback }, { call, put }) {
      const response = yield call(batchImportMobileReplayInfo, payload);
      if (callback) callback(response);
    },
    // 批量结果导入
    *batchImportReplayResult({ payload, callback }, { call, put }) {
      const response = yield call(batchImportReplayResult, payload);
      if (callback) callback(response);
    },

    // 编辑
    *updateMobileReplayDetailById({ payload, callback }, { call, put }) {
      const response = yield call(updateMobileReplayDetailById, payload);
      if (callback) callback(response);
    },

    // 详情
    *getMobileReplayDetailById({ payload, callback }, { call, put }) {
      const response = yield call(getMobileReplayDetailById, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { details: response.data },
      });
    },
    // 删除
    *deleteMobileReplayById({ payload, callback }, { call, put }) {
      const response = yield call(deleteMobileReplayById, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    // 批量删除
    *batchDeleteById({ payload, callback }, { call, put }) {
      const response = yield call(batchDeleteById, payload);
      if (!response) return;
      if (callback) callback(response);
    },

    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
    },

    // 批量查询
    *batchQueryRealtimeStatus({ payload, callback }, { call, put }) {
      const response = yield call(batchQueryRealtimeStatus, payload);
      if (callback) callback(response);
    },
    *saveGoSearchParamas({ payload, callback }, { call, put }) {
      yield put({
        type: 'save',
        payload: {
          searchParams: payload,
        },
      });
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
