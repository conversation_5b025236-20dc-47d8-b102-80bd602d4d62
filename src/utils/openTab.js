/*
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-08-19 18:55:40
 * @FilePath: /cloudPlatform/src/utils/openTab.js
 */
import { router } from 'ponshine';

const goPage = (url, param) => {
  router.push({
    pathname: url,
    query: param,
  });
};

const goBack = () => {
  router.goBack();
};

const listenHistory = (props, callback) => {
  const { history, match } = props;
  let isFirst = true;
  if (!props || !history || !match || !history.listen) {
    throw Error('props参数异常，无法监听路由变化！');
  }

  return history.listen((nextRoute) => {
    const nextPageRoute = { path: nextRoute.pathname, query: { ...(nextRoute.query || {}) } };
    const currentPageRoute = { path: match.url, query: { ...(match.params || {}) } };

    if (isFirst) {
      isFirst = false;
    } else {
      callback(nextPageRoute, currentPageRoute);
    }
  });
};

const onEnterPage = (props, callback) => {
  return listenHistory(props, (nextPageRoute, currentPageRoute) => {
    if (nextPageRoute.path === currentPageRoute.path) {
      if (callback)
        setTimeout(() => {
          callback();
        }, 1);
    }
  });
};

const onLeavePage = (props, callback) => {
  return listenHistory(props, (nextPageRoute, currentPageRoute) => {
    if (
      props?.match?.path === currentPageRoute.path &&
      nextPageRoute.path !== currentPageRoute.path
    ) {
      if (callback)
        setTimeout(() => {
          callback();
        }, 1);
    }
  });
};

export { goPage, goBack, listenHistory, onEnterPage, onLeavePage };
