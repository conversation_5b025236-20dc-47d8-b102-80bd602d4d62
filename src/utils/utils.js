import moment from 'moment';
import { message, Tooltip } from 'antd';
import request from '@/utils/request';
import { verifyEncryptionDocumentFlow } from '@/components/ExportApprove/utils';

export async function exportFile({
  urlAPi = '',
  title,
  params = '',
  method = 'POST',
  mime = 'xls',
  isDate = false,
  currentDateFormate = 'YYYYMMDD',
  decode = false,
  requestType = 'json',
  callback,
  stringifyOptions = {},
  //是否校验商业秘密电子文件流程
  isVerifyhEncryption = false,
}) {
  if (isVerifyhEncryption) {
    // 先进行文档加密流程校验
    const verifyResult = await verifyEncryptionDocumentFlow();
    if (!verifyResult) {
      callback && callback();
      return; // 如果校验未通过，直接返回
    }
  }
  request({
    url: urlAPi,
    method,
    responseType: 'blob',
    getResponse: true,
    [method === 'POST' ? 'data' : 'params']: params,
    requestType: requestType ?? 'json',
    stringifyOptions,
    // stringifyOptions: {
    //   arrayFormat: 'indices', //数组传参时有下标
    //   allowDots: true, //代表点  list[0].id: 1
    // },
  }).then(async (res) => {
    // 进行全局 loading，异步自行移除。
    callback && callback();
    if (!res || res?.data?.type === 'application/json') {
      const reader = new FileReader();
      reader.readAsText(res.data, 'utf-8');
      reader.onload = function () {
        const data = JSON.parse(reader.result);
        message.error(data?.message || '下载错误');
      };
      return;
    }
    const hide = message.loading('下载中...', 0);

    // 处理返回的文件流
    let fileName = res?.response?.headers?.get('content-disposition')?.split('filename=')[1] || '';

    if (decode) {
      fileName = decodeURIComponent(fileName);
    } else {
      fileName = title
        ? `${title}.${mime}`
        : decode
        ? decodeURIComponent(fileName.substr(0, fileName.length - 1))
        : fileName.substr(0, fileName.length - 1);
    }

    let currentName = isDate
      ? `${title}${moment().format(`${currentDateFormate}`)}.${mime}`
      : fileName;

    const blob = res.data;
    if ('download' in document.createElement('a')) {
      // 非IE下载
      const elink = document.createElement('a');
      elink.download = currentName;
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    } else {
      // IE10+下载
      navigator.msSaveBlob(blob, fileName);
    }
    hide();
  });
}

// 导出需要提示后端错误信息
export function exportFileTip({
  urlAPi = '',
  title,
  params = '',
  method = 'POST',
  mime = 'xls',
  isDate = false,
  currentDateFormate = 'YYYYMMDD',
  decode = false,
}) {
  request({
    url: urlAPi,
    method,
    responseType: 'blob',
    getResponse: true,
    [method === 'POST' ? 'data' : 'params']: params,
    requestType: 'json',
    // stringifyOptions: {
    //   arrayFormat: 'indices', //数组传参时有下标
    //   allowDots: true, //代表点  list[0].id: 1
    // },
  }).then((res) => {
    // 进行全局 loading，异步自行移除。
    const hide = message.loading('下载中...', 0);
    if (!res || res?.data?.type === 'application/json') {
      let reader = new FileReader();
      reader.readAsText(res.data, 'utf-8');
      reader.addEventListener('loadend', function () {
        let response = JSON.parse(reader.result);
        hide();
        return message.error(response.message || '下载错误');
      });
      return;
    }

    // 处理返回的文件流
    let fileName = res?.response?.headers?.get('content-disposition')?.split('filename=')[1] || '';
    if (decode) {
      fileName = decodeURIComponent(fileName);
    } else {
      fileName = title
        ? `${title}.${mime}`
        : decode
        ? decodeURIComponent(fileName.substr(0, fileName.length - 1))
        : fileName.substr(0, fileName.length - 1);
    }

    let currentName = isDate
      ? `${title}${moment().format(`${currentDateFormate}`)}.${mime}`
      : fileName;

    const blob = res.data;
    if ('download' in document.createElement('a')) {
      // 非IE下载
      const elink = document.createElement('a');
      elink.download = currentName;
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    } else {
      // IE10+下载
      navigator.msSaveBlob(blob, fileName);
    }
    hide();
  });
}

// 导出需要提示后端错误信息（白名单用）
export function whiteListexportFileTip({
  urlAPi = '',
  title,
  params = '',
  method = 'POST',
  mime = 'xls',
  isDate = false,
  currentDateFormate = 'YYYYMMDD',
  decode = false,
}) {
  request({
    url: urlAPi,
    method,
    responseType: 'blob',
    getResponse: true,
    [method === 'POST' ? 'data' : 'params']: params,
    requestType: 'json',
    // stringifyOptions: {
    //   arrayFormat: 'indices', //数组传参时有下标
    //   allowDots: true, //代表点  list[0].id: 1
    // },
  }).then((res) => {
    // 进行全局 loading，异步自行移除。
    const hide = message.loading('下载中...', 0);
    if (!res || res?.data?.type === 'application/json') {
      let reader = new FileReader();
      reader.readAsText(res.data, 'utf-8');
      reader.addEventListener('loadend', function () {
        let response = JSON.parse(reader.result);
        hide();
        return message.error(response.message || '下载错误');
      });
      return;
    }

    // 处理返回的文件流
    let fileName = res?.response?.headers?.get('content-disposition')?.split('filename=')[1] || '';
    if (decode) {
      fileName = decodeURIComponent(fileName);
    } else {
      fileName = title
        ? `${title}.${mime}`
        : decode
        ? decodeURIComponent(fileName.substr(0, fileName.length - 1))
        : fileName.substr(0, fileName.length - 1);
    }

    let currentName = isDate
      ? `${title}${moment().format(`${currentDateFormate}`)}.${mime}`
      : fileName;

    const blob = res.data;
    if ('download' in document.createElement('a')) {
      // 非IE下载
      const elink = document.createElement('a');
      elink.download = currentName;
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    } else {
      // IE10+下载
      navigator.msSaveBlob(blob, fileName);
    }
    hide();
  });
}

export function handleCommonResponse(response, callback, isNeedSuccessMessage = true) {
  if (response && response.code === 200) {
    callback();
    isNeedSuccessMessage && message.success(response.message);
  } else {
    message.error(response.message);
  }
}

export function parseXSS(str) {
  return (
    str &&
    str
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&lsquo;/g, "'")
  );
}

// 身份证等需要脱敏展示的方法
/**
 *
 * @param {*} str 传入字符串
 * @param {*} prexLength  传入前面需要展示的长度
 * @param {*} afterLength  传入后面需要展示的长度
 * @returns  '1234567890123456'  ===> '123456******3456'
 */

export function hyposensitization(str, prexLength = 6, afterLength = 4) {
  let beforeStr = str.slice(0, prexLength);
  let afterStr = str.substr(-afterLength);
  let contentLength = str.length - prexLength - afterLength;
  let contentStr = str?.substr(prexLength, contentLength)?.replace(/[^.]/g, '*');
  return beforeStr + contentStr + afterStr;
}

// 将对象转化为指定的数组
export function transforObj2Array(obj = {}) {
  return Object.keys(obj).map((ele) => {
    return {
      label: obj?.[ele],
      value: ele,
    };
  });
}

// 表格内容气泡显示
export const renderToolTip = (text) => {
  return text || text == 0 ? (
    <Tooltip placement="topLeft" title={text} autoAdjustOverflow={false}>
      <span>{text}</span>
    </Tooltip>
  ) : (
    '--'
  );
};

/**
 *
 * @param {String} str 要改变的字符串
 * @param {Number} index 从右往左第几位开始
 * @param {Number} num 每隔几个字符插入
 * @param {String} type 插入的内容
 * @returns
 */
export function formatNumber(str, index = 3, num = 3, type = ',') {
  if (str.length <= 3) return str;
  let newIndex = -1 * index;
  let after = str.slice(newIndex);
  let before = str.slice(0, str.length - after.length);
  str = before + type + after;
  if (before.length > num) {
    return formatNumber(str, Math.abs(newIndex - num - 1), num, type);
  }
  return str;
}

// 获取携带上次页码查询的页码
export const getReloadTableWithFilterPageNum = ({
  total = 0,
  pageSize = 10,
  current = 1,
  listLength = 0,
  selectedRowsLength = 0,
}) => {
  // 如果是最后一页，且只有一条数据，未避免查出来总数和当前请求页对不上，改为请求当前页的上一页
  // 如果是第一页，则还是请求第一页
  const totalPage = total % pageSize === 0 ? total / pageSize : parseInt(total / pageSize) + 1;
  const isEmptyPage = listLength === 1 || selectedRowsLength === listLength;
  const pageNum =
    current === totalPage && isEmptyPage ? (current === 1 ? 1 : current - 1) : current;
  return pageNum;
};

export const getIndexNumber = (current, pageSize, index) => {
  return (current - 1) * pageSize + index + 1;
};
