import bAES from 'browserify-aes';
import CryptoJS from 'crypto-js';
import Jsencrypt from 'jsencryptNew'; // @ts-ignore

//秘钥
const CRYPTOJSKEY = 'ponshineponshine';

// AES加密
export const aesEncode = (word) => {
  // 老版本
  let encrypted = '';
  const cipher = bAES.createCipheriv('aes-128-cbc', 'ponshineponshine', '1234567890123456');
  encrypted = cipher.update(word, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  return encrypted;
};

// AES解密
export const aesDecode = (word) => {
  var key = CryptoJS.enc.Latin1.parse(CRYPTOJSKEY); // Latin1 w8m31+Yy/Nw6thPsMpO5fg==
  var iv = CryptoJS.enc.Latin1.parse('1234567890123456');
  var decrypt = CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return decrypt.toString(CryptoJS.enc.Utf8);
};

// 公钥
const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzaCrNH49tWuCmTxnVivfIgg8OSljmE3Lx9+KZIBQxfXZhLsj7uLdXRB3Q9MQ5sUhS7dQ+XfUaczNizMv+gQ8AYsPW9P9VTYUNRug47KhPgU28oOlkI5qm5RPQg06B5S7SGYpihhmTjzFWNTlshFjVZTV4QyfuX5hNrigJ4ddFmwIDAQAB'; // 密码策略配置
// 私钥
let privateKey =
  'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALNoKs0fj21a4KZPGdWK98iCDw5KWOYTcvH' +
  '34pkgFDF9dmEuyPu4t1dEHdD0xDmxSFLt1D5d9RpzM2LMy/6BDwBiw9b0/1VNhQ1G6DjsqE+BTbyg6WQjmqblE9CDToHlLtIZimKGGZOPMVY1OWyEWNVlNXhDJ+5fmE2uKAnh10WbAgMBAAECgYB3B' +
  'JLoc5h97nz8N5um28NXxFhinZXuRFbMCSvNc8eRsW7YN+Zjf/45KVoyZj7LGz4krdxQ55oTyvr6/2wRUeL1vJ1WgXJqR2uxnmgoms7oVhFBHXCOtcTliaRj3GG+1K6ek4uigx0osCSseTOK9lOFgGL' +
  '63uaI5Gz/gZXo4ZWSeQJBAOSvArPQhRZSUzpciw5Wff0OqS564xNOuaOnuOEQNEdqqUN3DaASTXzKfT/wRiyum+euc9SwZ65zfK/RzULswRUCQQDI1k7M7WK22pYkcO3cfEDtQ6rsB7ssDkUamJeXS' +
  'Cummgu5YZdKBfS4GT/iVOVNbzDXVjt4DOqIMrgAK+jt5bfvAkBqFnJ3fp2vDEQGCBaOcTuDYQDFSkb7lwLx1AYlisHl+6pIXPuaLcN6x+kw90NmyR4Ubc7YdXmz6WmHmxnRGUo9AkApqJpr04m4WBr' +
  'wsCQGqhdln7Wig/AFMWiQHbrD/IphYm6wI1gYWKfsUAB6WBXAPCIraI2pVzSGz85u6qzEO/QtAkEAkhZ5KKSuHKbdHSE9C3sqTqWmwSIM9Gihhhb5UnYy4FWeIpcdg+WdCySl/0u3/N+zfZh6/t3eY' +
  'FAQ5gbUXUewgA==';

// 解密
export const decrypt = (val) => {
  const decrypt = new Jsencrypt();
  decrypt.setPrivateKey(privateKey);
  return decrypt.decrypt(val);
};

// 加密
export const encrypt = (val) => {
  const encrypt = new Jsencrypt();
  encrypt.setPublicKey(publicKey);
  return encrypt.encryptLong(val);
};
