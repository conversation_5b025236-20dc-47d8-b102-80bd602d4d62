@import (reference) '~antd/es/notification/style/index';

#root {
  width: 100% !important;
  height: 100% !important;
 
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

.@{notification-prefix-cls} {
  &-notice {
    &-message,
    &-description {
      white-space: normal !important;
      word-wrap: break-word !important;
      word-break: break-all !important;
    }
  }
}
.ant-message {
  z-index: 2147483647 !important;
}

.ant-input[disabled],
.ant-select-disabled,.ant-radio-disabled + span, .ant-select-disabled .ant-select-selection--multiple .ant-select-selection__choice
{
  color: rgba(0, 0, 0, 0.55) !important;
}
