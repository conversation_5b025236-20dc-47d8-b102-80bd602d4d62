import request from '@/utils/request';

// 获取所属地市
export async function getCityListsData(params) {
  return request(`/api/hn/systemConfig/getOrganizationByUser`, {
    method: 'GET',
    params,
  });
}

// 关停复机统计
export async function getSourdownAndRestartCount(params) {
  return request(`/api/hn/cityAnalysis/cityShutdownAndResumeTable`, {
    method: 'GET',
    params,
  });
}

// 获取关停复机趋势图
export async function getSourdownAndRestartTrend(params) {
  return request(`/api/hn/cityAnalysis/cityShutdownAndResumeTrend`, {
    method: 'GET',
    params,
  });
}

// 获取省内模型防欺诈关停标签
export async function getPhoneCloseSubtag(params) {
  return request(`/api/hn/cityAnalysis/cityModuleShutdownTag`, {
    method: 'GET',
    params,
  });
}

// 获取省内模型防欺诈趋势图
export async function getPhoneCloseTrend(params) {
  return request(`/api/hn/cityAnalysis/cityModuleShutdownData`, {
    method: 'GET',
    params,
  });
}

// 涉案号码分布
export async function getInvolvedNotificationReplay(params) {
  return request(`/api/hn/cityAnalysis/cityInvolvedPhone`, {
    method: 'GET',
    params,
  });
}

// 12321诈骗电话
export async function getReportReplay(params) {
  return request(`/api/hn/cityAnalysis/cityReportedPhone`, {
    method: 'GET',
    params,
  });
}

// 获取黑白灰名单数量统计
export async function getBlackWhiteGrayCount(params) {
  return request(`/api/sitation/getBlackWhiteGrayCount`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 白名单类型统计
export async function getWhiteType(params) {
  return request(`/api/sitation/getWhiteType`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
