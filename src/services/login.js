import request from '@/utils/request'; // @ts-ignore
import bAES from 'browserify-aes'; // @ts-ignore
import CryptoJS from 'crypto-js'; // 请注意 crypto-js 最好使用 3.x 版本，因为 4.x 版本可能会导致在 IE10 或更老的浏览器报错
import Jsencrypt from 'jsencryptNew'; // @ts-ignore

const aesEncode = (isOldBackEndVersion, value, secretKey) => {
  let encrypted = '';

  if (isOldBackEndVersion) {
    try {
      // 加密参数直接写死，可根据项目需要自行修改
      const cipher = bAES.createCipheriv('aes-128-cbc', 'ponshineponshine', '1234567890123456');
      encrypted = cipher.update(value, 'utf8', 'base64');
      encrypted += cipher.final('base64');
    } catch (e) {
      console.error(e);
    }
  } else {
    try {
      const key = CryptoJS.enc.Utf8.parse(secretKey);
      const srcs = CryptoJS.enc.Utf8.parse(
        typeof value === 'string' ? value : JSON.stringify(value),
      );
      encrypted = CryptoJS.AES.encrypt(srcs, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      }).toString();
    } catch (e) {
      console.error(e);
    }
  }

  return encrypted;
}; // 新版后端是以 width: 310 height: 155 作为校验标准的，所以需要转换坐标位置

export const aesDecode = (word, keyStr) => {
  keyStr = keyStr || 'ponshineponshine';
  var key = CryptoJS.enc.Latin1.parse(keyStr); // Latin1 w8m31+Yy/Nw6thPsMpO5fg==
  var iv = CryptoJS.enc.Latin1.parse('1234567890123456');
  var decrypt = CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return decrypt.toString(CryptoJS.enc.Utf8);
};

const coordinateConverter = (point, currentSize) => {
  let converterPoint = null;

  try {
    const backEndSize = {
      width: 310,
      height: 155,
    };
    const { x, y } = point;
    const { width, height } = currentSize;
    converterPoint = {
      x: Math.round(x / (width / backEndSize.width)),
      y: Math.round(y / (height / backEndSize.height)), // 实际上后端并不验证 y 坐标
    };
  } catch (e) {
    console.error(e);
  }

  return converterPoint;
};

const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzaCrNH49tWuCmTxnVivfIgg8OSljmE3Lx9+KZIBQxfXZhLsj7uLdXRB3Q9MQ5sUhS7dQ+XfUaczNizMv+gQ8AYsPW9P9VTYUNRug47KhPgU28oOlkI5qm5RPQg06B5S7SGYpihhmTjzFWNTlshFjVZTV4QyfuX5hNrigJ4ddFmwIDAQAB';
export async function fakeAccountLogin(params) {
  const encrypt = new Jsencrypt();
  encrypt.setPublicKey(publicKey);
  const password = encrypt.encryptLong(params.password);
  const { userName, size, track, token, secretKey, ...rest } = params;
  const isOldBackEndVersion = !secretKey; // 通过 secretKey 来判断是否是老版后端服务

  let newBackEndToken;

  // if (size && track && token && !isOldBackEndVersion) {
  //   const point = coordinateConverter(
  //     {
  //       x: track[track.length - 1]?.dragData.x,
  //       y: track[track.length - 1]?.dragData.y,
  //     },
  //     size?.bgImgSize,
  //   );
  //   newBackEndToken = aesEncode(
  //     isOldBackEndVersion,
  //     token.concat('---').concat(JSON.stringify(point)),
  //     secretKey,
  //   );
  // } // 为兼容单体服务和单点登录，登录接口改为 POST /api/login，登出接口改为 POST /api/logout

  return request('/api/phone/login', {
    method: 'POST',
    data: {
      ...rest,
      password,
      username: userName,
      userName,
      // imageToken: token,
      // 老版后端
      // blockPuzzleCaptcha: newBackEndToken,
      // 新版后端
      // width: size?.bgImgSize?.width, // width 参数没啥用，但还是传过去吧，后续滑动拼图验证码可能需要
    },
  });
}
export async function fakeAccountLogout() {
  return request('/api/user/logout', {
    method: 'GET',
  });
}
export async function getFakeCaptcha(mobile) {
  return request(`/api/login/captcha?mobile=${mobile}`);
}

export async function sendLoginVerificationCode({ userName, password }) {
  const encrypt = new Jsencrypt();
  encrypt.setPublicKey(publicKey);
  const newPassword = encrypt.encryptLong(password);
  return request(
    `/api/hn/sendMessage/sendLoginVerificationCode?userName=${userName}&password=${newPassword}`,
  );
}

export async function getFakeNormalCaptcha() {
  return request('/api/imgCode');
}
export async function getFakeSlideJigsawCaptcha() {
  return request({
    url: '/api/captcha/get',
    method: 'post',
    requestType: 'json',
    data: {
      captchaType: 'blockPuzzle',
    },
  });
}
export async function verifyFakeSlideJigsawCaptcha(params) {
  // 此处写法老版和新版后端兼容
  const { size, track, token, secretKey } = params;
  const isOldBackEndVersion = !secretKey; // 通过 secretKey 来判断是否是老版后端服务，老版直接在 aesEncode 中写死

  let data;

  if (isOldBackEndVersion) {
    const pathX = [];
    const pathY = [];
    data = {
      width: size?.bgImgSize?.width,
      // width 参数没啥用，但还是传过去吧，后续滑动拼图验证码可能需要
      imageToken: token,
      captchaType: 'slide-jigsaw', // captchaType 参数没啥用，但还是传过去吧
    };
    track.forEach((v) => {
      pathX.push(v.dragData.x);
      pathY.push(v.dragData.y);
    });
    Object.assign(data, {
      pathX: aesEncode(isOldBackEndVersion, pathX.join()),
      pathY: aesEncode(isOldBackEndVersion, pathY.join()),
    });
  } else {
    const point = coordinateConverter(
      {
        x: track[track.length - 1]?.dragData.x,
        y: track[track.length - 1]?.dragData.y,
      },
      size?.bgImgSize,
    );
    data = {
      width: size?.bgImgSize?.width,
      // width 参数没啥用，但还是传过去吧，后续滑动拼图验证码可能需要
      token,
      captchaType: 'blockPuzzle',
    };
    Object.assign(data, {
      pointJson: aesEncode(isOldBackEndVersion, point, secretKey),
    });
  }

  return request({
    url: '/api/captcha/check',
    method: 'post',
    requestType: isOldBackEndVersion ? 'form' : 'json',
    data,
  });
} // 找回密码-发送验证码

export async function sendBackPasswordCaptcha(params) {
  return request('/api/user/sendBackPasswordCaptcha', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
} // 找回密码-修改密码

export async function updateBackPassword(params) {
  const encrypt = new Jsencrypt();
  encrypt.setPublicKey(publicKey);
  const { password, confirm, ...rest } = params;
  return request('/api/user/backPassword', {
    method: 'POST',
    data: {
      ...rest,
      password: password ? encrypt.encryptLong(password) : '',
      confirm: confirm ? encrypt.encryptLong(confirm) : '',
    },
    requestType: 'form',
  });
}
