import request from "../../utils/request";

export async function getCityMonthlyData(params) {
    return request(`/api/hn/monthlyExamine/getCityMonthlyData`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function getConfigTypeByPidAndConfigType(params) {
    return request(`/api/hn/systemConfig/getConfigTypeByPidAndConfigType`, {
      method: 'GET',
      params,
    });
  }

  export async function getLineChartData(params) {
    return request(`/api/hn/monthlyExamine/getLineChartData`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function getSystemConfigListByConfigType(params) {
    return request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
      method: 'GET',
      params,
    });
  }