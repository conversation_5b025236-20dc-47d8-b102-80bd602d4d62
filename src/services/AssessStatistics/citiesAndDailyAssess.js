import request from "../../utils/request";

// 地市指标考核
export async function getCityExamineIndicators(params) {
  return request(`/api/hn/cityExamineIndicators/getCityExamineIndicators`, {
    method: 'GET',
    params,
  });
}
export async function updateCityExamineIndicators(params) {
  return request(`/api/hn/cityExamineIndicators/updateCityExamineIndicators`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
// 集团涉案及举报考核
export async function pageStatisticsInvolvedReportRank(params) {
  return request(`/api/hn/statisticsInvolvedReportRank/pageStatisticsInvolvedReportRank`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
export async function importStatisticsInvolvedReportRank(params) {
  return request(`/api/hn/statisticsInvolvedReportRank/importStatisticsInvolvedReportRank`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
export async function deleteByIdsStatisticsInvolvedReportRank(params) {
  return request(`/api/hn/statisticsInvolvedReportRank/deleteByIdsStatisticsInvolvedReportRank`, {
    method: 'POST',
    data: params.idList,
    requestType: 'json',
  });
}

export async function getAssessDaily(params) {
  return request(`/api/hn/statisticsAssessDaily/getAssessDaily`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
