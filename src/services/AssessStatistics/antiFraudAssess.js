import request from "../../utils/request";

export async function pageStatisticsInvolved(params) {
    return request(`/api/hn/statisticsInvolved/pageStatisticsInvolved`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function importStatisticsInvolved(params) {
    return request(`/api/hn/statisticsInvolved/importStatisticsInvolved`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function deleteByIdsStatisticsInvolved(params) {
    return request(`/api/hn/statisticsInvolved/deleteByIdsStatisticsInvolved`, {
      method: 'POST',
      data: params.idList,
      requestType: 'json',
    });
  }
  export async function pageStatisticsReportPhoneData(params) {
    return request(`/api/hn/statisticsReport/pageStatisticsReport`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function importStatisticsReportPhoneData(params) {
    return request(`/api/hn/statisticsReport/importStatisticsReport`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function deleteByIdsStatisticsReportPhoneData(params) {
    return request(`/api/hn/statisticsReport/deleteByIdsStatisticsReport`, {
      method: 'POST',
      data: params.idList,
      requestType: 'json',
    });
  }
  export async function pageStatisticsProvincial(params) {
    return request(`/api/hn/statisticsProvincial/pageStatisticsProvincial`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function importStatisticsProvincial(params) {
    return request(`/api/hn/statisticsProvincial/importStatisticsProvincial`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
  }

  export async function deleteByIdsStatisticsProvincial(params) {
    return request(`/api/hn/statisticsProvincial/deleteByIdsStatisticsProvincial`, {
      method: 'POST',
      data: params.idList,
      requestType: 'json',
    });
  }

  export async function getSystemConfigListByConfigType(params) {
    return request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
      method: 'GET',
      params,
    });
  }