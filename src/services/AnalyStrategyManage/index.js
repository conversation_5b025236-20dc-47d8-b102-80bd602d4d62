import request from "../../utils/request";
import { basePath } from "../basePath.js";

// 获取待审核用户
export function reqGetAllStrategy() {
  return request(`${basePath}strategy/getAllStrategy`, {
    method: "POST",
  });
}
// 更新策略表优先级
export function reqUpdateStrategyPriorityLevel(strategyIdList) {
  return request(`${basePath}strategy/updateStrategyPriorityLevel`, {
    method: "POST",
    body: strategyIdList,
  });
}
// 新增策略
export function reqAddStrategy(data) {
  return request(`${basePath}strategy/addStrategy`, {
    method: "POST",
    body: data,
  });
}
// 变更策略
export function reqChangeStrategy(data) {
  return request(`${basePath}strategy/changeStrategy`, {
    method: "POST",
    body: data,
  });
}
// 变更记录
export function reqGetStrategyChangeRecord(data) {
  return request(`${basePath}strategy/getStrategyChangeRecord`, {
    method: "POST",
    body: data,
  });
}
//  策略名单组
export function getWhiteGroup(data) {
  return request(`${basePath}strategy/getWhiteGroup`, {
    method: "POST",
    body: data,
  });
}
// 获取策略表策略id
export function getStrategyIds(data) {
  return request(`${basePath}strategy/getStrategyIds`, {
    method: "POST",
    body: data,
  });
}
// 保存白名单组信息
export function saveWhiteGroup(data) {
  return request(`${basePath}strategy/saveWhiteGroup`, {
    method: "POST",
    body: data,
  });
}
// 修改白名单组信息
export function updateWhiteGroup(data) {
  return request(`${basePath}strategy/updateWhiteGroup`, {
    method: "POST",
    body: data,
  });
}
