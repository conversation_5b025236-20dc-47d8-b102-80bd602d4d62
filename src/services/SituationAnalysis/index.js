import request from '@/utils/request';

export async function getSourdownAndRestartCount(params) {
  return request(`/api/sitation/getSoutdownAndRestartThread`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function getPhoneCloseTrend(params) {
  return request(`/api/sitation/getPhoneCloseTrend`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function getPhoneCloseSubtag(params) {
  return request(`/api/sitation/getPhoneCloseSubtag`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function getInvolvedNotificationReplay(params) {
  return request(`/api/sitation/getInvolvedNotificationReplay`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function getReportReplay(params) {
  return request(`/api/sitation/getReportReplay`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
export async function getBlackWhiteGrayCount(params) {
  return request(`/api/sitation/getBlackWhiteGrayCount`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function getWhiteType(params) {
  return request(`/api/sitation/getWhiteType`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
export async function getCityShutdownAndResumeAndWhiteCount(params) {
  return request(`/api/sitation/getCityShutdownAndResumeAndWhiteCount`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
