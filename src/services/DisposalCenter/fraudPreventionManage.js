/*
 * @Author: zxw
 * @Date: 2023-06-14 11:20:54
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-24 19:43:37
 * @FilePath: \newHunanfanzha\src\services\DisposalCenter\fraudPreventionManage.js
 * @Description:
 */
// api/hn/shutdown/pagePhoneShutdownDetail
import request from '@/utils/request';

export async function pagePhoneShutdownDetail(params) {
  return request(`/api/hn/shutdown/newPagePhoneShutdownDetail`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
export async function getAllFraudType(params) {
  return request(`/api/hn/shutdownTag/getAllFraudType`, {
    method: 'GET',
    params,
  });
}
export async function getAllFraudTypeAll(params) {
  return request(`/api/hn/shutdownTag/getAllFraudTypeAll`, {
    method: 'GET',
    params,
  });
}
export async function getShutdownTagListByFraudType(params) {
  return request(`/api/hn/shutdownTag/getShutdownTagListByFraudType`, {
    method: 'GET',
    params,
  });
}
export async function getShutdownTagListByFraudTypeAll(params) {
  return request(`/api/hn/shutdownTag/getShutdownTagListByFraudTypeAll`, {
    method: 'GET',
    params,
  });
}
export async function getShutdownSubTagListByShutdownTag(params) {
  return request(`/api/hn/shutdownTag/getShutdownSubTagListByShutdownTag`, {
    method: 'GET',
    params,
  });
}
export async function getShutdownSubTagListByShutdownTagAll(params) {
  return request(`/api/hn/shutdownTag/getShutdownSubTagListByShutdownTagAll`, {
    method: 'GET',
    params,
  });
}
//
export async function getOrganizationByUser(params) {
  return request(`/api/hn/systemConfig/getOrganizationByUser`, {
    method: 'GET',
    params,
  });
}
export async function shutdownSinglePhoneNum(params) {
  return request(`/api/hn/shutdown/shutdownSinglePhoneNum`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
export async function resumeSinglePhoneNum(params) {
  return request(`/api/hn/shutdown/resumeSinglePhoneNum`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export async function queryUserRole(params) {
  return request(`/api/hn/systemConfig/queryUserRole`, {
    method: 'GET',
    params,
  });
}

export async function forceResumeSinglePhoneNum(params) {
  return request(`/api/hn/shutdown/forceResumeSinglePhoneNum`, {
    method: 'GET',
    params,
  });
}
