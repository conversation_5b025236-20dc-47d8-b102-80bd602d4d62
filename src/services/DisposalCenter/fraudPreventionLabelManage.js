import request from '@/utils/request';

export async function findTableData(params) {
  return request(`/api/hn/shutdownTag/pageShutdownTag`, {
    method: 'GET',
    params,
  });
}

export async function findConfigRecordTableData(params) {
  return request(`/api/hn/shutdownTag/pageShutdownTagRecord`, {
    method: 'GET',
    params,
  });
}

export async function addEditTag(params) {
  return request(`/api/hn/shutdownTag/saveOrUpdateShutdownTag`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function deleteTag(params) {
  return request(`/api/hn/shutdownTag/deleteShutdownTagByIdList`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取涉诈性质/ 获取关停规则
export async function getAllFraudNatureTypeAndRule(params) {
  return request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
    method: 'GET',
    params,
  });
}

// 获取CRM接口类型
export async function getCRMInterfaceType(params) {
  return request(`/api/hn/shutdownTag/getAllCRMFraudType`, {
    method: 'GET',
    params,
  });
}

// 获取CRM标签
export async function getCRMTag(params) {
  return request(`/api/hn/shutdownTag/getCRMTagListByCRMFraudType`, {
    method: 'GET',
    params,
  });
}
