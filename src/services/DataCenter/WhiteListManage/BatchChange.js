/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 16:57:39
 * @LastEditors: zhao<PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-26 13:09:11
 * @FilePath: \hunanfanzha\src\services\DataCenter\WhiteListManage\BatchChange.js
 * @Description:
 */
import request from '@/utils/request';

//获取列表
export function findTableData(params) {
  return request('/api/white/getWhitePhoneWorkOrderTemplateData', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 批量导入
export async function batchUploadFile(params, query) {
  return request('/api/white/exportWhiteTemplateDownload', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 提交
export async function submitFileList(params, query) {
  return request('/api/white/submitWhitePhoneWorkOrderTemplateData', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
