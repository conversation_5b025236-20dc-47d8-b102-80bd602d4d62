/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 16:57:39
 * @LastEditors: zhao<PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-23 14:23:08
 * @FilePath: \hunanfanzha\src\services\DataCenter\WhiteListManage\WorkOrderManage.js
 * @Description:
 */
import request from '@/utils/request';

//获取列表
export function findTableData(params) {
  return request('/api/white/getWhiteWorkOrderManage', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
