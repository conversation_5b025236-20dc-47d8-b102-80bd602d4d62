/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 16:57:27
 * @LastEditors: z<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-23 14:27:03
 * @FilePath: \hunanfanzha\src\services\DataCenter\WhiteListManage\WorkOrderExamine.js
 * @Description:
 */
import request from '@/utils/request';

//获取列表
export function findTableData(params) {
  return request('/api/white/getWhiteWorkOrderApproval', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//审批
export function whiteListApprove(params) {
  return request('/api/white/singleApprovalWhiteWorkOrderApproval', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
