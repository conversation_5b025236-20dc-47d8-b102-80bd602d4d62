/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:45:55
 * @LastEditors: zhao<PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-23 13:26:06
 * @FilePath: \hunanfanzha\src\services\DataCenter\WhiteListManage\InfoQuery.js
 * @Description:
 */
import request from '@/utils/request';

//获取列表
export function findTableData(params) {
  return request('/api/white/getWhiteInformation', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//单个新增白名单
export function addWhiteInfo(params) {
  return request('/api/white/addWhiteInformation', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//单个修改白名单
export function updateWhiteInfo(params) {
  return request('/api/white/updateWhiteInformation', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//删除
export function deleteWhiteInfo(params) {
  return request('/api/white/deleteWhiteInformation', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//导出
export function exportWhiteInfo(params) {
  return request('/api/ipMapping/export', {
    method: 'POST',
    data: params,
  });
}
