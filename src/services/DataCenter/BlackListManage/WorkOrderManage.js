/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 16:57:39
 * @LastEditors: z<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-30 15:25:14
 * @FilePath: \hunanfanzha\src\services\DataCenter\BlackListManage\WorkOrderManage.js
 * @Description:
 */
import request from '@/utils/request';

//获取列表
export function findTableData(params) {
  return request('/api/hn/blackAndGrayList/pageBlackRecord', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
