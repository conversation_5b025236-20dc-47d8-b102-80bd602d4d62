/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:45:55
 * @LastEditors: z<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-30 15:40:56
 * @FilePath: \hunanfanzha\src\services\DataCenter\BlackListManage\InfoQuery.js
 * @Description:
 */
import request from '@/utils/request';

//获取列表
export async function findTableData(params) {
  return request('/api/hn/blackAndGrayList/pageBlackAndGrayList', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//单个新增黑名单
export async function addBlackInfo(params) {
  return request('/api/hn/blackAndGrayList/insertIntoBlackAndGrayList', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//单个修改黑名单
export async function updateBlackInfo(params) {
  return request('/api/hn/blackAndGrayList/updateBlackListById', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

//删除
export async function deleteBlackInfo(params) {
  return request('/api/hn/blackAndGrayList/deleteFromBlackAndGrayList', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

//导出
export async function exportBlackInfo(params) {
  return request('/api/ipMapping/export', {
    method: 'POST',
    data: params,
  });
}
