/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 16:57:39
 * @LastEditors: zhao<PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-30 15:49:38
 * @FilePath: \hunanfanzha\src\services\DataCenter\BlackListManage\BatchChange.js
 * @Description:
 */
import request from '@/utils/request';

//获取列表
export function findTableData(params) {
  return request('/api/hn/blackAndGrayList/pageBlackAndGrayTemp', {
    method: 'GET',
    params: params,
  });
}

// 提交
export async function submitFileList(params, query) {
  return request('/api/hn/blackAndGrayList/submitBlackAndGrayList', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 批量导入
export async function batchUploadFile(params, query) {
  return request('/api/hn/blackAndGrayList/parseBlackAndGrayExcelData', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
