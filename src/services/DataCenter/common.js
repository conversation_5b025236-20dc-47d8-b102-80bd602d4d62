/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 17:01:28
 * @LastEditors: zhao<PERSON>owen
 * @LastEditTime: 2022-09-23 11:30:31
 * @FilePath: \hunanfanzha\src\services\DataCenter\common.js
 * @Description:
 */
import request from '@/utils/request';

//获取客户标签 客户子标签
export function getCustomerTags(params) {
  return request('/api/white/getGrayBlackTag', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
//获取客户标签 客户子标签
export function getGrayBlackTagByUser(params) {
  return request('/api/white/getGrayBlackTagByUser', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取号码归属
export function getNumberAttribution(params) {
  return request('/api/hn/systemConfig/getOrganizationByUser', {
    method: 'GET',
    params,
    requestType: 'json',
  });
}

// 获取关联模型
export function getCorrelationModel(params) {
  return request('/api/white/getAssociationModel', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取公共下拉
export function getSearchList(params) {
  return request('/api/hn/systemConfig/getSystemConfigListByConfigType', {
    method: 'GET',
    params,
  });
}
