import request from '@/utils/request';

export async function getPageInvolvedReplay(params) {
  return request(`/api/hn/fraudInvolvedReplay/pageInvolvedReplay`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function batchImportInvolvedReplayInfo(params) {
  return request(`/api/hn/fraudInvolvedReplay/batchImportInvolvedReplayInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function updateInvolvedReplay(params) {
  return request(`/api/hn/fraudInvolvedReplay/updateInvolvedReplayDetailById`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function getInvolvedReplayDetail(params) {
  return request(`/api/hn/fraudInvolvedReplay/getInvolvedReplayDetailById`, {
    method: 'GET',
    params,
  });
}

export async function deleteInvolvedReplay(params) {
  return request(`/api/hn/fraudInvolvedReplay/deleteInvolvedReplayById`, {
    method: 'GET',
    params,
  });
}
export async function getOrganizationByUser(params) {
  return request(`/api/hn/systemConfig/getOrganizationByUser`, {
    method: 'GET',
    params,
  });
}

export async function getSystemConfigListByConfigType(params) {
  return request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
    method: 'GET',
    params,
  });
}
