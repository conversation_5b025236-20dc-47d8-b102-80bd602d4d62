import request from '@/utils/request';

export async function getPhoneReportReplay(params) {
  return request(`/api/hn/fraudPhoneReportReplay/pagePhoneReportReplay`, {
    method: 'POST',
    data:params,
    requestType: 'json',
  });
}

export async function batchImportPhoneReportReplayInfo(params) {
  return request(`/api/hn/fraudPhoneReportReplay/batchImportPhoneReportReplayInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function deletePhoneReportReplayById(params) {
  return request(`/api/hn/fraudPhoneReportReplay/deletePhoneReportReplayById`, {
    method: 'GET',
    params,
  });
}

export async function getPhoneReportReplayDetail(params) {
  return request(`/api/hn/fraudPhoneReportReplay/getPhoneReportReplayDetailById`, {
    method: 'GET',
    params,
  });
}

export async function updatePhoneReportReplay(params) {
  return request(`/api/hn/fraudPhoneReportReplay/updatePhoneReportReplayDetailById`, {
    method: 'POST',
    data:params,
    requestType: 'json',
  });
}

