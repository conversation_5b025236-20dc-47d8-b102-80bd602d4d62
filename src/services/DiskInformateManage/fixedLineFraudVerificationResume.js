import request from '@/utils/request';

// 分页
export async function pageFixTelReplay(params) {
  return request(`/api/hn/fixTelReplay/pageFixTelReplay`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 导入
export async function batchImportFixTelInfo(params) {
  return request(`/api/hn/fixTelReplay/batchImportFixTelInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 批量结果导入
export async function batchImportReplayResult(params) {
  return request(`/api/hn/mobileReplay/batchImportReplayResult`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 编辑
export async function updateFixTelReplayById(params) {
  return request(`/api/hn/fixTelReplay/updateFixTelReplayById`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 详情
export async function getFixTelReplayDetailById(params) {
  return request(`/api/hn/fixTelReplay/getFixTelReplayDetailById`, {
    method: 'GET',
    params,
  });
}

// 删除
export async function deleteMobileReplayById(params) {
  return request(`/api/hn/mobileReplay/deleteMobileReplayById`, {
    method: 'GET',
    params,
  });
}

// 批量删除
export async function batchDeleteById(params) {
  return request(`/api/hn/fixTelReplay/batchDeleteById`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

export async function getOrganizationByUser(params) {
  return request(`/api/hn/systemConfig/getOrganizationByUser`, {
    method: 'GET',
    params,
  });
}

// 获取分类
export async function getFixTelClassification(params) {
  return request(`/api/hn/fixTelReplay/getFixTelClassification`, {
    method: 'GET',
    params,
  });
}

export async function getSystemConfigListByConfigType(params) {
  return request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
    method: 'GET',
    params,
  });
}

// 批量查询
export async function batchQueryRealtimeStatus(params) {
  return request(`/api/hn/mobileReplay/batchQueryRealtimeStatus`, {
    method: 'POST',
    data: params,
    // requestType: 'json',
    getResponse: true,
    responseType: 'blob',
  });
}
