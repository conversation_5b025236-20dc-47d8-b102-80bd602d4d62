import request from '@/utils/request';

// 分页
export async function pageMobileReplay(params) {
  return request(`/api/hn/mobileReplay/pageMobileReplay`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 导入
export async function batchImportMobileReplayInfo(params) {
  return request(`/api/hn/mobileReplay/batchImportMobileReplayInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 批量结果导入
export async function batchImportReplayResult(params) {
  return request(`/api/hn/mobileReplay/batchImportReplayResult`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 编辑
export async function updateMobileReplayDetailById(params) {
  return request(`/api/hn/mobileReplay/updateMobileReplayDetailById`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 详情
export async function getMobileReplayDetailById(params) {
  return request(`/api/hn/mobileReplay/getMobileReplayDetailById`, {
    method: 'GET',
    params,
  });
}

// 删除
export async function deleteMobileReplayById(params) {
  return request(`/api/hn/mobileReplay/deleteMobileReplayById`, {
    method: 'GET',
    params,
  });
}

// 批量删除
export async function batchDeleteById(params) {
  return request(`/api/hn/mobileReplay/batchDeleteById`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function getOrganizationByUser(params) {
  return request(`/api/hn/systemConfig/getOrganizationByUser`, {
    method: 'GET',
    params,
  });
}

// 获取分类
export async function getAllClassification(params) {
  return request(`/api/hn/mobileReplay/getAllClassification`, {
    method: 'GET',
    params,
  });
}

export async function getSystemConfigListByConfigType(params) {
  return request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
    method: 'GET',
    params,
  });
}

// 批量查询
export async function batchQueryRealtimeStatus(params) {
  return request(`/api/hn/mobileReplay/batchQueryRealtimeStatus`, {
    method: 'POST',
    data: params,
    // requestType: 'json',
    getResponse: true,
    responseType: 'blob',
  });
}
