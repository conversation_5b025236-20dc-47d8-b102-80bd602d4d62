import request from '@/utils/request';

export async function getPagePoliceReplay(params) {
  return request(`/api/hn/fraudPoliceNotificationReplay/pagePoliceReplay`, {
    method: 'POST',
    data:params,
    requestType: 'json',
  });
}

export async function batchImportPoliceReplayInfo(params) {
  return request(`/api/hn/fraudPoliceNotificationReplay/batchImportPoliceReplayInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

export async function deletePoliceReplayById(params) {
  return request(`/api/hn/fraudPoliceNotificationReplay/deletePoliceReplayById`, {
    method: 'GET',
    params,
  });
}

export async function getPoliceReplayDetail(params) {
  return request(`/api/hn/fraudPoliceNotificationReplay/getPoliceReplayDetailById`, {
    method: 'GET',
    params,
  });
}

export async function updatePoliceReplay(params) {
  return request(`/api/hn/fraudPoliceNotificationReplay/updatePoliceReplayDetailById`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
