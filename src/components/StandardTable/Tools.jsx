import React, { useEffect, useState, useMemo } from 'react';
import { Icon, Button, Popover, Checkbox, Row, Col, Menu, Dropdown } from 'antd';

export default ({
  columns = [],
  onChange,
  onReload,
  onSizeChange,
  defaultcolumns,
}) => {
  const defaultValue = defaultcolumns.map((v) => v.dataIndex || v.key);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(true);
  const [checkedList, setCheckedList] = useState(defaultValue);

  useEffect(() => {
    setCheckedList(defaultcolumns.map((v) => v.dataIndex || v.key));
  }, [JSON.stringify(defaultcolumns)]);

  const handleChangeCheckbox = (checkedList) => {
    setCheckedList(checkedList);
    setIndeterminate(!!checkedList.length && checkedList.length < columns.length);
    setCheckAll(checkedList.length === columns.length);
  };

  const handleCheckAllChange = (e) => {
    setCheckedList(e.target.checked ? columns.map((v) => v.dataIndex || v.key) : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  const handleReset = () => {
    setCheckedList(defaultValue);
    setIndeterminate(false);
    setCheckAll(true);
  };

  const handleReload = () => {
    if (onReload) onReload();
  };

  const handleClickMenuItem = ({ key }) => {
    if (onSizeChange) onSizeChange(key);
  };

  useEffect(() => {
    if (onChange) onChange(checkedList);
  }, [checkedList]);

  const menu = (
    <Menu onClick={handleClickMenuItem}>
      <Menu.Item key="default">默认</Menu.Item>
      <Menu.Item key="middle">中等</Menu.Item>
      <Menu.Item key="small">紧凑</Menu.Item>
    </Menu>
  );
  const content = (
    <Checkbox.Group
      style={{ width: '180px', maxHeight: '460px', overflowY: 'auto' }}
      onChange={handleChangeCheckbox}
      value={checkedList}
    >
      <Row>
        {columns.map((v) => (
          <Col span={24} key={v.dataIndex || v.key}>
            <Checkbox value={v.dataIndex || v.key}>{v.title}</Checkbox>
          </Col>
        ))}
      </Row>
    </Checkbox.Group>
  );
  return (
    <div>
      <span>
        <Dropdown overlay={menu} placement="bottomCenter" trigger={['click']}>
          <Icon type="column-height" />
        </Dropdown>
      </span>
      <span style={{ margin: '0 8px' }}>
        <Icon type="reload" onClick={handleReload} />
      </span>
      <span>
        <Popover
          placement="bottom"
          title={
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '200px',
              }}
            >
              <Checkbox
                indeterminate={indeterminate}
                onChange={handleCheckAllChange}
                checked={checkAll}
              >
                列展示
              </Checkbox>
              <Button type="link" onClick={handleReset}>
                重置
              </Button>
            </div>
          }
          content={content}
          trigger="click"
        >
          <Icon type="setting" />
        </Popover>
      </span>
    </div>
  );
};
