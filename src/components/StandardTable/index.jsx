import React, { Fragment, useEffect, useState } from 'react';
import { Table, Alert, Tooltip } from 'antd';
import Tools from './Tools';

// 如表格列中包含以下字段，需特殊处理，必须展示全（因所含字段页面过多，改造时过于麻烦）
const getWidth = (title) => {
  let width = 14 * title.length + 16;
  let forceShowColumns = [
    {
      width: 100,
      text: '号码',
    },
    {
      width: 100,
      text: '本地网',
    },
    {
      width: 160,
      text: '时间',
    },
    {
      width: 100,
      text: '状态',
    },
    {
      width: 150,
      text: '证件号',
    },
    {
      width: 180,
      text: '工单号',
    },
  ];
  forceShowColumns.forEach((ele) => {
    if (title.includes(ele.text)) {
      if (title.includes('号码') && title !== '号码') {
        width = width;
      } else if (title === '到期时间') {
        width = 90;
      } else {
        width = ele.width;
      }
    }
  });
  return width;
};

const Index = (props) => {
  const {
    size = 'small',
    data: { list, pagination },
    bordered = false,
    isNeedAutoWidth = false,
    loading,
    columns,
    scroll,
    multiple = true,
    showSelectCount = true,
    rowSelectionProps = true,
    rowClickSelect = true,
    onRow,
    rowKey = 'key',
    tools = false,
    detailColumns,
    locale = {},
    rowClassName,
    selectedRows = [],
    onSelectRow,
    onChange,
    otherRowSelectionProps = {},
    extraButton = null,
    components = {},
    forceColumnAlign = 'left',
    crossPageSelect = false, // 新增参数：是否支持跨页勾选
    preserveSelectedOnRefresh = false, // 新增参数：刷新时是否保留已选中的数据
  } = props;

  let paginationProps =
    pagination === false
      ? false
      : {
          pageSizeOptions: ['10', '20', '30', '50'],
          showSizeChanger: true,
          showQuickJumper: true,
          defaultPageSize: 20,
          showTotal: (total, range) => {
            return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
          },
          ...pagination,
        };

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRowsMap, setSelectedRowsMap] = useState({}); // 新增：用于存储跨页选中的数据
  const [tableSize, setTableSize] = useState(size);
  const [checkedList, setCheckedList] = useState(
    columns ? columns.map((v) => v.dataIndex || v.key) : [],
  );

  // 监听所选内容为空时（防止用户非点击状态处理选中行）
  useEffect(() => {
    if (!selectedRows.length) {
      if (!crossPageSelect) {
        // 只在非跨页勾选模式下清空选择
        setSelectedRowKeys([]);
      }
    }
  }, [JSON.stringify(selectedRows), crossPageSelect]);

  // 当前页面数据变化时，更新选中状态
  useEffect(() => {
    if (crossPageSelect && list && list.length > 0) {
      // 获取当前页面中需要选中的项
      const currentPageSelectedKeys = list
        .filter((item) => selectedRowsMap[item[rowKey]])
        .map((item) => item[rowKey]);

      // 获取其他页面选中的项
      const otherPagesKeys = Object.keys(selectedRowsMap).filter(
        (key) => !list.find((item) => item[rowKey] === key),
      );

      // 合并所有选中项的key
      const allSelectedKeys = [...currentPageSelectedKeys, ...otherPagesKeys];

      // 更新选中状态
      setSelectedRowKeys(allSelectedKeys);
    }
  }, [JSON.stringify(list), crossPageSelect, JSON.stringify(selectedRowsMap)]);

  // 统一修改表格列colunms
  const newColumns = tools
    ? detailColumns.filter((v) => checkedList.includes(v.dataIndex || v.key))
    : columns;

  let newX = 0;
  newColumns.forEach((item) => {
    item.align = forceColumnAlign;
    if (!item.render) {
      item.render = (text) => {
        return (
          <Tooltip title={text} placement="topLeft">
            <span>{text || text === 0 ? text : '--'}</span>
          </Tooltip>
        );
      };
    }

    if (isNeedAutoWidth) {
      item.width = item.forceShow ? item.width : getWidth(item.title);
    }
    newX += Number(item.width);
    // 如果有要求必須显示的内容，使用forceShow
  });

  // 清空已选项
  const cleanSelectedKeys = () => {
    setSelectedRowKeys([]);
    setSelectedRowsMap({}); // 清空跨页选择的数据
    onSelectRow && onSelectRow([]);
  };

  // 表格分页
  const handleTableChange = (pagination, filters, sorter) => {
    if (!crossPageSelect && !preserveSelectedOnRefresh) {
      // 只有在非跨页勾选模式下且不保留选中数据时才清空选择
      onSelectRow && cleanSelectedKeys();
    }
    onChange(pagination, filters, sorter);
  };

  // 检查和清理选中项中不存在的行
  const cleanInvalidSelections = () => {
    if (crossPageSelect) {
      // 验证选中项是否有效
      const validKeys = [];
      const validMap = {};

      Object.keys(selectedRowsMap).forEach((key) => {
        const item = selectedRowsMap[key];
        if (item && item[rowKey] === key) {
          validKeys.push(key);
          validMap[key] = item;
        }
      });

      // 如果存在无效项，更新状态
      if (validKeys.length !== selectedRowKeys.length) {
        setSelectedRowKeys(validKeys);
        setSelectedRowsMap(validMap);
        onSelectRow && onSelectRow(Object.values(validMap));
        return true;
      }
    }
    return false;
  };

  // 在每次渲染前检查选中数据的有效性
  useEffect(() => {
    cleanInvalidSelections();
  }, []);

  // 更新跨页选择的数据映射
  const updateSelectedRowsMap = (keys, rows) => {
    if (crossPageSelect) {
      const newMap = { ...selectedRowsMap };

      // 当前页面中的数据
      const currentPageKeys = list.map((item) => item[rowKey]);

      // 更新当前页面中的选中状态
      currentPageKeys.forEach((key) => {
        const isSelected = keys.includes(key);
        const record = list.find((item) => item[rowKey] === key);

        if (isSelected && record) {
          newMap[key] = record;
        } else if (currentPageKeys.includes(key)) {
          // 只删除当前页的未选中项
          delete newMap[key];
        }
      });

      // 保留其他页面中的选中状态
      const updatedMap = { ...newMap };
      setSelectedRowsMap(updatedMap);
      return updatedMap;
    }
    return {};
  };

  // 获取所有跨页选择的数据数组
  const getAllSelectedRows = () => {
    if (crossPageSelect) {
      return Object.values(selectedRowsMap);
    }
    return selectedRows;
  };

  // 选择可选表格行
  const handleRowSelectChange = (newSelectedRowKeys, newSelectedRows) => {
    if (crossPageSelect) {
      // 处理跨页选择
      const updatedMap = updateSelectedRowsMap(newSelectedRowKeys, newSelectedRows);

      // 合并当前页选择的键和其他页已选择的键
      const currentPageKeys = list.map((item) => item[rowKey]);
      const otherPagesKeys = Object.keys(updatedMap).filter(
        (key) => !currentPageKeys.includes(key),
      );

      // 当前页选择的键
      const currentPageSelectedKeys = newSelectedRowKeys.filter((key) =>
        currentPageKeys.includes(key),
      );

      // 合并所有选中键
      const allSelectedKeys = [...currentPageSelectedKeys, ...otherPagesKeys];
      setSelectedRowKeys(allSelectedKeys);

      // 将map转换为数组传递给onSelectRow回调
      onSelectRow && onSelectRow(Object.values(updatedMap));
    } else {
      // 原有逻辑
      onSelectRow(newSelectedRows);
      setSelectedRowKeys(newSelectedRowKeys);
    }
  };

  // 点击当前行
  const handleSelectRow = (record, rowKey) => {
    if (!rowClickSelect) return;

    if (crossPageSelect) {
      // 跨页选择逻辑
      const recordKey = record[rowKey];
      const newMap = { ...selectedRowsMap };

      if (newMap[recordKey]) {
        // 取消选择
        delete newMap[recordKey];
      } else {
        // 新增选择
        if (!multiple) {
          // 单选模式，清除之前的选择
          setSelectedRowsMap({ [recordKey]: record });
          setSelectedRowKeys([recordKey]);
          onSelectRow && onSelectRow([record]);
          return;
        }
        // 多选模式，添加到选择
        newMap[recordKey] = record;
      }

      setSelectedRowsMap(newMap);
      const newKeys = Object.keys(newMap);
      setSelectedRowKeys(newKeys);
      onSelectRow && onSelectRow(Object.values(newMap));
    } else {
      // 原有逻辑
      let newSelectRows = JSON.parse(JSON.stringify(selectedRows));
      let newSelectRowsKeys = selectedRows.map((ele) => ele[rowKey]);

      const index = selectedRows.findIndex((ele) => ele[rowKey] === record[rowKey]);
      if (index === -1) {
        if (multiple) {
          newSelectRows.push(record);
          newSelectRowsKeys.push(record[rowKey]);
        } else {
          newSelectRows = [record];
          newSelectRowsKeys = [record[rowKey]];
        }
      } else {
        if (multiple) {
          newSelectRows.splice(index, 1);
          newSelectRowsKeys.splice(index, 1);
        } else {
          newSelectRows = [];
          newSelectRowsKeys = [];
        }
      }
      handleRowSelectChange(newSelectRowsKeys, newSelectRows);
    }
  };

  // 可选表格配置
  const rowSelection = rowSelectionProps
    ? {
        selectedRowKeys,
        onChange: handleRowSelectChange,
        getCheckboxProps: (record) => {
          // 合并外部和内部的 getCheckboxProps
          let external = (rowSelectionProps.getCheckboxProps && rowSelectionProps.getCheckboxProps(record)) || {};
          return {
            disabled: record.disabled,
            ...external,
          };
        },
        type: multiple ? 'checkbox' : 'radio',
        ...otherRowSelectionProps,
        // 保留了跨页选择的自定义选择项逻辑
        ...(crossPageSelect
          ? {
              onSelect: (record, selected) => {
                const recordKey = record[rowKey];
                const newMap = { ...selectedRowsMap };

                if (selected) {
                  newMap[recordKey] = record;
                } else {
                  delete newMap[recordKey];
                }

                setSelectedRowsMap(newMap);
                const newKeys = Object.keys(newMap);
                setSelectedRowKeys(newKeys);
                onSelectRow && onSelectRow(Object.values(newMap));
              },
              onSelectAll: (selected, selectedRows, changeRows) => {
                const newMap = { ...selectedRowsMap };

                changeRows.forEach((record) => {
                  const recordKey = record[rowKey];
                  if (selected) {
                    newMap[recordKey] = record;
                  } else {
                    delete newMap[recordKey];
                  }
                });

                setSelectedRowsMap(newMap);
                const newKeys = Object.keys(newMap);
                setSelectedRowKeys(newKeys);
                onSelectRow && onSelectRow(Object.values(newMap));
              },
            }
          : {}),
      }
    : null;

  // 编辑展示列
  const handleChangeCheckedList = (checkedList) => {
    setCheckedList(checkedList);
  };

  // 获取实际选中的行数
  const getActualSelectedCount = () => {
    if (crossPageSelect) {
      return Object.keys(selectedRowsMap).length;
    }
    return selectedRowKeys.length;
  };

  // 暴露给父组件的方法
  React.useImperativeHandle(
    props.tableRef,
    () => ({
      cleanSelectedKeys,
      getSelectedData: () => getAllSelectedRows(),
      getSelectedKeys: () => selectedRowKeys,
      getSelectedRowsMap: () => selectedRowsMap, // 新增，直接获取映射对象
      getActualSelectedCount, // 新增，获取实际选中的行数
    }),
    [selectedRowKeys, selectedRowsMap],
  );

  return (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <div>{extraButton}</div>
        {tools && (
          <div style={{ textAlign: 'right' }}>
            <Tools
              defaultcolumns={columns}
              columns={detailColumns}
              onChange={handleChangeCheckedList}
              onReload={() => {
                if (onChange) onChange(pagination);
              }}
              onSizeChange={(size) => {
                setTableSize(size);
              }}
            ></Tools>
          </div>
        )}
      </div>

      {showSelectCount && (
        <Alert
          message={
            <Fragment>
              已选择 <a style={{ fontWeight: 600 }}>{getActualSelectedCount()}</a> 项&nbsp;&nbsp;
              <a onClick={cleanSelectedKeys} style={{ marginLeft: 24 }}>
                清空
              </a>
            </Fragment>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Table
        locale={locale}
        loading={loading}
        size={tableSize}
        bordered={bordered}
        rowKey={(record) => record[rowKey]}
        rowSelection={rowSelection}
        dataSource={list}
        columns={newColumns}
        pagination={paginationProps}
        {...(Object.keys(components).length > 0 ? { components } : {})}
        onChange={handleTableChange}
        scroll={{ x: scroll?.x || newX, y: scroll?.y }}
        onRow={(record) => {
          return {
            ...(onRow ? onRow(record) : {}),
            onClick: () => {
              if (rowSelectionProps) {
                handleSelectRow(record, rowKey);
              }
              if (onRow && onRow(record).onClick) onRow(record).onClick();
            },
          };
        }}
        rowClassName={rowClassName}
      />
    </div>
  );
};

export default Index;
