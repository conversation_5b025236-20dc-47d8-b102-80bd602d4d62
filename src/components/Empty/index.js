import React, { Component } from 'react'

import styles from './index.less'

export default class Empty extends Component {
  static defaultProps = {
    width: '100%',
    height: '100%',
    description: '暂无数据'
  }

  render() {
    const { width, height, description } = this.props
    return (
      <div className={styles.empty} style={{ width, height }}>
        <div className={styles.empty_img}>&nbsp;</div>
        <span className={styles.empty_text}>{description}</span>
      </div>
    )
  }
}
