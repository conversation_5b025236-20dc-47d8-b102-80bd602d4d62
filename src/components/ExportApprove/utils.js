/*
 * @Author: zxw
 * @Date: 2023-08-11 09:32:56
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-11-11 14:26:16
 * @FilePath: \newHunanfanzha\src\components\ExportApprove\utils.js
 * @Description:
 */

import { message } from 'antd';
import request from 'ponshine-request';
import moment from 'moment';
import { getAuthState } from 'ponshine';

const api = 'http://127.0.0.1:10090/slinterface';

// 检查客户端是否安装

const checkClientInstalled = async () => {
  try {
    const response = await request(api, {
      method: 'POST',
      data: {
        method: 'get_clientPath',
      },
      requestType: 'json',
    });

    if (response.Result !== 0) {
      message.error('您未安装商密文档安全客户端，请在企业门户下载最新客户端安装');
      return false;
    }
    return true;
  } catch (error) {
    message.error('客户端检查失败，请确认客户端是否正确安装');
    return false;
  }
};

// 检查客户端是否启动
const checkClientStartup = async () => {
  try {
    const response = await request(api, {
      method: 'POST',
      data: {
        method: 'SL_IsClientStartup',
        ifStart: true, //true:未启动客户端的情况下，启动客户端; false: 未启动客户端的情况下，不启动客户端
      },
      requestType: 'json',
    });

    // 0客户端未启动,1客户端已启动

    if (response.Result !== 1) {
      message.error('客户端未启动，请先启动客户端');
      return false;
    }
    return true;
  } catch (error) {
    message.error('检查客户端启动状态失败');
    return false;
  }
};

// 检查客户端登录状态
const checkLoginStatus = async () => {
  try {
    const response = await request(api, {
      method: 'POST',
      data: {
        method: 'SL_GetLoginStatus',
      },
      requestType: 'json',
    });

    return response.Result; // 0-未登录, 1-在线登录，2客户端离线登录 , 3-客户端正在登录中
  } catch (error) {
    message.error('检查登录状态失败');
    return 0;
  }
};

// 获取用户名
const getUserName = async () => {
  try {
    const response = await request(api, {
      method: 'POST',
      data: {
        method: 'SL_GetUserId',
      },
      requestType: 'json',
    });

    return response.Data;
  } catch (error) {
    message.error('获取用户信息失败');
    return false;
  }
};

// 获取单点登录的票据和用户名
const getTicket = async () => {
  try {
    const response = await request('/api/fileEncryption/getTicket', {
      method: 'POST',
    });

    // const response = {
    //   state: 'SUCCESS',
    //   data: {
    //     ticket: '3558769582',
    //     userID: 'EIP71100405',
    //   },
    // };
    if (response.state === 'SUCCESS') {
      return response.data;
    } else {
      return false;
    }
  } catch (error) {
    message.error('获取票据失败');
    return false;
  }
};

// 单点登录
const singleSignOn = async () => {
  const ticketInfo = await getTicket();
  if (!ticketInfo) return false;
  try {
    const response = await request(api, {
      method: 'POST',
      data: {
        method: 'SL_SSOClient',
        // ticket, userID
        ...ticketInfo,
      },
      requestType: 'json',
    });
    if (response.Result === 0) {
      return true;
    } else {
      message.error(response.ErrMsg || '单点登录失败');
      return false;
    }
  } catch (error) {
    message.error('单点登录失败');
    return false;
  }
};

// 主流程
export const verifyEncryptionDocumentFlow = async () => {
  // 1. 校验客户端是否安装
  const isInstalled = await checkClientInstalled();
  if (!isInstalled) return false;

  // 2. 校验客户端是否启动
  const isStartup = await checkClientStartup();
  if (!isStartup) return false;

  // 3. 校验客户端是否登录
  const loginStatus = await checkLoginStatus();

  // 4. 根据登录状态处理
  if (loginStatus === 1 || loginStatus === 2) {
    // 已登录（在线或离线），获取用户名
    // const userName = await getUserName();
    // const authState = getAuthState();
    // const { user } = authState || {};
    // const { username: platUsername } = user || {};
    // if (platUsername !== userName) {
    //   message.error('当前操作用户与登录用户不一致');
    //   return false;
    // }

    return true;
  } else {
    // 未登录，进行单点登录
    return await singleSignOn();
  }
};

export function exportFile({
  urlAPi = '',
  title,
  params = '',
  method = 'POST',
  mime = 'xls',
  isDate = false,
  isShowDate = true,
  currentDateFormate = 'YYYYMMDD',
  decode = false,
  callback,
  closeLoading,
  //是否校验商业秘密电子文件流程
  isVerifyhEncryption = false,
}) {
  request({
    url: urlAPi,
    method,
    responseType: 'blob',
    getResponse: true,
    [method === 'POST' ? 'data' : 'params']: params,
    requestType: 'json',
  }).then(async (res) => {
    if (!res || res?.data?.type?.includes('application/json')) {
      let reader = new FileReader();
      reader.readAsText(res.data, 'utf-8');
      reader.addEventListener('loadend', function () {
        let response = JSON.parse(reader.result);
        closeLoading();
        if (response.code === 505) {
          callback && callback();
        } else {
          return message.error(response.message || '下载错误');
        }
      });
      return;
    }
    if (isVerifyhEncryption) {
      // 先进行文档加密流程校验
      const verifyResult = await verifyEncryptionDocumentFlow();
      if (!verifyResult) {
        closeLoading();
        return; // 如果校验未通过，直接返回
      }
    }

    closeLoading();

    // 校验通过后，执行下载逻辑
    const hide = message.loading('下载中...', 0);

    try {
      // 处理返回的文件流
      let fileName =
        res?.response?.headers?.get('content-disposition')?.split('filename=')[1] || '';
      if (decode) {
        fileName = decodeURIComponent(fileName);
      } else {
        fileName = title
          ? `${title}.${mime}`
          : decode
          ? decodeURIComponent(fileName.substr(0, fileName.length - 1))
          : fileName.substr(0, fileName.length - 1);
      }

      let currentName = isDate
        ? isShowDate
          ? `${title}${moment().format(`${currentDateFormate}`)}.${mime}`
          : `${title}.${mime}`
        : fileName;

      const blob = res.data;
      if ('download' in document.createElement('a')) {
        // 非IE下载
        const elink = document.createElement('a');
        elink.download = currentName;
        elink.style.display = 'none';
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
      } else {
        // IE10+下载
        navigator.msSaveBlob(blob, fileName);
      }
    } finally {
      hide(); // 确保loading被关闭
    }
  });
}
