/*
 * @Author: zxw
 * @Date: 2023-08-11 10:40:21
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-14 13:14:07
 * @FilePath: \newHunanfanzha\src\components\ExportApprove\services.js
 * @Description:
 */
import request from '@/utils/request';

// 获取审批人
export async function findApprove(params) {
  return request(`/api/hn/hnFraudExportAuth/getApproverInformation`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 审批
export async function approve(params) {
  return request(`/api/hn/hnFraudExportAuth/applyHnFraudExportAuthApprove`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
