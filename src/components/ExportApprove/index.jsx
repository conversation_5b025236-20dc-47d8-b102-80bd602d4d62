/*
 * @Author: zxw
 * @Date: 2023-08-08 15:12:13
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-23 17:42:38
 * @FilePath: \newHunanfanzha\src\components\ExportApprove\index.jsx
 * @Description:
 */
import React, { Fragment, useEffect, useState } from 'react';
import {
  Form,
  Modal,
  Row,
  Col,
  Input,
  Avatar,
  Upload,
  Button,
  Icon,
  message,
  Select,
  Alert,
} from 'antd';
import { approve, findApprove } from './services';
import { exportFile } from './utils';
const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 16,
  },
};

const ExportApprove = ({
  form,
  reload,
  exportParams,
  moduleTile,
  buttonText = '数据导出',
  buttonStyle = {},
  beforeExport,
  disabledExport = false,
  isVerifyhEncryption=false
}) => {
  const { getFieldDecorator, resetFields, getFieldsValue, validateFields, setFieldsValue } = form;
  const [addLoading, setAddLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [approver, setApprover] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      getApprover();
    }
  }, [visible]);

  // 确认审批
  const handleOk = async () => {
    validateFields(async (err, values) => {
      if (err) return;
      setAddLoading(true);
      let response = await approve({ ...values, moduleName: moduleTile });
      setAddLoading(false);

      if (response && response.code === 200) {
        message.success(response.message);
        setVisible(false);
      } else {
        message.error(response.message);
      }
    });
  };

  // 导出
  const handleExport = () => {
    if ((beforeExport && beforeExport()) || !beforeExport) {
      setLoading(true);
      exportFile({
        ...exportParams,
        closeLoading: () => {
          setLoading(false);
        },
        callback: () => {
          setVisible(true);
        },
        isVerifyhEncryption
      });
    }
  };

  // 获取审批人
  const getApprover = async () => {
    let response = await findApprove();
    if (response && response.code === 200) {
      setApprover(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  return (
    <Fragment>
      <Button
        type="primary"
        onClick={handleExport}
        style={buttonStyle}
        loading={loading}
        disabled={disabledExport}
      >
        {buttonText}
      </Button>
      <Modal
        title={`导出申请审批`}
        visible={visible}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        onOk={handleOk}
        onCancel={() => {
          setVisible(false);
        }}
        confirmLoading={addLoading}
      >
        <Alert
          type="error"
          message={
            <div>
              <div>
                根据信息安全管理要求，超过50条以上的敏感信息、用户个人信息的导出需审批，审批通过后该模块内可进行导出，再次导出超过50条以上需再次申请审批
              </div>
              <div>
                （审批人选择说明: 地市用户需地市三级管理员审批，省公司需省公司总管理员审批）
              </div>
            </div>
          }
          style={{ marginBottom: 16 }}
        />

        <Row gutter={[16, 16]}>
          <Form {...formItemLayout}>
            <Form.Item label="申请导出模块">{moduleTile}</Form.Item>
            <Form.Item label="请选择审批人">
              {getFieldDecorator('approverId', {
                rules: [{ required: true, message: '请选择审批人' }],
              })(
                <Select allowClear placeholder="请选择">
                  {approver?.map((ele, index) => (
                    <Select.Option value={ele.approverId} key={index}>
                      {ele.approverName}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item label="申请原因">
              {getFieldDecorator('applyReason', {
                rules: [
                  { required: true, message: '请输入申请原因' },
                  { max: 200, message: '最多输入200个字符' },
                ],
              })(<TextArea allowClear={true} placeholder="请输入" rows={3} />)}
            </Form.Item>
          </Form>
        </Row>
      </Modal>
    </Fragment>
  );
};
export default Form.create()(ExportApprove);
