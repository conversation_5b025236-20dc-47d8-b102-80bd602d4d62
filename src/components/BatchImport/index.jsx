import React, { useState } from 'react';
import { Modal, Upload, Button, Icon, Row, Col, message } from 'antd';
import styles from './index.less';
import { exportFile } from '@/utils/utils';

const BatchImportModal = (props) => {
  const {
    title,
    visible,
    onImport,
    onClose,
    downTemplateUrl,
    errorExportUrl,
    tipsText,
    importRequest,
    reload,
    closeModal,
    acceptType = ['.xlsx', '.xls'],
  } = props;
  const [fileList, setFileList] = useState([]);
  const [downloadVisible, setDownloadVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorTip, setErrorTip] = useState('');

  const okHandle = async () => {
    if (fileList.length) {
      const fileData = new FormData();
      fileData.append('file', fileList[0]);
      // 如果有自定义的导入方法
      if (onImport) {
        onImport(fileData, () => {
          setDownloadVisible(true);
        });
      } else {
        // 默认的导入方法
        setLoading(true);
        const response = await importRequest(fileData);
        setLoading(false);

        if (response.code === 200) {
          message.success(response.message || '导入成功');
          reload();
          closeModal();
        } else {
          if (response.code === 401) {
            reload();
            setDownloadVisible(true);
            setErrorTip(response.message);
          } else {
            message.error(response.message);
          }
        }
      }
    } else {
      message.warning('请选择文件');
    }
  };

  const downloadErrorFile = () => {
    exportFile({
      urlAPi: errorExportUrl,
      decode: true,
      method: 'POST',
    });
    setDownloadVisible(false);
    onClose();
  };

  const hideDownModal = () => {
    setDownloadVisible(false);
  };

  // 模板下载
  const handleDownload = () => {
    try {
      window.open(`${downTemplateUrl}`);
    } catch (e) {
      console.log(e);
    }
  };

  const uploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      if (acceptType.includes(tag)) {
        setFileList([file]);
        return false;
      } else {
        message.warning('文件格式不正确');
      }
    },
    fileList,
  };

  return (
    <>
      <Modal
        title={title}
        visible={visible}
        onCancel={onClose}
        destroyOnClose
        afterClose={() => {
          setFileList([]);
        }}
        cancelText="取消"
        okText="确认"
        onOk={okHandle}
        confirmLoading={loading}
      >
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col span={24} style={{ marginLeft: 10 }}>
            <div className={styles.uploadPanel}>
              <Upload {...uploadProps} accept={acceptType?.join(',')}>
                <Button>
                  <Icon type="upload" /> 选择文件
                </Button>
                {uploadProps.fileList.length === 0 ? '  未选择任何文件' : ''}
              </Upload>
            </div>
            <div className={styles.tips}>{tipsText ? tipsText : '*每个文件不得超过10万条数据'}</div>
            <a className={styles.download} onClick={handleDownload}>
              模板下载
            </a>
          </Col>
        </Row>
      </Modal>
      {downloadVisible && (
        <Modal
          visible={downloadVisible}
          title="下载错误文件"
          destroyOnClose
          maskClosable={false}
          onCancel={hideDownModal}
          footer={[
            <Button onClick={hideDownModal} key="1">
              取消
            </Button>,
            <Button type="primary" onClick={downloadErrorFile} key="2">
              确认下载
            </Button>,
          ]}
        >
          <div className={styles.confirmModalContent}>
            <span>{errorTip}</span>
          </div>
        </Modal>
      )}
    </>
  );
};

export default BatchImportModal;
