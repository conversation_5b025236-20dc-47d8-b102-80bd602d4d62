/*
 * @Description: 
 * @Author: ss
 * @Date: 2022-09-16 14:43:24
 * @LastEditTime: 2022-09-16 15:10:00
 * @LastEditors: ss
 * @Reference: 
 */
import { Icon, Menu } from 'antd'; // eslint-disable-next-line import/no-extraneous-dependencies
import { getLocale, setLocale ,formatMessage} from 'ponshine-plugin-react/locale';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import classNames from 'classnames';
const SelectLang = (props) => {
  const { className, reloadPage } = props;
  const selectedLang = getLocale();

  const changeLang = ({ key }) => setLocale(key, !!reloadPage);

  const locales = ['zh-CN', 'zh-TW', 'en-US', 'it-IT', 'pt-BR'];
  const languageLabels = {
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
    'en-US': 'English',
    'it-IT': 'Italia',
    'pt-BR': 'Português',
  };
  const languageIcons = {
    'zh-CN': '🇨🇳',
    'zh-TW': '🇭🇰',
    'en-US': '🇺🇸',
    'it-IT': '🇮🇹',
    'pt-BR': '🇧🇷',
  };
  const langMenu = (
    <Menu className={styles.menu} selectedKeys={[selectedLang]} onClick={changeLang}>
      {locales.map((locale) => (
        <Menu.Item key={locale}>
          <span role="img" aria-label={languageLabels[locale]}>
            {languageIcons[locale]}
          </span>{' '}
          {languageLabels[locale]}
        </Menu.Item>
      ))}
    </Menu>
  );
  return (
    <HeaderDropdown overlay={langMenu} placement="bottomRight">
      <span className={classNames(styles.dropDown, className)}>
        <Icon
          type="global"
          title={formatMessage({
            id: 'navBar.lang',
          })}
        />
      </span>
    </HeaderDropdown>
  );
};

export default SelectLang;
