/*
 * @Description: 
 * @Author: ss
 * @Date: 2022-09-16 14:43:24
 * @LastEditTime: 2022-09-16 15:14:15
 * @LastEditors: ss
 * @Reference: 
 */
import { Icon, Tag, Tooltip } from 'antd';
import { useContextSelector } from 'demasia-pro-layout';
import { connect } from 'dryad'; // eslint-disable-next-line import/no-extraneous-dependencies
import { formatMessage } from 'ponshine-plugin-react/locale';
import HeaderSearch from '../HeaderSearch';
import SelectLang from '../SelectLang';
import Avatar from './AvatarDropdown';
import styles from './index.less';

const ENVTagColor = {
  dev: 'orange',
  test: 'green',
  pre: '#87d068',
};

const GlobalHeaderRight = (props) => {
  const { theme, layout } = props;
  let className = styles.right;
  const isMobile = useContextSelector((v) => v.isMobile);

  if (
    theme &&
    (theme.indexOf('navdark_') === 0 || theme === 'realdark') &&
    layout === 'topmenu' &&
    !isMobile
  ) {
    className = `${styles.right}  ${styles.dark}`;
  }

  return (
    <div className={className}>
      {/* <HeaderSearch
        className={`${styles.action} ${styles.search}`}
        placeholder={formatMessage({
          id: 'component.globalHeader.search',
        })}
        defaultValue="ponshine ui"
        dataSource={[
          formatMessage({
            id: 'component.globalHeader.search.example1',
          }),
          formatMessage({
            id: 'component.globalHeader.search.example2',
          }),
          formatMessage({
            id: 'component.globalHeader.search.example3',
          }),
        ]}
        onSearch={() => {}}
        onPressEnter={() => {}}
      /> */}
      {/* <Tooltip
        title={formatMessage({
          id: 'component.globalHeader.help',
        })}
      >
        <a
          target="_blank"
          href="http://pro.demasia.org/docs/getting-started"
          rel="noopener noreferrer"
          className={styles.action}
        >
          <Icon type="question-circle-o" />
        </a>
      </Tooltip> */}
      <Avatar />
      {/* {REACT_APP_ENV && <Tag color={ENVTagColor[REACT_APP_ENV]}>{REACT_APP_ENV}</Tag>}
      <SelectLang className={styles.action} /> */}
    </div>
  );
};

export default connect(({ settings }) => ({
  theme: settings.navAndHeaderTheme,
  layout: settings.navLayoutMode,
}))(GlobalHeaderRight);
