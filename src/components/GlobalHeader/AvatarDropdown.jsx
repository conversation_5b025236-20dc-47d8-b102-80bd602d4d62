import request from '@/utils/request';
import { Avatar, Icon, Menu, message, Spin } from 'antd';
import { connect } from 'dryad';
import { router, useAuth, getAuthState } from 'ponshine';

// eslint-disable-next-line import/no-extraneous-dependencies
import { FormattedMessage } from 'ponshine-plugin-react/locale';
import React from 'react';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
class AvatarDropdown extends React.Component {
  getUnifiedCertificationApiPrefix = (api, customApi) => {
    const host = window.location.host;
    if (host === 'localhost:8000' || host === '**************:8000') {
      return customApi;
    } else {
      return api;
    }
  };

  getAddressOrParams = async (callback) => {
    const response = await request.get('/api/hn/systemConfig/getLoginParam', {});
    if (response.code === 200) {
      callback(response.data || {});
    } else {
      message.error(response.msg);
    }
  };
  onMenuClick = async (event) => {
    const { key } = event;

    if (key === 'logout') {
      // 为兼容单体服务和单点登录，登录接口改为 POST /api/login，登出接口改为 POST /api/logout
      const { accessToken } = getAuthState() || {};
      if (accessToken) {
        await request('/api/hn/autoLogin/autoLogout', {
          method: 'POST',
          requestType: 'form',
          data: {
            accessToken: 'Bearer ' + accessToken,
          },
        })
          .then((res) => {
            // debugger;
            if (res.code === 200 && res.data.code === 0) {
              message.success(res.data.msg);
            } else {
              message.error(res.message);
            }
          })
          .catch((err) => {});
      }

      await request('/api/logout', {
        method: 'post',
      });

      return;
    }

    router.push(`/account/${key}`);
  };

  render() {
    const {
      currentUser = {
        avatar: '',
        name: '',
        username: '',
      },
      menu,
    } = this.props;
    const menuHeaderDropdown = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={this.onMenuClick}>
        {menu && (
          <Menu.Item key="center">
            <Icon type="user" />
            <FormattedMessage id="menu.account.center" defaultMessage="account center" />
          </Menu.Item>
        )}
        {menu && (
          <Menu.Item key="settings">
            <Icon type="setting" />
            <FormattedMessage id="menu.account.settings" defaultMessage="account settings" />
          </Menu.Item>
        )}
        {menu && <Menu.Divider />}

        <Menu.Item key="logout">
          <Icon type="logout" />
          <FormattedMessage id="menu.account.logout" defaultMessage="logout" />
        </Menu.Item>
      </Menu>
    );
    // return currentUser && (currentUser.name || currentUser.username) ? (
    return currentUser && currentUser.realName ? (
      <HeaderDropdown overlay={menuHeaderDropdown}>
        <span className={`${styles.action} ${styles.account}`}>
          <Avatar size="small" className={styles.avatar} src={currentUser.avatar} alt="avatar" />
          {/* <span className={styles.name}>{currentUser.name || currentUser.username}</span> */}
          <span className={styles.name}>{currentUser.realName}</span>
        </span>
      </HeaderDropdown>
    ) : (
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    );
  }
}

export default typeof useAuth === 'function'
  ? function AvatarDropdownWrapper(props) {
      const authState = useAuth((authModel) => authModel.authState); // @ts-ignore

      const { user: currentUser } = authState || {};
      return <AvatarDropdown {...props} currentUser={currentUser} />;
    }
  : connect(({ user }) => ({
      currentUser: user.currentUser,
    }))(AvatarDropdown);
