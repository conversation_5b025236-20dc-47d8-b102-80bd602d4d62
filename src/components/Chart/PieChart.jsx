import React from 'react';
import Common<PERSON>hart from './index';

export default function Pie<PERSON><PERSON>({
  data = [],
  height,
  loading,
  legendOption = {},
  seriesOptions = {},
  labelLength = 20,
}) {
  const options = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}（{d}%）',
    },
    legend: {
      width: 50,
      tooltip: {
        show: true,
      },
      left: '50%',
      orient: 'vertical',
      top: 'middle',
      icon: 'circle',
      textStyle: {
        width: 148,
        fontSize: 12,
      },
      formatter: function (name) {
        let newName = name.length > labelLength ? name.slice(0, labelLength) + '...' : name;
        return newName;
      },
      ...legendOption,
    },
    series: [
      {
        type: 'pie',
        radius: '70%',
        center: ['30%', '50%'],
        data,
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        avoidLabelOverlap: true,
        label: {
          position: 'inside',
          formatter: (params) => {
            return params.percent + '%';
          },
          textStyle: {
            color: '#fff',
          },
        },
        ...seriesOptions,
      },
    ],
  };

  return <CommonChart option={options} height={height} loading={loading} />;
}
