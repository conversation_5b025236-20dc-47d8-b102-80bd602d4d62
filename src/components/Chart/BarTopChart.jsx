import React from 'react';
import CommonChart from './index';

export default function BarChart({
  color,
  xData,
  yData,
  legendData,
  height = 300,
  loading,
  seriesConfig = {},
  axisLabelOption = {},
  gridOption = {},
  onEvents = {},
  yAxisOption = {},
  xAxisOption = {},
}) {
  const options = {
    // color: ['#2db7f5', '#ff5500'],
    grid: {
      ...gridOption,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendData,
      left: 'left',
    },
    calculable: true,
    xAxis: [
      {
        type: 'value',
        minInterval: 1,
        ...xAxisOption,
      },
    ],
    yAxis: [
      {
        type: 'category',
        data: xData,
        axisLabel: {
          width: 100,
          rotate: 30,
          fontSize: 12,
          overflow: 'truncate',
          ...axisLabelOption,
        },
        ...yAxisOption,
      },
    ],
    series: yData.map((ele) => {
      return {
        ...ele,
        type: 'bar',
        label: {
          show: true,
          position: 'right',
        },
        labelLayout: {
          hideOverlap: true,
        },
        ...seriesConfig,
      };
    }),
  };
  return <CommonChart option={options} height={height} loading={loading} onEvents={onEvents} />;
}
