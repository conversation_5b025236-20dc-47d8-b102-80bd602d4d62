import React, { useEffect, useRef, useMemo } from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts/lib/echarts';

import hunan from '@/components/Chart/hunan.json';
import { Spin } from 'antd';

export default function index({ data, height = 600, loading = false }) {
  const maxData = useMemo(() => {
    return data.length ? Math.max(...data.map((ele) => ele.value)) : 100;
  }, [data]);
  const options = useMemo(() => {
    echarts.registerMap('湖南', hunan);
    return {
      tooltip: {
        trigger: 'item',
        show: true,
        enterable: true,
        textStyle: {
          fontSize: 14,
          // color: '#fff',
        },

        // backgroundColor: '#fff',
        formatter: '{b}：{c}',
        // formatter: (params) => {
        //   return `${params.name}：${params?.value || 0}`;
        // },
      },
      visualMap: {
        min: 0,
        max: maxData,
        realtime: false,
        calculable: true,
        inRange: {
          // color: ['#FFE09A', '#FAC858', '#ECAC1B', '#D77A12', '#B45114'], //纯黄
          color: ['#fff', '#F4D5D5', '#E8AAAA', '#DD8080', '#D25555', '#BB0000'], //红色
          // color: ['#5CA3F7', '#3281DD', '#0763CE', '#004EA8', '#002E6E'], //纯蓝
        },
      },

      series: [
        // map
        {
          map: '湖南', // 使用
          type: 'map',
          selectedMode: false, // 禁用选中模式
          roam: false, //是否开启鼠标缩放和平移漫游
          aspectScale: 1, //地图的长宽比
          zoom: 1, // 当前视角的缩放比例。
          layoutSize: '95%', //地图的大小
          layoutCenter: ['46%', '50%'], //定义地图中心在屏幕中的位置
          clickable: false,
          emphasis: {
            // 鼠标移入高亮
            itemStyle: {
              // areaColor: '#5694db',//蓝色
              // areaColor: '#FFF4DC', //淡黄
              areaColor: '#f1e1e1', //淡红色
            },
          },
          itemStyle: {
            borderWidth: 1,
            // borderColor: '#3f1414',
            borderColor: '#693b3b',
          },
          data: data,
          label: {
            normal: {
              show: true,
            },
            emphasis: {
              textStyle: {
                // color: 'rgba(0,0,0,0.6)',
              },
            },
          },
        },
      ],
    };
  }, [data]);

  return (
    <Spin spinning={loading}>
      <ReactEcharts option={options} style={{ width: '100%', height: height }} />
    </Spin>
  );
}
