import React from 'react';
import Common<PERSON>hart from './index';

export default function Pie<PERSON>hart({
  data = [],
  height,
  loading,
  legendOption = {},
  seriesOptions = {},
  labelLength = 20,
}) {
  const options = {
    tooltip: {
      position: (point, params, dom, rect, size) => {
        const padding = 10; // 设置边距
        return [point[0] + padding, point[1] - size.contentSize[1] - padding];
      },
      trigger: 'item',
      formatter: '{b}: {c}（{d}%）',
    },
    legend: {
      width: 50,
      tooltip: {
        show: true,
      },
      right: 0,
      // left: '70%',
      orient: 'vertical',
      top: 'middle',
      icon: 'circle',
      textStyle: {
        width: 148,
        fontSize: 12,
      },
      itemWidth: 8,
      itemHeight: 8,
      formatter: function (name) {
        let newName = name.length > labelLength ? name.slice(0, labelLength) + '...' : name;
        return newName;
      },
      ...legendOption,
    },

    series: [
      {
        type: 'pie',
        radius: ['35%', '50%'],
        center: ['33%', '50%'],
        data,
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        label: {
          // alignTo: 'edge',
          formatter: '{name|{b}}\n{value|{c}}',
          minMargin: 5,
          edgeDistance: 0,
          lineHeight: 15,
          rich: {
            name: {
              fontSize: 12,
              color: 'rgba(0,0,0,0.6)',
            },
            value: {
              fontSize: 12,
              fontWeight: 'bold',
              color: 'rgba(0,0,0,0.7)',
            },
          },
        },
      },
    ],
  };

  return <CommonChart option={options} height={height} loading={loading} />;
}
