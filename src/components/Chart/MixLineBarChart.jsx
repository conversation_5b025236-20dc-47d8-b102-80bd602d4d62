import React from 'react';
import CommonChart from './index';
const defaultColor = [
  '#ee6666',
  '#1492ff',
  '#fac858',
  '#91cc75',
  '#5470c6',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
  '#0ed2ff',
];

export default function BarChart({
  color,
  xData,
  yData,
  height = 300,
  loading,
  seriesConfig = {},
  axisLabelOption = {},
  gridOption = {},
}) {
  const options = {
    // color: ['#2db7f5', '#ff5500'],
    grid: {
      ...gridOption,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      left: 'left',
    },
    calculable: true,
    xAxis: [
      {
        type: 'category',
        // prettier-ignore
        data: xData,
        axisLabel: {
          width: 120,
          rotate: 30,
          fontSize: 12,
          overflow: 'truncate',
          ...axisLabelOption,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        minInterval: 1,
      },
    ],
    series: yData.map((ele) => {
      return {
        ...ele,
        label: {
          show: true,
          position: ele.type === 'bar' ? 'insideBottom' : 'top',
        },
        labelLayout: {
          hideOverlap: true,
        },
        ...seriesConfig,
      };
    }),
  };
  return <CommonChart option={options} height={height} loading={loading} />;
}
