import React from 'react';
import CommonChart from './index';
const defaultColor = [
  '#ee6666',
  '#1492ff',
  '#fac858',
  '#91cc75',
  '#5470c6',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
  '#0ed2ff',
];

export default function LineChart({
  xData,
  yData,
  legendData,
  height = 300,
  loading,
  isMutipleYAxisIndex = false,
  axisLabelOption = {},
  gridOption = {},
  onEvents,
  seriesConfig = {},
}) {
  const options = {
    grid: {
      ...gridOption,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendData,
      left: 'left',
    },
    calculable: true,
    xAxis: [
      {
        type: 'category',
        // prettier-ignore
        data: xData,
        axisLabel: {
          width: 120,
          rotate: 30,
          fontSize: 12,
          overflow: 'truncate',
          ...axisLabelOption,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
      {
        type: 'value',
      },
    ],
    series: yData.map((ele, index) => {
      return {
        ...ele,
        type: 'line',
        yAxisIndex: isMutipleYAxisIndex ? index : 0,
        ...seriesConfig,
      };
    }),
  };
  return <CommonChart option={options} height={height} loading={loading} onEvents={onEvents} />;
}
