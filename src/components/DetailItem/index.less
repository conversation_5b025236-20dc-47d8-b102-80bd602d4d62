// .detailDesBox {
//     :global {
//         .ant-descriptions-item {
//             // display: flex;
//             & > span {
//                 display: inline-flex;
//                 &> span {
//                     max-width: 148px;
//                     overflow: hidden;
//                     white-space: nowrap;
//                     text-overflow: ellipsis;
//                 }
//             }
//         }
       
//     }
// }
.NoItem{
    display: none !important;
}
.SpanItem{
    width: 80% !important;
  }