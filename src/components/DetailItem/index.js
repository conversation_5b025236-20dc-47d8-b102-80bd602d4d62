/*
 * @Descripttion: 
 * @version: 
 * @LastEditors: ss <EMAIL>
 * @Date: 2023-11-22 16:30:55
 * @LastEditTime: 2024-09-04 17:09:35
 */
import React, { Children, Fragment } from 'react';
import { Descriptions, Tooltip } from 'antd';
import styles from './index.less';
import CommonTitle from '@/components/CommonTitle';
import classNames from 'classnames';

const DetailItem = ({
  title,
  bordered = false,
  column = 3,
  data = [],
  children,
  dataValue = {},
  width=148,
}) => {
  return (
    <div className={styles.detailDesBox}>
      <Descriptions
        title={title?<CommonTitle title={title} underLine={false}/>:''}
        column={column}
        bordered={bordered}
      >
        {!!data.length &&
          data?.map((ele, index) => (
                <Descriptions.Item label={ele.label} key={index} className={classNames({[styles.NoItem]:ele.isNone,[styles.SpanItem]:ele.span,})} span={ele?.sapnInfo??1} >
                  {
                    ele.isNoTips? <span >{(ele.render && ele.render(dataValue?.[ele.key])) || dataValue?.[ele.key] || '--'}</span>: <Tooltip title={(ele.render && ele.render(dataValue?.[ele.key])) || dataValue?.[ele.key] || '--'} placement='topLeft'>
                    <span >{(ele.render && ele.render(dataValue?.[ele.key])) || dataValue?.[ele.key] || '--'}</span>
                  </Tooltip>
                  }
             
            </Descriptions.Item>
          ))}
      </Descriptions>
      {children}
    </div>
  );
};

export default DetailItem;
