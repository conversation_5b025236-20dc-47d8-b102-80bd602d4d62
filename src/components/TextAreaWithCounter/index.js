/*
 * @Author: ss <EMAIL>
 * @Date: 2024-11-18 14:05:07
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-11-20 15:56:09
 * @FilePath: /hunanfanzha/src/components/TextAreaWithCounter/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { forwardRef, useImperativeHandle, useState ,useEffect} from 'react';
import { Input } from 'antd';
import styles from './index.less';

const TextAreaWithCounter = forwardRef(({ disabled, placeholder, maxRows, minRows, maxLength,currentInfo,onChange },ref) => {
  useImperativeHandle(ref, () => ({
    getValue: () => value,
  }));
  const [value, setValue] = useState(currentInfo);
  const [currentLength, setCurrentLength] = useState(0);

  const handleChange = (e) => {
    const inputValue = e.target.value;
    setValue(inputValue);
    setCurrentLength(inputValue.length);
    if (onChange) {
      onChange(inputValue);
    }
  };

  return (
    <div className={styles.textAreaWithCounter}>
      <Input.TextArea
        disabled={disabled}
        placeholder={placeholder}
        allowClear
        autoSize={{ maxRows, minRows }}
        value={value}
        onChange={handleChange}
        maxLength={maxLength}
      />
      <div style={{ textAlign: 'right', marginTop: 10 }} className={styles.charCounter}>
        {currentLength} / {maxLength}
      </div>
    </div>
  );
});
export default TextAreaWithCounter;