/*
 * @Author: zxw
 * @Date: 2023-09-04 14:05:30
 * @LastEditors: zxw
 * @LastEditTime: 2023-09-08 13:32:35
 * @FilePath: \newHunanfanzha\src\components\CommonFormItem\localNetworkFormItem\index.jsx
 * @Description:
 */
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Form, Select, message } from 'antd';
import { getLocalNetwork } from './services';

const Index = forwardRef((props, ref) => {
  const {
    form: { getFieldDecorator },
    getListDatas,
    cref,
    style = {},
    otherInitParams = {},
    defaultSearch = true,
    formKey = 'localNetwork',
    formLable = '本地网',
  } = props;
  const [localNetworkList, setLocalNetworkList] = useState([]);

  const getInitialValue = (data) => {
    if (data?.length === 1) {
      return data?.[0]?.name;
    } else {
      return undefined;
    }
  };

  // 获取本地网和是否电渠下拉
  const getSearchList = async () => {
    const response = await getLocalNetwork();
    if (response.code === 200) {
      setLocalNetworkList(response?.data || []);
      defaultSearch &&
        getListDatas({
          [formKey]: getInitialValue(response?.data || []),
          ...otherInitParams,
        });
    } else {
      // defaultSearch && getListDatas();
      message.error(response.message);
    }
  };

  useEffect(() => {
    getSearchList();
  }, []);

  useImperativeHandle(cref, () => ({
    getInitialValue: () => {
      return getInitialValue(localNetworkList);
    },
  }));

  return (
    <Form.Item label={formLable}>
      {getFieldDecorator(formKey, {
        initialValue: getInitialValue(localNetworkList),
      })(
        <Select
          placeholder="请选择"
          allowClear={!Boolean(getInitialValue(localNetworkList))}
          style={style}
        >
          {localNetworkList?.map((ele, index) => (
            <Select.Option value={ele.name} key={index}>
              {ele.name}
            </Select.Option>
          ))}
        </Select>,
      )}
    </Form.Item>
  );
});

export default Index;
