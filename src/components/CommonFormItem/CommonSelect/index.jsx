import React, { useState, useEffect } from 'react';
import { Form, Select, message } from 'antd';
import { getSystemConfigListByConfigType } from '@/services/common';

const CommonSelect = ({
  configType = '',
  form,
  formItemKey = 'configValue',
  formItemLabel = '',
  rules = [],
  initialValue = undefined,
  disabled = false,
  data = [],
  valueKey = 'value',
  labelKey = 'name',
  availableData = [],
}) => {
  const { getFieldDecorator } = form;
  const [options, setOptions] = useState([]);

  const getOptions = async () => {
    if (data?.length > 0) {
      setOptions(data);
    } else {
      if (!configType) return;
      const response = await getSystemConfigListByConfigType({ configType });
      if (response.code === 200) {
        setOptions(response.data);
      } else {
        message.error(response.message);
      }
    }
  };

  useEffect(() => {
    getOptions();
  }, [JSON.stringify(data)]);

  return (
    <Form.Item label={formItemLabel}>
      {getFieldDecorator(formItemKey, {
        rules,
        initialValue,
      })(
        <Select
          placeholder="请选择"
          allowClear
          getPopupContainer={(triggerNode) => triggerNode.parentElement}
          disabled={disabled}
        >
          {options.map((option) => (
            <Select.Option
              key={option.value}
              value={option[valueKey]}
              disabled={availableData?.length ? !availableData.includes(option?.[labelKey]) : false}
            >
              {option?.[labelKey]}
            </Select.Option>
          ))}
        </Select>,
      )}
    </Form.Item>
  );
};

export default CommonSelect;
