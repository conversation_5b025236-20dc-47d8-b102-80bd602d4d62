import React, { useState, useEffect, useImperative<PERSON>andle, forwardRef, Fragment } from 'react';
import { Form, Select, Col } from 'antd';
import { getFraudType, getShutdownLabel, getChildrenLabel } from './services';

const Index = (props) => {
  const {
    form: { getFieldDecorator, setFieldsValue },
  } = props;
  const [fraudTypeList, setFraudTypeList] = useState([]);
  const [shutdownLabelList, setShutdownLabelList] = useState([]);
  const [childrenLabelList, setChildrenLabelList] = useState([]);

  // 获取本地网和是否电渠下拉
  const getFraudTypeList = async () => {
    const response = await getFraudType();
    if (response.code === 200) {
      setFraudTypeList(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getFraudTypeList();
  }, []);

  const changeFraudType = async (val) => {
    setFieldsValue({ shutdownTag: undefined, subtag: undefined });
    if (!val) {
      setShutdownLabelList([]);
      setChildrenLabelList([]);
      return;
    }
    const response = await getShutdownLabel({ fraudType: val });
    if (response.code === 200) {
      setShutdownLabelList(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  const changeShutdownLabel = async (val) => {
    setFieldsValue({ subtag: undefined });
    if (!val) {
      setChildrenLabelList([]);
      return;
    }
    const response = await getChildrenLabel({ shutdownTag: val });
    if (response.code === 200) {
      setChildrenLabelList(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  return [
    <Form.Item label="涉诈类型">
      {getFieldDecorator('fraudType')(
        <Select placeholder="请选择" allowClear onChange={changeFraudType}>
          {fraudTypeList?.map((ele, index) => (
            <Select.Option value={ele} key={index}>
              {ele}
            </Select.Option>
          ))}
        </Select>,
      )}
    </Form.Item>,
    <Form.Item label="关停标签">
      {getFieldDecorator('shutdownTag')(
        <Select placeholder="请选择" allowClear onChange={changeShutdownLabel}>
          {shutdownLabelList?.map((ele, index) => (
            <Select.Option value={ele} key={index}>
              {ele}
            </Select.Option>
          ))}
        </Select>,
      )}
    </Form.Item>,
    <Form.Item label="子标签">
      {getFieldDecorator('subtag')(
        <Select placeholder="请选择" allowClear>
          {childrenLabelList?.map((ele, index) => (
            <Select.Option value={ele} key={index}>
              {ele}
            </Select.Option>
          ))}
        </Select>,
      )}
    </Form.Item>,
  ];
};

export default Index;
