import React, { Fragment, useState } from 'react';
import { Row, Col, Form, Icon, Button } from 'antd';
import styles from './index.less';
const Index = ({ formList, form, optButton, colSpan = 6 }) => {
  const rowColNum = 24 / colSpan;
  const residualCol = formList.length % rowColNum;
  const optButtonColSpan = residualCol === 0 ? 24 : (rowColNum - residualCol) * colSpan;

  const [isExpand, setIsExpand] = useState(false);

  const handleExpand = (flag) => {
    setIsExpand(flag);
  };

  return (
    <Fragment>
      <Row>
        {formList.map((ele, index) => (
          <Col
            span={colSpan}
            style={{ display: isExpand ? 'block' : index > rowColNum - 2 ? 'none' : 'block' }}
            key={index}
          >
            {ele}
          </Col>
        ))}
        <Col
          span={
            isExpand ? optButtonColSpan : formList?.length < rowColNum ? optButtonColSpan : colSpan
          }
          style={{ textAlign: 'right' }}
        >
          <Form.Item wrapperCol={{ span: 24 }} className={styles.butnList}>
            {optButton}
            {formList?.length > rowColNum - 1 ? (
              isExpand ? (
                <a
                  type="link"
                  onClick={() => {
                    handleExpand(false);
                  }}
                  style={{ marginLeft: 16 }}
                >
                  收起
                  <Icon type="up" />
                </a>
              ) : (
                <a
                  type="link"
                  style={{ marginLeft: 16 }}
                  onClick={() => {
                    handleExpand(true);
                  }}
                >
                  展开
                  <Icon type="down" />
                </a>
              )
            ) : (
              <Fragment></Fragment>
            )}
          </Form.Item>
        </Col>
      </Row>
    </Fragment>
  );
};

export default Form.create()(Index);
