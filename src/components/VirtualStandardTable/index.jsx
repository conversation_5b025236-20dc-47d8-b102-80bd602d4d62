import React, { Fragment, useEffect, useState } from 'react';
import { Table, Alert } from 'antd';
import Tools from './Tools';
import { Tooltip } from 'demasia-pro-layout';
import styles from './index.less';

// 如表格列中包含以下字段，需特殊处理，必须展示全（因所含字段页面过多，改造时过于麻烦）
const getWidth = (title) => {
  let width = 14 * title.length + 16;
  let forceShowColumns = [
    {
      width: 100,
      text: '号码',
    },
    {
      width: 100,
      text: '本地网',
    },
    {
      width: 160,
      text: '时间',
    },
    {
      width: 100,
      text: '状态',
    },
    {
      width: 150,
      text: '证件号',
    },
    {
      width: 180,
      text: '工单号',
    },
  ];
  forceShowColumns.forEach((ele) => {
    if (title.includes(ele.text)) {
      if (title.includes('号码') && title !== '号码') {
        width = width;
      } else if (title === '到期时间') {
        width = 90;
      } else {
        width = ele.width;
      }
    }
  });
  return width;
};

const Index = (props) => {
  const {
    size = 'small',
    data: { list, pagination },
    bordered = false,
    isNeedAutoWidth = false,
    loading,
    columns,
    scroll,
    multiple = true,
    showSelectCount = true,
    rowSelectionProps = true,
    rowClickSelect = true,
    onRow,
    rowKey = 'key',
    tools = false,
    detailColumns,
    locale = {},
    rowClassName,
    selectedRows = [],
    onSelectRow,
    onChange,
    components = null,
  } = props;

  let paginationProps =
    pagination === false
      ? false
      : {
          pageSizeOptions: ['10', '20', '30', '50'],
          showSizeChanger: true,
          showQuickJumper: true,
          defaultPageSize: 20,
          showTotal: (total, range) => {
            return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
          },
          ...pagination,
        };

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [tableSize, setTableSize] = useState(size);
  const [checkedList, setCheckedList] = useState(
    columns ? columns.map((v) => v.dataIndex || v.key) : [],
  );

  // 监听所选内容为空时（防止用户非点击状态处理选中行）
  useEffect(() => {
    if (!selectedRows.length) {
      setSelectedRowKeys([]);
    }
  }, [JSON.stringify(selectedRows)]);

  // 统一修改表格列colunms
  const newColumns = tools
    ? detailColumns.filter((v) => checkedList.includes(v.dataIndex || v.key))
    : columns;

  let newX = 0;
  newColumns.forEach((item) => {
    item.align = 'left';
    if (!item.render) {
      item.render = (text) => {
        return (
          <Tooltip title={text} placement="topLeft">
            <span>{text ?? '--'}</span>
          </Tooltip>
        );
      };
    }

    if (isNeedAutoWidth) {
      item.width = item.forceShow ? item.width : getWidth(item.title);
    }
    newX += Number(item.width);
    // 如果有要求必須显示的内容，使用forceShow
  });

  // 清空已选项
  const cleanSelectedKeys = () => {
    setSelectedRowKeys([]);
    onSelectRow && onSelectRow([]);
  };

  // 表格分页
  const handleTableChange = (pagination, filters, sorter) => {
    onSelectRow && cleanSelectedKeys();
    onChange(pagination, filters, sorter);
  };

  // 选择可选表格行
  const handleRowSelectChange = (selectedRowKeys, newSelectedRows) => {
    onSelectRow(newSelectedRows);
    setSelectedRowKeys(selectedRowKeys);
  };

  // 点击当前行
  const handleSelectRow = (record, rowKey) => {
    let newSelectRows = JSON.parse(JSON.stringify(selectedRows));
    let newSelectRowsKeys = selectedRows.map((ele) => ele[rowKey]);
    if (!rowClickSelect) return;
    const index = selectedRows.findIndex((ele) => ele[rowKey] === record[rowKey]);
    if (index === -1) {
      if (multiple) {
        newSelectRows.push(record);
        newSelectRowsKeys.push(record[rowKey]);
      } else {
        newSelectRows = [record];
        newSelectRowsKeys = [record[rowKey]];
      }
    } else {
      if (multiple) {
        newSelectRows.splice(index, 1);
        newSelectRowsKeys.splice(index, 1);
      } else {
        newSelectRows = [];
        newSelectRowsKeys = [];
      }
    }
    handleRowSelectChange(newSelectRowsKeys, newSelectRows);
  };

  // 可选表格配置
  const rowSelection = rowSelectionProps
    ? {
        selectedRowKeys,
        onChange: handleRowSelectChange,
        getCheckboxProps: (record) => ({
          disabled: record.disabled,
        }),
        type: multiple ? 'checkbox' : 'radio',
      }
    : null;

  // 编辑展示列
  const handleChangeCheckedList = (checkedList) => {
    setCheckedList(checkedList);
  };

  return (
    <div className={styles.virtualStandardTable}>
      {tools && (
        <div style={{ marginBottom: 16, textAlign: 'right' }}>
          <Tools
            defaultcolumns={columns}
            columns={detailColumns}
            onChange={handleChangeCheckedList}
            onReload={() => {
              if (onChange) onChange(pagination);
            }}
            onSizeChange={(size) => {
              setTableSize(size);
            }}
          ></Tools>
        </div>
      )}
      {showSelectCount && (
        <Alert
          message={
            <Fragment>
              已选择 <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a> 项&nbsp;&nbsp;
              <a onClick={cleanSelectedKeys} style={{ marginLeft: 24 }}>
                清空
              </a>
            </Fragment>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Table
        locale={locale}
        loading={loading}
        size={tableSize}
        bordered={bordered}
        rowKey={(record) => record[rowKey]}
        rowSelection={rowSelection}
        dataSource={list}
        columns={newColumns}
        pagination={paginationProps}
        onChange={handleTableChange}
        scroll={{ x: scroll?.x || newX, y: scroll?.y }}
        components={components}
        onRow={(record) => {
          return {
            ...(onRow ? onRow(record) : {}),
            onClick: () => {
              if (rowSelectionProps) {
                handleSelectRow(record, rowKey);
              }
              if (onRow && onRow(record).onClick) onRow(record).onClick();
            },
          };
        }}
        rowClassName={rowClassName}
      />
    </div>
  );
};

export default Index;
