import React, { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { useSize, useDebounceEffect } from 'phooks';
import Empty from '@/components/Empty';
import * as echarts from 'echarts';

// 把默认的option和传入的option合并
const mergeOption = (option, defaultOption) => {
  Object.keys(option).forEach((key) => {
    Object.keys(defaultOption).forEach((defaultKey) => {
      if (key === defaultKey) {
        option[key] = {
          ...defaultOption[defaultKey],
          ...option[key],
        };
      } else {
        if (typeof option[key] === 'object') {
          mergeOption(option[key], defaultOption);
        }
      }
    });
  });
  return option;
};

const drawChart = (container, { option, show, onChartReady, onEvents }) => {
  if (!container || !show) return;
  const myChart = echarts.init(container);
  // myChart.clear();

  const defaultOption = {
    tooltip: {
      textStyle: {
        align: 'left',
        fontSize: 14,
      },
      extraCssText: 'box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.2)', //添加阴影
    },
    toolbox: {
      show: false,
    },
    legend: {
      textStyle: {
        fontSize: 14,
      },
    },
  };

  myChart.setOption(mergeOption(option, defaultOption));

  myChart.resize();
  if (onChartReady) {
    onChartReady(myChart);
  }

  if (onEvents && typeof onEvents == 'object') {
    Object.keys(onEvents).forEach((key) => {
      myChart.on(key, (v) => {
        onEvents[key](myChart, v);
      });
    });
  }
  return myChart;
};

// const defaultProps = {

// }

const Echarts = forwardRef(
  ({ option, show = true, onChartReady, onEvents, echartsId, ...restProps }, ref) => {
    const chartRef = useRef();
    const chartParentRef = useRef();
    const myChartRef = useRef();
    const size = useSize(chartParentRef);
    useImperativeHandle(ref, () => ({
      myChartRef: myChartRef.current,
    }));
    const handleResize = () => {
      myChartRef.current = drawChart(chartRef.current, { option, show, onChartReady, onEvents });
      return () => {
        if (myChartRef.current) myChartRef.current.dispose();
      };
    };
    useEffect(handleResize, [option]); // 数据发生变动，重新绘制
    useDebounceEffect(handleResize, [size], {
      // 增加防抖，大小发生变化，重新绘制
      wait: 200,
    });
    return (
      <div ref={chartParentRef} style={{ width: '100%', minHeight: '200px' }} {...restProps}>
        {show ? (
          <div ref={chartRef} style={{ height: '100%' }} id={echartsId} />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} height="100%" />
        )}
      </div>
    );
  },
);

export default Echarts;
