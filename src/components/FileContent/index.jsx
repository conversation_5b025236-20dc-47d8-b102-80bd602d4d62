import React, { Fragment, useState } from 'react';
import { Icon, Table, Modal, Popover } from 'antd';
import { exportFile } from '@/utils/utils';

const FileContent = ({ loading, data = [], downLoadApi = '' }) => {
  const columns = [
    {
      title: '附件名',
      dataIndex: 'fileName',
      key: 'fileName',
      align: 'left',
    },

    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (v, r) => (
        <Icon
          type="cloud-download"
          onClick={() => handleDownload(r)}
          title="下载"
          style={{ color: '#1890ff' }}
        />
      ),
    },
  ];
  const handleDownload = (record) => {
    exportFile({
      method: 'POST',
      urlAPi: downLoadApi,
      requestType: 'form',
      params: { id: record.id },
      decode: true,
    });
  };

  return (
    <Popover
      content={
        <Table
          rowKey="id"
          pagination={false}
          columns={columns}
          dataSource={data}
          loading={loading}
          locale={{ emptyText: '无附件' }}
        ></Table>
      }
      title="附件"
      trigger="click"
      placement="rightTop"
    >
      <a disabled={!data?.length}>
        <Icon type="cloud-download" />
      </a>
    </Popover>
  );
};

export default FileContent;
