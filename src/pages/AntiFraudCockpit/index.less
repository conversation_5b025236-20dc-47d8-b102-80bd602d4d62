@font-face {
    font-family: AlibabaPuHuiTi;
    src: url(./font/AlibabaPuHuiTi-3-45-Light.ttf);
    font-weight: normal;
}
.antiFraudCockpit {
    overflow-x: hidden;
    background: url(./imgs/bg.png) no-repeat;    
    background-size: 100% 100%;
    position: relative;
    width: 100%;
    height: 100%;
    font-family: 'AlibabaPuHuiTi';  
    .content {
        display: flex;
       .left {
            width: 30%;
            background: url(./imgs/leftBg.png) no-repeat;
            background-size: 100% 100%;
            margin-top: 2px;
            padding-top: 52px;
            
       } 
       .center {
            width: calc(40% - 32px) ;
            margin: 0 16px;

        } 
        .right {
            width: 30%;
            margin-top: 2px;
            padding-top: 52px;
            position: relative;
            &::before {
                content: ' ';
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                background: url(./imgs/leftBg.png) no-repeat;
                background-size: 100% 100%;
                transform: scaleX(-1);
                z-index: -1; /* 确保伪元素位于内容下方 */  

            }
            // transform: scaleX(-1);
        }

 
    }   
    .bottom {
        height: 32px;
        width: 100%;
        background: url(./imgs/bottom.png) no-repeat;
        background-size: 100%;
        margin-top: 8px;
    }
}