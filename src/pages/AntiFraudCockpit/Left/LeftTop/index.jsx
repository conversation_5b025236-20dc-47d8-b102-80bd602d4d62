import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '../../components/CommonCard';
import LineChart from '../../components/Chart/LineChart';
import { goPage } from '@/utils/openTab';
import moment from 'moment';
import { connect } from 'dryad';
import { withContext } from 'demasia-pro-layout';

const Index = ({ dispatch, timeValues, closeSingleTab }) => {
  window.moment = moment;
  const [listData, setListData] = useState();
  const [loading, setLoading] = useState(false);

  const getData = async () => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/provinceInvolvedTrend`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    if (timeValues) {
      getData();
    }
  }, [timeValues]);
  const onEvents = {
    click: (params) => {
      const { name, value } = params;
      if (closeSingleTab) {
        closeSingleTab(
          '{"path":"/diskInformateManage/mobileFraudVerificationResume","isExact":true,"params":{}}',
        );
      }
      dispatch({
        type: 'mobileFraudVerificationResume/saveGoSearchParamas',
        payload: {
          classification: '公安侦办平台',
          notificationDate: name,
        },
      });
      goPage('/diskInformateManage/mobileFraudVerificationResume', {
        type: 'pathJump',
      });
    },
  };
  return (
    <CommonCard title="全省涉案量趋势分析" style={{ width: '75%', paddingLeft: '24px' }}>
      <LineChart
        xData={listData?.map((ele) => ele?.countDate)}
        yData={[{ data: listData?.map((ele) => ele.caseCount), name: '涉案量' }]}
        legendData={['涉案量']}
        height={200}
        width="100%"
        loading={loading}
        isMutipleYAxisIndex={false}
        onEvents={onEvents}
      />
    </CommonCard>
  );
};

const config = [['closeSingleTab', 'tabKey']];

const YourComponent = withContext(...config)((props) => {
  return <Index {...props} />;
});

export default connect(({ mobileFraudVerificationResume }) => ({
  mobileFraudVerificationResume,
}))(YourComponent);
