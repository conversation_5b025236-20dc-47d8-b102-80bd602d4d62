import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '../../components/CommonCard';
import BarChart from '../../components/Chart/BarChart';

export default function index({ timeValues }) {
  const data = {
    短信提醒: [
      {
        value: 100,
        name: '01/01',
      },
      {
        value: 200,
        name: '01/02',
      },
      {
        value: 100,
        name: '01/03',
      },
      {
        value: 200,
        name: '01/04',
      },
      {
        value: 100,
        name: '01/05',
      },
      {
        value: 200,
        name: '01/06',
      },
      {
        value: 200,
        name: '01/07',
      },
    ],
    语音提醒: [
      {
        value: 400,
        name: '01/01',
      },
      {
        value: 300,
        name: '01/02',
      },
      {
        value: 100,
        name: '01/03',
      },
      {
        value: 200,
        name: '01/04',
      },
      {
        value: 100,
        name: '01/05',
      },
      {
        value: 200,
        name: '01/06',
      },
      {
        value: 200,
        name: '01/07',
      },
    ],
  };
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async () => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/outboundWarningTrend`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    if (timeValues) {
      getData();
    }
  }, [timeValues]);
  return (
    <CommonCard title="外呼预警趋势分析" style={{ paddingLeft: 24 }}>
      <BarChart
        xData={listData?.[Object.keys(listData)[0]]?.map((ele) => ele?.warnTime)}
        yData={Object.keys(listData)?.map((ele) => {
          return {
            name: ele,
            data: listData[ele].map((ele) => ele?.warnCount),
          };
        })}
        legendData={Object.keys(listData)}
        height={170}
        width="70%"
        loading={loading}
        seriesConfig={{ barWidth: 6 }}
        axisLabelOption={{ rotate: 0 }}
      />
    </CommonCard>
  );
}
