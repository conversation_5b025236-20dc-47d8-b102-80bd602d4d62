import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '../../components/CommonCard';
import YCategoryBarChart from '../../components/Chart/YCategoryBarChart';
import styles from './index.less';
import leftExtra from '../../imgs/leftExtra.png';
import CustomRadio from '../../components/CustomRadio';

export default function index({ timeValues }) {
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [radioValue, setRadioValue] = useState('responsibleUnit');

  const getData = async ({ type }) => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/cityInvolvedRank`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues, cityDimension: type },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data?.sort((a, b) => b.caseCount - a.caseCount) || []);
    } else {
      message.error(response.message);
    }
  };

  const changeRadio = (v) => {
    setRadioValue(v);
    getData({
      type: v,
      timeValues,
    });
  };

  useEffect(() => {
    if (timeValues) {
      getData({
        type: radioValue,
      });
    }
  }, [timeValues]);
  return (
    <div className={styles.leftCenter}>
      <img src={leftExtra} />
      <CommonCard
        title="地市涉案量排名(当月)"
        extra={
          <CustomRadio
            data={[
              { label: '责任单位', value: 'responsibleUnit' },
              { label: '本地网', value: 'localNetwork' },
            ]}
            value={radioValue}
            onChange={changeRadio}
          />
        }
        style={{ width: '75%', marginLeft: 50 }}
      >
        <YCategoryBarChart
          yData={listData?.map((ele) => ele?.responsibleUnit)}
          xData={listData?.map((ele) => ele?.caseCount || 0)}
          legendData={[]}
          height={400}
          loading={loading}
          isMutipleYAxisIndex={false}
          axisLabelOption={{ rotate: 0 }}
          seriesConfig={{ barWidth: 8 }}
        />
      </CommonCard>
    </div>
  );
}
