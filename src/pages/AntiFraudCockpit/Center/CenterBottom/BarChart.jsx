import React from 'react';
import CommonChart from '@/components/Chart/index';
import * as echarts from 'echarts/lib/echarts';

export default function BarChart({ data }) {
  const options = {
    grid: {
      //   left: '70%',
      bottom: '40%',
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      data: ['电渠'],
    },
    yAxis: {
      type: 'value',
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#00effc',
        },
      },
    },
    series: [
      {
        data: data,
        type: 'bar',
        barWidth: 7,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#FA084F',
                },
                {
                  offset: 1,
                  color: '#F88E1E',
                },
              ],
              false,
            ),
          },
        },
        label: {
          show: true,
          position: 'top',
          backgroundColor: '#05242F',
          padding: [4, 10],
          borderRadius: 4,
          borderWidth: 1,
          borderColor: 'rgba(30,75,255,0.34)',
          shadowColor: 'rgba(66,164,255,0.9)',
          shadowBlur: 5,
          color: '#777',
          formatter: function (params) {
            return `{a|${params.value}}{b|个}`;
          },
          rich: {
            a: {
              fontSize: 16,
              color: '#fff',
            },
            b: {
              fontSize: 12,
              align: 'center',
              width: 20,
              color: '#909FAB',
            },
          },
        },
      },
    ],
  };
  return <CommonChart option={options} height={200} />;
}
