import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';
import 'echarts-gl';

import hunan from '@/components/Chart/hunan.json';

export default function index() {
  const domRef = useRef(null);
  const myCharts = useRef();

  var geoCoordMap = {
    长沙市: [112.982279, 28.19409],
    株洲市: [113.151737, 27.835806],
    湘潭市: [112.944052, 27.82973],
    衡阳市: [112.607693, 26.900358],
    邵阳市: [111.46923, 27.237842],
    岳阳市: [113.132855, 29.37029],
    常德市: [111.691347, 29.040225],
    张家界市: [110.479921, 29.127401],
    益阳市: [112.355042, 28.570066],
    郴州市: [113.032067, 25.793589],
    永州市: [111.608019, 26.434516],
    怀化市: [109.97824, 27.550082],
    娄底市: [112.008497, 27.728136],
    湘西土家族苗族自治州: [109.739735, 28.314296],
  };

  const getValue = () => {};
  var customerBatteryCityData = [
    {
      name: '长沙市',
      value: [112.982279, 28.19409, 66],
      height: 66,
    },
    {
      name: '株洲市',
      height: 36,
      value: [113.151737, 27.835806, 36],
    },
    {
      name: '湘潭市',
      value: [112.944052, 27.82973, 100],
      height: 100,
    },
    {
      name: '衡阳市',
      value: [112.607693, 26.900358, 8],
      height: 8,
    },
    {
      name: '邵阳市',
      value: [111.46923, 27.237842, 17],
      height: 17,
    },
    {
      name: '岳阳市',
      value: [113.132855, 29.37029, 11],
      height: 11,
    },
    {
      name: '常德市',
      value: [111.691347, 29.040225, 29],
      height: 29,
    },
    {
      name: '张家界市',
      value: [110.479921, 29.127401, 29],
      height: 29,
    },
    {
      name: '益阳市',
      value: [112.355042, 28.570066, 46],
      height: 46,
    },
    {
      name: '郴州市',
      value: [113.032067, 25.793589, 40],
      height: 40,
    },
    {
      name: '永州市',
      value: [111.608019, 26.434516, 30],
      height: 30,
    },
    {
      name: '怀化市',
      value: [109.97824, 27.550082, 43],
      height: 43,
    },
    {
      name: '娄底市',
      value: [112.008497, 27.728136, 98],
      height: 98,
    },
    {
      name: '湘西土家族苗族自治州',
      value: [109.739735, 28.314296, 1],
      height: 1,
    },
  ];

  useEffect(() => {
    echarts.registerMap('湖南', hunan);
    initCharts();
  }, []);

  const initCharts = () => {
    if (myCharts.current) {
      myCharts.current.dispose();
    }
    myCharts.current = echarts.init(domRef.current);
    const option = {
      tooltip: {
        show: true,
        formatter: function (params) {
          return params.name;
        },
      },
      xAxis3D: {
        type: 'category',
        data: ['电渠'],
      },
      yAxis3D: {
        type: 'category',
        data: ['1'],
      },
      zAxis3D: {
        type: 'value',
      },
      grid3D: {
        boxWidth: 100,
        boxHeight: 100,
        boxDepth: 80,
        left: '40%',
        top: '0%',
        viewControl: {
          alpha: 40,
          beta: 40,
          distance: 300,
          rotateSensitivity: 1,
        },
        axisPointer: {
          show: false, // 隐藏指示器
        },
        axisLine: {
          lineStyle: {
            color: 'transparent',
          },
        },
        axisTick: {
          show: false, // 隐藏坐标轴的刻度
        },
        axisLabel: {
          show: false, // 隐藏坐标轴的标签
        },
        splitLine: {
          show: false, // 隐藏分割线
        },
      },
      geo3D: {
        map: '湖南',
        roam: true, // 支持缩放平移
        itemStyle: {
          areaColor: '#4CAF50',
          opacity: 1,
          borderWidth: 0.8,
          borderColor: '#000',
        },
        // right: '20%',

        // environment: 'asset/starfield.jpg'
        environment: 'rgb(9,50,122)',
        //地图环境色
        // environment: new echarts.graphic.LinearGradient(
        //   0,
        //   0,
        //   0,
        //   1,
        //   [
        //     {
        //       offset: 0,
        //       color: '#00aaff', // 天空颜色
        //     },
        //     {
        //       offset: 0.7,
        //       color: '#998866', // 地面颜色
        //     },
        //     {
        //       offset: 1,
        //       color: 'rgb(9,50,122)', // 地面颜色
        //     },
        //   ],
        //   false,
        // ),
        itemStyle: {
          color: 'rgb(72, 144, 246)', // 区域颜色
          borderColor: 'rgb(102, 207, 264)', // 边框颜色
          borderWidth: 2, // 边框宽度
          opacity: 1,
        },

        viewControl: {
          distance: 260, //地图视角 控制初始大小
          rotateSensitivity: [1, 1],
        },
        label: {
          show: true,
          textStyle: {
            color: '#000', // 文字颜色
            fontSize: 16, // 字体大小
            opacity: 1,
            backgroundColor: 'rgba(255, 255, 255, 0.7)', // 背景颜色
          },
        },
        // 鼠标移入颜色
        emphasis: {
          label: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: 18,
              backgroundColor: '#ff5722',
            },
          },
          itemStyle: {
            color: '#FFD700', // 悬停时区域颜色
            borderColor: '#0000FF', // 悬停时边框颜色
            borderWidth: 3, // 悬停时边框宽度
          },
        },
        // light: {
        //   // 光照效果
        //   main: {
        //     color: '#fff', // 光照颜色
        //     intensity: 1.2, // 强度
        //     shadow: true,
        //     alpha: 40,
        //     beta: 40,
        //   },
        //   ambient: {
        //     intensity: 0.3,
        //   },
        // },
      },

      series: [
        {
          name: 'bar3D',
          type: 'bar3D',
          coordinateSystem: 'geo3D',
          barSize: 2, //柱子粗细
          shading: 'lambert',
          opacity: 1,
          bevelSize: 0.3,
          label: {
            show: false,
            formatter: '{b}',
          },
          itemStyle: {
            color: '#ff0000', // 柱子的颜色
            opacity: 0.8, // 柱子的透明度
          },
          data: customerBatteryCityData,
        },
        {
          name: 'Outside Bar',
          type: 'bar3D',
          coordinateSystem: 'cartesian3D', // 使用 grid3D 的坐标系
          grid3DIndex: 0,
          data: [
            [0, 0, 150], // 这里的 [x, y, z] 表示柱子的坐标和高度
          ],
          barSize: 2,
          shading: 'lambert',
          label: {
            show: true,
            textStyle: {
              fontSize: 16,
              borderWidth: 1,
            },
          },
          itemStyle: {
            color: '#00ff00',
            opacity: 0.8,
          },
        },
      ],
    };

    myCharts.current.setOption(
      {
        ...option,
      },
      true,
    );

    window.addEventListener('resize', () => {
      myCharts.current.resize();
    });
    // DomSize.bind(domRef.current, () => {
    //   myCharts.current.resize();
    // });
  };

  return (
    <div>
      <div className="commonCharts" ref={domRef} style={{ width: '100%', height: 800 }}></div>
    </div>
  );
}
