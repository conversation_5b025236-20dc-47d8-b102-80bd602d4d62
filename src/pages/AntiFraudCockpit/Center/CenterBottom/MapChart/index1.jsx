import React, { useEffect, useRef, useMemo } from 'react';
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';

import bg from '../../../imgs/leftBg.png';

import hunan from '@/components/Chart/hunan.json';

export default function index({ data: mapData, loading, barData, allData, maxHeight }) {
  const domRef = useRef(null);
  const myCharts = useRef();
  const barWidth = 7;

  // 地图实际描绘的点
  var geoCoordMap = {
    长沙市: [112.982279, 28.19409],
    株洲市: [113.251737, 27.835806],
    湘潭市: [112.744052, 27.82973],
    衡阳市: [112.607693, 26.900358],
    邵阳市: [111.46923, 27.237842],
    岳阳市: [113.132855, 29.37029],
    常德市: [111.691347, 29.040225],
    张家界市: [110.479921, 29.127401],
    益阳市: [112.355042, 28.570066],
    郴州市: [113.032067, 25.793589],
    永州市: [111.608019, 26.434516],
    怀化市: [109.97824, 27.550082],
    娄底市: [112.008497, 27.728136],
    湘西土家族苗族自治州: [109.739735, 28.314296],
  };

  useEffect(() => {
    echarts.registerMap('湖南', hunan);
    initCharts();
  }, []);
  useEffect(() => {
    initCharts();
  }, [mapData]);

  const initCharts = () => {
    if (myCharts.current) {
      myCharts.current.dispose();
    }
    myCharts.current = echarts.init(domRef.current);
    var options = {
      top: '-40%',
      bottom: '-40%',
      tooltip: {
        trigger: 'axis',
      },

      geo: {
        type: 'map',
        map: '湖南',
        aspectScale: 0.9, //地图的长宽比
        roam: false, // 是否允许缩放
        zoom: 0.9, // 当前视角的缩放比例。
        layoutSize: '95%', //地图的大小
        layoutCenter: ['46%', '58%'], //定义地图中心在屏幕中的位置
        label: {
          normal: {
            textStyle: {
              color: '#fff',
            },
            show: false,
          },
          emphasis: {
            textStyle: {
              color: '#C6A300',
            },
            show: false,
          },
        },
        itemStyle: {
          normal: {
            //设置整个地图区域的颜色
            areaColor: {
              type: 'linear-gradient', //线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比
              x: 0,
              y: 700,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#152D52', // 0% 处的颜色 下
                },
                {
                  offset: 1,
                  color: '#385692', // 100% 处的颜色 上
                },
              ],
              global: true, // 缺省为 false
            },
            borderColor: 'rgba(255,255,255,0.5)',
            borderWidth: 1,
          },
        },
        emphasis: {
          // 鼠标移入高亮
          itemStyle: {
            areaColor: {
              type: 'linear-gradient',
              x: 0,
              y: 300,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(37,108,190,1)', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(15,169,195,1)', // 50% 处的颜色
                },
              ],
              global: true, // 缺省为 false
            },
          },
          label: {
            show: false,
            color: '#fff',
          },
        },
      },
    };

    myCharts.current.setOption(
      {
        ...options,
      },
      true,
    );

    window.addEventListener('resize', () => {
      myCharts.current.resize();
    });
    // DomSize.bind(domRef.current, () => {
    //   myCharts.current.resize();
    // });

    setTimeout(() => {
      renderEachCity();
    }, 0);
  };

  const renderEachCity = () => {
    let options = {
      xAxis: [],
      yAxis: [],
      grid: [],

      series: [
        {
          map: '湖南', // 使用

          geoIndex: 0, // geoIndex 指定一个 geo 组件,map 和 其他 series（例如散点图）就可以共享一个 geo 组件
          // coordinateSystem: 'geo',
          showLegendSymbol: true, //在图例相应区域显示图例的颜色标识（系列标识的小圆点）
          type: 'map',

          roam: true, //是否开启鼠标缩放和平移漫游
          label: {},

          data: mapData,
          // data: this.difficultData //热力图数据   不同区域 不同的底色
        },
      ],
    };

    echarts.util.each(mapData, function (dataItem, idx) {
      var geoCoord = geoCoordMap[dataItem.name];
      var coord = myCharts.current?.convertToPixel('geo', geoCoord);
      idx += '';

      options.xAxis.push({
        id: idx,
        gridId: idx,
        type: 'category',
        name: dataItem.name,
        nameTextStyle: {
          color: '#fff',
          fontSize: 14,
        },
        nameLocation: 'middle',
        nameGap: 3,
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#bbb',
          },
        },

        data: [dataItem.name],
      });

      options.yAxis.push({
        id: idx,
        gridId: idx,
        show: false,
        max: maxHeight,
      });

      options.grid.push({
        id: idx,
        width: 50,
        height: 40,
        left: coord[0] - 15,
        top: coord[1] - 35,
      });

      options.series.push({
        name: '',
        type: 'bar',
        xAxisId: idx,
        yAxisId: idx,
        barWidth: 7,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#FA084F',
                },
                {
                  offset: 1,
                  color: '#F88E1E',
                },
              ],
              false,
            ),
          },
        },
        label: {
          show: true,
          position: 'top',
          backgroundColor: '#05242F',
          padding: [4, 10],
          borderRadius: 4,
          borderWidth: 1,
          borderColor: 'rgba(30,75,255,0.34)',
          shadowColor: 'rgba(66,164,255,0.9)',
          shadowBlur: 5,
          color: '#777',
          formatter: function (params) {
            return `{a|${params.value}}{b|个}`;
          },
          rich: {
            a: {
              fontSize: 16,
              color: '#fff',
            },
            b: {
              fontSize: 12,
              align: 'center',
              width: 20,
              color: '#909FAB',
            },
          },
        },
        data: [dataItem.value],
      });
    });
    // const dianquId = mapData.length + '';
    // options.xAxis.push({
    //   id: dianquId,
    //   gridId: dianquId,
    //   type: 'category',
    //   name: '电渠',
    //   nameTextStyle: {
    //     color: '#fff',
    //     fontSize: 14,
    //   },
    //   nameLocation: 'middle',
    //   nameGap: 3,
    //   splitLine: {
    //     show: false,
    //   },
    //   axisTick: {
    //     show: false,
    //   },
    //   axisLabel: {
    //     show: false,
    //   },
    //   axisLine: {
    //     show: false,
    //     lineStyle: {
    //       color: '#bbb',
    //     },
    //   },

    //   data: ['电渠'],
    // });
    // options.yAxis.push({
    //   id: dianquId,
    //   gridId: dianquId,
    //   show: false,
    //   max: Math.max(...allData?.map((ele) => ele.value)),
    // });

    // options.grid.push({
    //   id: dianquId,
    //   width: 50,
    //   height: 40,
    //   left: 'center',
    //   bottom: 'center',
    // });

    // options.series.push({
    //   name: '',
    //   type: 'bar',
    //   xAxisId: dianquId,
    //   yAxisId: dianquId,
    //   barWidth: 7,
    //   zlevel: 6,
    //   itemStyle: {
    //     normal: {
    //       color: new echarts.graphic.LinearGradient(
    //         0,
    //         0,
    //         0,
    //         1,
    //         [
    //           {
    //             offset: 0,
    //             color: '#FA084F',
    //           },
    //           {
    //             offset: 1,
    //             color: '#F88E1E',
    //           },
    //         ],
    //         false,
    //       ),
    //     },
    //   },
    //   label: {
    //     show: true,
    //     position: 'top',
    //     backgroundColor: '#05242F',
    //     padding: [4, 10],
    //     borderRadius: 4,
    //     borderWidth: 1,
    //     borderColor: '#B9F5FF',
    //     color: '#777',
    //     formatter: function (params) {
    //       return `{a|${params.value}}{b|个}`;
    //     },
    //     rich: {
    //       a: {
    //         fontSize: 16,
    //         color: '#fff',
    //       },
    //       b: {
    //         fontSize: 12,
    //         align: 'center',
    //         width: 20,
    //         color: '#909FAB',
    //       },
    //     },
    //   },
    //   data: barData,
    // });

    myCharts.current.setOption(options);
  };

  const barMaxHeight = () => {
    return Math.max(...allData.map((item) => item.value));
  };

  return (
    <div>
      <div className="commonCharts" ref={domRef} style={{ width: '100%', height: 1000 }}></div>
    </div>
  );
}
