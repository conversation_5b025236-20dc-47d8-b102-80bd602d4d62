import React, { useEffect, useMemo, useState } from 'react';
import { message, Spin } from 'antd';
import Map<PERSON>hart from './MapChart';
import BarChart from './BarChart';
import request from 'ponshine-request';
import styles from './index.less';
import mapTitle from '../../imgs/mapTitle.png';

export default function index({ timeValues }) {
  const [loading, setLoading] = useState(false);

  var customerBatteryCityData = [
    {
      name: '长沙市',
      value: 2000,
    },
    {
      name: '株洲市',
      value: 1000086,
    },
    {
      name: '湘潭市',
      value: 12000,
    },
    {
      name: '衡阳市',
      value: 8,
    },
    {
      name: '邵阳市',
      value: 17,
    },
    {
      name: '岳阳市',
      value: 11,
    },
    {
      name: '常德市',
      value: 29,
    },
    {
      name: '张家界市',
      value: 2000,
    },
    {
      name: '益阳市',
      value: 46,
    },
    {
      name: '郴州市',
      value: 40,
    },
    {
      name: '永州市',
      value: 30,
    },
    {
      name: '怀化市',
      value: 43,
    },
    {
      name: '娄底市',
      value: 98,
    },
    {
      name: '湘西土家族苗族自治州',
      value: 1,
    },
    {
      name: '电渠',
      value: 10000600,
    },
  ];
  const [data, setData] = useState([]);
  const getData = async () => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/involvedPhoneDistribution`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues },
    });
    setLoading(false);

    if (response.code === 200) {
      setData(
        response?.data?.map((ele) => {
          return {
            name: ele.responsibleUnit,
            value: ele.caseCount,
          };
        }) || [],
      );
    } else {
      message.error(response.message);
    }
  };

  const maxHeight = useMemo(() => {
    return Math.max(...data.map((ele) => ele.value));
  }, [data]);
  useEffect(() => {
    if (timeValues) {
      getData();
    }
  }, [timeValues]);
  return (
    <Spin spinning={loading}>
      <div className={styles.centerBottom}>
        <img src={mapTitle} />
        <MapChart
          data={data.filter((ele) => ele.name !== '电渠')}
          loading={loading}
          barData={data.filter((ele) => ele.name === '电渠')}
          allData={data}
          maxHeight={maxHeight}
        />
        {/* <div className={styles.dianquBar}>
          <BarChart data={data.filter((ele) => ele.name === '电渠')} height={maxHeight} />
        </div> */}
      </div>
    </Spin>
  );
}
