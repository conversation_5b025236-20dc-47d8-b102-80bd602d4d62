import React, { useState, useEffect } from 'react';
import { Spin, message, Row, Col } from 'antd';
import request from 'ponshine-request';
import ListItem from './ListItem';
import styles from './index.less';

export default function index({ timeValues }) {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);

  const list = [
    {
      title: '当日涉案量排名',
      key: 'dayPercentMap',
    },
    {
      title: '当日涉案率排名',
      key: 'dayCountMap',
    },
    {
      title: '月涉案量排名',
      key: 'monthCountMap',
    },
    {
      title: '月涉案率排名',
      key: 'monthPercentMap',
    },
  ];
  const getData = async () => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/involvedRank`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues },
    });
    setLoading(false);

    if (response.code === 200) {
      setData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    if (timeValues) {
      getData();
    }
  }, [timeValues]);
  return (
    <div style={{ marginBottom: 16 }} className={styles.centerTop}>
      <Spin spinning={loading}>
        <Row>
          {list?.map((ele) => (
            <div className={styles.listItemBox}>
              <ListItem
                key={ele.key}
                {...ele}
                total={data?.[ele.key]?.rank}
                changeValue={data?.[ele.key]?.change}
              />
            </div>
          ))}
        </Row>
      </Spin>
    </div>
  );
}
