
.centerTop {
    position: absolute;   
    width: 46%;
    top: 138px; 
    left: 0;
    right: 0;
    margin: auto;

    .listItemBox {
        padding-left: 10px;
        width: 25%;
        display: inline-block;
        border-right: 1px solid transparent; /* 设置边框大小，但不显示（使用透明色） */  
        border-image: url('../../imgs/border.png') 1; /* 图片路径，裁剪大小，填充方式 */  
        &:last-child {
            border: none;
            background: url(../../imgs/borderRight.png) no-repeat;
            background-size: 15px;
            background-position: right;
        }
        &:first-child {
            background: url(../../imgs/borderLeft.png) no-repeat;
            background-size: 15px;
            background-position: left;
        }
      
    }
    
}