import React from 'react';
import styles from './index.less';
import down from '../../../imgs/down.png';
import up from '../../../imgs/up.png';

export default function index({ title, total, changeValue }) {
  return (
    <div className={styles.listItem}>
      <div className={styles.listItem_title}>{title}</div>
      <div className={styles.listItem_content}>
        <div className={styles.listItem_content_total}>
          {total === 0 || total ? (
            String(total)
              ?.split('')
              ?.map((ele) => <div className={styles.listItem_content_total_item}>{ele}</div>)
          ) : (
            <div style={{ color: '#fff' }}>--</div>
          )}
        </div>
        <div className={styles.listItem_content_detail}>
          <span className={styles.listItem_content_detail_des}>同比: </span>
          <span className={styles.listItem_content_detail_total}>
            {changeValue || changeValue === 0 ? String(changeValue)?.replace('-', '') : '--'}
          </span>
          <img src={changeValue > 0 ? up : down} />
        </div>
      </div>
    </div>
  );
}
