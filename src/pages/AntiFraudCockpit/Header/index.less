.header {
    display: flex;
    justify-content: center;
    align-items: center;
    background: url(../imgs/headerBg.png) no-repeat;
    background-size: 100%;
    width: 1511px;
    height: 68px;
    position:absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
   
    .title img{
        height: 27px;
        text-align: center;
    }
    .header_date {
        position: absolute;
        top: 61px;
        left: 0;
        right: 0;
        z-index: 999;
        margin: auto;
        text-align: center;
        background: url(../imgs/headerDateBg.png) no-repeat;
        background-position: center;
        background-size: 265px;
        :global {
            .ant-form-item-label > label {
                color: #fff;
            }
            .ant-input {
                border: none;
                background-color: transparent;
                color: #fff;
                font-size: 12px;
                width: 100px;
            }
            .ant-calendar-picker-icon {
                color: #fff;
                background: url(../imgs/rili.png) no-repeat;
                background-size: 12px 12px;
                svg {
                    display: none;
                }
            }
        }
    }
    

}