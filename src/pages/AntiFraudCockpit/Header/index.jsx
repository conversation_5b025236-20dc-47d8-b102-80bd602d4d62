import React from 'react';
import styles from './index.less';
import { Button, DatePicker, Form } from 'antd';
import moment from 'moment';
import title from '../imgs/title.png';

const Index = ({ form: { getFieldDecorator, getFieldsValue }, onSearch, initTime }) => {
  const handleChange = (date) => {
    onSearch(date?.format('YYYY-MM-DD'));
  };
  const disabledDate = (current) => {
    return current > moment();
  };

  return (
    <div className={styles.header}>
      {/* <div>111</div> */}
      <div className={styles.header_date}>
        <Form layout="inline">
          <Form.Item label="查询日期" style={{ marginBottom: 0, marginRight: 0 }}>
            {getFieldDecorator('date', {
              initialValue: initTime,
            })(
              <DatePicker
                format={'YYYY-MM-DD'}
                disabledDate={disabledDate}
                getCalendarContainer={() => document.getElementById('antiFraudCockpit')}
                allowClear={false}
                onChange={handleChange}
                size="small"
              />,
            )}
          </Form.Item>
        </Form>
      </div>
      <div className={styles.title}>
        <img src={title} />
      </div>
    </div>
  );
};
export default Form.create()(Index);
