import React, { useState, useRef } from 'react';
import styles from './index.less';
import Header from './Header';
import Left from './Left';
import Center from './Center';
import Right from './Right';

import moment from 'moment';

export default function index() {
  const fullscreenRef = useRef(null);
  const initTime = moment().subtract(1, 'days');
  const [timeValues, setTimeValues] = useState(initTime.format('YYYY-MM-DD'));
  const [isFullScreen, setIsFullScreen] = useState(false);
  const handleSearch = (timeParams) => {
    setTimeValues(timeParams);
  };

  const enterFullscreen = () => {
    if (fullscreenRef.current.requestFullscreen) {
      fullscreenRef.current.requestFullscreen();
    } else if (fullscreenRef.current.mozRequestFullScreen) {
      // Firefox
      fullscreenRef.current.mozRequestFullScreen();
    } else if (fullscreenRef.current.webkitRequestFullscreen) {
      // Chrome, Safari, Opera
      fullscreenRef.current.webkitRequestFullscreen();
    } else if (fullscreenRef.current.msRequestFullscreen) {
      // IE/Edge
      fullscreenRef.current.msRequestFullscreen();
    }
    setIsFullScreen(!isFullScreen);
  };

  // 退出全屏
  const exitFullscreen = () => {
    if (document.exitFullscreen) {
      document?.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      // Firefox
      document?.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      // Chrome, Safari, Opera
      document?.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      // IE/Edge
      document?.msExitFullscreen();
    }
    setIsFullScreen(!isFullScreen);
  };

  const handleFullScreen = () => {
    if (isFullScreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  };
  return (
    <div className={styles.antiFraudCockpit} ref={fullscreenRef} id="antiFraudCockpit">
      <Header onSearch={handleSearch} initTime={initTime} />
      <div className={styles.content}>
        <div className={styles.left}>
          <Left timeValues={timeValues} />
        </div>
        <div className={styles.center}>
          <Center timeValues={timeValues} />
        </div>
        <div className={styles.right}>
          <Right timeValues={timeValues} onFullScreen={handleFullScreen} />
        </div>
      </div>
      <div className={styles.bottom}></div>
    </div>
  );
}
