import React, { useEffect, useState } from 'react';
import styles from './index.less';
import moment from 'moment';
export default function index() {
  const date = new Date();
  const [time, setTime] = useState('');

  useEffect(() => {
    let timeStr = moment(date).format('HH:mm:ss');
    let timer = setInterval(() => {
      timeStr = moment(new Date()).format('HH:mm:ss');
      setTime(timeStr);
    }, 1000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  return <div className={styles.time}>{time}</div>;
}
