import React, { useState, useEffect } from 'react';
import { message, Select } from 'antd';
import request from 'ponshine-request';
import CommonCard from '../../components/CommonCard';
import LineChart from '../../components/Chart/LineChart';
import styles from './index.less';
import { connect } from 'dryad';
import { withContext } from 'demasia-pro-layout';
import { goPage } from '@/utils/openTab';

const Index = ({ dispatch, timeValues, closeSingleTab }) => {
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sourceType, setSourceType] = useState([]);
  const [sourceTypeValue, setSourceTypeValue] = useState('外呼研判诈骗');

  const getData = async ({ type }) => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/otherChannelPhoneTrend`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues, classification: type },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  const getSourceType = async () => {
    const response = await request(`/api/hn/mobileReplay/getAllClassification`, {
      method: 'GET',
    });
    if (response.code === 200) {
      setSourceType(response?.data?.filter((ele) => ele !== '公安侦办平台') || []);
    } else {
      message.error(response.message);
    }
  };

  const handleChangeType = (v) => {
    setSourceTypeValue(v);
    getData({
      type: v,
    });
  };

  useEffect(() => {
    if (timeValues) {
      getData({ type: sourceTypeValue });
      getSourceType();
    }
  }, [timeValues]);

  const onEvents = {
    click: (params) => {
      const { name, value } = params;
      if (closeSingleTab) {
        closeSingleTab(
          '{"path":"/diskInformateManage/mobileFraudVerificationResume","isExact":true,"params":{}}',
        );
      }
      dispatch({
        type: 'mobileFraudVerificationResume/saveGoSearchParamas',
        payload: {
          classification: sourceTypeValue,
          notificationDate: name,
        },
      });
      goPage('/diskInformateManage/mobileFraudVerificationResume', {
        type: 'pathJump',
      });
    },
  };
  return (
    <CommonCard
      title="各渠道来源号码趋势分析"
      extra={
        <div className={styles.rightTop_extra} style={{ width: '30%' }}>
          <Select
            placeholder="请选择"
            allowClear={false}
            style={{ width: '100%' }}
            size="small"
            value={sourceTypeValue}
            onChange={handleChangeType}
            getPopupContainer={() => document.getElementById('antiFraudCockpit')}
          >
            {sourceType?.map((ele) => (
              <Select.Option value={ele} title={ele}>
                {ele}
              </Select.Option>
            ))}
          </Select>
        </div>
      }
      style={{ width: '75%', paddingRight: '24px', marginLeft: '28%' }}
      headerStyle={{ margin: '0 16px' }}
    >
      <div>
        <LineChart
          xData={listData?.map((ele) => ele?.countDate)}
          yData={[{ data: listData?.map((ele) => ele.caseCount) }]}
          legendData={[]}
          height={200}
          width="100%"
          loading={loading}
          isMutipleYAxisIndex={false}
          axisLabelOption={{ rotate: 0 }}
          onEvents={onEvents}

          // axisLabelOption={{ width: 80 }}
          // gridOption={{ left: 60 }}
        />
      </div>
    </CommonCard>
  );
};

const config = [['closeSingleTab', 'tabKey']];

const YourComponent = withContext(...config)((props) => {
  return <Index {...props} />;
});

export default connect(({ mobileFraudVerificationResume }) => ({
  mobileFraudVerificationResume,
}))(YourComponent);
