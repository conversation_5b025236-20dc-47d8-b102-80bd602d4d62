import React from 'react';
import RightTop from './RightTop';
import RightCenter from './RightCenter';
import RightBottom from './RightBottom';
import Time from './Time';
import fullScreen from '../imgs/fullScreen.png';

import styles from './index.less';

export default function index({ timeValues, onFullScreen }) {
  return (
    <div className={styles.right}>
      <div className={styles.right_tool}>
        <Time />
        <img src={fullScreen} onClick={onFullScreen} />
      </div>
      <RightTop timeValues={timeValues} />
      <RightCenter timeValues={timeValues} />
      <RightBottom timeValues={timeValues} />
    </div>
  );
}
