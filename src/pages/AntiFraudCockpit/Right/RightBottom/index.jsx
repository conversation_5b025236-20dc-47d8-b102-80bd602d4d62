import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '../../components/CommonCard';
import LineChart from '../../components/Chart/LineChart';

export default function index({ timeValues }) {
  const data = {
    投诉量: [
      {
        countDate: '4-00',
        complaintCount: 8,
      },
      {
        countDate: '4-01',
        complaintCount: 58,
      },
      {
        countDate: '4-02',
        complaintCount: 87,
      },
    ],
    均值: [
      {
        countDate: '4-00',
        complaintCount: 90,
      },
      {
        countDate: '4-01',
        complaintCount: 14,
      },
      {
        countDate: '4-02',
        complaintCount: 98,
      },
      {
        countDate: '4-03',
        complaintCount: 65,
      },
    ],
  };
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async () => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/skipComplaintsCount`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    if (timeValues) {
      getData();
    }
  }, [timeValues]);
  return (
    <CommonCard title="越级投诉统计分析" style={{ paddingRight: 24, marginLeft: '30%' }}>
      <LineChart
        xData={listData?.[Object.keys(listData)[0]]?.map((ele) => ele?.countDate)}
        yData={Object.keys(listData)?.map((ele) => {
          return {
            name: ele,
            data: listData?.[ele]?.map((ele) => ele?.complaintCount) || [],
          };
        })}
        legendData={Object.keys(listData)}
        height={250}
        loading={loading}
        seriesConfig={{ barWidth: 20 }}
        axisLabelOption={{ rotate: 0 }}
      />
    </CommonCard>
  );
}
