import React, { useState, useEffect, useMemo } from 'react';
import { message, Radio, Row, Col } from 'antd';
import request from 'ponshine-request';
import CommonCard from '../../components/CommonCard';
import PieChart from '../../components/Chart/PieChart';
import CustomRadio from '../../components/CustomRadio';
import PieTitle from './PieTitle';
import moment from 'moment';
import restartPie from '../../imgs/restartPie.png';
import shutdownPie from '../../imgs/shutdownPie.png';
import leftExtra from '../../imgs/leftExtra.png';
import styles from './index.less';

const getFormatDate = (timeStr) => {
  return moment(timeStr)?.format('MM月DD日');
};

export default function index({ timeValues }) {
  const data = {
    关停号码: [
      {
        name: '集团大数据',
        value: 99999999,
        percent: '99.99%',
      },
      {
        name: '省内大数据',
        value: 10000000,
        percent: '50%',
      },
    ],
    复机号码: [
      {
        name: '集团大数据',
        value: 334343,
        percent: '50.67%',
      },
      {
        name: '省内大数据',
        value: null,
        percent: '5%',
      },
    ],
  };
  const [listData, setListData] = useState({});

  const [loading, setLoading] = useState(false);
  const [radioValue, setRadioValue] = useState(getFormatDate(timeValues));
  const [radioData, setRadioData] = useState([]);

  const getData = async ({ type }) => {
    setLoading(true);
    const response = await request(`/api/hn/cockpit/modelShutdownResumeCount`, {
      method: 'POST',
      requestType: 'json',
      data: { countDate: timeValues, timeDimension: type?.includes('日') ? 'day' : 'month' },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  const changeRadio = (value) => {
    setRadioValue(value);
    getData({
      type: value,
      timeValues,
    });
  };

  useEffect(() => {
    if (timeValues) {
      const time = getFormatDate(timeValues);
      getData({
        type: time,
      });
      setRadioData([time, moment(timeValues)?.format('MM月')]);
      setRadioValue(time);
    }
  }, [timeValues]);

  const radioDataArr = useMemo(() => {
    return radioData.map((ele) => {
      return {
        label: ele,
        value: ele,
      };
    });
  }, [radioData]);

  return (
    <div className={styles.rightCenter}>
      <CommonCard
        title="模型关停复开统计"
        extra={<CustomRadio data={radioDataArr} value={radioValue} onChange={changeRadio} />}
        style={{ width: '84%' }}
      >
        <Row>
          <Col span={12}>
            <PieTitle title={`${radioValue}关停号码数`} />
            <PieChart
              data={listData['关停号码'] || []}
              height={230}
              loading={loading}
              labelLength={10}
              centerImg={shutdownPie}
              title="关停号码"
            />
          </Col>
          <Col span={12}>
            <PieTitle title={`${radioValue}复机号码数`} />
            <PieChart
              data={listData['复机号码'] || []}
              height={230}
              loading={loading}
              labelLength={10}
              centerImg={restartPie}
              title="复机号码"
            />
          </Col>
        </Row>
      </CommonCard>
      <img src={leftExtra} />
    </div>
  );
}
