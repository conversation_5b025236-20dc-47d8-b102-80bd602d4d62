.radio {
    display: flex;
    height: 18px;
    line-height: 18px;
    cursor: pointer;
    
    .radio_item {
        color: #909FAB;
        position: relative;
        padding: 0 6px;
        &:first-child {
            margin-right: 6px;
        }
        
        &.active {
            color: #fff;
            &::before {
                content: ' ';
                display: block;
                width: 4px;
                height: 18px;
                background: url(../../imgs/radioRightBorder.png) no-repeat;
                background-size: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
            &::after {
                content: ' ';
                display: block;
                width: 4px;
                height: 18px;
                background: url(../../imgs/radioLeftBorder.png) no-repeat;
                background-size: 100%;
                position: absolute;
                top: 0;
                right: 0;
            }
        }
    }   
}