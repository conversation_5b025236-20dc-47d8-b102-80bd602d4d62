import React from 'react';
import styles from './index.less';

export default function index({ data, value, onChange }) {
  const handleChange = (v) => {
    onChange(v);
  };
  return (
    <div className={styles.radio}>
      {data?.map((ele) => (
        <div
          className={`${styles.radio_item} ${value === ele.value && styles.active} `}
          onClick={() => handleChange(ele.value)}
        >
          {ele.label}
        </div>
      ))}
    </div>
  );
}
