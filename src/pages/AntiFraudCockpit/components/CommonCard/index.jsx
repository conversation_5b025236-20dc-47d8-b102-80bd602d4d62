import React from 'react';
import styles from './index.less';

export default function index({ title, extra, children, style = {}, headerStyle = {} }) {
  return (
    <div className={styles.commonCard} style={style}>
      <div className={styles.commonCard_header} style={headerStyle}>
        <div className={styles.commonCard_header_title}>{title}</div>
        {extra && extra}
      </div>
      {children}
    </div>
  );
}
