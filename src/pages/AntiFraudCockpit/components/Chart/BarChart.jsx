import React from 'react';
import CommonChart from './index';
import * as echarts from 'echarts';

const defaultColor = [
  '#ee6666',
  '#1492ff',
  '#fac858',
  '#91cc75',
  '#5470c6',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
  '#0ed2ff',
];

export default function BarChart({
  color,
  xData,
  yData,
  legendData,
  height = 300,
  width = '100%',
  loading,
  seriesConfig = {},
  axisLabelOption = {},
  gridOption = {},
}) {
  const options = {
    color: ['#3AE3FF', '#3A80FF'],
    grid: {
      ...gridOption,
      right: 16,
      top: 24,
      bottom: '18%',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendData,
      right: 16,
      itemHeight: 1,
      itemWidth: 12,
      textStyle: {
        color: '#fff',
      },
    },
    calculable: true,
    xAxis: [
      {
        type: 'category',
        // prettier-ignore
        data: xData,
        axisLabel: {
          width: 120,
          fontSize: 12,
          color: '#909FAB',
          overflow: 'truncate',
          formatter: (params) => {
            return params.slice(5);
          },
          ...axisLabelOption,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgba(255,255,255,0.3)',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        minInterval: 1,
        axisLabel: {
          color: '#909FAB',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgba(255,255,255,0.3)',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255,255,255,0.3)',
          },
        },
      },
    ],
    series: yData.map((ele, index) => {
      return {
        ...ele,
        type: 'bar',
        label: {
          show: false,
        },
        labelLayout: {
          hideOverlap: true,
        },
        ...seriesConfig,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: index === 0 ? '#FA084F' : '#3A80FF',
                },
                {
                  offset: 1,
                  color: index === 0 ? '#F88E1E' : '#3AE3FF',
                },
              ],
              false,
            ),
          },
        },
      };
    }),
  };
  return <CommonChart option={options} height={height} loading={loading} width={width} />;
}
