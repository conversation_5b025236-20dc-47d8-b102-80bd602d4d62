import React from 'react';
import CommonChart from './index';
import * as echarts from 'echarts';
import barRankBg from '../../imgs/barRankBg.png';

export default function BarChart({
  color,
  xData,
  yData,
  legendData,
  height = 300,
  width = '100%',
  loading,
  seriesConfig = {},
  axisLabelOption = {},
  gridOption = {},
}) {
  const options = {
    color: ['#3AE3FF'],
    grid: {
      ...gridOption,
      top: 20,
      left: '25%',
      right: '8%',
      bottom: 20,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendData,
      left: 'left',
    },
    calculable: true,
    xAxis: [
      {
        show: false,
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#909FAB',
        },
      },
    ],
    yAxis: [
      {
        type: 'category',
        data: yData,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        inverse: true,
        axisLabel: {
          color: '#909FAB',
          formatter: function (name) {
            const index = yData?.findIndex((ele) => ele == name);
            return `{a|${index + 1}}{b|${name.replace('本地网', '')}}`;
          },
          rich: {
            a: {
              color: '#909FAB',
              width: 18,
              height: 18,
              backgroundColor: {
                image: barRankBg,
                width: 18,
                height: 18,
              },
              lineHeight: 20,

              align: 'center',
            },
            b: {
              color: '#fff',
              width: 46,
              align: 'left',
              // backgroundColor: '#4197FD',
              // width: 20,
              // height: 20,
              // align: 'center',
              // borderRadius: 2,
            },
          },
        },
      },
    ],
    series: [
      {
        name: '',
        type: 'bar',
        barWidth: 8,
        tooltip: {
          formatter: '{b}：{c}',
        },
        itemStyle: {
          normal: {
            // barBorderRadius: [0, 5, 5, 0],
            color: {
              colorStops: [
                {
                  offset: 0,
                  color: '#3A83FF', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#3AE3FF', // 100% 处的颜色
                },
              ],
            },
          },
        },
        data: xData,
        z: 10,
        zlevel: 0,
      },
      {
        // 分隔
        type: 'pictorialBar',
        itemStyle: {
          normal: {
            color: '#0F375F',
          },
        },
        symbolRepeat: 'fixed',
        symbolMargin: 1,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [2, 8],
        symbolPosition: 'start',
        symbolOffset: [0, 0],
        data: xData,
        // width: 25,
        z: 0,
        zlevel: 1,
        tooltip: {
          show: false,
        },
      },
      {
        name: '背景',
        type: 'bar',
        barWidth: 8,
        barGap: '-100%',
        data: xData.map((ele) => {
          return {
            total: ele,
            value: xData?.every((item) => !item) ? 100 : Math.max(...xData),
          };
        }),
        tooltip: {
          show: false,
        },
        itemStyle: {
          normal: {
            color: '#15294F',
            label: {
              // 标签显示位置
              color: '#FFFFFF',
              fontSize: 12,
              show: true,
              position: 'right',
              formatter: (params) => {
                return params?.data?.total;
              },
            },
          },
        },
      },
    ],
  };
  return <CommonChart option={options} height={height} width={width} loading={loading} />;
}
