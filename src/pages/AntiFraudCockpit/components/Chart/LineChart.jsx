import React from 'react';
import CommonChart from './index';

export default function LineChart({
  xData,
  yData,
  legendData,
  height = 300,
  width = '100%',
  loading,
  isMutipleYAxisIndex = false,
  axisLabelOption = {},
  gridOption = {},
  onEvents,
  seriesConfig = {},
  color = ['#3A80FF', '#FA084F'],
}) {
  const options = {
    color: color,
    grid: {
      ...gridOption,
      right: 16,
      top: 20,
      bottom: 20,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendData,
      right: 16,
      icon: 'rect',
      itemHeight: 1,
      itemWidth: 12,
      borderWidth: 0,
      textStyle: {
        color: '#fff',
      },

      // lineStyle: {
      //   color: color,
      // },
    },
    calculable: true,

    xAxis: {
      type: 'category',
      // prettier-ignore
      data: xData,
      axisLabel: {
        width: 120,
        fontSize: 12,
        color: '#909FAB',
        overflow: 'truncate',
        ...axisLabelOption,
        formatter: (params) => {
          return params.slice(5);
        },
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: 'rgba(255,255,255,0.3)',
        },
      },
      splitLine: {
        show: true,
        // interval: 1,
        lineStyle: {
          color: 'rgba(255,255,255,0.3)',
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        minInterval: 1,
        axisLabel: {
          fontSize: 12,
          color: '#909FAB',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgba(255,255,255,0.3)',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255,255,255,0.3)',
          },
        },
      },
    ],
    series: yData.map((ele, index) => {
      return {
        ...ele,
        type: 'line',
        // yAxisIndex: isMutipleYAxisIndex ? index : 0,

        areaStyle: {
          color: {
            type: 'linear', // 线性渐变
            x: 0, // 渐变起点横坐标，0为左侧
            y: 0, // 渐变起点纵坐标，0为上方
            x2: 0, // 渐变终点横坐标，1为右侧
            y2: 1, // 渐变终点纵坐标，1为下方
            colorStops: [
              {
                offset: 0,
                color: index === 0 ? 'rgba(58,128,255,0.2)' : 'rgba(255,58,58,0.2)', // 颜色从上到下，0% 处的颜色
              },
              {
                offset: 1,
                color: index === 0 ? 'rgba(56,122,255,0)' : 'rgba(255,166,56,0)', // 100% 处的颜色
              },
            ],

            global: false, // false 表示渐变色只在该 series 下应用
          },
        },
        // // lineStyle: {
        // //   normal: {
        // //     color: color[index],
        // //   },
        // // },
        // // 修改节点的形状
        // symbol: 'circle', // 节点形状，例如 'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none'
        // symbolSize: 6, // 节点大小
        // // // 显示所有数据点的标记
        // showAllSymbol: true,
        itemStyle: {
          normal: {
            borderWidth: 0,
            borderColor: '#fff',
            shadowColor: '#fff',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowBlur: 3,
          },
        },
      };
    }),
  };
  return (
    <CommonChart
      option={options}
      height={height}
      loading={loading}
      onEvents={onEvents}
      width={width}
    />
  );
}
