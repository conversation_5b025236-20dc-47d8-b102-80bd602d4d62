import React from 'react';
import CommonChart from './index';
import pieRing from '../../imgs/pieRing.png';
import insertPieRing from '../../imgs/insertPieRing.png';

function array2obj(array, key) {
  var resObj = {};
  for (var i = 0; i < array.length; i++) {
    resObj[array[i][key]] = array[i];
  }
  return resObj;
}

export default function PieChart({
  data = [],
  height,
  loading,
  legendOption = {},
  seriesOptions = {},
  labelLength = 20,
  centerImg,
  title,
}) {
  var objData = array2obj(data, 'name');

  const options = {
    color: ['#FA084F', '#3A80FF'],
    title: {
      text: title,
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 400,
      },
      left: 'center',
      top: '45%',
    },
    graphic: {
      elements: [
        {
          type: 'image',
          z: 1,
          style: {
            image: pieRing,
            width: 144,
            height: 144,
          },
          left: 'center',
          top: '10%',
        },
        {
          type: 'image',
          z: 2,
          style: {
            image: insertPieRing,
            width: 86,
            height: 86,
          },
          left: 'center',
          top: '22.5%',
        },
        {
          type: 'image',
          z: 3,
          style: {
            image: centerImg,
            width: 17,
            height: 25,
          },
          left: 'center',
          top: '32%',
        },
      ],
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}（{d}%）',
    },
    legend: {
      // width: 50,
      itemWidth: 3,
      itemHeight: 3,
      borderWidth: 0,
      borderColor: 'transparent',
      // itemStyle: {
      //   color: ['#FA084F', '#3A80FF'],
      // },
      tooltip: {
        show: true,
        formatter: (params) => {
          const { name } = params;
          const { value, percent } = objData[name];
          return `${name}: ${percent ?? '--'} ${value ?? '--'}`;
        },
      },
      top: 'bottom',
      left: 0,
      orient: 'vertical',
      icon: 'rect',
      formatter: function (name) {
        const { value, percent } = objData[name];
        return `{a|${name.length > 7 ? name.slice(0, 7) + '...' : name}}{b|${percent ?? '--'}}{c|${
          value || value === 0
            ? String(value).length > 6
              ? String(value).slice(0, 6) + '...'
              : value
            : '--'
        }}`;
      },

      textStyle: {
        rich: {
          a: {
            fontSize: 10,
            width: 60,
            color: '#909FAB',
          },
          b: {
            align: 'left',
            fontSize: 10,
            width: 30,
            color: '#fff',
          },
          c: {
            align: 'center',
            fontSize: 10,
            color: '#fff',
            width: 60,
            padding: [0, 0, 0, 10],
          },
        },
      },
      emphasis: {
        show: false,
      },
      ...legendOption,
    },
    series: [
      {
        type: 'pie',
        radius: ['56%', '64%'],
        center: ['50%', '41%'],
        data,
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        itemStyle: {
          borderWidth: 2, //扇区间隔大小
          borderColor: '', //和背景色保持一致
        },
        avoidLabelOverlap: true,
        label: {
          show: false,
        },
        ...seriesOptions,
      },
    ],
  };

  return <CommonChart option={options} height={height} loading={loading} />;
}
