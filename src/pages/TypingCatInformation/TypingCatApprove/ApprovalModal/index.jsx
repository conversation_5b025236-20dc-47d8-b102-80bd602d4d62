import React, { useState } from 'react';
import { Modal, Input, Row, Col, Form, Select, message, } from 'antd';
import styles from './index.less';
import request from '@/utils/request';

const Index = ({ form, visible, setVisible , record , setRecord,reloadTable}) => {
    const { getFieldDecorator } = form;

    const handleResultChange = (value) => {
        if(value){
            form.setFieldsValue({
                approveComment:value==2?'审批通过':'审批驳回'
            })
        }
    }

    const [confirmLoading, setConfirmLoading] = useState(false)

    const handleSubmit = () => {
        form.validateFields((errors,values)=>{
            if(errors) return ;
            setConfirmLoading(true);
            request('/api/hn/crackFaker/approveCrackFaker',{
                method:'POST',
                params:{
                    ...values,
                    id:record.id
                },
                requestType:'json'
            }).then(res=>{
                if(res.code==200){
                    message.success('操作成功');
                    setVisible(false);
                    setRecord({});
                    reloadTable();
                }else{
                    message.error(res.message);
                }
            }).finally(()=>{
                setConfirmLoading(false);
            })
        })
    }

    return (
        <Modal
            title={'打猫成效审批'}
            visible={visible}
            onCancel={() => { setVisible(false) }}
            onOk={() => { handleSubmit() }}
            cancelText='取消'
            okText='确认'
            destroyOnClose
            maskClosable={false}
            style={{ top: 40 }}
            width={520}
        >
            <Form className={styles.myStylesForm}>
            <Row gutter={24}>
                    <Col span={24}>
                        <Form.Item label='审批结果'>
                            {getFieldDecorator('approveResult',
                                {
                                    initialValue: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择审批结果'
                                        }
                                    ]
                                }
                            )(
                                <Select placeholder='请选择' getPopupContainer={node=>node.parentElement} onChange={handleResultChange}>
                                    <Select.Option value={2} key={2}>审批通过</Select.Option>
                                    <Select.Option value={3} key={3}>审批驳回</Select.Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                <Row gutter={24}>
                    <Col span={24}>
                        <Form.Item label='审批意见'>
                            {getFieldDecorator('approveComment',
                                {
                                    initialValue: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入审批意见'
                                        }
                                    ]
                                }
                            )(
                                <Input.TextArea placeholder='请输入' autoSize={{maxRows:5,minRows:5}}></Input.TextArea>
                            )}
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}

export default Form.create()(Index);