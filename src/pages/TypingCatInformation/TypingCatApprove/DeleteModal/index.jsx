import React, { useState } from 'react';
import { Modal, Input, Row, Col, Form, Select, message, } from 'antd';
import styles from './index.less';
import request from '@/utils/request';

const Index = ({ form, visible, setVisible, record, setRecord, reloadTable }) => {
    const { getFieldDecorator } = form;



    const [confirmLoading, setConfirmLoading] = useState(false)

    const handleSubmit = () => {
        form.validateFields((errors, values) => {
            if (errors) return;
            setConfirmLoading(true);
            request('/api/hn/crackFaker/shutdownCrackFaker', {
                method: 'POST',
                params: {
                    ...values,
                    id: record.id
                },
                requestType: 'json'
            }).then(res => {
                if (res.code == 200) {
                    message.success('操作成功');
                    setVisible(false);
                    setRecord({});
                    reloadTable();
                } else {
                    message.error(res.message);
                }
            }).finally(() => {
                setConfirmLoading(false);
            })
        })
    }

    return (
        <Modal
            title={'打猫成效删除'}
            visible={visible}
            onCancel={() => { setVisible(false) }}
            onOk={() => { handleSubmit() }}
            cancelText='取消'
            okText='确认'
            destroyOnClose
            maskClosable={false}
            style={{ top: 40 }}
            width={520}
            confirmLoading={confirmLoading}
        >
            <Form className={styles.myStylesForm}>
                <Row gutter={24}>
                    <Col span={24}>
                        <Form.Item label='删除原因'>
                            {getFieldDecorator('deleteReason',
                                {
                                    initialValue: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入删除原因'
                                        }
                                    ]
                                }
                            )(
                                <Input.TextArea placeholder='请输入' autoSize={{ maxRows: 5, minRows: 5 }}></Input.TextArea>
                            )}
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}

export default Form.create()(Index);