import React, { useEffect, useRef, useState } from 'react';
import { Modal, Input, Select, DatePicker, Row, Col, Form, InputNumber, Upload, Button, Icon, message } from 'antd';
import styles from './index.less';
import request from '@/utils/request';
import { exportFile } from '@/utils/utils';
import moment from 'moment';

const Index = ({ form, visible, setVisible, action, reloadTable, record, setRecord }) => {
    const titleText = {
        'add': '打猫成效上报',
        'info': '打猫成效详情',
        'edit': '打猫成效重新录入'
    }[action]

    const firstRef = useRef(true)

    const { getFieldDecorator } = form;

    const [baseInfo, setBaseInfo] = useState({});

    const getBaseInfo = (id) => {
        if (!id) return;
        request('/api/hn/crackFaker/getDetailById', {
            method: 'GET',
            params: {
                id
            }
        }).then(res => {
            if (res.code == 200) {
                setBaseInfo(res?.data || {})
                if (res?.data?.involvedCityId) {
                    getInvolvedCountyList(res?.data?.involvedCityId)
                }
                if (res?.data?.pdfFileList?.length &&action=='edit') {
                    form.setFieldsValue({
                        pdfFile: res.data.pdfFileList.map(file => ({
                            ...file,
                            name: file.fileName,
                            uid: file.id
                        }))
                    })
                }
            }
        })
    }

    // 地市查询
    const [involvedCityList, setInvolvedCityList] = useState([]);

    const getInvolvedCityList = () => {
        request('/api/hn/systemConfig/getCityByUser', {
            method: 'get'
        }).then(res => {
            if (res.code == 200) {
                setInvolvedCityList(res.data || [])
            }
        })
    }

    const cityChange = (value) => {
        if (value) {
            getInvolvedCountyList(value)
        } else {
            setInvolvedCountyList([])
            form.setFieldsValue({
                involvedCountyId: undefined
            })
        }
    }

    useEffect(() => {
        getInvolvedCityList();
    }, [])

    useEffect(() => {
        if (record?.id && firstRef.current) {
            firstRef.current = false
            getBaseInfo(record?.id);
        }
    }, [record])

    // 县区查询
    const [involvedCountyList, setInvolvedCountyList] = useState([]);

    const getInvolvedCountyList = (id) => {
        request('/api/hn/systemConfig/getCountyByPid', {
            method: 'get',
            params: {
                pid: id,
            }
        }).then(res => {
            if (res.code == 200) {
                setInvolvedCountyList(res.data || [])
            }
        })
    }

    const [confirmLoading, setConfirmLoading] = useState(false)

    const handleSubmit = () => {
        form.validateFieldsAndScroll((errors, values) => {
            if (errors) return;

            const formData = new FormData();
            formData.append('id', baseInfo.id);
            Object.keys(values)?.forEach(key => {
                if (key == 'pdfFile') {
                    (values.pdfFile || []).forEach((file, i) => {
                        if (file.fileName) return false;
                        formData.append(`pdfFileArray`, file.originFileObj);
                    })
                    // formData.append(`pdfFileArray`,values.pdfFile?.map(file=>file.originFileObj));

                } else {
                    formData.append(key, key == 'crackDate' ? (values[key]?.format('YYYYMMDDHHmmss')) : values[key]);
                }
            });
            setConfirmLoading(true);
            request('/api/hn/crackFaker/updateCrackFaker', {
                method: 'POST',
                data: formData,

            }).then(res => {
                if (res.code == 200) {
                    message.success('重新录入成功');
                    setVisible(false);
                    reloadTable();
                } else {
                    message.error(res.message)
                }
            }).finally(() => { setConfirmLoading(false) })

        })
    }

    // 文件下载
    const handleFileDown = (file) => {
        exportFile({
            urlAPi: '/api/hn/crackFaker/downloadUpdatePdfFile',
            params: { pdfFileId: file.id },
            method: 'GET',
            mime: 'pdf',
            decode: true
        })
    }

    const handleRemovePdf = (id) => {
        request('/api/hn/crackFaker/deletePdfFileById', {
            method: 'GET',
            params: {
                pdfFileId: id
            }
        }).then(res => {
            if (res.code == 200) {
                message.success('删除成功');
                form.setFieldsValue({
                    pdfFile: form.getFieldsValue()?.pdfFile?.filter(file => file.id !== id)
                })
            } else {
                message.error(res.message);
            }
        })
    }

    const uploadProps = {
        onRemove: (file) => {
            if (file.fileName) {
                handleRemovePdf(file.id)
            }
            form.setFieldsValue({
                pdfFile: form.getFieldsValue()?.pdfFile?.filter(file1 => file1.uid !== file.uid)
            })
            return false;
        },
        beforeUpload: () => {
            return false
        }
    }

    return (
        <Modal
            title={titleText}
            visible={visible}
            onCancel={() => { setVisible(false); setRecord({}) }}
            onOk={() => { handleSubmit() }}
            cancelText='取消'
            okText='确认上报'
            destroyOnClose
            maskClosable={false}
            style={{ top: 40 }}
            width={920}
            bodyStyle={{ height: 500, overflow: 'auto' }}
            confirmLoading={confirmLoading}
            footer={action == 'info' ? null : <div>
                <Button type='primary' loading={confirmLoading} onClick={() => { handleSubmit() }} style={{ marginRight: 12 }}>确认上报</Button>
                <Button onClick={() => { setVisible(false); setRecord({}) }}>取消</Button>
            </div>
            }
        >
            <Form className={styles.myStylesForm}>
                <Row gutter={24}>
                    <Col span={12}>
                        <Form.Item label='协助公安缴获设备日期' className={styles.widthForm}>
                            {getFieldDecorator('crackDate',
                                {
                                    initialValue: baseInfo.crackDate ? moment(baseInfo.crackDate) : undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择协助公安缴获设备日期'
                                        }
                                    ]
                                }
                            )(
                                <DatePicker disabled={action == 'info'} getCalendarContainer={node => node.parentElement} showTime={false} allowClear format={'YYYY-MM-DD'} style={{ width: '100%' }} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='涉案地市'>
                            {getFieldDecorator('involvedCityId',
                                {
                                    initialValue: baseInfo.involvedCityId ? (baseInfo.involvedCityId + '') : undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择涉案地市'
                                        }
                                    ]
                                }
                            )(
                                <Select disabled={action == 'info'} onChange={cityChange} getPopupContainer={node => node.parentElement} placeholder='请选择' allowClear style={{ width: '100%' }}>
                                    {
                                        involvedCityList.map((v, i) => (
                                            <Select.Option value={v.id + ''} key={v.id + ''}>{v.name}</Select.Option>
                                        ))
                                    }
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='涉案区县'>
                            {getFieldDecorator('involvedCountyId',
                                { initialValue: baseInfo.involvedCountyId ? (baseInfo.involvedCountyId + '') : undefined }
                            )(
                                <Select disabled={action == 'info'} getPopupContainer={node => node.parentElement} placeholder='请选择' allowClear style={{ width: '100%' }}>
                                    {
                                        involvedCountyList.map((v, i) => (
                                            <Select.Option value={v.id + ''} key={v.id + ''}>{v.name}</Select.Option>
                                        ))
                                    }
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='协助缴获设备数量' className={styles.widthForm}>
                            {getFieldDecorator('deviceCount',
                                {
                                    initialValue: (baseInfo.deviceCount||baseInfo.deviceCount==0)? baseInfo.deviceCount: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入协助缴获设备数量'
                                        }
                                    ]
                                }
                            )(
                                <InputNumber disabled={action == 'info'} min={0} step={0} precision={0} placeholder='请输入'></InputNumber>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='协助打击窝点数量' className={styles.widthForm}>
                            {getFieldDecorator('hideoutCount',
                                {
                                    initialValue: (baseInfo.hideoutCount||baseInfo.hideoutCount==0)? baseInfo.hideoutCount: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入协助打击窝点数量'
                                        }
                                    ]
                                }
                            )(
                                <InputNumber disabled={action == 'info'} min={0} step={0} precision={0} placeholder='请输入'></InputNumber>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='协助抓获犯罪嫌疑人数量' className={styles.widthForm}>
                            {getFieldDecorator('suspectCount',
                                {
                                    initialValue: (baseInfo.suspectCount||baseInfo.suspectCount==0)? baseInfo.suspectCount: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入协助抓获犯罪嫌疑人数量'
                                        }
                                    ]
                                }
                            )(
                                <InputNumber disabled={action == 'info'} min={0} step={0} precision={0} placeholder='请输入'></InputNumber>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='协助缴获号卡数量' className={styles.widthForm}>
                            {getFieldDecorator('phoneCardCount',
                                {
                                    initialValue: (baseInfo.phoneCardCount||baseInfo.phoneCardCount==0)? baseInfo.phoneCardCount: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入协助缴获号卡数量'
                                        }
                                    ]
                                }
                            )(
                                <InputNumber disabled={action == 'info'} min={0} step={0} precision={0} placeholder='请输入'></InputNumber>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label='当天关停“涉猫”号码量' className={styles.widthForm}>
                            {getFieldDecorator('phoneNumCount',
                                {
                                    initialValue: (baseInfo.phoneNumCount||baseInfo.phoneNumCount==0)? baseInfo.phoneNumCount: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入当天关停“涉猫”号码量'
                                        }
                                    ]
                                }
                            )(
                                <InputNumber disabled={action == 'info'} min={0} step={0} precision={0} placeholder='请输入'></InputNumber>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='关停“涉猫”号码清单' className={styles.widthForm}>
                            {getFieldDecorator('phoneNumList',
                                {
                                    initialValue: baseInfo.phoneNumList || undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入关停“涉猫”号码清单'
                                        }
                                    ]
                                }
                            )(
                                <Input disabled={action == 'info'} placeholder='请输入，多个时用英文“;”隔开'></Input>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='关联串码清单' className={styles.widthForm}>
                            {getFieldDecorator('relatedPhoneNumList',
                                {
                                    initialValue: baseInfo.relatedPhoneNumList || undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入关联串码清单'
                                        }
                                    ]
                                }
                            )(
                                <Input disabled={action == 'info'} placeholder='请输入，多个时用英文“;”隔开'></Input>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='远控APP' >
                            {getFieldDecorator('remoteAppList',
                                {
                                    initialValue: baseInfo.remoteAppList || undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入远控APP'
                                        }
                                    ]
                                }
                            )(
                                <Input disabled={action == 'info'} placeholder='请输入，多个时用英文“;”隔开'></Input>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='窝点小区ID' >
                            {getFieldDecorator('hideoutIdList',
                                {
                                    initialValue: baseInfo.hideoutIdList || undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入窝点小区ID'
                                        }
                                    ]
                                }
                            )(
                                <Input disabled={action == 'info'} placeholder='请输入，多个时用英文“;”隔开'></Input>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='窝点小区名称' className={styles.widthForm}>
                            {getFieldDecorator('hideoutNameList',
                                {
                                    initialValue: baseInfo.hideoutNameList || undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入窝点小区名称'
                                        }
                                    ]
                                }
                            )(
                                <Input disabled={action == 'info'} placeholder='请输入，多个时用英文“;”隔开'></Input>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label='成效备注' className={styles.widthForm}>
                            {getFieldDecorator('resultRemarks',
                                {
                                    initialValue: baseInfo.resultRemarks || undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入远控APP'
                                        }
                                    ]
                                }
                            )(
                                <Input.TextArea disabled={action == 'info'} placeholder='请输入' autoSize={{ maxRows: 5, minRows: 3 }}></Input.TextArea>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        {action != 'info' ? <Form.Item label='成效数据文件（时间+地点+人物主体+事件，限定pdf文件）' className={`${styles.widthForm} ${styles.uploadForm}`}>
                            {getFieldDecorator('pdfFile',
                                {
                                    initialValue: undefined,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请上传成效数据文件'
                                        }
                                    ],
                                    valuePropName: 'fileList',
                                    getValueFromEvent: e => {
                                        if (Array.isArray(e)) {
                                            return e;
                                        }
                                        if (e.file?.name?.split('.')[1] != 'pdf') {
                                            e.fileList.pop();
                                            message.error('文件格式错误')
                                            return e && e.fileList
                                        }
                                        return e && e.fileList;
                                    }
                                }
                            )(
                                <Upload {...uploadProps} accept='.pdf' ><Button><Icon type='upload' />选择文件</Button></Upload>
                            )}
                            <span>{(form.getFieldsValue()?.pdfFile?.length) ? "" : "未选择任何文件"}</span>
                        </Form.Item> : <Form.Item label='成效数据文件（时间+地点+人物主体+事件，限定pdf文件）' className={`${styles.widthForm} ${styles.uploadForm}`}>
                            <div >
                                {
                                    baseInfo?.pdfFileList?.map(file => {
                                        return (
                                            <div style={{ color: '#1190ff', cursor: 'pointer' }} onClick={() => { handleFileDown(file) }}>
                                                {file?.fileName}
                                            </div>
                                        )
                                    })
                                }
                            </div>
                        </Form.Item>}
                    </Col>
                </Row>
            </Form>
        </Modal>
    )
}

export default Form.create()(Index);