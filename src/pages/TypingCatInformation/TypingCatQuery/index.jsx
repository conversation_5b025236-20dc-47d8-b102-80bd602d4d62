import React, { useState, useEffect } from 'react';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Modal,
  Row,
  Col,
  Select,
  DatePicker,
  Icon,
  Tooltip,
} from 'antd';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import styles from './index.less';
import AddModal from './AddModal';
import { exportFile } from '@/utils/utils';
import { Licensee } from 'ponshine';

const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator },
  } = props;

  const [action, setAction] = useState('add');
  // 列表加载状态
  const [tableLoading, setTableLoading] = useState(false);
  // 列表数据
  const [tableData, setTableData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  // 列表选中项
  const [selectedRows, setSelectedRows] = useState([]);
  // 查询条件
  const [searchParams, setSearchParams] = useState({});
  // 表格配置项
  const columns = [
    {
      title: '协助公安缴获设备日期',
      dataIndex: 'crackDate',
      align: 'center',
      ellipsis: true,
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatus',
      align: 'center',
      // ellipsis: true,
      width: 120,
      render: (text) => {
        const arr = ['待审批', '已审批通过', '待重新录入', '已关闭'];
        return text ? arr[text - 1] : '--';
      },
    },
    {
      title: '涉案地市',
      dataIndex: 'involvedCityName',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '涉案区县',
      dataIndex: 'involvedCountyName',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '上报人',
      dataIndex: 'reportPersonName',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '上报时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      ellipsis: true,
      width: 200,
    },
    {
      title: '审批人',
      dataIndex: 'approverName',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '审批时间',
      dataIndex: 'approvalTime',
      align: 'center',
      ellipsis: true,
      width: 200,
    },
    {
      title: '删除原因',
      dataIndex: 'deleteReason',
      align: 'center',
      ellipsis: true,
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      // width: 80,
      fixed: 'right',
      render: (text, reccord) => {
        return (
          <Licensee license="typingCatQuery_getDetailById">
            <Tooltip title="查看详情">
              <a
                onClick={() => {
                  handleInfo(reccord);
                }}
              >
                <Icon type="search" />
              </a>
            </Tooltip>
          </Licensee>
        );
      },
    },
  ];

  const [record, setRecord] = useState({});

  // 打开详情页
  const handleInfo = (record) => {
    setRecord(record);
    setAction('info');
    setAddModalVisible(true);
  };

  const [involvedCityList, setInvolvedCityList] = useState([]);

  const getInvolvedCityList = () => {
    request('/api/hn/systemConfig/getCityByUser', {
      method: 'get',
    }).then((res) => {
      if (res.code == 200) {
        setInvolvedCityList(res.data || []);
      }
    });
  };

  // 列表查询
  const handleSearch = (current = 1, pageSize = 10, searchParams) => {
    const formData = searchParams || form.getFieldsValue() || {};
    let params = {
      // ...defaultParams,
      ...formData,
      pageNum: current,
      pageSize: pageSize,
    };
    if (!searchParams) {
      params = {
        ...params,
        crackFakerDateStart: formData?.crackFakerDateStart?.[0]?.format('YYYYMMDD000000') || '',
        crackFakerDateEnd: formData?.crackFakerDateStart?.[1]?.format('YYYYMMDD235959') || '',
        approvalTimeStart: formData?.approvalTimeStart?.[0]?.format('YYYYMMDD000000') || '',
        approvalTimeEnd: formData?.approvalTimeStart?.[1]?.format('YYYYMMDD235959') || '',
        gmtCreateStart: formData?.gmtCreateStart?.[0]?.format('YYYYMMDD000000') || '',
        gmtCreateEnd: formData?.gmtCreateStart?.[1]?.format('YYYYMMDD235959') || '',
      };
    }
    Object.keys(params).forEach((key) => {
      if (!params[key]) {
        delete params[key];
      }
    });
    setTableLoading(true);
    request('/api/hn/crackFaker/pageCrackResult', {
      method: 'POST',
      data: {
        ...params,
      },
      requestType: 'json',
    })
      .then((res) => {
        if (res.code == 200) {
          setTableData({
            list: res.data?.items || [],
            total: res.data?.totalNum || 0,
          });
          setPagination({
            current: current,
            pageSize: pageSize,
          });
          setSearchParams({
            ...params,
          });
        } else {
          message.error(res.message);
          setTableData({
            list: [],
            total: 0,
          });
          setPagination({
            current: 1,
            pageSize: 10,
          });
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  };
  // 重置列表查询
  const handleReset = () => {
    form.resetFields();
    handleSearch();
  };
  // 列表改变分页
  const handleTableChange = (p) => {
    handleSearch(p.current, p.pageSize, searchParams);
  };

  // 导出
  const handleImport = () => {
    // request('/api/hn/crackFaker/getCrackFakerExcel',{
    //     method:'get',
    //     params:{...searchParams},
    //     responseType: 'blob',
    // })
    // exportFile({
    //     urlAPi: '/api/hn/crackFaker/exportCrackFaker',
    //     params: { ...searchParams },
    //     method: 'POST',
    //     titleFn:(name)=>{

    //     }
    // })
    exportFile({
      urlAPi: '/api/hn/crackFaker/getCrackFakerExcel',
      params: { ...searchParams, exportType: 1 },
      method: 'GET',
      decode: true,
    });
  };
  // // 列表选中项
  // const handleSelectRows = (rows) => {
  //     setSelectedRows(rows);
  // };
  // 新增弹窗
  const [addModalVisible, setAddModalVisible] = useState(false);
  // 新增
  const handleAdd = () => {
    setAction('add');
    setAddModalVisible(true);
  };

  useEffect(() => {
    handleSearch();
    getInvolvedCityList();
  }, []);

  return (
    <Card>
      <Form className={styles.myStylesForm}>
        <Row gutter={24}>
          <Licensee license="typingCatQuery_pageCrackResult">
            <Col span={8}>
              <Form.Item label="协助公安缴获设备日期" className={styles.widthForm}>
                {getFieldDecorator('crackFakerDateStart', { initialValue: undefined })(
                  <DatePicker.RangePicker
                    showTime={false}
                    allowClear
                    format={'YYYY-MM-DD'}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="状态">
                {getFieldDecorator('approvalStatus', { initialValue: undefined })(
                  <Select placeholder="请选择" allowClear style={{ width: '100%' }}>
                    {['待审批', '已审批通过', '待重新录入', '已关闭'].map((v, i) => (
                      <Select.Option value={i + 1} key={i + 1}>
                        {v}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="涉案地市">
                {getFieldDecorator('involvedCityId', { initialValue: undefined })(
                  <Select placeholder="请输入" allowClear style={{ width: '100%' }}>
                    {involvedCityList.map((v, i) => (
                      <Select.Option value={v.id} key={v.id}>
                        {v.name}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="上报时间">
                {getFieldDecorator('gmtCreateStart', { initialValue: undefined })(
                  <DatePicker.RangePicker
                    showTime={false}
                    allowClear
                    format={'YYYY-MM-DD'}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="审批时间">
                {getFieldDecorator('approvalTimeStart', { initialValue: undefined })(
                  <DatePicker.RangePicker
                    showTime={false}
                    allowClear
                    format={'YYYY-MM-DD'}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="上报人">
                {getFieldDecorator('reportPersonName', { initialValue: undefined })(
                  <Input placeholder="请输入" allowClear />,
                )}
              </Form.Item>
            </Col>
          </Licensee>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Licensee license="typingCatQuery_reportCrackFaker">
              <Button
                type="primary"
                onClick={() => {
                  handleAdd();
                }}
              >
                新增上报
              </Button>
            </Licensee>
            <Licensee license="typingCatQuery_pageCrackResult">
              <Button
                type="primary"
                onClick={() => {
                  handleSearch();
                }}
                style={{ marginLeft: 12 }}
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  handleReset();
                }}
                style={{ marginLeft: 12 }}
              >
                重置
              </Button>
            </Licensee>
            <Licensee license="typingCatQuery_getCrackFakerExcel">
              <Button
                type="primary"
                onClick={() => {
                  handleImport();
                }}
                style={{ marginLeft: 12 }}
              >
                批量导出
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <div style={{ marginTop: 20 }}>
        <StandardTable
          selectedRows={selectedRows}
          data={{
            list: tableData.list,
            pagination: {
              total: tableData.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          }}
          columns={columns}
          scroll={{ x: 'max-content' }}
          onChange={handleTableChange}
          rowSelectionProps={false}
          // onSelectRow={handleSelectRows}
          showSelectCount={false}
          rowkey="id"
          loading={tableLoading}
          onRow={(record) => {
            return {
              onDoubleClick: () => handleInfo(record),
            };
          }}
        />
      </div>
      {addModalVisible && (
        <AddModal
          visible={addModalVisible}
          setVisible={setAddModalVisible}
          reloadTable={handleSearch}
          action={action}
          record={record}
          setRecord={setRecord}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
