import { Alert, Checkbox, message as antdMessage } from 'antd';
import { FormattedMessage, formatMessage } from 'ponshine-plugin-react/locale';
import React, { Component } from 'react';
import { connect } from 'dryad';
import { withSilence } from 'demasia-pro-layout';
import LoginComponents from './components/Login';
import ForgotPasswordComponents from './components/ForgotPassword';
import styles from './style.less';
import {
  getFakeNormalCaptcha,
  getFakeSlideJigsawCaptcha,
  verifyFakeSlideJigsawCaptcha,
  sendBackPasswordCaptcha,
  updateBackPassword,
} from './service';
const { UserName, Password, NormalCaptcha, SlideJigsawCaptcha, Submit } = LoginComponents;

class UserLogin extends Component {
  loginForm = undefined;
  state = {
    autoLogin: true,
    visible: true,
  };
  captchaRef = React.createRef();
  captchaImgHeraldRule = (rs) => {
    // 此处写法老版和新版后端兼容
    this.captchaLastImgToken = this.captchaImgToken;
    this.captchaLastImgSecretKey = this.captchaImgSecretKey;
    this.captchaImgToken = rs?.repData?.token || rs?.imageToken;
    this.captchaImgSecretKey = rs?.repData?.secretKey;
    return {
      // 当前是滑动拼图验证码，所以设置 bgImgSrc 和 frontImgSrc
      bgImgSrc: rs?.oripic || `data:image/png;base64,${rs?.repData?.originalImageBase64}`,
      frontImgSrc: rs?.newpic || `data:image/png;base64,${rs?.repData?.jigsawImageBase64}`,
    };
  };
  captchaVerifyService = (newInputValue) => {
    this.captchaImgTrack = newInputValue;
    this.captchaLastSize = this.captchaRef.current?.getSize?.();
    return verifyFakeSlideJigsawCaptcha({
      size: this.captchaLastSize,
      track: this.captchaImgTrack,
      token: this.captchaImgToken,
      secretKey: this.captchaImgSecretKey,
    });
  };
  captchaVerifyRule = (rs) => {
    // 此处写法老版和新版后端兼容
    const isVerifyOk = rs?.success === true || rs?.state === 'SUCCESS';
    const msg = rs?.repMsg || rs?.message;

    if (isVerifyOk) {
      antdMessage.success(
        `${
          msg ||
          formatMessage({
            id: 'userlogin.verification-code.succeeded',
          })
        }`,
      );
    } else {
      antdMessage.warn(
        `${
          msg ||
          formatMessage({
            id: 'userlogin.verification-code.failed',
          })
        }`,
      );
    }

    return isVerifyOk;
  };
  changeAutoLogin = (e) => {
    this.setState({
      autoLogin: e.target.checked,
    });
  };
  changeVisible = (e = true) => {
    this.setState({
      visible: e,
    });
  };
  handleSubmit = (err, values) => {
    const { autoLogin } = this.state;

    if (!err) {
      const { dispatch } = this.props;
      dispatch({
        type: 'userLogin/login',
        payload: {
          ...values,
          size: this.captchaLastSize,
          track: this.captchaImgTrack,
          token: this.captchaLastImgToken,
          secretKey: this.captchaLastImgSecretKey,
          'remember-me': autoLogin,
        },
      });
    }
  };
  renderMessage = (content) => (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );

  render() {
    // @ts-ignore
    const { userLogin, submitting } = this.props;
    const { status, message } = userLogin;
    const { autoLogin, visible } = this.state;
    return (
      <div className={styles.main}>
        {visible ? (
          <LoginComponents onSubmit={this.handleSubmit}>
            {['ERROR', 'FAIL'].includes(`${status}`.toUpperCase()) &&
              !submitting &&
              this.renderMessage(
                message ||
                  formatMessage({
                    id: 'userlogin.login.message-invalid-credentials',
                  }),
              )}
            <UserName
              name="username"
              placeholder={`${formatMessage({
                id: 'userlogin.login.userName',
              })}: admin or user`}
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: 'userlogin.userName.required',
                  }),
                },
              ]}
            />
            <Password
              name="password"
              placeholder={`${formatMessage({
                id: 'userlogin.login.password',
              })}: demasia-pro`}
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: 'userlogin.password.required',
                  }),
                },
              ]}
            />
            <NormalCaptcha
              forwardRef={this.captchaRef}
              name="captcha"
              inputProps={{
                placeholder: formatMessage({
                  id: 'userlogin.verification-code.placeholder',
                }),
              }}
              imgProps={getFakeNormalCaptcha}
              refreshBtnProps={{
                children: formatMessage({
                  id: 'userlogin.verification-code.refresh',
                }),
              }}
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: 'userlogin.verification-code.required',
                  }),
                },
              ]}
            />
            <SlideJigsawCaptcha // @ts-ignore
              key={this.props.locale}
              forwardRef={this.captchaRef}
              name="captcha"
              imgHeraldService={getFakeSlideJigsawCaptcha}
              imgHeraldRule={this.captchaImgHeraldRule}
              verifyService={this.captchaVerifyService}
              verifyRule={this.captchaVerifyRule}
            />
            <Alert
              style={{
                marginBottom: 24,
              }}
              type="warning"
              message={formatMessage({
                id: 'userlogin.verification-code.dev-info',
              })}
            />
            <div>
              <Checkbox checked={autoLogin} onChange={this.changeAutoLogin}>
                <FormattedMessage id="userlogin.login.remember-me" />
              </Checkbox>
              <a
                style={{
                  float: 'right',
                }}
                onClick={(e) => {
                  this.changeVisible(false);
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <FormattedMessage id="userlogin.login.forgot-password" />
              </a>
            </div>
            <Submit loading={submitting}>
              <FormattedMessage id="userlogin.login.login" />
            </Submit>
          </LoginComponents>
        ) : (
          <ForgotPasswordComponents
            sendBackPasswordCaptcha={sendBackPasswordCaptcha}
            updateBackPassword={updateBackPassword}
            changeVisible={this.changeVisible}
          />
        )}
      </div>
    );
  }
}

export default withSilence(
  connect(({ userLogin, loading }) => ({
    userLogin,
    submitting: loading.effects['userLogin/login'],
  }))(UserLogin),
);
