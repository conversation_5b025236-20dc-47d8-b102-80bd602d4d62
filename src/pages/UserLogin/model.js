import { accountLogin, fakeAccountLogout, getFake<PERSON><PERSON>tcha, getFakeNormalCaptcha } from './service';
const Model = {
  namespace: 'userLogin',
  state: {
    status: undefined,
    message: '',
  },
  effects: {
    *login({ payload, callback }, { call, put }) {
      const response = yield call(accountLogin, payload);
      yield put({
        type: 'changeLoginStatus',
        payload: response,
      });
      callback?.(response);
    },

    *getCaptcha({ payload }, { call }) {
      yield call(getFakeCaptcha, payload);
    },

    *getNormalCaptcha(_, { call }) {
      yield call(getFakeNormalCaptcha);
    },

    *logout(_, { call }) {
      yield call(fakeAccountLogout);
    },
  },
  reducers: {
    changeLoginStatus(state, { payload }) {
      return {
        ...state,
        message: payload.message || payload.msg,
        status:
          payload.status ||
          (!payload.error &&
          (`${payload.state || ''}`.toUpperCase() === 'SUCCESS' || payload.success)
            ? 'SUCCESS'
            : 'FAIL'),
      };
    },
  },
};
export default Model;
