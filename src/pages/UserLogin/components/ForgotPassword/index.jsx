import React, { useRef, useState } from 'react';
import { Form, Button, Row, Input, Col, message, Tooltip, Icon, notification } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { router, useAuth, refreshAuth } from 'ponshine';
import { useInterval } from 'phooks';
import { validatePasswordStrength } from '../../utils';
import styles from './index.less';
const FormItem = Form.Item;

const CountDown = () => {
  const [count, setCount] = useState(5);
  useInterval(
    () => {
      setCount(count - 1);
    },
    count <= 0 ? null : 1000,
  );
  return <span>{count}</span>;
};

const ForgotPassword = (props) => {
  const {
    form: { validateFieldsAndScroll, getFieldDecorator, getFieldValue, validateFields },
    sendBackPasswordCaptcha,
    updateBackPassword,
    changeVisible,
  } = props;
  const [captchaCount, setCaptchaCount] = useState(0);
  const [confirmDirty, setConfirmDirty] = useState(false);
  const { authState } = useAuth() || {};
  const passwordPolicy = authState?.passwordPolicy || {};
  const myRef = useRef();

  const handleToLogin = async () => {
    if (myRef.current) clearInterval(myRef.current);
    await refreshAuth(); // TODO 下方跳转路由在非单点登录时只是一个双保险（因为 refreshAuth() 后在 auth.[j|t]sx? 文件中有对回包头中的 session-status 进行特殊处理），而如果是单点登录，则不需要跳转路由，完全由 refreshAuth() 后通过 auth.[j|t]sx? 文件中的逻辑去控制

    router.push({
      pathname: '/user/login',
    });
    if (changeVisible) changeVisible(true);
    notification.close('userLogin');
  };

  const handleOk = () => {
    validateFieldsAndScroll(async (err, values) => {
      if (err) return;
      const response = await updateBackPassword(values);

      if (response?.state === 'SUCCESS') {
        if (myRef.current) clearInterval(myRef.current);
        myRef.current = setTimeout(() => {
          handleToLogin();
        }, 1000 * 5);
        notification.success({
          key: 'userLogin',
          message: formatMessage({
            id: 'userlogin.forgotPassword.submit.success.message',
          }),
          duration: 5,
          description: (
            <div>
              <div>
                {formatMessage({
                  id: 'userlogin.forgotPassword.submit.success.description',
                })}
              </div>
              <a onClick={handleToLogin}>
                {formatMessage({
                  id: 'userlogin.forgotPassword.submit.success.goToLogin',
                })}
                （<CountDown />
                s）
              </a>
            </div>
          ),
        });
      } else {
        message.error(
          response.message ||
            formatMessage({
              id: 'userlogin.forgotPassword.submit.error.message',
            }),
        );
      }
    });
  };

  const formItemLayout = {
    labelCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 0,
      },
    },
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 24,
      },
    },
  };
  useInterval(
    () => {
      setCaptchaCount(captchaCount - 1);
    },
    captchaCount <= 0 ? null : 1000,
  );
  /**
   * 获取验证码
   */

  const getCaptcha = async () => {
    const username = getFieldValue('username');

    if (!username) {
      message.error(
        formatMessage({
          id: 'userlogin.forgotPassword.username.placeholder',
        }),
      );
      return;
    }

    const response = await sendBackPasswordCaptcha({
      username,
    });

    if (response?.state === 'SUCCESS') {
      message.success(
        response.messag ||
          formatMessage({
            id: 'userlogin.forgotPassword.captcha.success.message',
          }),
      );
      setCaptchaCount(60);
    } else {
      message.error(
        response.message ||
          formatMessage({
            id: 'userlogin.forgotPassword.captcha.error.message',
          }),
      );
    }
  };

  const compareToFirstPassword = (rule, value, callback) => {
    if (value && value !== getFieldValue('password')) {
      callback(
        formatMessage({
          id: 'userlogin.forgotPassword.password.compareToFirstPassword',
        }),
      );
    } else {
      callback();
    }
  };

  const validateToNextPassword = (rule, value, callback) => {
    const username = getFieldValue('username');

    if (!validatePasswordStrength(value, username)) {
      callback(
        formatMessage({
          id: 'userlogin.forgotPassword.password.pattern',
        }),
      );
    }

    if (value && confirmDirty) {
      validateFields(['confirm'], {
        force: true,
      });
    }

    callback();
  };

  const handleConfirmBlur = (e) => {
    const { value } = e.target;
    setConfirmDirty(confirmDirty || !!value);
  };

  return (
    <Form {...formItemLayout} className={styles.forgotPassword}>
      <FormItem>
        {getFieldDecorator('username', {
          rules: [
            {
              required: true,
              message: formatMessage({
                id: 'userlogin.forgotPassword.username.required',
              }),
            },
          ],
        })(
          <Input
            size="large"
            prefix={
              <Icon
                type="user"
                style={{
                  color: 'rgba(0,0,0,.25)',
                }}
              />
            }
            placeholder={formatMessage({
              id: 'userlogin.forgotPassword.username',
            })}
          />,
        )}
      </FormItem>
      <FormItem>
        <Row gutter={8}>
          <Col span={12}>
            {getFieldDecorator('captcha', {
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'userlogin.forgotPassword.captcha.required',
                  }),
                },
              ],
            })(
              <Input
                size="large"
                prefix={
                  <Icon
                    type="code"
                    style={{
                      color: 'rgba(0,0,0,.25)',
                    }}
                  />
                }
                placeholder={formatMessage({
                  id: 'userlogin.forgotPassword.captcha',
                })}
              />,
            )}
          </Col>
          <Col span={12}>
            <Button
              size="large"
              type="primary"
              onClick={() => {
                getCaptcha();
              }}
              disabled={captchaCount !== 0}
            >
              {captchaCount === 0
                ? formatMessage({
                    id: 'userlogin.forgotPassword.captcha.button',
                  })
                : `${captchaCount}s`}
            </Button>
          </Col>
        </Row>
      </FormItem>
      <FormItem hasFeedback>
        {getFieldDecorator('password', {
          validateFirst: true,
          rules: [
            {
              required: true,
              message: formatMessage({
                id: 'userlogin.forgotPassword.password.required',
              }),
            },
            {
              min: passwordPolicy?.minimumLengthEnable ? passwordPolicy?.minimumLength : -1,
              message: formatMessage(
                {
                  id: 'userlogin.forgotPassword.password.minLength',
                },
                {
                  minimumLength: passwordPolicy?.minimumLength,
                },
              ),
            },
            {
              validator: validateToNextPassword,
            },
          ],
        })(
          <Input.Password
            size="large"
            prefix={
              <Icon
                type="lock"
                style={{
                  color: 'rgba(0,0,0,.25)',
                }}
              />
            }
            placeholder={formatMessage({
              id: 'userlogin.forgotPassword.password',
            })}
            addonBefore={
              <Tooltip
                title={formatMessage({
                  id: 'userlogin.forgotPassword.password.tooltip',
                })}
              >
                <Icon
                  type="info-circle"
                  style={{
                    color: 'rgba(0,0,0,.45)',
                  }}
                />
              </Tooltip>
            }
          />,
        )}
      </FormItem>

      <FormItem>
        {getFieldDecorator('confirm', {
          validateFirst: true,
          rules: [
            {
              required: true,
              message: formatMessage({
                id: 'userlogin.forgotPassword.confirm.required',
              }),
            },
            {
              validator: compareToFirstPassword,
            },
          ],
        })(
          <Input.Password
            size="large"
            prefix={
              <Icon
                type="lock"
                style={{
                  color: 'rgba(0,0,0,.25)',
                }}
              />
            }
            onBlur={handleConfirmBlur}
            placeholder={formatMessage({
              id: 'userlogin.forgotPassword.confirm',
            })}
            addonBefore={
              <Tooltip
                title={formatMessage({
                  id: 'userlogin.forgotPassword.confirm.tooltip',
                })}
              >
                <Icon
                  type="info-circle"
                  style={{
                    color: 'rgba(0,0,0,.45)',
                  }}
                />
              </Tooltip>
            }
          />,
        )}
      </FormItem>

      <div
        style={{
          textAlign: 'center',
          width: '100%',
          marginTop: '30px',
        }}
      >
        <Button type="primary" onClick={handleOk}>
          {formatMessage({
            id: 'userlogin.forgotPassword.submit',
          })}
        </Button>

        <Button
          style={{
            marginLeft: 20,
          }}
          onClick={() => {
            handleToLogin();
          }}
        >
          {formatMessage({
            id: 'userlogin.forgotPassword.cancel',
          })}
        </Button>
      </div>
    </Form>
  );
};

const ForgotPasswordForm = Form.create()(ForgotPassword);
export default ForgotPasswordForm;
