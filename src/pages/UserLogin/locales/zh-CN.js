export default {
  'userlogin.login.userName': '用户名',
  'userlogin.login.password': '密码',
  'userlogin.login.captcha': '验证码',
  'userlogin.error.captchaError': '验证码错误',
  'userlogin.login.message-invalid-credentials': '账户或密码错误',
  'userlogin.login.message-invalid-verification-code': '验证码错误',
  'userlogin.login.tab-login-credentials': '账户密码登录',
  'userlogin.login.tab-login-mobile': '手机号登录',
  'userlogin.login.remember-me': '记住我',
  'userlogin.login.forgot-password': '忘记密码',
  'userlogin.login.sign-in-with': '其他登录方式',
  'userlogin.login.signup': '注册账户',
  'userlogin.login.login': '登录',
  'userlogin.register.register': '注册',
  'userlogin.register.get-verification-code': '获取验证码',
  'userlogin.register.sign-in': '使用已有账户登录',
  'userlogin.register-result.msg': '你的账户：{email} 注册成功',
  'userlogin.register-result.activation-email':
    '激活邮件已发送到你的邮箱中，邮件有效期为24小时。请及时登录邮箱，点击邮件中的链接激活帐户。',
  'userlogin.register-result.back-home': '返回首页',
  'userlogin.register-result.view-mailbox': '查看邮箱',
  'userlogin.email.required': '请输入邮箱地址！',
  'userlogin.email.wrong-format': '邮箱地址格式错误！',
  'userlogin.userName.required': '请输入用户名!',
  'userlogin.password.required': '请输入密码！',
  'userlogin.password.twice': '两次输入的密码不匹配!',
  'userlogin.strength.msg': '请至少输入 6 个字符。请不要使用容易被猜到的密码。',
  'userlogin.strength.strong': '强度：强',
  'userlogin.strength.medium': '强度：中',
  'userlogin.strength.short': '强度：太短',
  'userlogin.confirm-password.required': '请确认密码！',
  'userlogin.phone-number.required': '请输入手机号！',
  'userlogin.phone-number.wrong-format': '手机号格式错误！',
  'userlogin.verification-code.required': '请输入验证码！',
  'userlogin.verification-code.refresh': '换一张',
  'userlogin.verification-code.succeeded': '验证成功！',
  'userlogin.verification-code.failed': '验证失败！',
  'userlogin.verification-code.slide-jigsaw.on-drag-timeout':
    '滑动超时了，用了 {duration} 毫秒，请在 {timeout} 毫秒内完成滑动！',
  'userlogin.verification-code.slide-jigsaw.verify-errored': '验证异常',
  'userlogin.verification-code.slide-jigsaw.verify-failed': '验证失败',
  'userlogin.verification-code.slide-jigsaw.verify-succeeded': '验证成功',
  'userlogin.verification-code.slide-jigsaw.ready': '向右滑动填充拼图',
  'userlogin.verification-code.slide-jigsaw.image-ing': '加载中...',
  'userlogin.verification-code.slide-jigsaw.image-errored': '加载失败',
  'userlogin.verification-code.slide-jigsaw.drag-timeout': '滑动超时',
  'userlogin.verification-code.dev-info': '请开发人员自行选择验证码类型',
  'userlogin.title.required': '请输入标题',
  'userlogin.date.required': '请选择起止日期',
  'userlogin.goal.required': '请输入目标描述',
  'userlogin.standard.required': '请输入衡量标准',
  'userlogin.form.get-captcha': '获取验证码',
  'userlogin.captcha.second': '秒',
  'userlogin.captcha.required': '请输入验证码',
  'userlogin.form.optional': '（选填）',
  'userlogin.form.submit': '提交',
  'userlogin.form.save': '保存',
  'userlogin.email.placeholder': '邮箱',
  'userlogin.password.placeholder': '至少6位密码，区分大小写',
  'userlogin.confirm-password.placeholder': '确认密码',
  'userlogin.phone-number.placeholder': '手机号',
  'userlogin.verification-code.placeholder': '验证码',
  'userlogin.title.label': '标题',
  'userlogin.title.placeholder': '给目标起个名字',
  'userlogin.date.label': '起止日期',
  'userlogin.placeholder.start': '开始日期',
  'userlogin.placeholder.end': '结束日期',
  'userlogin.goal.label': '目标描述',
  'userlogin.goal.placeholder': '请输入你的阶段性工作目标',
  'userlogin.standard.label': '衡量标准',
  'userlogin.standard.placeholder': '请输入衡量标准',
  'userlogin.client.label': '客户',
  'userlogin.label.tooltip': '目标的服务对象',
  'userlogin.client.placeholder': '请描述你服务的客户，内部客户直接 @姓名／工号',
  'userlogin.invites.label': '邀评人',
  'userlogin.invites.placeholder': '请直接 @姓名／工号，最多可邀请 5 人',
  'userlogin.weight.label': '权重',
  'userlogin.weight.placeholder': '请输入',
  'userlogin.public.label': '目标公开',
  'userlogin.label.help': '客户、邀评人默认被分享',
  'userlogin.radio.public': '公开',
  'userlogin.radio.partially-public': '部分公开',
  'userlogin.radio.private': '不公开',
  'userlogin.publicUsers.placeholder': '公开给',
  'userlogin.option.A': '同事甲',
  'userlogin.option.B': '同事乙',
  'userlogin.option.C': '同事丙',
  'userlogin.navBar.lang': '语言',
  'userlogin.forgotPassword.username': '用户名',
  'userlogin.forgotPassword.username.placeholder': '请输入用户名！',
  'userlogin.forgotPassword.username.required': '该字段为必填项！',
  'userlogin.forgotPassword.captcha': '验证码',
  'userlogin.forgotPassword.captcha.required': '该字段为必填项！',
  'userlogin.forgotPassword.captcha.button': '获取验证码',
  'userlogin.forgotPassword.captcha.success.message': '验证码发送成功！',
  'userlogin.forgotPassword.captcha.error.message': '获取验证码失败！',
  'userlogin.forgotPassword.password': '密码',
  'userlogin.forgotPassword.password.compareToFirstPassword': '与上一次密码不符，请确认！',
  'userlogin.forgotPassword.password.tooltip':
    '密码必须符合下列最低要求： 不能包含用户的账号名， 至少包含以下四类字符中的三类字符： 英文大写字母（A-Z） 英文小写字母（a-z） 数字（0-9） 特殊字符（如：@、#、￥、%）',
  'userlogin.forgotPassword.password.required': '该字段不能为空！',
  'userlogin.forgotPassword.password.minLength': '密码不能小于{minimumLength}位！',
  'userlogin.forgotPassword.password.pattern': '密码复杂度不符合要求！',
  'userlogin.forgotPassword.confirm': '确认密码',
  'userlogin.forgotPassword.confirm.tooltip': '请确保2次新密码相同！',
  'userlogin.forgotPassword.confirm.required': '该字段不能为空！',
  'userlogin.forgotPassword.submit': '提交',
  'userlogin.forgotPassword.cancel': '返回',
  'userlogin.forgotPassword.submit.success.message': '密码修改成功！',
  'userlogin.forgotPassword.submit.success.description':
    '为避免您的账号无法正常使用，还请你使用新密码进行平台登录。',
  'userlogin.forgotPassword.submit.success.goToLogin': '返回登录页',
  'userlogin.forgotPassword.submit.error.message': '密码修改失败！',
};
