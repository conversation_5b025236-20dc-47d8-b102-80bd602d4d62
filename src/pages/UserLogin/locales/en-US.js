export default {
  'userlogin.login.userName': 'userName',
  'userlogin.login.password': 'password',
  'userlogin.login.captcha': 'captcha',
  'userlogin.error.captchaError': 'captchaError',
  'userlogin.login.message-invalid-credentials':
    'Invalid username or password',
  'userlogin.login.message-invalid-verification-code': 'Invalid verification code',
  'userlogin.login.tab-login-credentials': 'Credentials',
  'userlogin.login.tab-login-mobile': 'Mobile number',
  'userlogin.login.remember-me': 'Remember me',
  'userlogin.login.forgot-password': 'Forgot your password?',
  'userlogin.login.sign-in-with': 'Sign in with',
  'userlogin.login.signup': 'Sign up',
  'userlogin.login.login': 'Login',
  'userlogin.register.register': 'Register',
  'userlogin.register.get-verification-code': 'Get code',
  'userlogin.register.sign-in': 'Already have an account?',
  'userlogin.register-result.msg': 'Account：registered at {email}',
  'userlogin.register-result.activation-email':
    'The activation email has been sent to your email address and is valid for 24 hours. Please log in to the email in time and click on the link in the email to activate the account.',
  'userlogin.register-result.back-home': 'Back to home',
  'userlogin.register-result.view-mailbox': 'View mailbox',
  'userlogin.email.required': 'Please enter your email!',
  'userlogin.email.wrong-format': 'The email address is in the wrong format!',
  'userlogin.userName.required': 'Please enter your userName!',
  'userlogin.password.required': 'Please enter your password!',
  'userlogin.password.twice': 'The passwords entered twice do not match!',
  'userlogin.strength.msg':
    "Please enter at least 6 characters and don't use passwords that are easy to guess.",
  'userlogin.strength.strong': 'Strength: strong',
  'userlogin.strength.medium': 'Strength: medium',
  'userlogin.strength.short': 'Strength: too short',
  'userlogin.confirm-password.required': 'Please confirm your password!',
  'userlogin.phone-number.required': 'Please enter your phone number!',
  'userlogin.phone-number.wrong-format': 'Malformed phone number!',
  'userlogin.verification-code.required': 'Please enter the verification code!',
  'userlogin.verification-code.refresh': 'Refresh',
  'userlogin.verification-code.succeeded': 'Validation successful!',
  'userlogin.verification-code.failed': 'Validation failed!',
  'userlogin.verification-code.slide-jigsaw.on-drag-timeout':
    'Sliding timeout, it took {duration} MS, please complete sliding within {timeout} Ms!',
  'userlogin.verification-code.slide-jigsaw.verify-errored': 'Verify errored',
  'userlogin.verification-code.slide-jigsaw.verify-failed': 'Verify failed',
  'userlogin.verification-code.slide-jigsaw.verify-succeeded': 'Verify succeeded',
  'userlogin.verification-code.slide-jigsaw.ready': 'Slide right to fill puzzle',
  'userlogin.verification-code.slide-jigsaw.image-ing': 'Image loading...',
  'userlogin.verification-code.slide-jigsaw.image-errored': 'Image errored',
  'userlogin.verification-code.slide-jigsaw.drag-timeout': 'Drag timeout',
  'userlogin.verification-code.dev-info': 'Please choose the verification code type by yourself',
  'userlogin.title.required': 'Please enter a title',
  'userlogin.date.required': 'Please select the start and end date',
  'userlogin.goal.required': 'Please enter a description of the goal',
  'userlogin.standard.required': 'Please enter a metric',
  'userlogin.form.get-captcha': 'Get Captcha',
  'userlogin.captcha.second': 'sec',
  'userlogin.captcha.required': 'Please enter Captcha',
  'userlogin.form.optional': ' (optional) ',
  'userlogin.form.submit': 'Submit',
  'userlogin.form.save': 'Save',
  'userlogin.email.placeholder': 'Email',
  'userlogin.password.placeholder': 'Password',
  'userlogin.confirm-password.placeholder': 'Confirm password',
  'userlogin.phone-number.placeholder': 'Phone number',
  'userlogin.verification-code.placeholder': 'Verification code',
  'userlogin.title.label': 'Title',
  'userlogin.title.placeholder': 'Give the target a name',
  'userlogin.date.label': 'Start and end date',
  'userlogin.placeholder.start': 'Start date',
  'userlogin.placeholder.end': 'End date',
  'userlogin.goal.label': 'Goal description',
  'userlogin.goal.placeholder': 'Please enter your work goals',
  'userlogin.standard.label': 'Metrics',
  'userlogin.standard.placeholder': 'Please enter a metric',
  'userlogin.client.label': 'Client',
  'userlogin.label.tooltip': 'Target service object',
  'userlogin.client.placeholder':
    'Please describe your customer service, internal customers directly @ Name / job number',
  'userlogin.invites.label': 'Inviting critics',
  'userlogin.invites.placeholder':
    'Please direct @ Name / job number, you can invite up to 5 people',
  'userlogin.weight.label': 'Weight',
  'userlogin.weight.placeholder': 'Please enter weight',
  'userlogin.public.label': 'Target disclosure',
  'userlogin.label.help': 'Customers and invitees are shared by default',
  'userlogin.radio.public': 'Public',
  'userlogin.radio.partially-public': 'Partially public',
  'userlogin.radio.private': 'Private',
  'userlogin.publicUsers.placeholder': 'Open to',
  'userlogin.option.A': 'Colleague A',
  'userlogin.option.B': 'Colleague B',
  'userlogin.option.C': 'Colleague C',
  'userlogin.navBar.lang': 'Languages',
  'userlogin.forgotPassword.username': 'Username',
  'userlogin.forgotPassword.username.placeholder': 'Please enter your username!',
  'userlogin.forgotPassword.username.required': 'This field is required!',
  'userlogin.forgotPassword.captcha': 'Verification Code',
  'userlogin.forgotPassword.captcha.required': 'This field is required!',
  'userlogin.forgotPassword.captcha.button': 'Get verification code',
  'userlogin.forgotPassword.captcha.success.message':
    'The verification code was sent successfully! ',
  'userlogin.forgotPassword.captcha.error.message': 'Failed to get the verification code!',
  'userlogin.forgotPassword.password': 'Password',
  'userlogin.forgotPassword.password.compareToFirstPassword':
    'Does not match the last password, please confirm!',
  'userlogin.forgotPassword.password.tooltip':
    'The password must meet the following requirements: It cannot contain the username, and at least contains three of the following four types of characters: English uppercase letters (A-Z) English lowercase letters (a-z) Numbers (0-9) Special characters (such as: @,#,￥,%)',
  'userlogin.forgotPassword.password.required': 'This field cannot be empty!',
  'userlogin.forgotPassword.password.minLength':
    'The password cannot be less than {minimumLength}!',
  'userlogin.forgotPassword.password.pattern':
    'Password complexity does not meet the requirements!',
  'userlogin.forgotPassword.confirm': 'Confirm password',
  'userlogin.forgotPassword.confirm.tooltip':
    'Please make sure that the new password is the same twice!',
  'userlogin.forgotPassword.confirm.required': 'This field cannot be empty!',
  'userlogin.forgotPassword.submit': 'Submit',
  'userlogin.forgotPassword.cancel': 'Return',
  'userlogin.forgotPassword.submit.success.message': 'Password changed successfully!',
  'userlogin.forgotPassword.submit.success.description':
    'In order to avoid your account cannot be used normally, please use the new password to log in to the platform.',
  'userlogin.forgotPassword.submit.success.goToLogin': 'Return to login page',
  'userlogin.forgotPassword.submit.error.message': 'Password modification failed!',
};
