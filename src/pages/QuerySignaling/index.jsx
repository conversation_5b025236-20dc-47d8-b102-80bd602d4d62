import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, message, Tabs, Row, Col, Select, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import styles from './index.less';
const { TabPane } = Tabs;

const Index = (props) => {

    const { form, form: { getFieldDecorator } } = props;
    // 列表加载状态
    const [tableLoading, setTableLoading] = useState(false);
    // 列表数据
    const [tableData, setTableData] = useState({
        list: [],
        total: 0
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
    });
    // 查询条件
    const [searchParams, setSearchParams] = useState({});
    // 表格配置项
    const allColumns = {
        1: [
            {
                title: '号码',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: 'IMEI',
                dataIndex: 'noticeType1',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '分类',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '时间',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '区域',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '基站id',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '基站名称',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '经度',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '维度',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
        ],
        2: [
            {
                title: '主叫号码',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '被叫号码',
                dataIndex: 'noticeType1',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '对端运营商',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '对端归属地',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },

            {
                title: '通话时间',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '时长',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '通话地区',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '基站id',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,

            },
            {
                title: '基站名称',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
            {
                title: '话单类型',
                dataIndex: 'noticeType2',
                align: 'center',
                ellipsis: true,
                width: 180,
            },

        ],
        3:[
            {
                title: '号码',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            }, 
            {
                title: '日期',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            }, 
            {
                title: '号码',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },  {
                title: '单日呼出总数',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            }, {
                title: '单日呼入次数',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日通话总次数',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日主叫占比',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日呼出总时长',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日平均接通率',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日平均通话时长',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日离散度',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日长途通话占比',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日主叫40秒内占比',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },{
                title: '单日主叫120秒以上占比',
                dataIndex: 'noticeType',
                align: 'center',
                ellipsis: true,
                width: 180,
            },
        ]
    };

    const [columns, setColumns] = useState(allColumns['1']); 
    // 列表查询
    const handleSearch = (current = 1, pageSize = 10, searchParams) => {
        const formData = searchParams || form.getFieldsValue() || {};
        const params = {
            // ...defaultParams,
            ...formData,
            current: current,
            pageSize: pageSize,
        };
        setTableLoading(true);
        request('', {
            method: '',
            data: {},
        }).then((res) => {
            if (res.code == 200) {
                setTableData({
                    list: res.data?.list || [],
                    total: res.data?.total || 0
                });
                setPagination({
                    current: current,
                    pageSize: pageSize
                });
                setSearchParams({
                    // ...defaultParams,
                    ...formData
                })
            } else {
                message.error(res.message);
                setTableData({
                    list: [],
                    total: 0
                });
                setPagination({
                    current: 1,
                    pageSize: 10
                })
            }
        }).finally(() => { setTableLoading(false); })
    };
    // 重置列表查询
    const handleReset = () => {
        form.resetFields();
        handleSearch();
    };
    // 列表改变分页
    const handleTableChange = (p) => {
        handleSearch(p.current, p.pageSize, searchParams);
    };

    const [tabKey, setTabKey] = useState('1');

    const onTabsChange = (key) => {
        setTabKey(key)
        setColumns(allColumns[key]);
    }


    return (
        <Card>
            <Form className={styles.myStylesForm}>
                <Row gutter={24}>
                    <Col span={6}>
                        <Form.Item label='查询类型'>
                            {getFieldDecorator('type',
                                { initialValue: undefined }
                            )(
                                <Select placeholder='请选择类型' allowClear style={{ width: '100%' }} >
                                    {
                                        ['号码', 'IMEI'].map((v, index) => (
                                            <Select.Option value={v} key={index + 1}>{v}</Select.Option>
                                        ))
                                    }
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={4}>
                        <Form.Item label=''>
                            {getFieldDecorator('type1',
                                { initialValue: undefined }
                            )(
                                <Input placeholder='请输入查询条件' />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={9}>
                        <Form.Item label='分析时间'>
                            {getFieldDecorator('input1',
                                { initialValue: undefined }
                            )(
                                <DatePicker.RangePicker showTime allowClear format={'YYYY-MM-DD HH:mm:ss'} style={{ width: '100%' }} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={5} style={{ textAlign: 'right' }}>
                        <Button type='primary' onClick={() => { handleSearch() }}>查询</Button>
                        <Button onClick={() => { handleReset() }} style={{ marginLeft: 12 }}>重置</Button>
                    </Col>
                </Row>
            </Form>
            <div>
                <Tabs activeKey={tabKey} onChange={onTabsChange} type="card">
                    {
                        ['位置信息', '通话信息', '特征分析'].map((v, index) => {
                            return (
                                <TabPane tab={v} key={index + 1}>
                                    {tabKey==index+1&&<StandardTable
                                        key={index+1}
                                        data={{
                                            list: tableData.list,
                                            pagination: {
                                                total: tableData.total,
                                                current: pagination.current,
                                                pageSize: pagination.pageSize,
                                            }
                                        }}   
                                        columns={
                                            columns 
                                        }
                                        onChange={handleTableChange}
                                        rowSelectionProps={null}
                                        showSelectCount={false}
                                        rowKey='id'
                                        loading={tableLoading}
                                    />}
                                </TabPane>
                            )
                        })
                    }
                </Tabs>

            </div>
        </Card>
    )

};

export default Form.create()(Index);