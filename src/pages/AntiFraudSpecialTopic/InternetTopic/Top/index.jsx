import React, { useState, useEffect, memo } from 'react';
import { Row, Col, message } from 'antd';
import request from 'ponshine-request';

import OverviewItem from '@/components/OverviewItem';

import one from './imgs/one.png';
import two from './imgs/two.png';
import three from './imgs/three.png';
import four from './imgs/four.png';
const Index = ({ dateValue }) => {
  const dataList = [
    {
      key: 'abroadVisitCount',
      title: '境外涉诈IP(APP)被访问量',
      img: one,
    },
    {
      key: 'domesticVisitCount',
      title: '国内涉诈IP(APP)被访问量',
      img: two,
    },
    {
      key: 'provinceVisitCount',
      title: '省内涉诈IP(APP)被访问量',
      img: three,
    },
    {
      key: 'provincePhoneVisitCount',
      title: '省内号码访问涉诈IP(APP)量',
      img: four,
    },
  ];
  const [data, setData] = useState({});

  const findData = async () => {
    const response = await request('/api/hn/internetVisit/getVisitCount', {
      method: 'POST',
      requestType: 'form',
      data: dateValue,
    });

    if (response.code === 200) {
      setData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && findData();
  }, [dateValue]);

  return (
    <Row gutter={[16, 16]}>
      {dataList?.map((ele) => (
        <Col span={6} key={ele.key}>
          <OverviewItem {...ele} value={data?.[ele.key]} />
        </Col>
      ))}
    </Row>
  );
};

export default memo(Index);
