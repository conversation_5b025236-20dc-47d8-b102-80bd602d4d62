import React from 'react';
import { Row, Col } from 'antd';
import CommonCard from '@/components/CommonCard';

import One from './One';
import BarTop from './BarTop';
import Four from './Four';

export default function index({ dateValue }) {
  return (
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <One dateValue={dateValue} />
      </Col>
      <Col span={12}>
        <CommonCard title="省内号码访问涉诈IP(APP)地区TOP5">
          <BarTop type="areaTop" dateValue={dateValue} />
        </CommonCard>
      </Col>
      <Col span={12}>
        <CommonCard title="省内涉诈IP(APP)地区被访问量TOP5">
          <BarTop type="cityTop" dateValue={dateValue} />
        </CommonCard>
      </Col>
      <Col span={12}>
        <Four dateValue={dateValue} />
      </Col>
    </Row>
  );
}
