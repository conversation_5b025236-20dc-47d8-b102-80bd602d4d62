import React, { useState, useEffect } from 'react';
import BarTopChart from '@/components/Chart/BarTopChart';
import request from 'ponshine-request';

export default function index({ dateValue, type }) {
  const commonBarChartPorps = {
    legendData: [],
    height: 220,
    width: '100%',
    isMutipleYAxisIndex: false,
    seriesConfig: { barWidth: 10 },
    gridOption: { left: 120, top: 20, bottom: 50 },
    axisLabelOption: { width: 100, rotate: 0 },
  };
  const [loading, setLoading] = useState(false);

  const [chartData, setChartData] = useState([]);
  const getData = async () => {
    setLoading(true);
    const response = await request('/api/hn/internetVisit/getTopFive', {
      method: 'POST',
      data: {
        ...dateValue,
        type,
      },
      requestType: 'form',
    });

    setLoading(false);
    if (response.code === 200) {
      setChartData(response?.data?.reverse() || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue]);
  return (
    <BarTopChart
      {...commonBarChartPorps}
      xData={chartData?.map((ele) => ele?.xvalue)}
      yData={[{ data: chartData?.map((ele) => ele?.yvalue) }]}
      loading={loading}
    />
  );
}
