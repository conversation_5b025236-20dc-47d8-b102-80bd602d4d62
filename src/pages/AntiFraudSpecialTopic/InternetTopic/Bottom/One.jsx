import React, { useState, useEffect } from 'react';
import Ring<PERSON><PERSON><PERSON>hart from '@/components/Chart/RingPieChart';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '@/components/CommonCard';

export default function AccessNetNum({ dateValue }) {
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async (date) => {
    setLoading(true);
    const response = await request(`/api/hn/internetVisit/getWebTypePieChart`, {
      method: 'POST',
      requestType: 'form',
      data: dateValue,
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue]);

  return (
    <CommonCard title="省内涉诈IP(APP)访问类型占比">
      <Ring<PERSON>ie<PERSON><PERSON>
        data={listData}
        height={220}
        loading={loading}
        legendOption={{ left: '60%' }}
        labelLength={10}
      />
    </CommonCard>
  );
}
