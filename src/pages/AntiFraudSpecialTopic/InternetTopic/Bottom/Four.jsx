import React, { useState, useEffect } from 'react';
import LineChart from '@/components/Chart/LineChart';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '@/components/CommonCard';

export default function AccessNetNum({ dateValue }) {
  const legendData = [
    {
      name: '境外访问量',
      key: 'abroadVisitCount',
    },
    {
      name: '国内访问量',
      key: 'domesticVisitCount',
    },
    {
      name: '省内访问量',
      key: 'provinceVisitCount',
    },
  ];
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async (date) => {
    setLoading(true);
    const response = await request(`/api/hn/internetVisit/getInternetVisitCountTrend`, {
      method: 'POST',
      requestType: 'form',
      data: dateValue,
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data?.reverse() || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue]);

  return (
    <CommonCard title="涉诈IP(APP)被访问量趋势">
      <LineChart
        xData={listData?.map((ele) => ele?.countDate)}
        yData={legendData?.map((ele) => {
          return {
            name: ele.name,
            data: listData?.map((itme) => itme[ele.key]),
          };
        })}
        legendData={legendData?.map((ele) => ele.name)}
        height={220}
        width="100%"
        loading={loading}
        isMutipleYAxisIndex={false}
        axisLabelOption={{ width: 80 }}
        gridOption={{ left: 60 }}
      />
    </CommonCard>
  );
}
