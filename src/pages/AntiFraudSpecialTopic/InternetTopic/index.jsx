import React, { useEffect, useRef, useState } from 'react';
import { Card, Form, DatePicker, Button } from 'antd';
import Top from './Top';
import Bottom from './Bottom';
import Center from './Center';

import moment from 'moment';

const Index = ({ form: { getFieldDecorator, setFieldsValue, getFieldValue } }) => {
  const initDate = moment().subtract(1, 'day');
  const initDateValue = {
    countDate: initDate.format('YYYY-MM-DD'),
  };
  const [dateValue, setDateValue] = useState(initDateValue);
  const centerRef = useRef();

  const handleSearch = () => {
    const timeValue = getFieldValue('time');
    setDateValue({
      countDate: timeValue?.format('YYYY-MM-DD'),
    });
  };
  const handleReset = () => {
    setFieldsValue({
      time: initDate,
    });
    centerRef.current.resetRadioValue();
    setDateValue(initDateValue);
  };

  const disabledDate = (current) => {
    return current && current >= moment().endOf('day');
  };

  return (
    <Card>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="时间范围">
          {getFieldDecorator('time', {
            initialValue: initDate,
          })(<DatePicker disabledDate={disabledDate} allowClear={false} />)}
        </Form.Item>
        <Form.Item>
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
            查询
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Form.Item>
      </Form>
      <Top dateValue={dateValue} />
      <Center dateValue={dateValue} ref={centerRef} />
      <Bottom dateValue={dateValue} />
    </Card>
  );
};
export default Form.create()(Index);
