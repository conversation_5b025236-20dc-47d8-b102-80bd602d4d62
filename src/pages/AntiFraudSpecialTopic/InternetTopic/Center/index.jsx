import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Card, Radio, Spin } from 'antd';
import request from 'ponshine-request';
import MapChart from './MapChart';

const Index = forwardRef(({ dateValue }, ref) => {
  const data = [
    {
      name: '长沙市',
      value: 30000,
    },
    {
      name: '株洲市',
      value: 30000,
    },
    {
      name: '益阳市',
      value: 2000,
    },
    {
      name: '郴州市',
      value: 30000,
    },
    {
      name: '张家界市',
      value: 30000,
    },
    {
      name: '湘潭市',
      value: 3000,
    },
    {
      name: '衡阳市',
      value: 24550,
    },
    {
      name: '邵阳市',
      value: 50000,
    },
    {
      name: '岳阳市',
      value: 30000,
    },
    {
      name: '常德市',
      value: 30000,
    },
    {
      name: '永州市',
      value: 30000,
    },
    {
      name: '怀化市',
      value: 50000,
    },
    {
      name: '娄底市',
      value: 50000,
    },
    {
      name: '湘西土家族苗族自治州',
      value: 50000,
    },
  ];

  const [loading, setLoading] = useState(false);
  const [radioValue, setRadioValue] = useState('user');

  const [mapData, setMapData] = useState([]);

  useImperativeHandle(ref, () => ({
    resetRadioValue: () => {
      setRadioValue('user');
    },
  }));
  const getData = async () => {
    setLoading(true);
    const response = await request('/api/hn/internetVisit/getVisitCityDistribution', {
      method: 'POST',
      data: {
        ...dateValue,
        type: radioValue,
      },
      requestType: 'form',
    });

    setLoading(false);
    if (response.code === 200) {
      setMapData(
        response?.data?.map((ele) => {
          return {
            name: ele?.xvalue?.includes('湘西') ? '湘西土家族苗族自治州' : ele.xvalue + '市',
            value: ele.yvalue,
          };
        }) || [],
      );
    } else {
      message.error(response.message);
    }
  };

  const handleChange = (v) => {
    setRadioValue(v.target.value);
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue, radioValue]);

  return (
    <Spin spinning={loading}>
      <Card style={{ margin: '16px 0' }}>
        <Radio.Group
          defaultValue="a"
          buttonStyle="solid"
          onChange={handleChange}
          value={radioValue}
        >
          <Radio.Button value="user">访问涉诈IP(APP)用户分布(省内)</Radio.Button>
          <Radio.Button value="app">涉诈IP(APP)所在地分布(省内)</Radio.Button>
        </Radio.Group>

        <MapChart data={mapData} />
      </Card>
    </Spin>
  );
});

export default Index;
