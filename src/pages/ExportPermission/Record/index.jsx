/*
 * @Author: zxw
 * @Date: 2023-08-10 10:33:56
 * @LastEditors: zxw
 * @LastEditTime: 2023-10-12 14:57:23
 * @FilePath: \newHunanfanzha\src\pages\ExportPermission\Record\index.jsx
 * @Description:
 */
import React, { useMemo, useState, useEffect, Fragment } from 'react';
import { Card, Form, Row, Col, Select, Input, Button, Tooltip, message, DatePicker } from 'antd';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { exportFile } from '@/utils/utils';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const stateList = ['待审批', '已审批', '已驳回'];

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ currentPage: pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas();
  }, []);

  const handleSearch = () => {
    const startFormatStr = 'YYYYMMDD000000';
    const endFormatStr = 'YYYYMMDD235959';
    const values = getFieldsValue();
    const { applyTime, approveTime } = values;
    getListDatas({
      ...values,
      approvalStartTime: approveTime?.[0]?.format(startFormatStr),
      approvalEndTime: approveTime?.[1]?.format(endFormatStr),
      applicantStartTime: applyTime?.[0]?.format(startFormatStr),
      applicantEndTime: applyTime?.[1]?.format(endFormatStr),
      applyTime: undefined,
      approveTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas();
  };

  const [exportLoading, setExportLoading] = useState(false);

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/hn/hnFraudExportAuth/exportHnFraudExportAuthQuery',
      decode: true,
      params: serachParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  let columns = [
    {
      title: '申请导出模块',
      width: 100,
      dataIndex: 'moduleName',

      ellipsis: true,
      align: 'center',
    },
    {
      title: '申请人',
      width: 100,
      dataIndex: 'applicant',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请时间',
      width: 140,
      dataIndex: 'applicantTime',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请原因',
      width: 100,
      dataIndex: 'applicantReason',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'state',
      render: (v) => {
        const text = v ? stateList?.[Number(v) - 1] : '--';
        return (
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        );
      },
      align: 'center',
      ellipsis: true,
    },
    {
      title: '待审批人',
      width: 100,
      dataIndex: 'waitApprover',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '审批人',
      width: 100,
      dataIndex: 'approver',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '审批时间',
      width: 140,
      dataIndex: 'approvalTime',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '审批意见',
      width: 100,
      dataIndex: 'approvalOpinions',
      align: 'center',
      ellipsis: true,
    },
    // {
    //   title: '生效截止到期时间',
    //   width: 140,
    //   dataIndex: 'deadline',

    //   align: 'center',
    //   ellipsis: true,
    // },
  ];

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="申请导出模块">
              {getFieldDecorator('moduleName')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="状态">
              {getFieldDecorator('state')(
                <Select placeholder="请选择">
                  {stateList?.map((ele, index) => (
                    <Select.Option value={index + 1} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="申请人">
              {getFieldDecorator('applicant')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="待审批人">
              {getFieldDecorator('waitApprover')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="审批人">
              {getFieldDecorator('approver')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="申请时间">
              {getFieldDecorator('applyTime')(
                <RangePicker allowClear={true} placeholder="请输入" format="YYYY-MM-DD" />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="审批时间">
              {getFieldDecorator('approveTime')(
                <RangePicker allowClear={true} placeholder="请输入" format="YYYY-MM-DD" />,
              )}
            </Form.Item>
          </Col>

          <Col align="right" span={6}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
              重置
            </Button>
            <Button
              type=""
              loading={exportLoading}
              onClick={handleExport}
              disabled={!listData?.list?.length}
            >
              导出
            </Button>
          </Col>
        </Form>
      </Row>
      <StandardTable
        columns={columns}
        loading={loading}
        data={listData}
        rowKey="id"
        showSelectCount={false}
        rowSelectionProps={false}
        // scroll={{ x: true }}
        onChange={handleTableChange}
      />
    </Card>
  );
};
export default Form.create({})(Index);
