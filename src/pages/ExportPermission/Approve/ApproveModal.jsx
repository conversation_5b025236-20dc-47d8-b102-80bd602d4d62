/*
 * @Author: zxw
 * @Date: 2023-08-08 15:12:13
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-14 11:25:08
 * @FilePath: \newHunanfanzha\src\pages\ExportPermission\Approve\ApproveModal.jsx
 * @Description:
 */
import React, { useState } from 'react';
import { Form, Modal, Row, Col, Input, Avatar, Upload, Button, Icon, message, Select } from 'antd';
import { approve } from './services';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 16,
  },
};

const ApproveModal = ({ visible, currentRow, form, cancel, reload, selectedRows }) => {
  const { getFieldDecorator, resetFields, getFieldsValue, validateFields, setFieldsValue } = form;
  const [addLoading, setAddLoading] = useState(false);

  const handleOk = async () => {
    validateFields(async (err, values) => {
      if (err) return;
      setAddLoading(true);
      let response = await approve({ ...values, ids: selectedRows?.map((ele) => ele.id) });
      setAddLoading(false);

      if (response && response.code === 200) {
        message.success(response.message);
        reload();
      } else {
        message.error(response.message);
      }
    });
  };

  const handleChangeResult = (v) => {
    setFieldsValue({ approvalOpinions: v || '' });
  };

  return (
    <Modal
      title={`导出申请审批`}
      visible={visible}
      afterClose={() => {
        resetFields();
      }}
      maskClosable={false}
      onOk={handleOk}
      onCancel={cancel}
      confirmLoading={addLoading}
    >
      <Row gutter={[16, 16]}>
        <Form {...formItemLayout}>
          <Form.Item label="审批结果">
            {getFieldDecorator('approvalResults', {
              rules: [
                { required: true, message: '请选择审批结果' },
                { max: 200, message: '最多输入200个字符' },
              ],
            })(
              <Select allowClear placeholder="请选择" onChange={handleChangeResult}>
                {['审批通过', '审批驳回']?.map((ele, index) => (
                  <Select.Option value={ele} key={index}>
                    {ele}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="审批意见">
            {getFieldDecorator('approvalOpinions', {
              rules: [
                { required: true, message: '请输入审批意见' },
                { max: 200, message: '最多输入200个字符' },
              ],
            })(<TextArea allowClear={true} placeholder="请输入" rows={3} />)}
          </Form.Item>
        </Form>
      </Row>
    </Modal>
  );
};
export default Form.create()(ApproveModal);
