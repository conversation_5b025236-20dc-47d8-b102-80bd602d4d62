/*
 * @Author: zxw
 * @Date: 2023-08-10 10:33:56
 * @LastEditors: zxw
 * @LastEditTime: 2023-10-12 14:52:16
 * @FilePath: \newHunanfanzha\src\pages\ExportPermission\Approve\index.jsx
 * @Description:
 */
import React, { useMemo, useState, useEffect, Fragment } from 'react';
import { Card, Form, Row, Col, Select, Input, Button, Tooltip, message, DatePicker } from 'antd';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { exportFile } from '@/utils/utils';

import ApproveModal from './ApproveModal';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);
  const [approveVisible, setApproveVisible] = useState(false);

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ currentPage: pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas();
  }, []);

  const handleSearch = () => {
    const startFormatStr = 'YYYYMMDD000000';
    const endFormatStr = 'YYYYMMDD235959';
    const values = getFieldsValue();
    const { applyTime, approveTime } = values;
    getListDatas({
      ...values,
      applicantStartTime: applyTime?.[0]?.format(startFormatStr),
      applicantEndTime: applyTime?.[1]?.format(endFormatStr),
      applyTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas();
    setSelectedRows([]);
  };

  const [exportLoading, setExportLoading] = useState(false)

  const handleExport = () => {
    setExportLoading(true)
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/hnFraudExportAuth/exportHnFraudExportAuthApprove',
      params: { ...serachParams },
      decode: true,
      callback:()=>{
        setExportLoading(false)
      }
    });
  };

  let columns = [
    {
      title: '申请导出模块',
      width: 100,
      dataIndex: 'moduleName',
      align: 'center',
    },
    {
      title: '申请人',
      width: 100,
      dataIndex: 'applicant',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请时间',
      width: 140,
      dataIndex: 'applicantTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请原因',
      width: 100,
      dataIndex: 'applicantReason',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '待审批人',
      width: 100,
      dataIndex: 'waitApprover',
      align: 'center',
      ellipsis: true,
    },
  ];

  const handleApprove = () => {
    if (!selectedRows?.length) {
      return message.info('请勾选要审批的数据');
    }
    setApproveVisible(true);
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="申请导出模块">
              {getFieldDecorator('moduleName')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="申请人">
              {getFieldDecorator('applicant')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="申请时间">
              {getFieldDecorator('applyTime')(
                <RangePicker allowClear={true} placeholder="请输入" format="YYYY-MM-DD" />,
              )}
            </Form.Item>
          </Col>

          <Col align="right" span={6}>
            <Form.Item wrapperCol={{ span: 24 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
                重置
              </Button>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleApprove}>
                审批
              </Button>
              <Button type="" loading={exportLoading} onClick={handleExport} disabled={!listData?.list?.length}>
                导出
              </Button>
            </Form.Item>
          </Col>
        </Form>
      </Row>
      <StandardTable
        columns={columns}
        loading={loading}
        data={listData}
        rowKey="id"
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        // scroll={{ x: true }}
        onChange={handleTableChange}
      />
      <ApproveModal
        visible={approveVisible}
        cancel={() => {
          setApproveVisible(false);
        }}
        reload={() => {
          setApproveVisible(false);
          onReset();
        }}
        selectedRows={selectedRows}
      />
    </Card>
  );
};
export default Form.create({})(Index);
