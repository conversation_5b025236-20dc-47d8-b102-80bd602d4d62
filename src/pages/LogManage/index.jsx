import React, { useState } from 'react';
import ProTable from 'demasia-pro-table';
import { Tooltip, withSilence } from 'demasia-pro-layout';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { Card, DatePicker, Button } from 'antd';
import moment from 'moment';
import { getLoggings } from './services';
const { RangePicker } = DatePicker;
import { exportFile } from '@/utils/utils';

const LogManage = () => {
  const [searchParams, setSearchParams] = useState({});

  const request = async (rawParams, sort) => {
    const { current, pageSize, occurTime, ...other } = rawParams;
    const sortKeys = Object.keys(sort);
    const params = {
      ...other,
      page: current - 1,
      size: pageSize,
      sort:
        sortKeys.length === 0
          ? rawParams.sort
          : sortKeys.map((key) => `${key},${sort[key].replace('end', '')}`),
      startTime: occurTime?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endTime: occurTime?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
    };
    const response = await getLoggings(params);
    setSearchParams(params);
    return Promise.resolve({
      data: response.content,
      success: true,
      total: response.totalElements,
    });
  };

  const columns = [
    // {
    //   width: 200,
    //   ellipsis: true,
    //   align: 'center',
    //   title: formatMessage({
    //     id: 'logmanage.columns.occurTime',
    //   }),
    //   dataIndex: 'occurTime',
    //   valueType: 'dateTime',
    //   order: 2,
    //   sorter: true,
    //   renderFormItem: (item, { value, onChange }) => {
    //     return (
    //       <RangePicker
    //         style={{
    //           width: '100%',
    //         }}
    //         showTime={{
    //           defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
    //         }}
    //         format="YYYY-MM-DD HH:mm"
    //         value={value}
    //         onChange={onChange}
    //       />
    //     );
    //   },
    // },
    // {
    //   ellipsis: true,
    //   align: 'center',
    //   title: formatMessage({
    //     id: 'logmanage.columns.type',
    //   }),
    //   dataIndex: 'type',
    // },
    // {
    //   ellipsis: true,
    //   align: 'center',
    //   title: formatMessage({
    //     id: 'logmanage.columns.detail',
    //   }),
    //   dataIndex: 'detail',
    //   hideInSearch: true,
    // },
    // {
    //   ellipsis: true,
    //   align: 'center',
    //   title: formatMessage({
    //     id: 'logmanage.columns.principal',
    //   }),
    //   dataIndex: 'principal',
    //   order: 1,
    // },
    // {
    //   ellipsis: true,
    //   align: 'center',
    //   title: formatMessage({
    //     id: 'logmanage.columns.remoteAddress',
    //   }),
    //   dataIndex: 'remoteAddress',
    // },
    // {
    //   ellipsis: true,
    //   align: 'center',
    //   title: formatMessage({
    //     id: 'logmanage.columns.requestUri',
    //   }),
    //   dataIndex: 'requestUri',
    // },
    {
      ellipsis: true,
      align: 'center',
      title: '操作类型',
      dataIndex: 'type',
      render: (text) => text?.props?.title || '/',
    },
    {
      ellipsis: true,
      align: 'center',
      title: 'IP',
      dataIndex: 'remoteAddress',
      hideInSearch: true,
      render: (text) => text?.props?.title || '/',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '操作事项',
      dataIndex: 'detail',
      hideInSearch: true,
      render: (text) => text?.props?.title || '/',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '操作数据条数',
      dataIndex: 'operateDataCount',
      hideInSearch: true,
      render: (text) => (text?.props?.title === null ? '/' : text?.props?.title),
    },
    {
      ellipsis: true,
      align: 'center',
      title: '操作数据类型',
      dataIndex: 'operateDataType',
      hideInSearch: true,
      render: (text) => {
        return (
          <div
            style={{
              width: '200px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
            title={text?.props?.title || '/'}
          >
            <Tooltip title={text?.props?.title || '/'} placement="topLeft">
              <span>{text?.props?.title || '/'}</span>
            </Tooltip>
          </div>
        );
      },
    },
    {
      align: 'center',
      title: '导出条件',
      dataIndex: 'exportCondition',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
      render: (text) => {
        return (
          <div
            style={{
              width: '200px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
            title={text?.props?.title || '/'}
          >
            <Tooltip title={text?.props?.title || '/'} placement="topLeft">
              <span>{text?.props?.title || '/'}</span>
            </Tooltip>
          </div>
        );
      },
    },
    {
      ellipsis: true,
      align: 'center',
      title: '操作时间',
      dataIndex: 'occurTime',
      render: (text) => text?.props?.title || '/',
      renderFormItem: (item, { value, onChange }) => {
        return (
          <RangePicker
            style={{
              width: '100%',
            }}
            // showTime
            format="YYYY-MM-DD HH:mm:ss"
            value={value}
            onChange={onChange}
          />
        );
      },
    },
    {
      ellipsis: true,
      align: 'center',
      title: '操作人',
      dataIndex: 'principal',
      render: (text) => text?.props?.title || '/',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '操作人工号',
      dataIndex: 'staffId',
      hideInSearch: true,
      render: (text) => text?.props?.title || '/',
    },
  ];

  const [exportLoading, setExportLoading] = useState(false);

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/audit/export',
      decode: true,
      params: searchParams,
      method: 'POST',
      requestType: 'form',
      callback:()=>{
        setExportLoading(false);
      }
    });
  };

  return (
    <Card>
      <ProTable
        columns={columns}
        request={request}
        rowKey="id"
        pagination={{
          defaultCurrent: 1,
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ['5', '10', '15', '20'],
          showTotal: (total, [start, end]) =>
            formatMessage(
              {
                id: 'logmanage.showTotal',
              },
              {
                start,
                end,
                total,
              },
            ),
        }}
        scroll={{
          x: 'max-content',
        }}
        toolBarRender={() => [
          <Button type="primary" onClick={handleExport} loading={exportLoading}>
            导出
          </Button>,
        ]}
      />
    </Card>
  );
};

export default withSilence(LogManage);
