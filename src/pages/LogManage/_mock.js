let globalMock = null;

try {
  delete require.cache[require.resolve('../../mock')]; // eslint-disable-next-line global-require

  globalMock = require('../../mock'); // eslint-disable-next-line no-empty
} catch (e) {}

export default {
  ...globalMock,
  'POST /api/audit/list': {
    content: [
      {
        id: 1,
        occurTime: '2020-08-20T06:38:49.000+00:00',
        type: '鉴权失败',
        detail: '不允许访问',
        principal: 'anonymousUser',
        remoteAddress: '0:0:0:0:0:0:0:1',
        requestUri: '/api/imgCode1',
      },
      {
        id: 2,
        occurTime: '2020-08-20T06:39:01.000+00:00',
        type: '登录成功',
        detail: null,
        principal: null,
        remoteAddress: '*************',
        requestUri: '/api/user/login',
      },
      {
        id: 3,
        occurTime: '2020-08-20T06:39:01.000+00:00',
        type: null,
        detail: null,
        principal: null,
        remoteAddress: '*************',
        requestUri: '/api/user/login',
      },
      {
        id: 4,
        occurTime: '2020-08-20T06:39:01.000+00:00',
        type: null,
        detail: null,
        principal: 'admin',
        remoteAddress: '*************',
        requestUri: '/api/user/login',
      },
      {
        id: 5,
        occurTime: '2020-08-20T06:51:33.000+00:00',
        type: '登录成功',
        detail: null,
        principal: null,
        remoteAddress: '0:0:0:0:0:0:0:1',
        requestUri: '/api/user/login',
      },
      {
        id: 6,
        occurTime: '2020-08-20T06:51:33.000+00:00',
        type: null,
        detail: null,
        principal: null,
        remoteAddress: '0:0:0:0:0:0:0:1',
        requestUri: '/api/user/login',
      },
      {
        id: 7,
        occurTime: '2020-08-20T06:51:33.000+00:00',
        type: null,
        detail: null,
        principal: 'admin',
        remoteAddress: '0:0:0:0:0:0:0:1',
        requestUri: '/api/user/login',
      },
      {
        id: 8,
        occurTime: '2020-08-20T06:53:15.000+00:00',
        type: '鉴权失败',
        detail: '不允许访问',
        principal: 'anonymousUser',
        remoteAddress: '0:0:0:0:0:0:0:1',
        requestUri: '/api/audit/list',
      },
      {
        id: 9,
        occurTime: '2020-08-20T07:17:28.000+00:00',
        type: '登录成功',
        detail: null,
        principal: null,
        remoteAddress: '0:0:0:0:0:0:0:1',
        requestUri: '/api/user/login',
      },
      {
        id: 10,
        occurTime: '2020-08-20T07:17:28.000+00:00',
        type: null,
        detail: null,
        principal: null,
        remoteAddress: '0:0:0:0:0:0:0:1',
        requestUri: '/api/user/login',
      },
    ],
    pageable: {
      sort: {
        sorted: false,
        unsorted: true,
        empty: true,
      },
      offset: 0,
      pageSize: 10,
      pageNumber: 0,
      unpaged: false,
      paged: true,
    },
    totalElements: 187,
    totalPages: 19,
    last: false,
    number: 0,
    size: 10,
    sort: {
      sorted: false,
      unsorted: true,
      empty: true,
    },
    numberOfElements: 10,
    first: true,
    empty: false,
  },
};
