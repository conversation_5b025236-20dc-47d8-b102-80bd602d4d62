import React, { useMemo, useState } from 'react';
import { Card, Form, DatePicker, Input, Button, message, notification } from 'antd';
import request from 'ponshine-request';
import { useAuth, refreshAuth } from 'ponshine';
import moment from 'moment';
// 省级角色延长不超过3个月，4方角色不超过6个月，其他用户最长不超过1年
const dateConfig = {
  sj: {
    type: 'month',
    value: 3,
  },
  sf: {
    type: 'month',
    value: 6,
  },
  other: {
    type: 'year',
    value: 1,
  },
};

const Index = ({ form: { getFieldDecorator, validateFields } }) => {
  const {
    authState: {
      user: {
        id: userId,
        ifFourthEmp,
        role: { id: roleId },
      },
    },
  } = useAuth() || {};

  const [loading, setLoading] = useState(false);

  const handleOk = () => {
    validateFields(async (err, values) => {
      if (err) return;
      setLoading(true);
      const response = await request('/api/hn/userAccountDelay/submitDelayApproval', {
        method: 'POST',
        requestType: 'form',
        data: {
          userId,
          ...values,
          delayTime: values?.delayTime?.format('YYYY-MM-DD 23:59:59'),
        },
      });
      setLoading(false);
      if (response.code === 200) {
        // 关闭所有通知
        notification.destroy();
        message.success(response.message);
        refreshAuth();
      } else {
        message.error(response.message);
      }
    });
  };

  //  省级角色延长不超过3个月，4方角色不超过6个月，其他用户最长不超过1年，
  //    判断getSessionValues中的role.id 在不在 省级角色id  12
  // 4方角色通过判断getSessionValues中的ifFourthEmp字段，1就是，其他不是
  const currentRole = useMemo(() => {
    if ([12]?.includes(roleId)) {
      return 'sj';
    }
    if (ifFourthEmp == 1) {
      return 'sf';
    }
    return 'other';
  }, [roleId]);

  const disabledDate = (current) => {
    return (
      current < moment().startOf('day') ||
      current > moment().add(dateConfig?.[currentRole]?.value, dateConfig?.[currentRole]?.type)
    );
  };
  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 8 }}>
        <Form.Item label="延期时间">
          {getFieldDecorator('delayTime', {
            rules: [
              {
                required: true,
                message: '请选择延期时间',
              },
            ],
          })(<DatePicker format={'YYYY-MM-DD'} disabledDate={disabledDate} />)}
        </Form.Item>
        <Form.Item label="延期原因">
          {getFieldDecorator('delayReason', {
            rules: [
              {
                required: true,
                message: '请输入延期原因',
              },
            ],
          })(<Input.TextArea allowClear placeholder="请输入" />)}
        </Form.Item>
        <div style={{ textAlign: 'center' }}>
          <Button type="primary" style={{ marginLeft: 24 }} onClick={handleOk} loading={loading}>
            确定
          </Button>
        </div>
      </Form>
    </Card>
  );
};
export default Form.create()(Index);
