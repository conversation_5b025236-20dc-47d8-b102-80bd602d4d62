/*
 * @Author: zxw
 * @Date: 2023-09-04 15:21:40
 * @LastEditors: zxw
 * @LastEditTime: 2023-09-08 17:05:12
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\ShutdownApprove\ApproveModal.js
 * @Description:
 */
import { Modal, Form, Select, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';

const { TextArea } = Input;

import { approve } from './services';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const approveType = [
  { value: '1', label: '审批通过' },
  { value: '2', label: '审批驳回' },
];

const ApproveModal = Form.create()(
  ({
    visible,
    cancel,
    form: { validateFields, getFieldDecorator, resetFields, setFieldsValue },
    reload,
    selectedRows,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);

    const handleOk = () => {
      validateFields(async (err, fieldsValue) => {
        if (err) return;
        setConfirmLoading(true);
        const response = await approve({
          ...fieldsValue,
          ids: selectedRows?.map((ele) => ele.id)?.join(','),
        });
        setConfirmLoading(false);

        if (response.code === 200) {
          message.success(response.message);
          reload();
          cancel();
        } else {
          message.error(response.message);
        }
      });
    };

    const handleChange = (value) => {
      if (!value) return;
      setFieldsValue({ approvalRemark: approveType?.find((ele) => ele.value === value)?.label });
    };

    useEffect(() => {
      if (!visible) {
        confirmLoading && setConfirmLoading(false);
      }
    }, [visible]);

    return (
      <Modal
        title="关停审批"
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        confirmLoading={confirmLoading}
        maskClosable={false}
      >
        <Form {...formItemLayout}>
          <Form.Item label="审批结果">
            {getFieldDecorator('approvalResult', {
              rules: [
                {
                  required: true,
                  message: '请选择审批结果',
                },
              ],
            })(
              <Select placeholder="请选择" onChange={handleChange}>
                {approveType.map((ele) => (
                  <Select.Option value={ele.value} key={ele.value}>
                    {ele.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="审批意见">
            {getFieldDecorator('approvalRemark', {
              rules: [
                {
                  required: true,
                  message: '请输入审批意见',
                },
                {
                  max: 200,
                  message: '最多输入200个字符',
                },
              ],
            })(<TextArea placeholder="请输入" allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default ApproveModal;
