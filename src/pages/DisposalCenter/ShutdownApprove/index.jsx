/*
 * @Author: zxw
 * @Date: 2023-09-04 13:22:18
 * @LastEditors: zxw
 * @LastEditTime: 2023-09-12 13:53:47
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\ShutdownApprove\index.jsx
 * @Description:
 */

import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Descriptions,
} from 'antd';
import moment from 'moment';
import { Licensee } from 'ponshine';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { exportFile } from '@/utils/utils';

import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import ApproveModal from './ApproveModal';
import CascadeFormItem from '../components/CascadeFormItem';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const pageSizeOptions = ['10', '20', '30', '50', '200'];

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 200,
      pageSizeOptions,
    },
  });
  const [loading, setLoading] = useState(false);
  const [approveVisible, setApproveVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [searchList, setSearchList] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);
  const [expand, setExpand] = useState(false);
  const localNetworkRef = useRef();

  const stateList = [
    '待市公司审批',
    '待省公司审批',
    '待省公司三级管理员（支撑）审批',
    '待省公司总管理员通过',
    '审批通过',
    '审批驳回',
  ];

  // 获取表格数据
  const getListDatas = async ({ pageNum = 1, pageSize = 200, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
          pageSizeOptions,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  // 搜索
  const handleSearch = () => {
    const values = getFieldsValue();
    const { optTime, approveTime } = values;
    getListDatas({
      ...values,
      beginApplicationTime: optTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endApplicationTime: optTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      handleTimeStart: approveTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      handleTimeEnd: approveTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      optTime: undefined,
      approveTime: undefined,
    });
  };

  // 分頁
  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  // 重置
  const onReset = () => {
    form.resetFields();

    getListDatas({
      localNetwork: localNetworkRef.current.getInitialValue(),
    });
    setSelectedRows([]);
  };

  // 导出
  const handleExport = () => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/shutdownResumeApproval/exportShutdownApproval',
      params: { ...searchParams, pageSize: 1, pageNum: 1 },
      // decode: true,
      title: '关停审批',
      mime: 'xlsx',
    });
  };

  // 切换展开收起
  const handleToggle = () => {
    setExpand(!expand);
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  let columns = [
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '关停类型',
      width: 100,
      dataIndex: 'shutdownType',
      ellipsis: true,
    },
    {
      title: '涉诈类型',
      dataIndex: 'fraudType',
      width: 100,

      ellipsis: true,
    },
    {
      title: '关停标签',
      dataIndex: 'shutdownTag',
      width: 100,
      ellipsis: true,
    },
    {
      title: '子标签',
      dataIndex: 'subtag',
      width: 100,
      ellipsis: true,
    },
    {
      title: '涉诈性质',
      width: 100,
      dataIndex: 'fraudNature',
      ellipsis: true,
    },
    {
      title: '备注',
      width: 80,
      dataIndex: 'remark',
      forceShow: true,
      ellipsis: true,
    },
    {
      title: '是否关联关停',
      width: 100,
      dataIndex: 'relatedShutdown',
      ellipsis: true,
      render: (v) => {
        return v == null ? '--' : v === '1' ? '是' : '否';
      },
    },
    {
      title: '关联关停类型',
      width: 100,
      dataIndex: 'relatedShutdownType',
      ellipsis: true,
    },
    {
      title: '复通号码',
      width: 100,
      dataIndex: 'reOpenPhoneNum',
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '操作人',
      width: 100,
      dataIndex: 'applicantName',
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 180,
      dataIndex: 'applicationTime',
      ellipsis: true,
    },
  ];

  const handleApprove = async () => {
    if (!selectedRows?.length) return message.error('至少勾选一个待审批项');
    setApproveVisible(true);
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 7 }} wrapperCol={{ span: 17 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作时间">
              {getFieldDecorator('optTime')(
                <RangePicker format={'YYYY-MM-DD'} style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem form={form} getListDatas={getListDatas} cref={localNetworkRef} />
          </Col>
          <Col span={6}>
            <Form.Item label="关停类型">
              {getFieldDecorator('shutdownType')(
                <Select placeholder="请选择" allowClear>
                  {['语音短信单停', '非实名单停', '非实名双停']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <div style={{ display: expand ? 'block' : 'none' }}>
            <CascadeFormItem form={form} colSpanList={[6, 6, 6]} />

            <Col span={6}>
              <Form.Item label="操作人">
                {getFieldDecorator('applicantName')(
                  <Input allowClear={true} placeholder="请输入" />,
                )}
              </Form.Item>
            </Col>
          </div>

          <Col align="right" span={24} style={{ marginBottom: 16 }}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
              重置
            </Button>
            {/* <Licensee license="outbound_export_all"> */}
            <Button type="primary" onClick={handleApprove} style={{ marginRight: 10 }}>
              审批
            </Button>
            {/* </Licensee> */}

            {/* <Licensee license="outbound_export_all"> */}
            <Button type="primary" onClick={handleExport} disabled={!listData?.list?.length}>
              批量导出
            </Button>
            {/* </Licensee> */}

            <a style={{ marginLeft: 8, fontSize: 12 }} onClick={handleToggle}>
              {expand ? '收起' : '展开'} <Icon type={expand ? 'up' : 'down'} />
            </a>
          </Col>
        </Form>
      </Row>

      <StandardTable
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        isNeedAutoWidth={true}
        scroll={{
          x: 1000,
          y: 500,
        }}
      />
      <ApproveModal
        visible={approveVisible}
        reload={onReset}
        cancel={() => {
          setApproveVisible(false);
        }}
        selectedRows={selectedRows}
      />
    </Card>
  );
};
export default Form.create({})(Index);
