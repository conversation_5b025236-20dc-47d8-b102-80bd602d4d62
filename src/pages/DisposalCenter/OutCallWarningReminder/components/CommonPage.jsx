import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import { Card, Form, Row, Col, DatePicker, Select, Input, Button, message } from 'antd';

import moment from 'moment';

import StandardTable from '@/components/StandardTable';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';

import { exportFile } from '@/utils/utils';
import request from 'ponshine-request';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form, api, extraColumns } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState({});
  const initDate = [moment().subtract(1, 'days'), moment().subtract(1, 'days')];
  const localNetworkRef = useRef();

  const isShow = extraColumns?.some((ele) => ele.title === '是否接通');

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);

    const response = await request(api, {
      method: 'POST',
      requestType: 'json',
      data: {
        pageNum,
        pageSize,
        ...props,
      },
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    const values = getFieldsValue();
    const { time } = values;

    getListDatas({
      ...values,
      time: undefined,
      startTime: time?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endTime: time?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas({
      localNetwork: localNetworkRef.current.getInitialValue(),
      startTime: initDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endTime: initDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  let columns = [
    {
      title: '预警日期',
      width: 140,
      dataIndex: 'warnTime',
      ellipsis: true,
    },
    {
      title: '预警号码',
      dataIndex: 'called',
      width: 140,
      ellipsis: true,
    },
    ...extraColumns,
    {
      title: '关联涉诈号码',
      width: 100,
      dataIndex: 'calling',
      ellipsis: true,
    },
    {
      title: '诈骗类型',
      width: 100,
      dataIndex: 'type',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
  ];

  return (
    <Card>
      <Form wrapperCol={{ span: 16 }} labelCol={{ span: 8 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="预警日期">
              {getFieldDecorator('time', {
                initialValue: initDate,
              })(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem
              form={form}
              getListDatas={getListDatas}
              cref={localNetworkRef}
              otherInitParams={{
                startTime: initDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
                endTime: initDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
              }}
            />
          </Col>
          <Col span={6}>
            <Form.Item label="预警号码">
              {getFieldDecorator('called')(<Input allowClear placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="关联涉诈号码">
              {getFieldDecorator('calling')(<Input allowClear placeholder="请输入" />)}
            </Form.Item>
          </Col>
          {isShow && (
            <Col span={6}>
              <Form.Item label="是否接通">
                {getFieldDecorator('ifConnect')(
                  <Select allowClear placeholder="请选择">
                    <Select.Option value="是">是</Select.Option>
                    <Select.Option value="否">否</Select.Option>
                  </Select>,
                )}
              </Form.Item>
            </Col>
          )}

          <Col span={isShow ? 18 : 24} align="right">
            <Form.Item style={{ marginRight: 0 }} wrapperCol={{ span: 24 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={onReset}>重置</Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </Card>
  );
};
export default Form.create({})(Index);
