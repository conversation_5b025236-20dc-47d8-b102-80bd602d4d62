import React from 'react';
import CommonPage from '../components/CommonPage';
import { Col } from 'antd';

export default function index() {
  const extraColumns = [
    {
      title: '外呼任务建立时间',
      width: 140,
      dataIndex: 'sendTime',
      ellipsis: true,
    },
    {
      title: '外呼任务建立状态',
      dataIndex: 'sendState',
      width: 140,
      ellipsis: true,
      render: (v) => {
        return <span style={{ color: v === '失败' ? 'red' : '' }}>{v}</span>;
      },
    },
    {
      title: '是否接通',
      width: 140,
      dataIndex: 'ifConnect',
      ellipsis: true,
    },
  ];

  return (
    <div>
      <CommonPage
        extraColumns={extraColumns}
        api="/api/hn/outboundWarn/pageOutboundWarningCallData"
      />
    </div>
  );
}
