import React from 'react';
import { Form, Modal, Input, Select } from 'antd';

const { Option } = Select;
const { TextArea } = Input;

const formItemLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 15,
    },
};

const ApprovalModal = (props) => {
    const { visible, form: { getFieldDecorator, validateFields, resetFields, getFieldValue }, hideApproval, handleConfirm } = props

    const onOk = () => {
        validateFields((err, values) => {
            if (err) {
                return;
            }
            handleConfirm(values, () => {
                handleCancle()
            })
        });
    }
    const handleCancle = () => {
        hideApproval()
        resetFields()
    }
    return (
        <Modal
            title='涉案复机审批'
            destroyOnClose
            visible={visible}
            onCancel={handleCancle}
            onOk={onOk}
        >
            <Form {...formItemLayout}>
                <Form.Item label="审批结果">
                    {getFieldDecorator('approvalResult', {
                        rules: [{ required: true, message: '请选择' }],
                        initialValue: '审批通过',
                    })(
                        <Select placeholder="请选择" allowClear>
                            <Option value={'审批通过'}>审批通过</Option>
                            <Option value={'审批驳回'}>审批驳回</Option>
                        </Select>,
                    )}
                </Form.Item>
                <Form.Item label="审批意见">
                    {getFieldDecorator('approvalComments', {
                        rules: [
                            { required: true, message: '请输入审批意见' },
                            { max: 200, message: '输入字数不得超出200字' }
                        ],
                        initialValue: getFieldValue('approvalResult')
                    })(
                        <TextArea rows={3} allowClear placeholder='请输入' />
                    )}
                </Form.Item>
            </Form>
        </Modal>
    )
}

export default Form.create()(ApprovalModal)