import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input } from 'antd';
import { connect } from 'dryad';
import { useEffect, useState } from 'react';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import { Licensee } from 'ponshine';
import ApprovalModal from './ApprovalModal';
import request from '@/utils/request';
import { Tooltip } from 'demasia-pro-layout';

const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultParams = {
  pageNum: 1,
  pageSize: 10,
  status: '待审批',
};
const formItemLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 15,
  },
};

const responsibleUnitList = [
  '长沙本地网',
  '株洲本地网',
  '湘潭本地网',
  '衡阳本地网',
  '邵阳本地网',
  '岳阳本地网',
  '常德本地网',
  '张家界本地网',
  '益阳本地网',
  '郴州本地网',
  '永州本地网',
  '怀化本地网',
  '娄底本地网',
  '湘西本地网',
];

const InvolveFraudCaseApproval = (props) => {
  const {
    form: { getFieldDecorator, validateFields, resetFields },
  } = props;
  const [params, setParams] = useState(defaultParams);
  const [selectedRows, setSelectedRows] = useState([]);
  const [approvalVisible, setApprovalVisible] = useState(false);
  const [data, setData] = useState({});
  const [OrganizationByUser, setOrganizationByUser] = useState([]);
  const [Loading, setLoading] = useState(false);
  const columns = [
    {
      title: '号码',
      dataIndex: 'phoneNum',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '本地网',
      dataIndex: 'localNetwork',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'approveStatus',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '复机原因',
      dataIndex: 'resumeReason',
      width: 130,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '涉诈类型',
      dataIndex: 'fraudType',
      ellipsis: true,
      width: 120,
      align: 'center',
    },
    {
      title: '关停标签',
      ellipsis: true,
      dataIndex: 'shutdownTag',
      width: 120,
      align: 'center',
    },
    {
      title: '子标签',
      dataIndex: 'subtag',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '涉诈性质',
      dataIndex: 'fraudNature',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '关停备注',
      dataIndex: 'shutdownRemark',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请人',
      dataIndex: 'applicantName',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'applicationTime',
      width: 160,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '审批人',
      dataIndex: 'approverName',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '审批时间',
      dataIndex: 'approvalTime',
      width: 160,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '审批意见',
      dataIndex: 'approvalComments',
      width: 130,
      align: 'center',
      ellipsis: true,
    },
  ];

  const handleExport = () => {
    validateFields((err, { months, ...rest }) => {
      const newParams = {
        pageNum: data?.current || 1,
        pageSize: data?.pageSize || 10,
        applicationTimeStart:
          months && months.length > 0 ? moment(months[0]).format('YYYY-MM-DD') : '',
        applicationTimeEnd:
          months && months.length > 0 ? moment(months[1]).format('YYYY-MM-DD') : '',
        ...rest,
      };
      exportFile({
        urlAPi: '/api/hn/resumeApprove/exportResumeApproval',
        decode: true,
        params: newParams,
        method: 'POST',
      });
    });
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  //查询
  const handleSearch = (pagination) => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      setLoading(true);
      const newParams = {
        pageNum: pagination?.current || 1,
        pageSize: pagination?.pageSize || 10,
        applicationTimeStart:
          months && months.length > 0 ? moment(months[0]).format('YYYY-MM-DD') : '',
        applicationTimeEnd:
          months && months.length > 0 ? moment(months[1]).format('YYYY-MM-DD') : '',
        ...rest,
      };

      request(`/api/hn/resumeApprove/pageResumeApproval`, {
        method: 'POST',
        data: newParams,
        requestType: 'json',
      }).then((res) => {
        setLoading(false);
        let obj = {
          current: pagination?.current || 1,
          pageSize: pagination?.pageSize || 10,
          total: res?.data?.totalNum || 0,
          list: res?.data?.items || [],
        };
        setData(obj);
      });
    });
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    handleSearch(pagination);
  };

  const handleReset = () => {
    resetFields();
    handleSearch();
  };

  const showApproval = () => {
    setApprovalVisible(true);
  };
  const hideApproval = () => {
    setApprovalVisible(false);
  };
  const handleConfirm = (values, callback) => {
    const newParams = {
      idList: selectedRows.map((item) => item.id),
      ...values,
    };
    request(`/api/hn/resumeApprove/batchApprove`, {
      method: 'POST',
      data: newParams,
      requestType: 'json',
    }).then((res) => {
      if (res?.code == 200) {
        setSelectedRows([]);
        handleSearch();
        callback();
        message.success(res?.message);
      } else {
        message.error(res?.message);
      }
    });
  };
  const getOrganizationByUser = () => {
    request(`/api/hn/systemConfig/getOrganizationByUser`, {
      method: 'get',
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setOrganizationByUser(
          (res?.data || []).map((item) => {
            return item?.name;
          }),
        );
      }
    });
  };
  useEffect(() => {
    handleSearch();
    getOrganizationByUser();
  }, []);
  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input allowClear placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="本地网">
              {getFieldDecorator('localNetwork')(
                <Select placeholder="请选择" allowClear>
                  {OrganizationByUser?.map((item, index) => (
                    <Option value={item} key={index}>
                      {item}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="状态">
              {getFieldDecorator('approveStatus', {
                initialValue: params.status,
              })(
                <Select placeholder="请选择" allowClear>
                  <Option value={''}>全部</Option>
                  <Option value={'待审批'}>待审批</Option>
                  <Option value={'审批通过'}>审批通过</Option>
                  <Option value={'审批驳回'}>审批驳回</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="申请时间">
              {getFieldDecorator('months')(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="申请复机人">
              {getFieldDecorator('applicantName')(<Input allowClear placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={8} style={{ marginTop: 3 }}>
            <Licensee license="involveFraudCaseApproval_pageQuery">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ margin: '0px 10px' }}>
                重置
              </Button>
            </Licensee>
            <Licensee license="involveFraudCaseApproval_export">
              <Button type="primary" onClick={handleExport} style={{ marginRight: 10 }}>
                批量导出
              </Button>
            </Licensee>
            <Licensee license="involveFraudCaseApproval_batchApproval">
              <Button type="primary" onClick={showApproval} disabled={selectedRows.length === 0}>
                审批
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        loading={Loading}
        data={{
          list: data?.list || [],
          pagination: {
            current: data?.current || 1,
            pageSize: data?.pageSize || 10,
            total: data?.total || 0,
          },
        }}
        onChange={handlePaginationTable}
        rowKey="id"
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        // isNeedAutoWidth={true}
        // scroll={{ x: columns?.length * 200 }}
      />
      {approvalVisible && (
        <ApprovalModal
          visible={approvalVisible}
          hideApproval={hideApproval}
          handleConfirm={handleConfirm}
        />
      )}
    </Card>
  );
};

export default Form.create()(InvolveFraudCaseApproval);
