import React from 'react';
import { Tabs, Card } from 'antd';
import FraudPreventionManage from './FraudPreventionManage';
import ModelShutdownFullRecord from './ModelShutdownFullRecord';
import { Licensee } from 'ponshine';
import NoAuth from '@/pages/NoAuth';

const { TabPane } = Tabs;

export default function index() {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab="防欺诈关停管理" key="1">
          <FraudPreventionManage />
        </TabPane>
        <TabPane tab="模型关停全量记录" key="2">
          <Licensee license="fraudPreventionManage_modelShutdownRecord" fallback={<NoAuth />}>
            <ModelShutdownFullRecord />
          </Licensee>
        </TabPane>
      </Tabs>
    </Card>
  );
}
