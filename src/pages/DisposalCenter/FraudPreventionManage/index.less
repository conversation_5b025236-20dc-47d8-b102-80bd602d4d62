.defineFormItemBox{
  display: flex;
  .formTitle{
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    --antd-wave-shadow-color: #1890ff;
    --swiper-theme-color: #007aff;
    --swiper-navigation-size: 44px;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: 'tnum';
    line-height: 39.9999px;
    white-space: nowrap;
    text-align: right;
    touch-action: manipulation;
    box-sizing: border-box;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85)!important;
    position: relative;
    align-items: center;
    margin-right: 6px;
  }
  .formTitle::before {
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}

.myStyleForm{
  :global{
      .ant-form-item{
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
      }
      .ant-form-item-label{
          width: 160px;
          flex-shrink: 0;
      }
      .ant-form-item-control-wrapper{
          flex: 1;
          .ant-form-item-control{
              width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
          }
      }
  }
}
