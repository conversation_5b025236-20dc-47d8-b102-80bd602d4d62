import React, { useState, useEffect } from 'react';
import { connect } from 'dryad';
import { Modal, Input, message, Row, Col, Form, Select, Checkbox } from 'antd';
import ForcedReplayApprove from './ForcedReplayApprove';

import request from 'ponshine-request';
const onUserReuse = Form.create()((props) => {
  const { dispatch, visible, setSingleUserVisible, form, netWorkList } = props;
  const { getFieldDecorator } = form;

  const [confirmLoading, setConfirmLoading] = useState(false);
  const [forcedReplayApproveVisible, setForcedReplayApproveVisible] = useState(false);
  const [response402, setResponse402] = useState({});

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      setConfirmLoading(false);
    }
  }, [visible]);

  const beforeSubmitCheck = async (params) => {
    setConfirmLoading(true);
    const response = await request('/api/hn/shutdown/checkRelatedResume', {
      method: 'POST',
      data: { ...params, ifRelatedResume: params?.ifRelatedResume ? 1 : 2 },
      requestType: 'form',
    });

    if (response) {
      const { code } = response;
      const nextParams = {
        paramData: response.data,
        phoneNum: params.phoneNum,
        cityId: params.cityId,
      };
      if (code === 200) {
        handleConfirm(params);
      } else if (code === 402) {
        setConfirmLoading(false);
        setResponse402(nextParams);
        setForcedReplayApproveVisible(true);
      } else if (code === 205) {
        handle205Response({ ...nextParams, remark: params.remark });
      } else {
        setConfirmLoading(false);
        message.error(response?.message);
      }
    }
  };
  const handle205Response = async (params) => {
    const response = await request('/api/hn/shutdown/forceRelatedResume', {
      method: 'POST',
      data: params,
      requestType: 'form',
    });
    setConfirmLoading(false);
    if (response.code === 200) {
      setSingleUserVisible();
      message.success(response.message);
    } else {
      message.error(response.message);
    }
  };
  const handleSubmit = () => {
    form.validateFields((errors, values) => {
      if (errors) return;
      beforeSubmitCheck(values);
    });
  };

  const handleConfirm = (values) => {
    dispatch({
      type: 'fraudPreventionManage/forceResumeSinglePhoneNum',
      payload: {
        ...values,
      },
      callback: (res) => {
        setConfirmLoading(false);
        if (res.code == 200) {
          message.success('强制复机成功');
          setSingleUserVisible();
        } else {
          message.error(res.message);
        }
      },
    });
  };

  return (
    <Modal
      title="单个用户强制复机"
      visible={visible}
      destroyOnClose
      onOk={handleSubmit}
      onCancel={() => {
        setSingleUserVisible();
      }}
      width={720}
      confirmLoading={confirmLoading}
      maskClosable={false}
    >
      <Form wrapperCol={{ span: 16 }} labelCol={{ span: 8 }}>
        <Row>
          <Col span={12}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum', {
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: '请输入号码',
                  },
                ],
              })(<Input style={{ width: '100%' }} placeholder="请输入号码" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="本地网">
              {getFieldDecorator('cityId', {
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: '请选择本地网',
                  },
                ],
              })(
                <Select placeholder="请选择" allowClear>
                  {netWorkList.map((item) => (
                    <Select.Option value={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="备注">
              {getFieldDecorator('remark', {
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: '请输入备注',
                  },
                  {
                    max: 200,
                    message: '最多输入200个字符',
                  },
                ],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否关联复机">
              {form.getFieldDecorator('ifRelatedResume')(<Checkbox />)}
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <ForcedReplayApprove
        visible={forcedReplayApproveVisible}
        onCancel={() => {
          setForcedReplayApproveVisible(false);
          setResponse402({});
        }}
        setSingleUserVisible={setSingleUserVisible}
        response402={response402}
      />
    </Modal>
  );
});
export default connect(({ fraudPreventionManage }) => ({ fraudPreventionManage }))(onUserReuse);
