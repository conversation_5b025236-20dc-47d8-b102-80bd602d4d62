import React, { useReducer, useEffect, useState } from 'react';
import { Modal, Descriptions, Badge } from 'antd';
import MessageDetail from './MessageDetail';

const Detail = (props) => {
  const { visible, detail, showDetailModal, columns } = props;
  const newColunms = JSON.parse(JSON.stringify(columns));
  const [detailVisible, setDetailVisible] = useState(false);
  const index = newColunms.findIndex((ele) => ele.title === '复机原因');
  const otherIndex = newColunms.findIndex((ele) => ele.title === '是否关联关停');

  newColunms.splice(index, 0, {
    dataIndex: 'resumeReason',
    title: '关停短信查询',
    width: 75,
    render: () => {
      return <a onClick={handleSelectDetail}>查看明细</a>;
    },
  });

  const handleSelectDetail = () => {
    setDetailVisible(true);
  };

  return (
    <Modal
      width={900}
      title="详情"
      destroyOnClose
      visible={visible}
      onCancel={showDetailModal}
      bodyStyle={{ padding: 0 }}
      footer=""
    >
      <Descriptions bordered size="middle" column={2}>
        {newColunms.map((item, index) => {
          if (index !== newColunms.length - 1) {
            return (
              <Descriptions.Item label={item.title}>
                {item.render ? item.render() : detail[item.dataIndex]}
              </Descriptions.Item>
            );
          }
        })}
      </Descriptions>
      <MessageDetail
        visible={detailVisible}
        onCancel={() => {
          setDetailVisible(false);
        }}
        detail={detail}
      />
    </Modal>
  );
};
export default Detail;
