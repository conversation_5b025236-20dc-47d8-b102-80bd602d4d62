import React, { useState, useEffect } from 'react';
import { Modal, message } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';

export default function MessageDetail({ visible, detail, onCancel }) {
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
  });
  const initColunms = [
    {
      title: '手机号',
      width: 70,
      dataIndex: 'smsMobile',
      ellipsis: true,
    },
    {
      title: '发送短信内容',
      width: 140,
      dataIndex: 'smsContext',
      ellipsis: true,
    },
    {
      title: '发送时间',
      width: 100,
      dataIndex: 'smsSendTime',
      ellipsis: true,
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'smsReportStatus',
      ellipsis: true,
    },
  ];
  const [loading, setLoading] = useState(false);
  const [columns, setColumns] = useState(initColunms);

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/shutdown/queryShutdownMessageDetail', {
      method: 'POST',
      requestType: 'json',
      data: {
        pageNum,
        pageSize,
        phoneNum: detail?.phoneNum,
        importTime: detail?.importTime,
        messageTagDto: {
          shutdownTag: detail?.shutdownTag,
          subtag: detail?.subtag,
        },
      },
    });
    setLoading(false);

    if (response.code === 200) {
      if (response?.data?.[0]?.source === 'group') {
        let newColumns = JSON.parse(JSON.stringify(columns));
        newColumns.splice(3, 0, {
          title: '报告时间',
          width: 100,
          dataIndex: 'smsReportTime',
          ellipsis: true,
        });
        setColumns(newColumns);
      }
      setTableData({
        list: response?.data || [],
        pagination: {
          total: response?.data?.length || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    const {
      list,
      pagination: { total },
    } = tableData;
    const newData = {
      list: list,
      pagination: {
        total: total,
        current,
        pageSize,
      },
    };
    setTableData(newData);
  };
  useEffect(() => {
    if (visible) {
      findTableDataPager();
    } else {
      setTableData({
        list: [],
        pagination: {},
      });
      setColumns(initColunms);
    }
  }, [visible]);
  return (
    <Modal visible={visible} onCancel={onCancel} width={900} footer={null} title="关停短信发送详情">
      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={tableData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
      />
    </Modal>
  );
}
