import React, { useState } from 'react';
import { connect } from 'dryad';
import { Modal, Input, message, Upload, Form, Button, Icon, Alert, Checkbox } from 'antd';
import request from 'ponshine-request';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const fileType =
  '.csv,.xls,.xlsx,.jpg,.png,.bmp,.jpeg,.rar,.zip,.7z,.doc,.docx,.txt,.pdf,.ppt,.pptx';

const ForcedReplayApprove = (props) => {
  const { dispatch, visible, response402 = {}, form, onCancel, setSingleUserVisible } = props;

  const [fileList, setFileList] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const handleSubmit = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const formData = new FormData();

      formData.append('resumeReason', values.resumeReason);
      fileList.forEach((ele) => {
        formData.append('fileArray', ele);
      });
      Object.keys(response402)?.forEach((ele) => {
        formData.append(ele, response402?.[ele]);
      });

      setConfirmLoading(true);
      const response = await request('/api/hn/shutdown/resumeFraudPhone', {
        method: 'POST',
        requestType: 'form',
        data: formData,
      });
      setConfirmLoading(false);

      if (response && response.code === 200) {
        onCancel();
        setSingleUserVisible();
        message.success(response.message);
      } else {
        message.error(response.message);
      }
    });
  };

  const uploadProps = {
    accept: fileType,
    fileList: fileList,
    beforeUpload: (file, fileListArr) => {
      const isLt20M = file.size / 1024 / 1024 <= 20;
      const isTrueType = fileType.includes(file.name?.split('.')[1]);
      const newFileList = [...fileList];
      if (fileList.length >= 50) {
        return message.error('累计文件个数不能超过50个，请知悉');
      }
      if (!isTrueType)
        return message.error(
          '因安全管理要求，仅能上传以下类型文件，表格：csv、xls、xlsx；图片:jpg、png、bmp、jpeg；压缩包：rar、zip、7z；文字：doc、docx、txt、pdf、ppt、pptx。',
        );
      if (!isLt20M) {
        message.error('单个文件最多不能超过20M');
        return false;
      }

      newFileList.push(file);
      setFileList(newFileList);
      return false;
    },
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
  };
  return (
    <Modal
      title="单个用户复机"
      visible={visible}
      destroyOnClose
      onOk={handleSubmit}
      onCancel={onCancel}
      afterClose={() => {
        form.resetFields();
        setFileList([]);
      }}
      maskClosable={false}
      confirmLoading={confirmLoading}
      width={720}
    >
      <Form {...formItemLayout}>
        <Form.Item label="复机原因" style={{ marginBottom: 0 }}>
          {form.getFieldDecorator('resumeReason', {
            rules: [
              { required: true, message: '请输入复机原因' },
              {
                max: 1000,
                message: '最多不能超过1000个字符',
              },
            ],
          })(<TextArea placeholder="请填写" allowClear />)}
        </Form.Item>

        <Form.Item label="附件上传">
          <Upload {...uploadProps}>
            <Button>
              <Icon type="upload" /> 选择文件
            </Button>
          </Upload>
        </Form.Item>
      </Form>
      <Alert
        message={
          <div>
            注：
            <br />
            1.
            涉案号码复机请上传相关附件证明，涉案号码复机会自动进入省公司审批流程，省公司审批通过后方可复机。
            <br />
            2.
            涉诈骗案件复机审批通过后将自动关联复机，审批完成后先复机主卡，10分钟后自动复机副卡，30分钟后根据审批备注要求对主副卡进行自动处置（如审批备注为专班研判中风险，则对所有主副卡打专班研判（中风险）标签）
            <br />
            3.
            如涉及涉案关联号码复机，请直接申请对涉案号码进行复机，涉案号码复机后将自动对关联号码复机;若涉案号码已复机，关联号码未复机可提交涉案号码关联复机。
          </div>
        }
        type="error"
      />
    </Modal>
  );
};

export default Form.create()(ForcedReplayApprove);
