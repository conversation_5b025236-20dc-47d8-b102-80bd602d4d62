import React, { useReducer, useEffect } from 'react';
import { connect } from 'dryad';
import { Form, Modal, Row, Col, Input, Select, InputNumber, message } from 'antd';
import request from '@/utils/request';
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};
const formItemLayout1 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19 },
};
const { Option } = Select;
const { TextArea } = Input;
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const initialState = {};
const OneUserStop = (props) => {
  const [state, setState] = useReducer(reducer, initialState);
  const { visible, showOneUserStopVisible, form, netWorkList = [], dispatch } = props;
  const {
    shutdownTagList = [],
    shutdownSubTagList = [],
    fraudTypeList = [],
    disabled = false,
    shutdownTypeList = [],
    qualitativeList = [],
    relatedShutdownList = [],
    relatedShutdownTypeList = [],
    confirmLoading = false,
  } = state;
  useEffect(() => {
    if (visible) {
      setState({ shutdownTagList: [], shutdownSubTagList: [] });
    }
  }, [visible]);
  useEffect(() => {
    getAllFraudType();
  }, []);
  const getShutdownTagListByFraudType = (fraudType) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownTagListByFraudType',
      payload: { fraudType },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownTagList: res.data || [] });
          return;
        }
        setState({ shutdownTagList: [] });
      },
    });
  };
  const getShutdownSubTagListByShutdownTag = (shutdownTag) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownSubTagListByShutdownTag',
      payload: { shutdownTag },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownSubTagList: res.data || [] });
          return;
        }
        setState({ shutdownSubTagList: [] });
      },
    });
  };
  const relatedShutdownChange = (value) => {
    if (value && value == '否') {
      setState({ disabled: true });
      form.setFieldsValue({ relatedShutdownType: undefined });
    } else {
      setState({ disabled: false });
    }
  };
  const handleSubmit = () => {
    form.validateFieldsAndScroll((err, values) => {
      if (err) return;
      setState({ confirmLoading: true });
      dispatch({
        type: 'fraudPreventionManage/shutdownSinglePhoneNum',
        payload: {
          ...values,
        },
        callback: (res) => {
          setState({ confirmLoading: false });
          if (res.code == '200') {
            message.success('关停成功');
            showOneUserStopVisible();
          } else {
            message.error(res.message);
          }
        },
      });
    });
  };

  // 获取可选值
  const getList = (params) => {
    request('/api/hn/shutdownTag/getShutdownConfigByTag', {
      method: 'POST',
      data: params,
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setState({
          shutdownTypeList: res.data?.shutdownTypeList || [],
          qualitativeList: res.data?.qualitativeList || [],
          relatedShutdownList: res.data?.relatedShutdownList || [],
          relatedShutdownTypeList: res.data?.relatedShutdownTypeList || [],
        });
        form.setFieldsValue({
          shutdownType: res.data?.shutdownTypeList?.[0] || undefined,
          fraudNature: res.data?.qualitativeList?.[0] || undefined,
          relatedShutdown: res.data?.relatedShutdownList?.[0] || undefined,
          relatedShutdownType: res.data?.relatedShutdownTypeList?.[0] || undefined,
        });
        //  if(!res.data?.qualitativeList?.length||!res.data?.relatedShutdownList?.length||!res.data?.shutdownTypeList?.length){
        //    message.warning('选择的标签或子标签配置的停机类型，涉案性质或是否关联关停的枚举值为空，请重新选择标签或子标签');
        //  }
      }
    });
  };
  const getAllFraudType = () => {
    dispatch({
      type: 'fraudPreventionManage/getAllFraudType',
      callback: (res) => {
        if (res && res.code == 200) {
          setState({ fraudTypeList: res.data || [] });
        } else {
          setState({ fraudTypeList: [] });
        }
      },
    });
  };
  return (
    <Modal
      width={800}
      title="单个用户关停"
      destroyOnClose
      visible={visible}
      onCancel={showOneUserStopVisible}
      onOk={handleSubmit}
      confirmLoading={confirmLoading}
      maskClosable={false}
    >
      <Form>
        <Row>
          <Col span={12}>
            <Form.Item label="关停号码" {...formItemLayout}>
              {form.getFieldDecorator('phoneNum', {
                rules: [{ required: true, message: '请填写关停号码' }],
              })(<Input style={{ width: '100%' }} placeholder="请输入号码" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="本地网" {...formItemLayout}>
              {form.getFieldDecorator('localNetwork', {
                rules: [{ required: true, message: '请选择本地网' }],
              })(
                <Select placeholder="请选择" allowClear>
                  {netWorkList.map((item) => (
                    <Option value={item.name}>{item.name}</Option>
                  ))}
                  {/* <Option value="长沙本地网">长沙本地网</Option>
                  <Option value="株洲本地网">株洲本地网</Option>
                  <Option value="湘潭本地网">湘潭本地网</Option>
                  <Option value="衡阳本地网">衡阳本地网</Option>
                  <Option value="邵阳本地网">邵阳本地网</Option>
                  <Option value="岳阳本地网">岳阳本地网</Option>
                  <Option value="常德本地网">常德本地网</Option>
                  <Option value="张家界本地网">张家界本地网</Option>
                  <Option value="益阳本地网">益阳本地网</Option>
                  <Option value="郴州本地网">郴州本地网</Option>
                  <Option value="永州本地网">永州本地网</Option>
                  <Option value="怀化本地网">怀化本地网</Option>
                  <Option value="娄底本地网">娄底本地网</Option>
                  <Option value="湘西本地网">湘西本地网</Option> */}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label="涉诈类型" {...formItemLayout}>
              {form.getFieldDecorator('fraudType', {
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select
                  placeholder="请输入"
                  allowClear
                  onChange={(value) => {
                    setState({ shutdownTagList: [], shutdownSubTagList: [] });
                    form.setFieldsValue({ shutdownTag: undefined, subtag: undefined });
                    if (value) getShutdownTagListByFraudType(value);
                  }}
                >
                  {fraudTypeList.map((item) => (
                    <Option value={item}>{item}</Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="关停标签名称" {...formItemLayout}>
              {form.getFieldDecorator('shutdownTag', {
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select
                  placeholder="请输入"
                  allowClear
                  onChange={(value) => {
                    setState({ shutdownSubTagList: [] });
                    form.setFieldsValue({ subtag: undefined });
                    if (value) {
                      getShutdownSubTagListByShutdownTag(value);
                      getList({ fraudType: form.getFieldValue('fraudType'), shutdownTag: value });
                    }
                  }}
                >
                  {shutdownTagList.map((item) => (
                    <Option value={item}>{item}</Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="子标签名称" {...formItemLayout}>
              {form.getFieldDecorator('subtag')(
                <Select
                  placeholder="请输入"
                  allowClear
                  onChange={(value) => {
                    // if(value){
                    getList({
                      fraudType: form.getFieldValue('fraudType'),
                      shutdownTag: form.getFieldValue('shutdownTag'),
                      subtagName: value,
                    });
                    // }
                  }}
                >
                  {shutdownSubTagList.map((item) => (
                    <Option value={item}>{item}</Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="停机类型" {...formItemLayout}>
              {form.getFieldDecorator('shutdownType', {
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select placeholder="请输入" allowClear>
                  {shutdownTypeList?.map((v) => {
                    return <Option value={v}> {v}</Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="涉诈性质" {...formItemLayout}>
              {form.getFieldDecorator('fraudNature', {
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select placeholder="请选择" allowClear>
                  {qualitativeList?.map((v) => {
                    return <Option value={v}> {v}</Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否关联关停" {...formItemLayout}>
              {form.getFieldDecorator('relatedShutdown', {
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select placeholder="请选择" allowClear onChange={relatedShutdownChange}>
                  {relatedShutdownList?.map((v) => {
                    return <Option value={v}> {v}</Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="关联关停类型" {...formItemLayout}>
              {form.getFieldDecorator('relatedShutdownType', {
                rules: [
                  {
                    validator: (rule, value, callback) => {
                      if (form.getFieldsValue().relatedShutdown == '是' && !value) {
                        callback('请选择关联关停类型');
                      }
                      callback();
                    },
                  },
                ],
              })(
                <Select placeholder="请选择" allowClear disabled={disabled}>
                  {relatedShutdownTypeList?.map((v) => {
                    return <Option value={v}> {v}</Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="复通号码" {...formItemLayout}>
              {form.getFieldDecorator('reOpenPhoneNum')(<Input placeholder="请填写" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否关停白名单" {...formItemLayout}>
              {form.getFieldDecorator('ifShutdownWhenWhite', {
                initialValue: '0',
                rules: [
                  {
                    required: true,
                    message: '请选择是否关停白名单',
                  },
                ],
              })(
                <Select placeholder="请选择" allowClear>
                  <Select.Option value={'1'}>是</Select.Option>
                  <Select.Option value={'0'}>否</Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="关停备注" {...formItemLayout1}>
              {form.getFieldDecorator('shutdownRemark', {
                rules: [{ required: true, message: '请填写' }],
              })(
                <TextArea
                  autoSize={{ minRows: '3', maxRows: '100' }}
                  placeholder="请填写"
                  allowClear
                />,
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};
export default connect(({ fraudPreventionManage }) => ({ fraudPreventionManage }))(
  Form.create()(OneUserStop),
);
