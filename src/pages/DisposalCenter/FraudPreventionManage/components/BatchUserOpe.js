import React, { useReducer, useEffect, useState } from 'react';
import { Modal, Upload, message, Button, Icon, Form, Row, Col, Input, Select } from 'antd';
import { connect } from 'dryad';
import request from '@/utils/request';
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};
const formItemLayout1 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19 },
};
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}

const initialState = {};
const BatchUserStopModal = (props) => {
  const [state, setState] = useReducer(reducer, initialState);
  const {
    visible,
    showBatchUserOpeVisible,
    type,
    form,
    netWorkList,
    relatedShutdownChange,
    dispatch,
  } = props;
  const [fileList, setFileList] = useState([]);
  const [okLoading, setOkLoading] = useState(false);
  useEffect(() => {
    if (!visible) {
      setFileList([]);
    }
  }, [visible]);
  useEffect(() => {
    getAllFraudType();
  }, []);
  const uploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      const size = file.size / 1024 / 1024;
      if (tag === '.xlsx') {
        setFileList([file]);
        return false;
      }
      message.warning('文件类型错误');
    },
    fileList,
  };
  const templateDownload = () => {
    let url = '/api/template/getTemplate',
      filename = '复机批量导入模板.xlsx',
      data = { templateCode: 'batchResume' };
    if (type === 'stop') {
      filename = '停机批量导入模板.xlsx';
      url = '/api/hn/shutdown/downLoadTemplate';
      data = {};
    }
    return new Promise((resolve, reject) => {
      request
        .get(url, {
          responseType: 'blob',
          useCSRFToken: false,
          getResponse: true,
          params: data,
        })
        .then(({ data, response = {} }) => {
          const name = filename;
          if (data) {
            if ('download' in document.createElement('a')) {
              // 非IE下载
              const elink = document.createElement('a');
              elink.download = name;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(data);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(data, name);
            }
          } else {
            reject();
            return;
          }
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  };

  const handleApprove = async (formData) => {
    let url = '/api/hn/shutdown/checkResumeCount';
    if (type === 'stop') {
      url = '/api/hn/shutdown/checkShutdownCount';
    }
    setOkLoading(true);
    await request(url, {
      method: 'POST',
      data: formData,
      requestType: 'json',
    }).then((res) => {
      if (res.code === 200) {
        const { ifPop, popMessage, approvalType, approvalStatus, cityId } = res.data;
        formData.append('approvalType', approvalType);
        formData.append('approvalStatus', approvalStatus);
        formData.append('cityId', cityId);
        if (ifPop) {
          Modal.confirm({
            title: '关停审批',
            content: popMessage,
            onOk: () => {
              submitConfirm(formData);
            },
          });
        } else {
          submitConfirm(formData);
        }
      } else {
        setOkLoading(false);
        message.error(res.message);
      }
    });
  };

  const submitConfirm = async (formData) => {
    let url = '/api/hn/shutdown/batchResumeWithApproval';
    if (type === 'stop') {
      url = '/api/hn/shutdown/batchShutdownWithApproval';
    }
    // setOkLoading(true);
    try {
      const response = await request(url, { method: 'POST', data: formData });
      setOkLoading(false);
      showBatchUserOpeVisible();
      if (!response) {
        message.success('导入成功');
      } else {
        if (response && response.code) {
          if (response.code == 200) {
            message.success(response.message);
          } else if (response.code == 401) {
            Modal.confirm({
              title: '导出错误文件',
              content: response.message,
              okText: '确定',
              cancelText: '取消',
              onOk: () => {
                let filename = `批量用户复机错误文件导出.xlsx`;
                url = '/api/hn/shutdown/downLoadBatchResumeErrorFile';
                if (type == 'stop') {
                  url = '/api/hn/shutdown/downLoadBatchShutdownErrorFileNew';
                  filename = `批量用户关停错误文件导出.xlsx`;
                }
                commDownLoadErrFile({ url, filename });
              },
            });
          } else {
            message.error(response.message);
          }
          // return;
        }
      }
    } catch (e) {}
  };

  const handleSubmit = async () => {
    if (fileList.length === 0) return message.warning('请选择上传文件！');

    form.validateFields(async (error, values) => {
      if (error) return;
      const formData = new FormData();
      formData.append('file', fileList[0]);
      for (let key in values) {
        if (values[key]) {
          formData.append(key, values[key]);
        }
      }
      handleApprove(formData);
    });
  };
  const commDownLoadErrFile = ({ filename = '', url = '' }) => {
    request
      .get(url, {
        responseType: 'blob',
        getResponse: true,
      })
      .then((res) => {
        // 处理返回的文件流
        if (res.data) {
          const blob = res.data;
          if ('download' in document.createElement('a')) {
            // 非IE下载
            const elink = document.createElement('a');
            elink.download = filename;
            elink.style.display = 'none';
            elink.href = URL.createObjectURL(blob);
            document.body.appendChild(elink);
            elink.click();
            URL.revokeObjectURL(elink.href); // 释放URL对象
            document.body.removeChild(elink);
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, fileName);
          }
        } else {
          message.warning('下载报告失败');
        }
      });
  };
  const [shutdownTagList, setshutdownTagList] = useState([]);
  const [shutdownSubTagList, setshutdownSubTagList] = useState([]);
  const getShutdownTagListByFraudType = (fraudType) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownTagListByFraudType',
      payload: { fraudType },
      callback: (res) => {
        if (res && res.code == '200') {
          setshutdownTagList(res.data || []);
          return;
        }
        setshutdownTagList([]);
      },
    });
  };

  const getShutdownSubTagListByShutdownTag = (shutdownTag) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownSubTagListByShutdownTag',
      payload: { shutdownTag },
      callback: (res) => {
        if (res && res.code == '200') {
          setshutdownSubTagList(res.data || []);
          return;
        }
        setshutdownSubTagList([]);
      },
    });
  };

  // 获取可选值
  const getList = (params) => {
    request('/api/hn/shutdownTag/getShutdownConfigByTag', {
      method: 'POST',
      data: params,
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setState({
          shutdownTypeList: res.data?.shutdownTypeList || [],
          qualitativeList: res.data?.qualitativeList || [],
          relatedShutdownList: res.data?.relatedShutdownList || [],
          relatedShutdownTypeList: res.data?.relatedShutdownTypeList || [],
        });
        form.setFieldsValue({
          shutdownType: res.data?.shutdownTypeList?.[0] || undefined,
          fraudNature: res.data?.qualitativeList?.[0] || undefined,
          relatedShutdown: res.data?.relatedShutdownList?.[0] || undefined,
          relatedShutdownType: res.data?.relatedShutdownTypeList?.[0] || undefined,
        });
        //  if(!res.data?.qualitativeList?.length||!res.data?.relatedShutdownList?.length||!res.data?.shutdownTypeList?.length){
        //    message.warning('选择的标签或子标签配置的停机类型，涉案性质或是否关联关停的枚举值为空，请重新选择标签或子标签');
        //  }
      }
    });
  };
  const {
    disabled = false,
    shutdownTypeList = [],
    qualitativeList = [],
    relatedShutdownList = [],
    relatedShutdownTypeList = [],
    fraudTypeList = [],
  } = state;
  const getAllFraudType = () => {
    dispatch({
      type: 'fraudPreventionManage/getAllFraudType',
      callback: (res) => {
        if (res && res.code == 200) {
          setState({ fraudTypeList: res.data || [] });
        } else {
          setState({ fraudTypeList: [] });
        }
      },
    });
  };
  return (
    <Modal
      width={800}
      title={type == 'stop' ? '批量用户关停' : '批量用户复机'}
      destroyOnClose
      visible={visible}
      onCancel={showBatchUserOpeVisible}
      onOk={handleSubmit}
      okButtonProps={{
        loading: okLoading,
      }}
      maskClosable={false}
    >
      <div>
        {type == 'stop' ? (
          <Form>
            <Row>
              <Col span={12}>
                <Form.Item label="涉诈类型" {...formItemLayout}>
                  {form.getFieldDecorator('fraudType', {
                    rules: [{ required: true, message: '请选择' }],
                  })(
                    <Select
                      placeholder="请输入"
                      allowClear
                      onChange={(value) => {
                        setState({ shutdownTagList: [], shutdownSubTagList: [] });
                        form.setFieldsValue({ shutdownTag: undefined, subtag: undefined });
                        if (value) getShutdownTagListByFraudType(value);
                      }}
                    >
                      {fraudTypeList.map((item) => (
                        <Option value={item}>{item}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="关停标签名称" {...formItemLayout}>
                  {form.getFieldDecorator('shutdownTag', {
                    rules: [{ required: true, message: '请选择' }],
                  })(
                    <Select
                      placeholder="请输入"
                      allowClear
                      onChange={(value) => {
                        setState({ shutdownSubTagList: [] });
                        form.setFieldsValue({ subtag: undefined });
                        if (value) {
                          getShutdownSubTagListByShutdownTag(value);
                          getList({
                            fraudType: form.getFieldValue('fraudType'),
                            shutdownTag: value,
                          });
                        }
                      }}
                    >
                      {shutdownTagList.map((item) => (
                        <Option value={item}>{item}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="子标签名称" {...formItemLayout}>
                  {form.getFieldDecorator('subtag')(
                    <Select
                      placeholder="请输入"
                      allowClear
                      onChange={(value) => {
                        // if(value){
                        getList({
                          fraudType: form.getFieldValue('fraudType'),
                          shutdownTag: form.getFieldValue('shutdownTag'),
                          subtagName: value,
                        });
                        // }
                      }}
                    >
                      {shutdownSubTagList.map((item) => (
                        <Option value={item}>{item}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="停机类型" {...formItemLayout}>
                  {form.getFieldDecorator('shutdownType', {
                    rules: [{ required: true, message: '请选择' }],
                  })(
                    <Select placeholder="请输入" allowClear>
                      {shutdownTypeList?.map((v) => {
                        return <Option value={v}> {v}</Option>;
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="涉诈性质" {...formItemLayout}>
                  {form.getFieldDecorator('fraudNature', {
                    rules: [{ required: true, message: '请选择' }],
                  })(
                    <Select placeholder="请选择" allowClear>
                      {qualitativeList?.map((v) => {
                        return <Option value={v}> {v}</Option>;
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="是否关联关停" {...formItemLayout}>
                  {form.getFieldDecorator('relatedShutdown', {
                    rules: [{ required: true, message: '请选择' }],
                  })(
                    <Select
                      placeholder="请选择"
                      allowClear
                      onChange={(value) => {
                        if (value == '否') {
                          form.setFieldsValue({
                            relatedShutdownType: undefined,
                          });
                        }
                        // relatedShutdownChange(value);
                      }}
                    >
                      {relatedShutdownList?.map((v) => {
                        return <Option value={v}> {v}</Option>;
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="关联关停类型" {...formItemLayout}>
                  {form.getFieldDecorator('relatedShutdownType', {
                    rules: [
                      {
                        validator: (rule, value, callback) => {
                          if (form.getFieldsValue().relatedShutdown == '是' && !value) {
                            callback('请选择关联关停类型');
                          }
                          callback();
                        },
                      },
                    ],
                  })(
                    <Select
                      placeholder="请选择"
                      allowClear
                      disabled={disabled || form.getFieldValue('relatedShutdown') == '否'}
                    >
                      {relatedShutdownTypeList?.map((v) => {
                        return <Option value={v}> {v}</Option>;
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="是否关停白名单" {...formItemLayout}>
                  {form.getFieldDecorator('ifShutdownWhenWhite', {
                    initialValue: '0',
                    rules: [
                      {
                        required: true,
                        message: '请选择是否关停白名单',
                      },
                    ],
                  })(
                    <Select placeholder="请选择" allowClear>
                      <Select.Option value={'1'}>是</Select.Option>
                      <Select.Option value={'0'}>否</Select.Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Form>
        ) : (
          <></>
        )}

        <div style={{ marginLeft: 31 }}>
          <Upload {...uploadProps} accept=".xlsx">
            <Button>
              <Icon type="upload" /> 选择文件
            </Button>
            {uploadProps.fileList.length === 0 ? <span>未选择任何文件</span> : ''}
          </Upload>
          <div style={{ margin: '16px 0' }}>*每个文件不超过500条号码</div>
          <div>
            <a onClick={templateDownload}>模板下载</a>
          </div>
        </div>
      </div>
    </Modal>
  );
};
export default connect(({ fraudPreventionManage }) => ({ fraudPreventionManage }))(
  Form.create()(BatchUserStopModal),
);
