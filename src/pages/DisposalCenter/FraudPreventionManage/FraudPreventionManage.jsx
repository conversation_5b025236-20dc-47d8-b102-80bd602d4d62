import React, { useReducer, useEffect, Fragment, useState } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Input,
  Select,
  Button,
  Tooltip,
  DatePicker,
  Icon,
  InputNumber,
  message,
  Modal,
  Upload,
} from 'antd';
import { onEnterPage } from '@/utils/openTab';
import StandardTable from '@/components/StandardTable';
import { connect } from 'dryad';
import request from '@/utils/request';
import DetailModal from './components/Deatil';
import OneUserStopModal from './components/OneUserStop';
import BatchUserOpeModal from './components/BatchUserOpe';
import OneUserReuseModal from './components/OneUserReuse';
import moment from 'moment';
import SingleUser from './components/SingleUser';
import { exportFile } from '@/utils/utils';
import { Licensee, useLicensee } from 'ponshine';
import ExportApprove from '@/components/ExportApprove';
import { v4 as uuidv4 } from 'uuid';

const defaultPagination = {
  pageSize: 10,
  current: 1,
};
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout1 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const initialState = {};
const { Option } = Select;
const { RangePicker } = DatePicker;

const FraudPreventionManage = (props) => {
  const {
    form,
    form: { getFieldDecorator, getFieldsValue, resetFields },
    dispatch,
    loading,
  } = props;
  const [state, setState] = useReducer(reducer, initialState);
  const {
    dataSource = [],
    total = 0,
    expand = false,
    fraudTypeList = [],
    shutdownTagList = [],
    shutdownSubTagList = [],
    netWorkList = [],
    searchValue = {},
    pagination = {},
    detail = {},
    detailVisible = false,
    oneUserStopVisible = false,
    batchUserOpeVisible = false,
    OneUserReuseVisible = false,
    singleUserModal = false,
    batchQueryVisible = false,
    batchQueryFile = undefined,
    ifBatchQuery = false,
    cityAdmin = false,
    provinceAdmin = false,
    type = '',
    isShowFJButton = false,
  } = state;
  const detailColumns = [
    { dataIndex: 'phoneNum', title: '号码', width: 60 },
    { dataIndex: 'phoneCardNum', title: '手机号卡', width: 75 },
    { dataIndex: 'responsibleUnit', title: '责任单位', width: 75 },
    { dataIndex: 'localNetwork', title: '本地网', width: 65 },
    { dataIndex: 'businessArea', title: '营业区', width: 65 },
    // { dataIndex: 'masterCard', title: '是否主副卡', width: 75 },
    { dataIndex: 'realTimeStatus', title: '实时状态', width: 75 },
    { dataIndex: 'infoSource', title: '信息来源', width: 75 },
    { dataIndex: 'shutdownType', title: '停机类型', width: 75 },
    { dataIndex: 'fraudType', title: '涉诈类型', width: 75 },
    { dataIndex: 'shutdownTag', title: '关停标签', width: 75 },
    { dataIndex: 'subtag', title: '子标签', width: 65 },
    { dataIndex: 'fraudNature', title: '涉诈性质', width: 75 },
    { dataIndex: 'importTime', title: '关停时间', width: 140, forceShow: true },
    { dataIndex: 'channelType', title: '渠道类型', width: 75 },
    { dataIndex: 'shutdownRemark', title: '关停备注', width: 75 },
    { dataIndex: 'relatedShutdown', title: '是否关联关停', width: 110 },
    {
      dataIndex: 'orderShutdownTime',
      title: '停机订单时间',
      width: 75,
    },
    {
      dataIndex: 'shutdownOrderNum',
      title: '停机单号',
      width: 75,
    },
    { dataIndex: 'relatedPhoneNum', title: '关联号码', width: 75 },
    { dataIndex: 'shutdownOperator', title: '关停人', width: 65 },
    { dataIndex: 'shutdownOperatorWorkerId', title: '关停工号', width: 75 },
    { dataIndex: 'resumeReason', title: '复机原因', width: 75 },
    { dataIndex: 'resumeTime', title: '复机时间', width: 75 },
    { dataIndex: 'ifResumed', title: '是否复机', width: 75 },
    { dataIndex: 'resumeWay', title: '复机方式', width: 75 },
    { dataIndex: 'ifLatestUser', title: '是否是最新用户', width: 115 },
    { dataIndex: 'resumeOperator', title: '复机人', width: 75 },
    { dataIndex: 'resumeOperatorWorkerId', title: '复机工号', width: 75 },
    { dataIndex: 'batchNum', title: '批次号', width: 75 },
    { dataIndex: 'phonePlanName', title: '套餐名称', width: 75 },
    { dataIndex: 'internetAccessPoint', title: '入网网点', width: 75 },
    { dataIndex: 'receptionIdentityCheck', title: '是否前台身份核验', width: 130 },
    { dataIndex: 'reviewCustomerOrderNum', title: '复核客户订单号', width: 130 },
    { dataIndex: 'reviewWorkerNum', title: '复核工号', width: 75 },
    { dataIndex: 'reviewSalesClerk', title: '复核营业员', width: 75 },
    { dataIndex: 'reviewDepartment', title: '复核部门', width: 75 },
    { dataIndex: 'reviewAcceptanceTime', title: '复核受理日期', width: 90 },
    { dataIndex: 'reOpenPhoneNum', title: '复通号码', width: 100 },
    {
      title: '操作',
      align: 'right',
      width: 70,
      fixed: 'right',
      render: (record, v, index) => {
        return (
          <span style={{ fontSize: '20px', cursor: 'pointer' }}>
            {record.infoSource == '当前关停表' || record.infoSource == '沉默当前关停表' ? (
              <Tooltip title="复机" placement="top">
                <Icon
                  type="reload"
                  rotate={180}
                  style={{ color: '#1890FF', marginRight: '6px' }}
                  onClick={() => {
                    setUserReuseVisible();
                    setState({ detail: record });
                  }}
                />
              </Tooltip>
            ) : null}
            {record.infoSource != '实时接口调用' && (
              <Tooltip title="详情" placement="top">
                <Icon
                  type="file-text"
                  style={{ color: '#1890FF' }}
                  onClick={() => showDetailModal(record)}
                />
              </Tooltip>
            )}
          </span>
        );
      },
    },
  ];

  useEffect(() => {
    queryUserRole();
    getOrganizationByUser();
    getAllFraudType();
    // const unlistenHistory = onEnterPage(props, () => {
    //   getOrganizationByUser();
    // });
    // return () => {
    //   if (unlistenHistory) unlistenHistory();
    // };
  }, []);
  const columns = [
    {
      dataIndex: 'phoneNum',
      title: '号码',
      key: 'phoneNum',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    // {
    //   dataIndex: 'phoneCardNum',
    //   // key: 'phoneCardNum',
    //   title: '手机号卡',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    {
      dataIndex: 'responsibleUnit',
      key: 'responsibleUnit',
      title: '责任单位',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'localNetwork',
      title: '本地网',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'businessArea',
      title: '营业区',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    // {
    //   dataIndex: 'masterCard',
    //   title: '是否主副卡',
    //   ellipsis: true,
    //   width: 100,
    //   align: 'center',
    // },

    {
      dataIndex: 'realTimeStatus',
      title: '实时状态',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'infoSource',
      title: '信息来源',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'shutdownType',
      title: '停机类型',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'fraudType',
      title: '涉诈类型',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'shutdownTag',
      title: '关停标签',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'subtag',
      title: '子标签',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'fraudNature',
      title: '涉诈性质',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'ifResumed',
      title: '是否复机',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'resumeWay',
      title: '复机方式',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'resumeTime',
      title: '复机时间',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'reviewAcceptanceTime',
      title: '复核受理日期',
      width: 120,
      ellipsis: true,
      align: 'center',
    },

    {
      dataIndex: 'ifLatestUser',
      title: '是否最新用户',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'importTime',
      title: '关停时间',
      ellipsis: true,
      width: 120,
      align: 'center',
    },
    // {
    //   dataIndex: 'channelType',
    //   title: '渠道类型',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    {
      dataIndex: 'shutdownRemark',
      title: '关停备注',
      ellipsis: true,
      width: '200',
      align: 'center',
    },
    // {
    //   dataIndex: 'relatedShutdown',
    //   title: '是否关联关停',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    //   // render: (data) => {
    //   //   let text = '';
    //   //   text = data ? (data == '1' ? '是' : '否') : '';
    //   //   return text;
    //   // },
    // },
    // {
    //   dataIndex: 'relatedPhoneNum',
    //   title: '关联号码',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'shutdownOperator',
    //   title: '关停人',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'resumeReason',
    //   title: '复机原因',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'resumeTime',
    //   title: '复机时间',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'resumeOperator',
    //   title: '复机人',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'batchNum',
    //   title: '批次号',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'phonePlanName',
    //   title: '套餐名称',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'internetAccessPoint',
    //   title: '入网网点',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'receptionIdentityCheck',
    //   title: '是否前台身份核验',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'reviewCustomerOrderNum',
    //   title: '复核客户订单号',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'reviewWorkerNum',
    //   title: '复核工号',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'reviewSalesClerk',
    //   title: '复核营业员',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'reviewDepartment',
    //   title: '复核部门',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    // {
    //   dataIndex: 'reviewAcceptanceTime',
    //   title: '复核受理日期',
    //   ellipsis: true,
    //   width: '200',
    //   align: 'center',
    // },
    {
      title: '操作',
      align: 'right',
      width: 75,
      fixed: 'right',
      render: (v, record) => {
        return (
          <span style={{ fontSize: '20px', cursor: 'pointer' }}>
            {record.infoSource == '当前关停表' || record.infoSource == '沉默当前关停表' ? (
              <Tooltip title="复机" placement="top">
                <Icon
                  type="reload"
                  rotate={180}
                  style={{ color: '#1890FF', marginRight: '6px' }}
                  onClick={() => {
                    setUserReuseVisible();
                    setState({ detail: record });
                  }}
                />
              </Tooltip>
            ) : null}
            {record.infoSource != '实时接口调用' && (
              <Tooltip title="详情" placement="top">
                <Icon
                  type="file-text"
                  style={{ color: '#1890FF' }}
                  onClick={() => showDetailModal(record)}
                />
              </Tooltip>
            )}
          </span>
        );
      },
    },
  ];
  const initTable = (params = {}) => {
    const { pagination = {}, values, ...rest } = params;
    const searchParams = values ? values : searchValue;
    if (!searchParams.phoneNum && !searchParams?.importTimeBegin) {
      return message.error('号码、关停时间 至少填写一个才能进行查询，请重新选择');
    }
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };
    setState({ pagination: newPagination });
    dispatch({
      type: 'fraudPreventionManage/pagePhoneShutdownDetail',
      payload: {
        ...searchParams,
        pageNum: newPagination.current,
        pageSize: newPagination.pageSize,
      },
      callback: (res) => {
        if (res && res.data) {
          const isShowFJButton =
            res.data.items[0]?.realTimeStatus == '正常' &&
            res.data.items[0]?.infoSource == '实时接口调用';
          setState({
            dataSource:
              res?.data?.items?.map((ele) => {
                return {
                  ...ele,
                  pageId: uuidv4(),
                };
              }) || [],
            total: res.data.totalNum || 0,
            ifBatchQuery: false,
            isShowFJButton,
          });
        } else {
          setState({ dataSource: [], total: 0 });
          message.error(res.message);
        }
      },
    });
  };
  const handleSearch = () => {
    const values = getFieldsValue();
    const { time, fjTime } = values;

    const params = {
      ...values,
      importTimeBegin: time?.[0]?.format('yyyyMMDD000000'),
      importTimeEnd: time?.[1]?.format('yyyyMMDD235959'),
      resumeTimeBegin: fjTime?.[0]?.format('yyyyMMDD000000'),
      resumeTimeEnd: fjTime?.[1]?.format('yyyyMMDD235959'),
      time: undefined,
      fjTime: undefined,
    };

    setState({ searchValue: { ...params } });
    // if (!values.phoneNum && values.phoneNum !== 0) {
    //   message.error('请输入号码');
    //   return;
    // }
    initTable({ values: params });
  };
  const reloadTable = () => {
    // initTable({ pagination: { ...defaultPagination },values:{},searchValue:{}})
    setState({
      pagination: { ...defaultPagination },
      searchValue: {},
      selectedRows: [],
      shutdownSubTagList: [],
      shutdownTagList: [],
      dataSource: [],
      total: 0,
    });
    resetFields();
  };
  const handlePaginationTable = (pagination) => {
    setState({ selectedRows: [] });
    if (!ifBatchQuery) {
      initTable({ pagination });
    } else {
      setState({
        pagination: pagination,
      });
    }
  };
  const queryUserRole = () => {
    dispatch({
      type: 'fraudPreventionManage/queryUserRole',
      callback: (res) => {
        if (res && res.data) {
          const { cityAdministrator, provincialCompanyAdministrator } = res.data;
          setState({ cityAdmin: cityAdministrator, provinceAdmin: provincialCompanyAdministrator });
        }
      },
    });
  };
  const getAllFraudType = () => {
    dispatch({
      type: 'fraudPreventionManage/getAllFraudTypeAll',
      callback: (res) => {
        if (res && res.code == 200) {
          setState({ fraudTypeList: res.data || [] });
        } else {
          setState({ fraudTypeList: [] });
        }
      },
    });
  };
  const getShutdownTagListByFraudType = (fraudType) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownTagListByFraudTypeAll',
      payload: { fraudType },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownTagList: res.data || [] });
          return;
        }
        setState({ shutdownTagList: [] });
      },
    });
  };
  const getShutdownSubTagListByShutdownTag = (shutdownTag) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownSubTagListByShutdownTagAll',
      payload: { shutdownTag },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownSubTagList: res.data || [] });
          return;
        }
        setState({ shutdownSubTagList: [] });
      },
    });
  };
  const getOrganizationByUser = () => {
    dispatch({
      type: 'fraudPreventionManage/getOrganizationByUser',
      callback: (res) => {
        if (res && res.code == '200' && res.data) {
          setState({ netWorkList: res.data || [] });
          if (res.data.length === 1) {
            const values = { ...searchValue, localNetwork: res.data[0].name };
            setState({ searchValue: values });
            // initTable({ values });
            form.setFieldsValue({ localNetwork: res.data[0].name });
            return;
          }
          // initTable();
          return;
        }
        setState({ netWorkList: [] });
      },
    });
  };
  const showDetailModal = (record) => {
    setState({ detailVisible: !detailVisible });
    if (record) setState({ detail: record });
  };
  const setUserReuseVisible = (isInit) => {
    setState({ OneUserReuseVisible: !OneUserReuseVisible });
    if (isInit) {
      initTable({ pagination: { ...defaultPagination } });
    }
  };
  const showOneUserStopVisible = (isInit) => {
    setState({ oneUserStopVisible: !oneUserStopVisible });
    if (isInit) {
      initTable({ pagination: { ...defaultPagination } });
    }
  };

  const showBatchUserOpeVisible = () => {
    setState({ batchUserOpeVisible: !batchUserOpeVisible });
  };

  const setSingleUserVisible = () => {
    setState({ singleUserModal: !singleUserModal });
  };
  const handleExport = () => {
    const filename = '防欺诈关停管理数据导出.xlsx';
    return new Promise((resolve, reject) => {
      request
        .post(
          ifBatchQuery
            ? '/api/hn/shutdown/exportBatchShutdownInfo'
            : '/api/hn/shutdown/exportPhoneShutdownDetail',
          {
            responseType: 'blob',
            useCSRFToken: false,
            getResponse: true,
            data: ifBatchQuery
              ? { detailList: dataSource }
              : { ...searchValue, pageNum: 1, pageSize: 10 },
            requestType: 'json',
          },
        )
        .then(({ data, response = {} }) => {
          const name = filename;
          if (data) {
            if ('download' in document.createElement('a')) {
              // 非IE下载
              const elink = document.createElement('a');
              elink.download = name;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(data);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(data, name);
            }
          } else {
            reject();
            return;
          }
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  };

  const [startTime, setStartTime] = useState(undefined);
  const [fjStartTime, setFjStartTime] = useState(undefined);

  const disabledDate = (current) => {
    if (startTime) {
      return (
        current > moment(startTime).add(89, 'day') ||
        current < moment(startTime).subtract(89, 'day') ||
        current > moment().endOf('day')
      );
    } else {
      return current > moment().endOf('day');
    }
  };

  const fjDisabledDate = (current) => {
    if (fjStartTime) {
      return (
        current > moment(fjStartTime).add(89, 'day') ||
        current < moment(fjStartTime).subtract(89, 'day') ||
        current > moment().subtract(1, 'day')
      );
    } else {
      return current > moment().subtract(0, 'day');
    }
  };

  const handleTempExport = () => {
    const filename = '关停批量查询模板.xlsx';
    return new Promise((resolve, reject) => {
      request
        .post('/api/template/getTemplate', {
          responseType: 'blob',
          useCSRFToken: false,
          getResponse: true,
          params: { templateCode: 'batchQueryShutdownStatus' },
        })
        .then(({ data, response = {} }) => {
          const name = filename;
          if (data) {
            if ('download' in document.createElement('a')) {
              // 非IE下载
              const elink = document.createElement('a');
              elink.download = name;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(data);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(data, name);
            }
          } else {
            reject();
            return;
          }
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  };

  const handleBatchQuery = () => {
    if (!batchQueryFile) {
      return message.error('请选择上传文件');
    }
    const params = new FormData();
    params.append('file', batchQueryFile);
    request('/api/hn/shutdown/batchQueryShutdownStatus', {
      method: 'post',
      data: params,
    }).then((res) => {
      if (res.code == 200) {
        setState({
          batchQueryVisible: false,
          batchQueryFile: undefined,
          dataSource: res.data,
          total: res.data?.length || 0,
          ifBatchQuery: true,
          pagination: { current: 1, pageSize: 10 },
        });
      } else {
        message.error(res.message);
        setState({ dataSource: [], total: 0 });
      }
    });
  };

  const handleChangeInfoSource = (value) => {
    if (['反诈平台-人工', '反诈平台-模型'].includes(value)) {
      form.setFieldsValue({
        ifLatestUser: undefined,
      });
    } else {
      !form.getFieldValue('ifLatestUser') &&
        form.setFieldsValue({
          ifLatestUser: '是',
        });
    }
  };

  return (
    <Fragment>
      <Licensee license="fraudPreventionManage_pagePhoneShutdownDetail">
        <Row>
          <Col span={22}>
            <Form {...formItemLayout}>
              <Row>
                <Col span={6}>
                  <Form.Item label="号码">
                    {getFieldDecorator(
                      'phoneNum',
                      {},
                    )(<Input style={{ width: '100%' }} placeholder="请输入" allowClear />)}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="关停时间" {...formItemLayout1}>
                    {getFieldDecorator('time')(
                      <RangePicker
                        allowClear
                        format="YYYY-MM-DD"
                        showTime={false}
                        placeholder={['开始时间', '结束时间']}
                        style={{ width: '100%' }}
                        onCalendarChange={(dates) => {
                          if (dates[1]) {
                            setStartTime();
                          } else {
                            setStartTime(dates[0]);
                          }
                        }}
                        disabledDate={disabledDate}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="实时状态">
                    {getFieldDecorator('realTimeStatus')(
                      <Select placeholder="请输入" allowClear>
                        {/* <Option value="语音短信单停"> 语音短信单停</Option>
                        <Option value="非实名双停">非实名双停</Option>
                        <Option value="非实名单停">非实名单停</Option>
                        <Option value="已复机">已复机</Option> */}
                        {[
                          '大数据保护停机单停',
                          '正常',
                          '换机保护性单停',
                          '公安关停双停',
                          '未激活',
                          '停机',
                          '核查系统用户举报号码关停双停',
                          '超流量停机',
                          '违章停机',
                          '欠费单停',
                          '非实名单停',
                          '非实名双停',
                          '语音短信单停',
                          '挂失',
                          '工信部断卡单停',
                          '欠费双停',
                          '预拆机',
                          '一证超五卡不合规单停',
                          '一证超五卡不合规双停',
                        ].map((v) => {
                          return <Option value={v}>{v}</Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                {/* <Col span={6}>
                  <Form.Item label="手机卡号">
                    {getFieldDecorator('phoneCardNum')(
                      <Input
                        style={{ width: '100%' }}
                        placeholder="请输入"
                        allowClear
                      />,
                    )}
                  </Form.Item>
                </Col> */}
              </Row>
              <Row style={{ display: expand ? 'block' : 'none' }}>
                {/* <Col span={6}>
                  <Form.Item label="实时状态">
                    {getFieldDecorator('realTimeStatus')(
                      <Select placeholder="请输入" allowClear>
                        <Option value="语音短信单停"> 语音短信单停</Option>
                        <Option value="非实名双停">非实名双停</Option>
                        <Option value="非实名单停">非实名单停</Option>
                        <Option value="已复机">已复机</Option>
                        {
                          ['大数据保护停机单停','正常','换机保护性单停','公安关停双停','未激活','欠费单停','停机','核查系统用户举报号码关停双停','超流量停机','欠费双停','违章停机','挂失','工信部断卡单停','预拆机','一证超五卡不合规单停','一证超五卡不合规双停'].map(v=>{
                            return (
                              <Option value={v}>{v}</Option>
                            )
                          })
                        }
                      </Select>,
                    )}
                  </Form.Item>
                </Col> */}
                <Col span={6}>
                  <Form.Item label="本地网">
                    {getFieldDecorator('localNetwork')(
                      <Select placeholder="请输入" allowClear disabled={netWorkList.length === 1}>
                        {netWorkList.map((item) => (
                          <Option value={item.name}>{item.name}</Option>
                        ))}
                        {/* <Option value="长沙本地网">长沙本地网</Option>
                        <Option value="株洲本地网">株洲本地网</Option>
                        <Option value="湘潭本地网">湘潭本地网</Option>
                        <Option value="衡阳本地网">衡阳本地网</Option>
                        <Option value="邵阳本地网">邵阳本地网</Option>
                        <Option value="岳阳本地网">岳阳本地网</Option>
                        <Option value="常德本地网">常德本地网</Option>
                        <Option value="张家界本地网">张家界本地网</Option>
                        <Option value="益阳本地网">益阳本地网</Option>
                        <Option value="郴州本地网">郴州本地网</Option>
                        <Option value="永州本地网">永州本地网</Option>
                        <Option value="怀化本地网">怀化本地网</Option>
                        <Option value="娄底本地网">娄底本地网</Option>
                        <Option value="湘西本地网">湘西本地网</Option> */}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="停机类型">
                    {getFieldDecorator('shutdownType')(
                      <Select placeholder="请输入" allowClear>
                        <Option value="语音短信单停"> 语音短信单停</Option>
                        <Option value="非实名双停">非实名双停</Option>
                        <Option value="非实名单停">非实名单停</Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="涉诈类型">
                    {getFieldDecorator('fraudType')(
                      <Select
                        placeholder="请输入"
                        allowClear
                        onChange={(value) => {
                          setState({ shutdownTagList: [], shutdownSubTagList: [] });
                          form.setFieldsValue({ shutdownTag: undefined, subtag: undefined });
                          if (value) getShutdownTagListByFraudType(value);
                        }}
                      >
                        {fraudTypeList.map((item) => (
                          <Option value={item}>{item}</Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="关停标签">
                    {getFieldDecorator('shutdownTag')(
                      <Select
                        placeholder="请输入"
                        allowClear
                        onChange={(value) => {
                          setState({ shutdownSubTagList: [] });
                          form.setFieldsValue({ subtag: undefined });
                          if (value) getShutdownSubTagListByShutdownTag(value);
                        }}
                      >
                        {shutdownTagList.map((item) => (
                          <Option value={item}>{item}</Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="子标签" {...formItemLayout}>
                    {getFieldDecorator('subtag')(
                      <Select placeholder="请输入" allowClear>
                        {shutdownSubTagList.map((item) => (
                          <Option value={item}>{item}</Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="信息来源">
                    {getFieldDecorator('infoSource')(
                      <Select placeholder="请选择" allowClear onChange={handleChangeInfoSource}>
                        {[
                          '历史关停表',
                          '当前关停表',
                          '沉默历史关停表',
                          '沉默当前关停表',
                          '反诈平台-模型',
                          '反诈平台-人工',
                        ].map((v) => {
                          return <Option value={v}>{v}</Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="是否复机">
                    {getFieldDecorator('ifResumed')(
                      <Select placeholder="请选择" allowClear>
                        {['是', '否'].map((v) => {
                          return <Option value={v}>{v}</Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="是否最新用户">
                    {getFieldDecorator('ifLatestUser', {
                      initialValue: '是',
                    })(
                      <Select placeholder="请选择" allowClear>
                        {['是', '否'].map((v) => {
                          return <Option value={v}>{v}</Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="责任单位">
                    {getFieldDecorator('responsibleUnit')(
                      <Select placeholder="请选择" allowClear>
                        {[
                          '长沙本地网',
                          '株洲本地网',
                          '湘潭本地网',
                          '衡阳本地网',
                          '邵阳本地网',
                          '岳阳本地网',
                          '常德本地网',
                          '张家界本地网',
                          '益阳本地网',
                          '郴州本地网',
                          '永州本地网',
                          '怀化本地网',
                          '娄底本地网',
                          '湘西本地网',
                          '省电渠',
                        ].map((v) => {
                          return <Option value={v}>{v}</Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="复机方式">
                    {getFieldDecorator('resumeWay')(
                      <Select placeholder="请选择" allowClear>
                        {['10000号复机', '线下复机', '线上复机', '线下读卡'].map((v) => {
                          return <Option value={v}>{v}</Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="复机时间" {...formItemLayout1}>
                    {getFieldDecorator('fjTime')(
                      <RangePicker
                        allowClear
                        format="YYYY-MM-DD"
                        showTime={false}
                        placeholder={['开始时间', '结束时间']}
                        style={{ width: '100%' }}
                        onCalendarChange={(dates) => {
                          if (dates[1]) {
                            setFjStartTime();
                          } else {
                            setFjStartTime(dates[0]);
                          }
                        }}
                        disabledDate={fjDisabledDate}
                      />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Col>
          <Col span={2}>
            <div style={{ color: '#1890FF', margin: '12px 0 0 10px', cursor: 'pointer' }}>
              <span onClick={() => setState({ expand: !expand })}>
                {expand ? '折叠' : '展开'}
                <Icon type={expand ? 'up' : 'down'} style={{ marginLeft: '5px' }} />
              </span>
            </div>
          </Col>
        </Row>
      </Licensee>
      <Row style={{ marginBottom: 8 }}>
        <Col span={12}>
          <Licensee license="fraudPreventionManage_shutdownSinglePhoneNum">
            <Button
              onClick={() => {
                showOneUserStopVisible();
              }}
              disabled={!cityAdmin && !provinceAdmin}
            >
              单个用户关停
            </Button>
          </Licensee>
          <Licensee license="fraudPreventionManage_batchShutdown">
            <Button
              style={{ margin: '0 8px' }}
              disabled={!cityAdmin && !provinceAdmin}
              onClick={() => {
                setState({ type: 'stop' });
                showBatchUserOpeVisible();
              }}
            >
              批量用户关停
            </Button>
          </Licensee>
          <Licensee license="fraudPreventionManage_batchResume">
            <Button
              style={{ margin: '0 8px' }}
              onClick={() => {
                setState({ type: 'use' });
                showBatchUserOpeVisible();
              }}
            >
              批量用户复机
            </Button>
          </Licensee>
          <Licensee license="fraudPreventionManage_forceResumeSinglePhoneNum">
            <Button
              onClick={() => {
                setSingleUserVisible();
              }}
            >
              单个用户强制复机
            </Button>
          </Licensee>
        </Col>
        <Col span={12} style={{ textAlign: 'right' }}>
          <Licensee license="fraudPreventionManage_pagePhoneShutdownDetail">
            <Button
              type="primary"
              onClick={() => {
                handleSearch();
              }}
            >
              号码状态查询
            </Button>
            <Button onClick={reloadTable} style={{ margin: '0 8px' }}>
              重置
            </Button>
          </Licensee>
          <Licensee license="fraudPreventionManage_batchQueryShutdownStatus">
            <Button
              style={{ margin: '0 8px' }}
              onClick={() => {
                setState({ batchQueryVisible: true });
              }}
            >
              批量查询
            </Button>
          </Licensee>
          <Licensee license="fraudPreventionManage_exportPhoneShutdownDetail">
            {/* <Button
                type="primary"
                onClick={() => {
                  handleExport();
                }}
              >
                数据导出
              </Button> */}

            <ExportApprove
              exportParams={{
                title: '防欺诈关停管理数据导出',
                urlAPi: ifBatchQuery
                  ? '/api/hn/shutdown/exportBatchShutdownInfo'
                  : '/api/hn/shutdown/exportPhoneShutdownDetail',
                params: ifBatchQuery
                  ? { detailList: dataSource }
                  : { ...searchValue, pageNum: 1, pageSize: 10 },
                method: 'POST',
                decode: true,
              }}
              moduleTile="防欺诈关停管理"
            />
          </Licensee>
        </Col>
      </Row>
      <StandardTable
        loading={loading}
        rowKey="pageId"
        columns={columns}
        showSelectCount={false}
        rowSelection={null}
        rowSelectionProps={false}
        isNeedAutoWidth={true}
        detailColumns={detailColumns.map((item) => {
          return {
            ...item,
            key: item?.dataIndex,
            ellipsis: true,
            align: 'center',
            render: (v, record, index) => {
              const t = v || '--';

              if (item.title !== '操作') {
                return (
                  <Tooltip title={t} placement="topLeft">
                    <span>{t}</span>
                  </Tooltip>
                );
              } else {
                return item.render(record, v, index);
              }
            },
          };
        })}
        tools={true}
        float="right"
        data={{
          list: dataSource || [],
          pagination: {
            total: total | 0,
            showSizeChanger: true,
            showQuickJumper: true,
            ...pagination,
            // showTotal: (total, range) => {
            //   // return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
            // },
          },
        }}
        scroll={{
          x: 'max-content',
        }}
        onChange={handlePaginationTable}
      />

      <DetailModal
        showDetailModal={showDetailModal}
        visible={detailVisible}
        detail={detail}
        columns={detailColumns}
      />
      <OneUserStopModal
        showOneUserStopVisible={() => showOneUserStopVisible()}
        visible={oneUserStopVisible}
        fraudTypeList={fraudTypeList}
        shutdownTagList={shutdownTagList}
        shutdownSubTagList={shutdownSubTagList}
        netWorkList={netWorkList}
        getShutdownTagListByFraudType={getShutdownTagListByFraudType}
        getShutdownSubTagListByShutdownTag={getShutdownSubTagListByShutdownTag}
      />
      {batchUserOpeVisible && (
        <BatchUserOpeModal
          fraudTypeList={fraudTypeList}
          netWorkList={netWorkList}
          showBatchUserOpeVisible={showBatchUserOpeVisible}
          visible={batchUserOpeVisible}
          type={type}
        />
      )}
      {OneUserReuseVisible && (
        <OneUserReuseModal
          detail={detail}
          setUserReuseVisible={setUserReuseVisible}
          visible={OneUserReuseVisible}
        />
      )}

      <SingleUser
        netWorkList={netWorkList}
        setSingleUserVisible={setSingleUserVisible}
        visible={singleUserModal}
      />

      <Modal
        title="批量查询"
        visible={batchQueryVisible}
        onCancel={() => {
          setState({ batchQueryVisible: false, batchQueryFile: undefined });
        }}
        onOk={() => {
          handleBatchQuery();
        }}
      >
        <Form.Item label="文件上传" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
          <Upload
            accept=".xls,.xlsx"
            beforeUpload={(file) => {
              setState({ batchQueryFile: file });
              return false;
            }}
            onRemove={() => {
              setState({ batchQueryFile: undefined });
              return false;
            }}
            fileList={batchQueryFile ? [batchQueryFile] : []}
          >
            <Button>
              {' '}
              <Icon type="upload" />
              上传文件
            </Button>
          </Upload>
          <div>注：每个文件不能超过20个号码。</div>
          <Button type="link" onClick={() => handleTempExport()} style={{ paddingLeft: 0 }}>
            模板下载
          </Button>
        </Form.Item>
      </Modal>
    </Fragment>
  );
};
export default connect(({ fraudPreventionManage, loading }) => ({
  fraudPreventionManage,
  loading: loading.effects['fraudPreventionManage/pagePhoneShutdownDetail'],
}))(Form.create()(FraudPreventionManage));
