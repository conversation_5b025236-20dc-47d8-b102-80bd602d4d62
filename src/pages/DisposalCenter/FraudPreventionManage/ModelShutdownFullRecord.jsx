import React, { Fragment, useEffect, useState, useRef } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';

const { RangePicker } = DatePicker;

import { Licensee } from 'ponshine';
import request from 'ponshine-request';
import { getIndexNumber } from '@/utils/utils';

const ModelShutdownFullRecord = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const serachParams = useRef();

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 80,
      ellipsis: true,
      render: (text, record, index) => {
        const {
          pagination: { current = 1, pageSize = 10 },
        } = listData;
        return getIndexNumber(current, pageSize, index);
      },
    },
    {
      title: '处置编码',
      dataIndex: 'uuid',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '分析时间',
      dataIndex: 'analysisTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '数据来源',
      dataIndex: 'dataSource',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '模型编码',
      dataIndex: 'modelCode',
      align: 'center',
      width: 130,
      ellipsis: true,
    },
    {
      title: '号码',
      dataIndex: 'phoneNum',
      align: 'center',
      width: 140,
      ellipsis: true,
    },
    {
      title: '本地网',
      dataIndex: 'cityId',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '批次号',
      dataIndex: 'batchId',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '是否白名单',
      dataIndex: 'ifWhite',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: (text) => {
        return text === '1' ? '是' : '否';
      },
    },
    {
      title: '白名单类型',
      dataIndex: 'whiteType',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '处置时间',
      dataIndex: 'disposeTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '是否处置成功',
      dataIndex: 'handleResult',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '失败原因',
      dataIndex: 'handleMsg',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '是否正式运行',
      dataIndex: 'dataType',
      align: 'center',
      width: 150,
      ellipsis: true,
      render: (text) => (text === '上线' ? '是' : '否'),
    },
  ];

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/autoDispose/pageModelAnalysisDataTask', {
      data: { pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };
  useEffect(() => {
    // findTableDataPager();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { fxsj, uuid, phoneNum, modelCode, batchId, handleResult } = formValue;

    // 检查是否至少填写了一项查询条件
    const hasValue = Object.values({
      uuid,
      phoneNum,
      modelCode,
      batchId,
      handleResult,
      fxsj: fxsj?.length > 0,
    }).some((value) => value);

    if (!hasValue) {
      message.warning('请至少填写一项查询条件');
      return;
    }

    findTableDataPager({
      ...formValue,
      analysisTimeStart: fxsj?.[0]?.format('YYYY-MM-DD 00:00:00'),
      analysisTimeEnd: fxsj?.[1]?.format('YYYY-MM-DD 23:59:59'),
      fxsj: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    setListData({
      list: [],
      pagination: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
    });
  };

  return (
    <Fragment>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="处置编码">
              {getFieldDecorator('uuid')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="模型编码">
              {getFieldDecorator('modelCode')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="批次号">
              {getFieldDecorator('batchId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="是否处置成功">
              {getFieldDecorator('handleResult')(
                <Select placeholder="请选择" allowClear>
                  {['成功', '失败']?.map((ele) => (
                    <Select.Option value={ele}>{ele}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,

            <Form.Item label="分析时间">
              {getFieldDecorator('fxsj')(
                <RangePicker allowClear format="YYYY-MM-DD" style={{ width: '100%' }} />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
      />
    </Fragment>
  );
};

export default Form.create()(ModelShutdownFullRecord);
