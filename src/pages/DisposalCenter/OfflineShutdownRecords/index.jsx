/*
 * @Author: zxw
 * @Date: 2023-09-04 13:19:35
 * @LastEditors: ss songsa@ponshine
 * @LastEditTime: 2024-03-13 14:28:02
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\OfflineShutdownRecords\index.jsx
 * @Description:
 */
import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Descriptions,
  Table,
  Popover,
} from 'antd';
import moment from 'moment';
import { Licensee } from 'ponshine';

import StandardTable from '@/components/StandardTable';
import { selectPage, selectFile, getApprovalStatusEnums } from './services';
import { exportFile } from '@/utils/utils';

import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import CascadeFormItem from '../components/CascadeFormItem';
import { renderToolTip } from '@/utils/utils';
import { render } from 'react-dom';

const { RangePicker } = DatePicker;

const FileContent = ({ loading, data }) => {
  const handleDownload = (record) => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/shutdownResumeApproval/downResumeApprovalFile',
      requestType: 'form',
      params: { id: record.id },
      decode: true,
      // title: record?.fileName?.split('.')?.[0],
      // mime: record?.fileName?.split('.')?.[1],
    });
  };
  const columns = [
    {
      title: '附件名',
      dataIndex: 'fileName',
      key: 'fileName',
      align: 'left',
    },

    {
      title: '下载',
      key: 'action',
      align: 'center',
      render: (v, r) => (
        <a>
          <Icon type="cloud-download" onClick={() => handleDownload(r)} />
        </a>
      ),
    },
  ];
  return (
    <Table
      rowKey="id"
      pagination={false}
      columns={columns}
      dataSource={data}
      loading={loading}
      locale={{ emptyText: '无附件' }}
    ></Table>
  );
};

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [fileLoading, setFileLoading] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [fileList, setFileList] = useState([]);
  const [stateList, setStateList] = useState({});
  const [expand, setExpand] = useState(false);
  const localNetworkRef = useRef();

  const operateTypeList = ['关停', '复机', '涉诈骗案件复机', '强制复机', '关联复机'];

  // 获取状态
  const getStateListData = async () => {
    const response = await getApprovalStatusEnums();
    if (response.code === 200) {
      setStateList(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  // 获取表格数据
  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  // 搜索
  const handleSearch = () => {
    const values = getFieldsValue();
    const { optTime, approveTime } = values;
    getListDatas({
      ...values,
      beginApplicationTime: optTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endApplicationTime: optTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      beginCityApprovalTime: approveTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endCityApprovalTime: approveTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      optTime: undefined,
      approveTime: undefined,
    });
  };

  // 分頁
  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  // 重置
  const onReset = () => {
    form.resetFields();
    getListDatas({
      localNetwork: localNetworkRef.current.getInitialValue(),
    });
  };

  // 导出
  const handleExport = () => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/shutdownResumeApproval/exportShutdownResumeApprovalRecord',
      params: { ...searchParams, pageSize: 1, pageNum: 1 },
      // decode: true,
      title: '线下关停复机记录查询',
      mime: 'xlsx',
    });
  };

  // 切换展开收起
  const handleToggle = () => {
    setExpand(!expand);
  };

  // 获取附件列表
  const getFileList = async (id) => {
    setFileLoading(true);
    const response = await selectFile({ id });
    setFileLoading(false);
    if (response.code === 200) {
      setFileList(response.data);
    } else {
      message.error(response.message);
    }
  };

  let columns = [
    {
      title: '操作类型',
      width: 100,
      dataIndex: 'operateType',
      ellipsis: true,

      render: (v) => {
        const text = operateTypeList?.[Number(v) - 1] || '--';
        return (
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',

      ellipsis: true,
    },
    {
      title: '主卡信息',
      width: 110,
      dataIndex: 'masterCardInfo',
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '状态',
      width: 140,
      dataIndex: 'approvalStatus',
      ellipsis: true,
      render: (v) => {
        return stateList?.[v] || '--';
      },
      forceShow: true,
    },
    {
      title: '审批备注',
      width: 140,
      dataIndex: 'approvalNote',
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '关停类型',
      width: 100,
      dataIndex: 'shutdownType',
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '涉诈类型',
      dataIndex: 'fraudType',
      width: 100,
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '关停标签',
      dataIndex: 'shutdownTag',
      width: 120,
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '子标签',
      dataIndex: 'subtag',
      width: 140,
      ellipsis: true,
      forceShow: true,
    },

    {
      title: '涉诈性质',
      width: 100,
      dataIndex: 'fraudNature',
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '操作时间',
      width: 180,
      dataIndex: 'applicationTime',
      ellipsis: true,
    },

    {
      title: '操作人',
      width: 100,
      dataIndex: 'applicantName',
      ellipsis: true,
    },
    {
      title: '备注',
      width: 120,
      dataIndex: 'remark',
      ellipsis: true,
      forceShow: true,
      render: (v) => renderToolTip(v),
    },
    {
      title: '是否关联关停',
      width: 100,
      dataIndex: 'relatedShutdown',
      ellipsis: true,
      render: (v) => {
        return v == null ? '--' : v === '1' ? '是' : '否';
      },
    },
    {
      title: '关联关停类型',
      width: 100,
      dataIndex: 'relatedShutdownType',
      ellipsis: true,
    },
    {
      title: '复通号码',
      width: 100,
      dataIndex: 'reOpenPhoneNum',
      ellipsis: true,
    },
    {
      title: '附件',
      width: 100,
      dataIndex: 'fileBtn',
      ellipsis: true,
      render: (v, r) => (
        <Popover
          content={<FileContent data={fileList} loading={fileLoading} />}
          title="附件"
          trigger="click"
          placement="rightTop"
        >
          <a>
            <Icon type="cloud-download" onClick={() => getFileList(r.id)} />
          </a>
        </Popover>
      ),
    },
    {
      title: '是否操作成功',
      width: 120,
      dataIndex: 'ifHandleSuccess',
      ellipsis: true,
      render: (v) => {
        return v === null ? '--' : v === '1' ? '是' : '否';
      },
    },
    {
      title: '操作结果',
      width: 100,
      dataIndex: 'handleResult',
      ellipsis: true,
    },
    {
      title: '地市审批人',
      width: 100,
      dataIndex: 'cityApproverName',

      ellipsis: true,
    },
    {
      title: '地市审批时间',
      width: 180,
      dataIndex: 'cityApprovalTime',
      ellipsis: true,
    },
    {
      title: '地市审批意见',
      width: 120,
      dataIndex: 'cityApprovalRemark',
      ellipsis: true,
    },
    {
      title: '省公司三级审批人',
      width: 120,
      dataIndex: 'firstProvinceApproverName',
      ellipsis: true,
    },
    {
      title: '省公司三级审批时间',
      dataIndex: 'firstProvinceApprovalTime',
      ellipsis: true,
    },
    {
      title: '省公司三级审批意见',
      width: 120,
      dataIndex: 'firstProvinceApprovalRemark',
      ellipsis: true,
    },
    {
      title: '省公司管理员审批人',
      width: 120,
      dataIndex: 'secondProvinceApproverName',
      ellipsis: true,
    },
    {
      title: '省公司管理员审批时间',
      width: 180,
      dataIndex: 'secondProvinceApprovalTime',
      ellipsis: true,
    },
    {
      title: '省公司管理员审批意见',
      width: 120,
      dataIndex: 'secondProvinceApprovalRemark',
      ellipsis: true,
    },
  ];

  useEffect(() => {
    getStateListData();
  }, []);

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作时间">
              {getFieldDecorator('optTime')(
                <RangePicker format={'YYYY-MM-DD'} style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="状态">
              {getFieldDecorator('approvalStatus')(
                <Select placeholder="请选择" allowClear>
                  {Object.keys(stateList)?.map((ele, index) => (
                    <Select.Option value={ele} key={ele}>
                      {stateList[ele]}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作类型">
              {getFieldDecorator('operateType')(
                <Select placeholder="请选择" allowClear>
                  {operateTypeList?.map((ele, index) => (
                    <Select.Option value={String(index + 1)} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <div style={{ display: expand ? 'block' : 'none' }}>
            <Col span={6}>
              <LocalNetworkFormItem
                form={form}
                getListDatas={getListDatas}
                cref={localNetworkRef}
              />
            </Col>
            <Col span={6}>
              <Form.Item label="关停类型">
                {getFieldDecorator('shutdownType')(
                  <Select placeholder="请选择" allowClear>
                    {['语音短信单停', '非实名单停', '非实名双停']?.map((ele, index) => (
                      <Select.Option value={ele} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <CascadeFormItem form={form} colSpanList={[6, 6, 6]} />

            <Col span={6}>
              <Form.Item label="是否操作成功">
                {getFieldDecorator('ifHandleSuccess')(
                  <Select placeholder="请选择" allowClear>
                    {['是', '否']?.map((ele, index) => (
                      <Select.Option value={ele === '是' ? '1' : '0'} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="操作人">
                {getFieldDecorator('applicantName')(
                  <Input allowClear={true} placeholder="请输入" />,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="地市审批人/省公司审批人"
                wrapperCol={{ span: 10 }}
                labelCol={{ span: 14 }}
              >
                {getFieldDecorator('cityApproverName')(
                  <Input allowClear={true} placeholder="请输入" />,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="地市/省公司审批时间"
                wrapperCol={{ span: 18 }}
                labelCol={{ span: 6 }}
              >
                {getFieldDecorator('approveTime')(
                  <RangePicker
                    format={'YYYY-MM-DD'}
                    // disabledDate={disabledDate}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="审批备注">
                {getFieldDecorator('approvalNote')(
                  <Select
                    placeholder="请选择"
                    allowClear
                    getPopupContainer={(triggerNode) => triggerNode.parentElement}
                  >
                    {['专班研判高风险', '专班研判中风险', '专班研判低风险', '去标拆机']?.map(
                      (ele, index) => (
                        <Select.Option value={ele} key={index}>
                          {ele}
                        </Select.Option>
                      ),
                    )}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </div>

          <Col align="right" span={24} style={{ marginBottom: 16 }}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
              重置
            </Button>

            {/* <Licensee license="outbound_export_all"> */}
            <Button type="primary" onClick={handleExport} disabled={!listData?.list?.length}>
              批量导出
            </Button>
            {/* </Licensee> */}

            <a style={{ marginLeft: 8, fontSize: 12 }} onClick={handleToggle}>
              {expand ? '收起' : '展开'} <Icon type={expand ? 'up' : 'down'} />
            </a>
          </Col>
        </Form>
      </Row>

      <StandardTable
        rowSelectionProps={false}
        showSelectCount={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        isNeedAutoWidth={true}
        scroll={{
          x: 1000,
          // y: 500,
        }}
      />
    </Card>
  );
};
export default Form.create({})(Index);
