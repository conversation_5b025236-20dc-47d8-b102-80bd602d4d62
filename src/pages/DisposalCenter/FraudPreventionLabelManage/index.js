import React, { useReducer, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Input,
  Select,
  Button,
  Tooltip,
  DatePicker,
  Icon,
  message,
  Modal,
} from 'antd';
import { connect } from 'dryad';

import { exportFile } from '@/utils/utils';

import { goPage, onEnterPage } from '@/utils/openTab';
import StandardTable from '@/components/StandardTable';
import AddEdit from './AddEdit';
import { Licensee, useLicensee } from 'ponshine';
import { getIndexNumber } from '@/utils/utils';

const defaultPagination = {
  pageSize: 10,
  current: 1,
};
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout1 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const initialState = {};
const { Option } = Select;
const FraudPreventionLableManage = (props) => {
  const {
    form,
    form: { getFieldDecorator, getFieldsValue, resetFields },
    dispatch,
    loading,
  } = props;
  const [state, setState] = useReducer(reducer, initialState);
  const {
    visible = false,
    searchValue = {},
    pagination = {},
    detail = {},
    dataSource = [],
    total = 0,
    fraudTypeList = [],
    shutdownTagList = [],
    shutdownSubTagList = [],
    selectedRows = [],
    cityAdmin = false,
    provinceAdmin = false,
  } = state;
  useEffect(() => {
    getAllFraudType();
    initTable();
    queryUserRole();
    const unlistenHistory = onEnterPage(props, () => {
      initTable();
    });
    return () => {
      if (unlistenHistory) unlistenHistory();
    };
  }, []);

  const queryUserRole = () => {
    dispatch({
      type: 'fraudPreventionManage/queryUserRole',
      callback: (res) => {
        if (res && res.data) {
          const { cityAdministrator, provincialCompanyAdministrator } = res.data;
          setState({ cityAdmin: cityAdministrator, provinceAdmin: provincialCompanyAdministrator });
        }
      },
    });
  };

  const columns = [
    {
      dataIndex: 'index',
      key: 'index',
      title: '序号',
      ellipsis: true,
      align: 'center',
      render: (text, record, index) => {
        const { current = 1, pageSize = 10 } = pagination;
        return getIndexNumber(current, pageSize, index);
      },
    },
    {
      dataIndex: 'fraudType',
      key: 'fraudType',
      title: '涉诈类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'shutdownTag',
      key: 'shutdownTag',
      title: '关停标签',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'subtagName',
      key: 'subtagName',
      title: '子标签',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'qualitative',
      key: 'qualitative',
      title: '涉诈性质',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'involvedType',
      key: 'involvedType',
      title: 'Involved_type',
      ellipsis: true,
      width: 110,
      align: 'center',
    },
    {
      dataIndex: 'crmFraudType',
      key: 'crmFraudType',
      title: 'CRM接口类型',
      ellipsis: true,
      width: 110,
      align: 'center',
    },
    {
      dataIndex: 'recordId',
      key: 'recordId',
      title: 'Record_id',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'crmTag',
      key: 'crmTag',
      title: 'CRM标签',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'priority',
      key: 'priority',
      title: '优先级',
      ellipsis: true,
      align: 'center',
      width: 80,
    },
    {
      dataIndex: 'shutdownType',
      key: 'shutdownType',
      title: '停机类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'relatedShutdown',
      key: 'relatedShutdown',
      title: '是否关联关停',
      ellipsis: true,
      align: 'center',
      width: 100,
    },
    {
      dataIndex: 'relatedShutdownType',
      key: 'relatedShutdownType',
      title: '关联关停停机规则',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      dataIndex: 'crmFrontDeskDisplay',
      key: 'crmFrontDeskDisplay',
      title: 'CRM/10000号前显示',
      ellipsis: true,
      align: 'center',
      width: 160,
    },
    {
      dataIndex: 'beforeShutdownMessage',
      key: 'beforeShutdownMessage',
      title: '短信内容配置(关停前)',
      ellipsis: true,
      width: 170,
      align: 'center',
    },
    {
      dataIndex: 'messageContent',
      key: 'messageContent',
      title: '短信内容配置(关停后)',
      ellipsis: true,
      width: 170,
      align: 'center',
    },
    {
      dataIndex: 'messageInterval',
      key: 'messageInterval',
      title: '关停前后短信时间间隔（分）',
      ellipsis: true,
      width: 200,
      align: 'center',
    },

    {
      dataIndex: 'ifFrontEntry',
      key: 'ifFrontEntry',
      title: '是否前台录入',
      ellipsis: true,
      align: 'center',
      width: 100,
      render: (value) => {
        const type = {
          1: '是',
          0: '否',
        };
        return type[value];
      },
    },
    {
      dataIndex: 'ifResume',
      key: 'ifResume',
      title: '是否允许复机',
      ellipsis: true,
      align: 'center',
      width: 100,
    },
    {
      dataIndex: 'ifCityResume',
      key: 'ifCityResume',
      title: '是否允许地市复机',
      ellipsis: true,
      align: 'center',
      width: 140,
    },
    {
      dataIndex: 'creator',
      key: 'creator',
      title: '配置人',
      ellipsis: true,
      align: 'center',
      width: 70,
    },
    {
      dataIndex: 'gmtUpdate',
      key: 'gmtUpdate',
      title: '配置时间',
      ellipsis: true,
      align: 'center',
      width: 160,
    },
  ];
  const initTable1 = (params = {}) => {
    const { pagination = {}, values, ...rest } = params;
    const searchParams = values ? values : searchValue;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };
    setState({ pagination: newPagination, selectedRows: [] });

    dispatch({
      type: 'fraudPreventionLabelManage/findTableData',
      payload: {
        ...searchParams,
        pageNum: newPagination.current,
        pageSize: newPagination.pageSize,
      },
      callback: (res) => {
        if (res && res.data) {
          setState({ dataSource: res.data.items || [], total: res.data.totalNum || 0 });
        } else {
          setState({ dataSource: [], total: 0 });
        }
      },
    });
  };

  const initTable = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setState({ pagination: { current: pageNum, pageSize } });
    dispatch({
      type: 'fraudPreventionLabelManage/findTableData',
      payload: {
        pageNum,
        pageSize,
        ...props,
      },
      callback: (res) => {
        if (res && res.data) {
          setState({ dataSource: res.data.items || [], total: res.data.totalNum || 0 });
        } else {
          setState({ dataSource: [], total: 0 });
          message.error(res.message);
        }
      },
    });
  };

  const reloadTable = () => {
    initTable();
    setState({ searchValue: {}, selectedRows: [], shutdownSubTagList: [], shutdownTagList: [] });
    resetFields();
  };

  const handleSearch = () => {
    const values = getFieldsValue();
    setState({ searchValue: { ...values } });
    initTable(values);
  };
  const handleSelectRows = (rows) => {
    setState({
      selectedRows: rows,
    });
  };
  const setVisible = () => {
    setState({ visible: !visible });
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '是否确定删除此关停标签?',
      okType: 'danger',
      onOk: () => {
        dispatch({
          type: 'fraudPreventionLabelManage/deleteTag',
          payload: selectedRows?.map((ele) => ele.id) || [],
          callback: (res) => {
            if (res && res.code == 200) {
              message.success(res.message);
              initTable();
              setState({ selectedRows: [] });
            } else {
              message.error(res.message);
            }
          },
        });
      },
    });
  };

  const getAllFraudType = () => {
    dispatch({
      type: 'fraudPreventionManage/getAllFraudTypeAll',
      callback: (res) => {
        if (res && res.code == 200) {
          setState({ fraudTypeList: res.data || [] });
        } else {
          setState({ fraudTypeList: [] });
        }
      },
    });
  };
  const getShutdownTagListByFraudType = (fraudType) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownTagListByFraudTypeAll',
      payload: { fraudType },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownTagList: res.data || [] });
          return;
        }
        setState({ shutdownTagList: [] });
      },
    });
  };
  const getShutdownSubTagListByShutdownTag = (shutdownTag) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownSubTagListByShutdownTagAll',
      payload: { shutdownTag },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownSubTagList: res.data || [] });
          return;
        }
        setState({ shutdownSubTagList: [] });
      },
    });
  };
  const handlePaginationTable = (pagination) => {
    const { current, pageSize } = pagination;
    setState({ selectedRows: [] });
    initTable({ pageNum: current, pageSize, ...searchValue });
  };
  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/shutdownTag/exportShutdownTag',
      title: '防欺诈关停标签管理',
      params: searchValue,
      method: 'GET',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };
  let x = 0;
  columns.forEach((item) => {
    x += item.width;
  });
  const filterListStr =
    '序号,涉诈类型,关停标签,子标签,涉诈性质,Involved_type,CRM接口类型,Record_id,CRM标签,优先级,停机类型,是否关联关停,关联关停停机规则,CRM/10000号前显示,短信内容配置(关停前),短信内容配置(关停后),关停前后短信时间间隔（分）,是否前台录入,是否允许复机,是否允许地市复机,配置人,配置时间';
  const filterList = filterListStr.split(',');
  let newfilterList = [];
  columns.forEach((item) => {
    if (
      filterList.some((items) => {
        return items == item.title;
      })
    ) {
      newfilterList.push({ dataIndex: item.dataIndex });
    }
  });
  return (
    <Fragment>
      <Card style={{ width: '100%' }}>
        <Licensee license="fraudPreventionLabelManage_pageShutdownTag">
          <Form {...formItemLayout}>
            <Row>
              <Col span={6}>
                <Form.Item label="涉诈类型">
                  {getFieldDecorator('fraudType')(
                    <Select
                      placeholder="请输入"
                      allowClear
                      onChange={(value) => {
                        setState({ shutdownTagList: [], shutdownSubTagList: [] });
                        form.setFieldsValue({ shutdownTag: undefined, subtagName: undefined });
                        if (value) getShutdownTagListByFraudType(value);
                      }}
                    >
                      {fraudTypeList.map((item, index) => (
                        <Option value={item} key={index}>
                          {item}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="关停标签">
                  {getFieldDecorator('shutdownTag')(
                    <Select
                      placeholder="请输入"
                      allowClear
                      onChange={(value) => {
                        setState({ shutdownSubTagList: [] });
                        form.setFieldsValue({ subtagName: undefined });
                        if (value) getShutdownSubTagListByShutdownTag(value);
                      }}
                    >
                      {shutdownTagList.map((item, index) => (
                        <Option value={item} key={index}>
                          {item}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="子标签" {...formItemLayout}>
                  {getFieldDecorator('subtagName')(
                    <Select placeholder="请输入" allowClear>
                      {shutdownSubTagList.map((item, index) => (
                        <Option value={item} key={index}>
                          {item}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Record_id" {...formItemLayout}>
                  {getFieldDecorator('recordId')(<Input placeholder="请输入" allowClear />)}
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Licensee>
        <Row style={{ marginBottom: 8 }}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Licensee license="fraudPreventionLabelManage_pageShutdownTag">
              <Button
                type="primary"
                onClick={() => {
                  handleSearch();
                }}
                style={{ marginRight: 16 }}
              >
                查询
              </Button>
              <Button style={{ marginRight: 16 }} onClick={reloadTable}>
                重置
              </Button>
            </Licensee>
            <Licensee license="fraudPreventionLabelManage_exportShutdownTag">
              <Button type="primary" onClick={handleExport} style={{ marginRight: 16 }}>
                数据导出
              </Button>
            </Licensee>
            <Licensee license="fraudPreventionLabelManage_saveOrUpdateShutdownTag">
              {/* <Button
                icon="plus"
                style={{ marginRight: 16 }}
                type="primary"
                // disabled={!cityAdmin && !provinceAdmin}
                onClick={() => {
                  setState({ type: 'add', visible: true, selectedRows: [] });
                }}
              >
                新增
              </Button> */}
              <Button
                icon="edit"
                style={{ marginRight: 16 }}
                disabled={selectedRows.length === 0}
                // disabled={(!cityAdmin && !provinceAdmin)&&selectedRows.length !== 1}
                onClick={() => {
                  setVisible();
                }}
              >
                编辑
              </Button>
            </Licensee>
            {/* <Licensee license="fraudPreventionLabelManage_pageTagRecord"> */}
            <Button
              onClick={() => goPage('/disposalCenter/fraudPreventionLabelManage/configRecord')}
            >
              配置记录
            </Button>
            {/* </Licensee> */}
            {/* <Licensee license="fraudPreventionLabelManage_deleteShutdownTagByIdList">
              <Button
                icon="delete"
                disabled={selectedRows.length === 0}
                // disabled={(!cityAdmin && !provinceAdmin)&&selectedRows.length === 0}
                onClick={handleDelete}
                style={{ marginRight: 16 }}
              >
                删除
              </Button>
            </Licensee> */}
          </Col>
        </Row>
        <StandardTable
          loading={loading}
          detailColumns={columns.map((item) => {
            return {
              ...item,
              title: <Tooltip title={item.title}>{item.title}</Tooltip>,
              key: item?.dataIndex,
              ellipsis: true,
              width: item.width || 85,
              // width: 60,
              align: 'center',
              render:
                item.render ||
                ((text) => {
                  return (
                    <Tooltip title={text}>
                      <span>{text || '--'}</span>
                    </Tooltip>
                  );
                }),
            };
          })}
          tools={true}
          float="right"
          rowKey="id"
          columns={newfilterList}
          showSelectCount={true}
          onSelectRow={handleSelectRows}
          selectedRows={selectedRows}
          data={{
            list: dataSource || [],
            pagination: {
              total: total | 0,
              showSizeChanger: true,
              showQuickJumper: true,
              ...pagination,
              // showTotal: (total, range) => {
              //   // return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
              // },
            },
          }}
          // scroll={{ x: x }}
          onChange={handlePaginationTable}
        />
      </Card>
      <AddEdit
        visible={visible}
        setVisible={setVisible}
        fraudTypeList={fraudTypeList}
        currentRow={selectedRows?.[0] || {}}
        reload={reloadTable}
        cancel={() => {
          setState({
            selectedRows: [],
          });
          setVisible();
        }}
      />
    </Fragment>
  );
};
export default connect(({ fraudPreventionManage, fraudPreventionLabelManage, loading }) => ({
  fraudPreventionManage,
  fraudPreventionLabelManage,
  loading: loading.effects['fraudPreventionLabelManage/findTableData'],
}))(Form.create()(FraudPreventionLableManage));
