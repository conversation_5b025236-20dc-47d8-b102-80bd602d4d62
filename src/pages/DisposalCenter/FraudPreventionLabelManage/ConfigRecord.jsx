import React, { useReducer, useEffect, Fragment } from 'react';
import { Card, Form, Row, Col, Input, Select, Button, Tooltip } from 'antd';
import { connect } from 'dryad';

import { exportFile } from '@/utils/utils';

import { onEnterPage } from '@/utils/openTab';
import StandardTable from '@/components/StandardTable';
import { Licensee, useLicensee } from 'ponshine';
import { getIndexNumber } from '@/utils/utils';

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const initialState = {};
const { Option } = Select;
const FraudPreventionLableManage = (props) => {
  const {
    form,
    form: { getFieldDecorator, getFieldsValue, resetFields },
    dispatch,
    loading,
  } = props;
  const [state, setState] = useReducer(reducer, initialState);
  const {
    searchValue = {},
    pagination = {},
    dataSource = [],
    total = 0,
    fraudTypeList = [],
    shutdownTagList = [],
    shutdownSubTagList = [],
  } = state;
  useEffect(() => {
    getAllFraudType();
    initTable();
    queryUserRole();
    const unlistenHistory = onEnterPage(props, () => {
      initTable();
    });
    return () => {
      if (unlistenHistory) unlistenHistory();
    };
  }, []);

  const queryUserRole = () => {
    dispatch({
      type: 'fraudPreventionManage/queryUserRole',
      callback: (res) => {
        if (res && res.data) {
          const { cityAdministrator, provincialCompanyAdministrator } = res.data;
          setState({ cityAdmin: cityAdministrator, provinceAdmin: provincialCompanyAdministrator });
        }
      },
    });
  };

  const columns = [
    {
      dataIndex: 'index',
      key: 'index',
      title: '序号',
      ellipsis: true,
      align: 'center',
      render: (text, record, index) => {
        const { current = 1, pageSize = 10 } = pagination;
        return getIndexNumber(current, pageSize, index);
      },
    },
    {
      dataIndex: 'fraudType',
      key: 'fraudType',
      title: '涉诈类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'shutdownTag',
      key: 'shutdownTag',
      title: '关停标签',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'subtagName',
      key: 'subtagName',
      title: '子标签',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'qualitative',
      key: 'qualitative',
      title: '涉诈性质',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'involvedType',
      key: 'involvedType',
      title: 'Involved_type',
      ellipsis: true,
      width: 110,
      align: 'center',
    },
    {
      dataIndex: 'crmFraudType',
      key: 'crmFraudType',
      title: 'CRM接口类型',
      ellipsis: true,
      width: 110,
      align: 'center',
    },
    {
      dataIndex: 'recordId',
      key: 'recordId',
      title: 'Record_id',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'crmTag',
      key: 'crmTag',
      title: 'CRM标签',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'priority',
      key: 'priority',
      title: '优先级',
      ellipsis: true,
      align: 'center',
      width: 80,
    },
    {
      dataIndex: 'shutdownType',
      key: 'shutdownType',
      title: '停机类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'relatedShutdown',
      key: 'relatedShutdown',
      title: '是否关联关停',
      ellipsis: true,
      align: 'center',
      width: 100,
    },
    {
      dataIndex: 'relatedShutdownType',
      key: 'relatedShutdownType',
      title: '关联关停停机规则',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      dataIndex: 'crmFrontDeskDisplay',
      key: 'crmFrontDeskDisplay',
      title: 'CRM/10000号前显示',
      ellipsis: true,
      align: 'center',
      width: 160,
    },
    {
      dataIndex: 'beforeShutdownMessage',
      key: 'beforeShutdownMessage',
      title: '短信内容配置(关停前)',
      ellipsis: true,
      width: 170,
      align: 'center',
    },
    {
      dataIndex: 'messageContent',
      key: 'messageContent',
      title: '短信内容配置(关停后)',
      ellipsis: true,
      width: 170,
      align: 'center',
    },
    {
      dataIndex: 'messageInterval',
      key: 'messageInterval',
      title: '关停前后短信时间间隔（分）',
      ellipsis: true,
      width: 200,
      align: 'center',
    },

    {
      dataIndex: 'ifFrontEntry',
      key: 'ifFrontEntry',
      title: '是否前台录入',
      ellipsis: true,
      align: 'center',
      width: 100,
      render: (value) => {
        const type = {
          1: '是',
          0: '否',
        };
        return type[value];
      },
    },
    {
      dataIndex: 'creator',
      key: 'creator',
      title: '配置人',
      ellipsis: true,
      align: 'center',
      width: 70,
    },
    {
      dataIndex: 'gmtUpdate',
      key: 'gmtUpdate',
      title: '配置时间',
      ellipsis: true,
      align: 'center',
      width: 180,
    },
    {
      dataIndex: 'operateType',
      key: 'operateType',
      title: '操作类型',
      ellipsis: true,
      align: 'center',
      width: 120,
    },
  ];

  const initTable = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setState({ pagination: { current: pageNum, pageSize } });
    dispatch({
      type: 'fraudPreventionLabelManage/findConfigRecordTableData',
      payload: {
        pageNum,
        pageSize,
        ...props,
      },
      callback: (res) => {
        if (res && res.data) {
          setState({ dataSource: res.data.items || [], total: res.data.totalNum || 0 });
        } else {
          setState({ dataSource: [], total: 0 });
        }
      },
    });
  };

  const reloadTable = () => {
    initTable();
    setState({ searchValue: {}, selectedRows: [], shutdownSubTagList: [], shutdownTagList: [] });
    resetFields();
  };

  const handleSearch = () => {
    const values = getFieldsValue();
    setState({ searchValue: { ...values } });
    initTable(values);
  };

  const getAllFraudType = () => {
    dispatch({
      type: 'fraudPreventionManage/getAllFraudTypeAll',
      callback: (res) => {
        if (res && res.code == 200) {
          setState({ fraudTypeList: res.data || [] });
        } else {
          setState({ fraudTypeList: [] });
        }
      },
    });
  };
  const getShutdownTagListByFraudType = (fraudType) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownTagListByFraudTypeAll',
      payload: { fraudType },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownTagList: res.data || [] });
          return;
        }
        setState({ shutdownTagList: [] });
      },
    });
  };
  const getShutdownSubTagListByShutdownTag = (shutdownTag) => {
    dispatch({
      type: 'fraudPreventionManage/getShutdownSubTagListByShutdownTagAll',
      payload: { shutdownTag },
      callback: (res) => {
        if (res && res.code == '200') {
          setState({ shutdownSubTagList: res.data || [] });
          return;
        }
        setState({ shutdownSubTagList: [] });
      },
    });
  };
  const handlePaginationTable = (pagination) => {
    const { current, pageSize } = pagination;
    setState({ selectedRows: [] });
    initTable({ pageNum: current, pageSize, ...searchValue });
  };
  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/shutdownTag/exportShutdownTag',
      title: '防欺诈关停标签管理',
      params: searchValue,
      method: 'GET',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };
  let x = 0;
  columns.forEach((item) => {
    x += item.width;
  });
  const filterListStr =
    '序号,涉诈类型,关停标签,子标签,涉诈性质,Involved_type,CRM接口类型,Record_id,CRM标签,优先级,停机类型,是否关联关停,关联关停停机规则,CRM/10000号前显示,短信内容配置(关停前),短信内容配置(关停后),关停前后短信时间间隔（分）,是否前台录入,配置人,配置时间,操作类型';
  const filterList = filterListStr.split(',');
  let newfilterList = [];
  columns.forEach((item) => {
    if (
      filterList.some((items) => {
        return items == item.title;
      })
    ) {
      newfilterList.push({ dataIndex: item.dataIndex });
    }
  });
  return (
    <Fragment>
      <Card style={{ width: '100%' }}>
        <Form {...formItemLayout}>
          <Row>
            <Col span={6}>
              <Form.Item label="涉诈类型">
                {getFieldDecorator('fraudType')(
                  <Select
                    placeholder="请输入"
                    allowClear
                    onChange={(value) => {
                      setState({ shutdownTagList: [], shutdownSubTagList: [] });
                      form.setFieldsValue({ shutdownTag: undefined, subtagName: undefined });
                      if (value) getShutdownTagListByFraudType(value);
                    }}
                  >
                    {fraudTypeList.map((item, index) => (
                      <Option value={item} key={index}>
                        {item}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="关停标签">
                {getFieldDecorator('shutdownTag')(
                  <Select
                    placeholder="请输入"
                    allowClear
                    onChange={(value) => {
                      setState({ shutdownSubTagList: [] });
                      form.setFieldsValue({ subtagName: undefined });
                      if (value) getShutdownSubTagListByShutdownTag(value);
                    }}
                  >
                    {shutdownTagList.map((item, index) => (
                      <Option value={item} key={index}>
                        {item}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="子标签" {...formItemLayout}>
                {getFieldDecorator('subtagName')(
                  <Select placeholder="请输入" allowClear>
                    {shutdownSubTagList.map((item, index) => (
                      <Option value={item} key={index}>
                        {item}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Record_id" {...formItemLayout}>
                {getFieldDecorator('recordId')(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Row style={{ marginBottom: 8 }}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              onClick={() => {
                handleSearch();
              }}
              style={{ marginRight: 16 }}
            >
              查询
            </Button>
            <Button style={{ marginRight: 16 }} onClick={reloadTable}>
              重置
            </Button>

            {/* <Licensee license="fraudPreventionLabelManage_exportShutdownTag"> */}
            <Button type="primary" onClick={handleExport}>
              数据导出
            </Button>
            {/* </Licensee> */}
          </Col>
        </Row>
        <StandardTable
          loading={loading}
          detailColumns={columns.map((item) => {
            return {
              ...item,
              title: <Tooltip title={item.title}>{item.title}</Tooltip>,
              key: item?.dataIndex,
              ellipsis: true,
              width: item.width || 85,
              // width: 60,
              align: 'center',
              render:
                item.render ||
                ((text) => {
                  return (
                    <Tooltip title={text}>
                      <span>{text || '--'}</span>
                    </Tooltip>
                  );
                }),
            };
          })}
          tools={true}
          float="right"
          rowKey="id"
          columns={newfilterList}
          data={{
            list: dataSource || [],
            pagination: {
              total: total | 0,
              showSizeChanger: true,
              showQuickJumper: true,
              ...pagination,
              // showTotal: (total, range) => {
              //   // return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
              // },
            },
          }}
          // scroll={{ x: x }}
          onChange={handlePaginationTable}
          showSelectCount={false}
          rowSelectionProps={false}
        />
      </Card>
    </Fragment>
  );
};
export default connect(({ fraudPreventionManage, fraudPreventionLabelManage, loading }) => ({
  fraudPreventionManage,
  fraudPreventionLabelManage,
  loading: loading.effects['fraudPreventionLabelManage/findTableData'],
}))(Form.create()(FraudPreventionLableManage));
