import React, { useReducer, useEffect, useState } from 'react';
import { Form, Modal, Row, Col, InputNumber, Input, Select, message } from 'antd';
import { connect } from 'dryad';
import { stringify } from 'qs';

const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 12 },
};
const formItemLayout1 = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};
const { Option } = Select;
const { TextArea } = Input;
const AddEdit = (props) => {
  const {
    visible,
    setVisible,
    form,
    form: { setFieldsValue, resetFields, validateFields },
    dispatch,
    currentRow,
    fraudTypeList,
    reload,
    cancel,
  } = props;

  const [fraudNatureList, setFraudNatureList] = useState([]);
  const [stopRuleList, setStopRuleList] = useState([]);
  const [crmInterfaceList, setCrmInterfaceList] = useState([]);
  const [crmTagList, setCrmTagList] = useState([]);
  const [disabled, setDisabled] = useState(false);
  const isEdit = currentRow?.id;

  useEffect(() => {
    if (visible) {
      ['shutdownType', 'qualitative'].forEach((ele) => {
        getAllFraudNatureTypeAndRule(ele);
      });
      getCrmInterfaceList();
      if (currentRow?.id) {
        getCrmTagList(currentRow?.crmFraudType, true);
        if (currentRow?.shutdownType === '否') setDisabled(true);
      }
    } else {
      setCrmTagList([]);
    }
  }, [visible]);

  const handleSubmit = () => {
    validateFields((err, fieldsValue) => {
      if (!err) {
        const { qualitative, priority } = fieldsValue;
        dispatch({
          type: 'fraudPreventionLabelManage/addEditTag',
          payload: {
            ...fieldsValue,
            id: currentRow?.id,
            qualitative: fieldsValue.qualitative?.join(','),
            shutdownType: fieldsValue.shutdownType?.join(','),
            relatedShutdown: fieldsValue.relatedShutdown?.join(','),
            relatedShutdownType: fieldsValue.relatedShutdownType?.join(','),
            priority: String(priority),
          },
          callback: (res) => {
            if (res.code === 200) {
              setVisible();
              reload();
              message.success(res.message);
            } else {
              message.error(res.message);
            }
          },
        });
      }
    });
  };

  // 获取涉诈性质和关停规则
  const getAllFraudNatureTypeAndRule = (type) => {
    dispatch({
      type: 'fraudPreventionLabelManage/getAllFraudNatureTypeAndRule',
      payload: {
        configType: type,
      },
      callback: (res) => {
        if (res.code === 200) {
          if (type === 'qualitative') {
            setFraudNatureList(res.data || []);
          } else {
            setStopRuleList(res.data || []);
          }
        } else {
          message.error(res.message);
        }
      },
    });
  };

  // 获取CRM接口类型
  const getCrmInterfaceList = () => {
    dispatch({
      type: 'fraudPreventionLabelManage/getCRMInterfaceType',
      callback: (res) => {
        if (res.code === 200) {
          setCrmInterfaceList(res.data || []);
        } else {
          message.error(res.message);
        }
      },
    });
  };

  // 获取CRM标签
  const getCrmTagList = (value, type) => {
    if (type !== true) {
      // 触发改变事件时，清空crm标签
      setFieldsValue({
        crmTag: undefined,
      });
    }
    if (!value) {
      return setCrmTagList([]);
    }
    dispatch({
      type: 'fraudPreventionLabelManage/getCRMTag',
      payload: { crmFraudType: value },
      callback: (res) => {
        if (res.code === 200) {
          setCrmTagList(res.data || []);
        } else {
          message.error(res.message);
        }
      },
    });
  };
  const relatedShutdownChange = (value) => {
    if (value && value?.length == 1 && value?.[0] == '否') {
      setDisabled(true);
      form.setFieldsValue({ relatedShutdownType: undefined });
    } else {
      setDisabled(false);
    }
  };
  return (
    <Modal
      width={1000}
      title={`${currentRow?.id ? '编辑' : '增加'}标签`}
      destroyOnClose
      visible={visible}
      onCancel={() => {
        cancel();
      }}
      onOk={handleSubmit}
      afterClose={() => {
        resetFields();
      }}
    >
      <Form {...formItemLayout}>
        <Row>
          <Col span={12}>
            <Form.Item label="涉诈类型">
              {form.getFieldDecorator('fraudType', {
                rules: [{ required: true, message: '请选择' }],
                initialValue: currentRow?.fraudType,
              })(
                <Select placeholder="请输入" allowClear>
                  {fraudTypeList?.map((ele, index) => (
                    <Option value={ele} key={index}>
                      {ele}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="关停标签">
              {form.getFieldDecorator('shutdownTag', {
                rules: [{ required: true, message: '请选择' }],
                initialValue: currentRow?.shutdownTag,
              })(<Input placeholder="请填写" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="子标签">
              {form.getFieldDecorator('subtagName', {
                initialValue: currentRow?.subtagName,
              })(<Input placeholder="请填写" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="涉诈性质">
              {form.getFieldDecorator('qualitative', {
                // rules: [{ required: true, message: '请选择' }],
                initialValue: currentRow?.qualitative?.split(','),
              })(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                  maxTagCount={1}
                  mode="multiple"
                  disabled={isEdit}
                >
                  {fraudNatureList?.map((ele, index) => (
                    <Option value={ele.name} key={index}>
                      {ele.name}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="involved_type">
              {form.getFieldDecorator('involvedType', {
                initialValue: currentRow?.involvedType,
                // rules: [{ required: true, message: '请填写' }],
              })(<Input placeholder="请填写" allowClear disabled={isEdit} />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="CRM接口类型">
              {form.getFieldDecorator('crmFraudType', {
                rules: [{ required: true, message: '请输入' }],
                initialValue: currentRow?.crmFraudType,
              })(
                // <Select placeholder="请选择" onChange={getCrmTagList}>
                //   {crmInterfaceList?.map((ele, index) => (
                //     <Option value={ele} key={index}>
                //       {ele}
                //     </Option>
                //   ))}
                // </Select>,
                <Input placeholder="请输入" allowClear></Input>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="record_id">
              {form.getFieldDecorator('recordId', {
                initialValue: currentRow?.recordId,
                // rules: [{ required: true, message: '请填写' }],
              })(<Input placeholder="请填写" allowClear disabled={isEdit} />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="CRM标签">
              {form.getFieldDecorator('crmTag', {
                // rules: [{ required: true, message: '请输入' }],
                initialValue: currentRow?.crmTag,
              })(
                // <Select placeholder="请选择" allowClear>
                //   {crmTagList?.map((ele, index) => (
                //     <Option value={ele} key={index}>
                //       {ele}
                //     </Option>
                //   ))}
                // </Select>,
                <Input placeholder="请输入" allowClear disabled={isEdit}></Input>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="优先级">
              {form.getFieldDecorator('priority', {
                // rules: [{ required: true, message: '请填写' }],
                initialValue: currentRow?.priority,
              })(
                <InputNumber
                  min={1}
                  allowClear
                  precision={0}
                  style={{ width: '100%' }}
                  placeholder="请填写"
                  disabled={isEdit}
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="停机类型">
              {form.getFieldDecorator('shutdownType', {
                // rules: [{ required: true, message: '请选择' }],
                initialValue: currentRow?.shutdownType?.split(','),
              })(
                <Select placeholder="请选择" allowClear mode="multiple" disabled={isEdit}>
                  <Option value="语音短信单停">语音短信单停</Option>
                  <Option value="非实名双停">非实名双停</Option>
                  <Option value="非实名单停">非实名单停</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否关联关停">
              {form.getFieldDecorator('relatedShutdown', {
                // rules: [{ required: true, message: '请选择' }],
                initialValue: currentRow?.relatedShutdown?.split(','),
              })(
                <Select
                  placeholder="请选择"
                  allowClear
                  disabled={isEdit}
                  onChange={(value) => {
                    relatedShutdownChange(value);
                  }}
                  mode="multiple"
                >
                  <Option value="是">是</Option>
                  <Option value="否">否</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="关联关停停机规则">
              {form.getFieldDecorator('relatedShutdownType', {
                initialValue: currentRow?.relatedShutdownType?.split(','),
                // rules: [
                //   {
                //     validator: (rule, value, callback) => {
                //       if (
                //         form.getFieldsValue().relatedShutdown?.length == 1 &&
                //         form.getFieldsValue().relatedShutdown?.[0] == '是' &&
                //         !value
                //       ) {
                //         callback('请选择关联关停类型');
                //       }
                //       callback();
                //     },
                //   },
                // ],
              })(
                <Select placeholder="请选择" allowClear disabled={isEdit} mode="multiple">
                  {/* <Option value="语音短信单停">语音短信单停</Option> */}
                  <Option value="非实名双停">非实名双停</Option>
                  <Option value="非实名单停">非实名单停</Option>
                  {/* {stopRuleList?.map((ele, index) => (
                    <Option value={ele.name} key={index}>
                      {ele.name}
                    </Option>
                  ))} */}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="CRM/10000号当前显示">
              {form.getFieldDecorator('crmFrontDeskDisplay', {
                // rules: [{ required: true, message: '请填写' }],
                initialValue: currentRow?.crmFrontDeskDisplay,
              })(<Input placeholder="请填写" allowClear disabled={isEdit} />)}
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label="关停前后短信时间间隔（分）">
              {form.getFieldDecorator('messageInterval', {
                initialValue: currentRow?.messageInterval,
                // rules: [{ required: true, message: '请填写' }],
              })(
                <InputNumber
                  placeholder="请填写"
                  allowClear
                  disabled={isEdit}
                  style={{ width: '34%' }}
                />,
              )}
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="短信内容配置(关停前)" {...formItemLayout1}>
              {form.getFieldDecorator('beforeShutdownMessage', {
                initialValue: currentRow?.beforeShutdownMessage,
              })(
                <TextArea
                  autoSize={{ minRows: '3', maxRows: '100' }}
                  placeholder="请填写"
                  allowClear
                  disabled={isEdit}
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="短信内容配置(关停后)" {...formItemLayout1}>
              {form.getFieldDecorator('messageContent', {
                initialValue: currentRow?.messageContent,
              })(
                <TextArea
                  autoSize={{ minRows: '3', maxRows: '100' }}
                  placeholder="请填写"
                  allowClear
                  disabled={isEdit}
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否前台录入">
              {form.getFieldDecorator('ifFrontEntry', {
                initialValue:
                  `${currentRow?.ifFrontEntry}` !== 'undefined'
                    ? `${currentRow?.ifFrontEntry}`
                    : undefined,
                rules: [{ required: true, message: '请填写' }],
              })(
                <Select placeholder="请选择" allowClear>
                  <Option value="1">是</Option>
                  <Option value="0">否</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否允许复机">
              {form.getFieldDecorator('ifResume', {
                initialValue: currentRow?.ifResume,
                // rules: [{ required: true, message: '请选择' }],
              })(
                <Select placeholder="请选择" allowClear>
                  {['是', '否']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否允许地市复机">
              {form.getFieldDecorator('ifCityResume', {
                initialValue: currentRow?.ifCityResume,
                // rules: [{ required: true, message: '请选择' }],
              })(
                <Select placeholder="请选择" allowClear>
                  {['是', '否']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default connect(({ fraudPreventionLabelManage }) => ({ fraudPreventionLabelManage }))(
  Form.create()(AddEdit),
);
