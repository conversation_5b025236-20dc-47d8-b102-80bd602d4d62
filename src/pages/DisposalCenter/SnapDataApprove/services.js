import request from '@/utils/request';

export function getTableData(params) {
  return request('/api/hn/cardBreak/pageAppealInfo', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取7天内的卡断数量
export function get7DaysToal(params) {
  return request('/api/hn/cardBreak/examineCount', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 审批
export function approve(params) {
  return request('/api/hn/cardBreak/examineCardBreak', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 批量导入
export function importCardBreak(params) {
  return request('/api/hn/cardBreak/importCardBreak', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
