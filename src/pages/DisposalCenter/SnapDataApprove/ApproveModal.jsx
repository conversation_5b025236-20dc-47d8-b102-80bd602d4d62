import { Modal, Form, DatePicker, Input, Select, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { get7DaysToal, approve } from './services';
import moment from 'moment';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const ApproveModal = ({
  visible,
  reload,
  cancel,
  form: {
    getFieldDecorator,
    getFieldsValue,
    resetFields,
    setFieldsValue,
    getFieldValue,
    validateFields,
  },
  examineStateList,
}) => {
  const [totalObj, setTotalObj] = useState({});
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleChange = (v) => {
    setFieldsValue({
      examineOpinion: examineStateList?.find((ele) => ele.value == v)?.text,
    });
  };
  const handleOk = () => {
    validateFields(async (err, values) => {
      if (err) return;
      setConfirmLoading(true);
      const response = await approve({
        ...values,
        examineTime: values?.date?.format('YYYY-MM-DD'),
        date: undefined,
      });
      setConfirmLoading(false);
      if (response.code === 200) {
        message.success(response?.message);
        cancel();
        reload();
      } else {
        message.error(response?.message);
      }
    });
  };

  const get7DaysToalData = async () => {
    const response = await get7DaysToal();

    if (response.code === 200) {
      let obj = {};
      response?.data?.forEach((ele) => {
        obj[ele.time] = ele.count;
      });
      setTotalObj(obj);
    } else {
      message.success(response?.message);
    }
  };

  useEffect(() => {
    if (visible) {
      get7DaysToalData();
    } else {
      setTotalObj({});
    }
  }, [visible]);

  const disabledDate = (current) => {
    return (
      current &&
      (current > moment().endOf('day') || current < moment().subtract(6, 'days').startOf('day'))
    );
  };

  return (
    <Modal
      title="审批"
      visible={visible}
      maskClosable={false}
      afterClose={() => {
        resetFields();
      }}
      onOk={handleOk}
      onCancel={cancel}
      okButtonProps={{
        disabled:
          JSON.stringify(totalObj) === '{}' ||
          !totalObj?.[getFieldValue('date')?.format('YYYY-MM-DD')],
        loading: confirmLoading,
      }}
    >
      <Form {...formItemLayout}>
        <Form.Item label="卡断时间">
          {getFieldDecorator('date', {
            initialValue: moment(),
          })(<DatePicker format={'YYYY-MM-DD'} disabledDate={disabledDate} />)}
        </Form.Item>

        <Form.Item label="卡断数量">
          {getFieldDecorator('examineCount', {
            initialValue: totalObj?.[getFieldValue('date')?.format('YYYY-MM-DD')],
          })(<Input style={{ display: 'none' }} />)}
          <span>{totalObj?.[getFieldValue('date')?.format('YYYY-MM-DD')] ?? '--'}</span>
        </Form.Item>
        <Form.Item label="审批结果">
          {getFieldDecorator('examineState', {
            rules: [
              {
                required: true,
                message: '请选择审批结果',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              onChange={handleChange}
            >
              {examineStateList
                ?.filter((ele) => ele.value !== 1)
                ?.map((ele, index) => (
                  <Select.Option value={ele.value} key={index}>
                    {ele.text}
                  </Select.Option>
                ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item label="审批意见">
          {getFieldDecorator('examineOpinion', {
            rules: [
              {
                required: true,
                message: '请输入审批意见',
              },
              {
                max: 200,
                message: '最多输入200个字符',
              },
            ],
          })(<TextArea rows={2} placeholder="请输入" allowClear />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(ApproveModal);
