import React, { useRef, useState, useEffect } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, Button, Card, Upload } from 'antd';
import StandardTable from '@/components/StandardTable';
import BatchImportModal from '@/components/BatchImport';

import ApproveModal from './ApproveModal';
import { getTableData, importCardBreak } from './services';
import moment from 'moment';
import { exportFile } from '@/utils/utils';
import request from 'ponshine-request';

const { RangePicker } = DatePicker;

const Index = ({ form, form: { getFieldDecorator, validateFields } }) => {
  const examineStateList = [
    {
      text: '待审批',
      value: 1,
    },
    {
      text: '审批通过',
      value: 3,
    },
    {
      text: '审批驳回',
      value: 2,
    },
  ];
  const initDate = [moment(), moment()];
  const initParams = {
    examineState: 1,
    startTime: initDate?.[0]?.format('YYYY-MM-DD'),
    endTime: initDate?.[1]?.format('YYYY-MM-DD'),
  };
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [importVisible, setImportVisible] = useState(false);
  const [cardBreakType, setCardBreakType] = useState([]);

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [serachParams, setSearchParams] = useState({});

  let columns = [
    {
      title: '卡断时间',
      width: 120,
      dataIndex: 'cardBreakTime',
      ellipsis: true,
    },
    {
      title: '卡断号码',
      width: 100,
      dataIndex: 'cardBreakNum',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '审批状态',
      width: 120,
      dataIndex: 'examineState',
      ellipsis: true,
      render: (v) => {
        const text = examineStateList?.find((ele) => ele.value == v)?.text;
        return text;
      },
    },
    {
      title: '批次号',
      width: 120,
      dataIndex: 'batchNum',
      ellipsis: true,
    },

    {
      title: '审批人',
      width: 100,
      dataIndex: 'examineUsername',
      ellipsis: true,
    },

    {
      title: '审批时间',
      width: 140,
      dataIndex: 'examineTime',
      ellipsis: true,
    },
    {
      title: '审批意见',
      width: 140,
      dataIndex: 'examineOpinion',
      ellipsis: true,
    },
    {
      title: '卡断类型',
      width: 140,
      dataIndex: 'cardBreakType',
      ellipsis: true,
    },

    {
      title: '卡断状态',
      width: 120,
      dataIndex: 'cardBreakState',
      ellipsis: true,
    },
    {
      title: '失败原因',
      width: 120,
      dataIndex: 'errorMsg',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await getTableData({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      const { kdDate } = values;
      getListDatas({
        ...values,
        startTime: kdDate?.[0]?.format('YYYY-MM-DD'),
        endTime: kdDate?.[1]?.format('YYYY-MM-DD'),
        kdDate: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas(initParams);
  };

  const handleApprove = async () => {
    setVisible(true);
  };

  // 获取卡断类型
  const getCardBreakType = async () => {
    const response = await request.get('/api/hn/cardBreak/getCardBreakType');
    if (response.code === 200) {
      setCardBreakType(response.data);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    // getCardBreakType();
    getListDatas(initParams);
  }, []);

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/hn/cardBreak/exportCardBreak',
      decode: true,
      method: 'POST',
      requestType: 'json',
      params: { ...serachParams, pageNum: 1, pageSize: 1 },
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="卡断时间">
              {getFieldDecorator('kdDate', {
                initialValue: initDate,
              })(<RangePicker format={'YYYY-MM-DD'} disabledDate={disabledDate} />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="卡断号码">
              {getFieldDecorator('cardBreakNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="审批状态">
              {getFieldDecorator('examineState', { initialValue: initParams?.examineState })(
                <Select placeholder="请选择" allowClear>
                  {examineStateList?.map((ele) => (
                    <Select.Option value={ele.value}>{ele.text}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="卡断状态">
              {getFieldDecorator('cardBreakState')(
                <Select placeholder="请选择" allowClear>
                  <Select.Option value={'成功'}>成功</Select.Option>
                  <Select.Option value={'失败'}>失败</Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="卡断类型">
              {getFieldDecorator('cardBreakType')(
                <Input placeholder="请输入" allowClear />,
                // <Select placeholder="请选择" allowClear>
                //   {cardBreakType?.map((ele) => (
                //     <Select.Option value={ele}>{ele}</Select.Option>
                //   ))}
                // </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={18} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>
            <Button onClick={onReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Button type="primary" onClick={handleApprove} style={{ marginRight: 8 }}>
              审批
            </Button>
            <Button
              onClick={() => {
                setImportVisible(true);
              }}
              type="primary"
              style={{ marginRight: 8 }}
            >
              导入
            </Button>

            <Button
              onClick={handleExport}
              loading={exportLoading}
              disabled={!listData?.list?.length}
            >
              导出
            </Button>
          </Col>
        </Row>
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
      <ApproveModal
        visible={visible}
        cancel={() => {
          setVisible(false);
        }}
        reload={onReset}
        examineStateList={examineStateList}
      />
      {/* 批量导入 */}
      <BatchImportModal
        title="批量导入"
        tipsText="*每个文件不超过5000条"
        visible={importVisible}
        onClose={() => {
          setImportVisible(false);
        }}
        errorExportUrl={''}
        downTemplateUrl={` /api/template/getTemplate?templateCode=cardBreakImport`}
        importRequest={importCardBreak}
        reload={() => {
          onReset();
        }}
        closeModal={() => {
          setImportVisible(false);
        }}
      />
    </Card>
  );
};

export default Form.create()(Index);
