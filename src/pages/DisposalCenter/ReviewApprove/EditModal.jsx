import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Upload, Button, Icon, message, Spin } from 'antd';
const { TextArea } = Input;
import { editData, selectFile } from './CurrentInitiatedServices';
import { difference, multiply } from 'lodash';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const fileType =
  '.csv,.xls,.xlsx,.jpg,.png,.bmp,.jpeg,.rar,.zip,.7z,.doc,.docx,.txt,.pdf,.ppt,.pptx';
const EditModal = ({
  visible,
  form: { getFieldDecorator, validateFields, getFieldValue, resetFields, setFieldsValue },
  currentRow,
  cancel,
  reload,
}) => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [fileLoading, setFileLoading] = useState(false);
  const [currentFileList, setCurrentFileList] = useState([]);

  useEffect(() => {
    if (visible) {
      getFileList();
    } else {
      setCurrentFileList([]);
      submitLoading && setSubmitLoading(false)
    }
  }, [visible]);

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const uploadProps = {
    accept: fileType,
    multiple: true,
    // fileList: fileList,
    beforeUpload: (file) => {
      const isLt20M = file.size / 1024 / 1024 <= 20;
      const isTrueType = fileType.includes(file.name?.split('.')[1]);
      const fileList = getFieldValue('uploadFile');
      if (fileList?.length >= 50) {
        message.error('累计文件个数不能超过50个，请知悉');
        return Promise.reject();
      }
      if (!isTrueType) {
        message.error(
          '因安全管理要求，仅能上传以下类型文件，表格：csv、xls、xlsx；图片:jpg、png、bmp、jpeg；压缩包：rar、zip、7z；文字：doc、docx、txt、pdf、ppt、pptx。',
        );

        return Promise.reject();
      }

      if (!isLt20M) {
        message.error('单个文件最多不能超过20M');
        return Promise.reject();
      }

      return false;
    },
  };

  const getFileList = async (id) => {
    setFileLoading(true)
    const response = await selectFile({ id: currentRow?.id });
    setFileLoading(false)

    if (response.code === 200) {
      setCurrentFileList(
        response?.data?.map((ele) => {
          return {
            uid: ele.id,
            id: ele.id,
            name: ele.fileName,
            status: 'done',
          };
        }),
      );
    } else {
      message.error(response.message);
    }
  };

  const filterDeleteId = () => {
    const fileList = getFieldValue('uploadFile');
    const deleteId = difference(
      currentFileList?.map((ele) => ele.id),
      fileList?.filter((ele) => ele.id)?.map((ele) => ele.id),
    );
    return deleteId;
  };

  const handleOk = () => {
    validateFields(async (err, fieldsValue) => {
      if (err) return;
      const { uploadFile } = fieldsValue;

      const formData = new FormData();
      Object.keys(fieldsValue).forEach((ele) => {
        if (ele !== 'uploadFile') {
          formData.append(`${ele}`, fieldsValue[ele]);
        }
      });

      // 筛选新增的文件
      uploadFile
        ?.filter((ele) => ele.originFileObj)
        ?.forEach((ele) => {
          formData.append('fileArray', ele.originFileObj);
        });

      formData.append('id', currentRow?.id);

      filterDeleteId(uploadFile)?.forEach((ele) => {
        formData.append('deletedFileIdList', ele);
      });

      setSubmitLoading(true);
      let response = await editData(formData);

      setSubmitLoading(false);

      if (response && response.code === 200) {
        message.success(response.message);
        cancel();
        reload();
      } else {
        message.error(response.message);
      }
    });
  };

  return (
    <Modal
      visible={visible}
      title="编辑"
      onCancel={cancel}
      onOk={handleOk}
      width={600}
      confirmLoading={submitLoading}
      maskClosable={false}
      afterClose={() => {
        resetFields();
        setSubmitLoading(false);
      }}
    >
      <Form {...formItemLayout}>
        <Form.Item label="号码">
          {getFieldDecorator('phoneNum', {
            initialValue: currentRow?.phoneNum,
          })(<Input disabled />)}
        </Form.Item>
        <Form.Item label="复机原因">
          {getFieldDecorator('resumeReason', {
            initialValue: currentRow?.remark,
            rules: [
              {
                required: true,
                message: '请输入复机原因',
              },
              {
                max: 1000,
                message: '最多输入1000个字符',
              },
            ],
          })(<TextArea rows={3} />)}
        </Form.Item>
        <Spin spinning={fileLoading}>
        <Form.Item label="附件">
          {getFieldDecorator('uploadFile', {
            valuePropName: 'fileList',
            getValueFromEvent: normFile,
            initialValue: currentFileList,
          })(
            <Upload {...uploadProps}>
              <Button>
                <Icon type="upload" /> 上传文件
              </Button>
            </Upload>,
          )}
        </Form.Item>
        </Spin>
        
      </Form>
    </Modal>
  );
};

export default Form.create()(EditModal);
