/*
 * @Author: zxw
 * @Date: 2023-09-05 15:49:18
 * @LastEditors: zxw
 * @LastEditTime: 2023-09-08 13:46:43
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\ReviewApprove\SingleApproveInfo.jsx
 * @Description:
 */
import React, { Fragment } from 'react';
import { Form, Row, Col, Descriptions, Icon } from 'antd';
import { exportFile } from '@/utils/utils';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

export default function SingleApproveInfo({ currentRow, fileList } = {}) {
  const info = [
    {
      label: '号码',
      key: 'phoneNum',
      colSpan: 12,
    },
    {
      label: '本地网',
      key: 'localNetwork',
      colSpan: 12,
    },
    {
      label: '涉诈性质',
      key: 'fraudNature',
      colSpan: 12,
    },
    {
      label: '涉诈类型',
      key: 'fraudType',
      colSpan: 12,
    },
    {
      label: '关停标签',
      key: 'shutdownTag',
      colSpan: 12,
    },
    {
      label: '子标签',
      key: 'subtag',
      colSpan: 12,
    },
    {
      label: '复机原因',
      key: 'remark',
      colSpan: 24,
    },
    {
      label: '附件',
      key: 'file',
      colSpan: 24,
      render: (v, r) => {
        return (
          <div>
            {fileList?.map((ele, index) => (
              <div key={index}>
                <a onClick={() => handleDownload(ele)}>
                  <Icon type="link" style={{ marginRight: 4 }} />
                  {ele.fileName}
                </a>
              </div>
            ))}
            {!fileList?.length && <span>无附件</span>}
          </div>
        );
      },
    },
  ];

  const handleDownload = (ele) => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/shutdownResumeApproval/downResumeApprovalFile',
      params: { id: ele.id },
      // title: ele?.fileName?.split('.')?.[0],
      // mime: ele?.fileName?.split('.')?.[1],
      requestType: 'form',
      decode: true,
    });
  };

  return (
    <Fragment>
      {info.map((ele, index) => (
        <Col span={ele.colSpan} key={index}>
          <Form.Item label={ele.label} labelCol={{ span: ele.colSpan === 24 ? 4 : 8 }}>
            {ele.render ? ele.render() : currentRow?.[ele.key] || '--'}
          </Form.Item>
        </Col>
      ))}
    </Fragment>
  );
}
