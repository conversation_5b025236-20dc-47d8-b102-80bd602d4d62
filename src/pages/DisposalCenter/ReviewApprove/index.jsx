import React from 'react';
import { Card, Tabs } from 'antd';
import WaitingApprove from './WaitingApprove';
import CurrentInitiated from './CurrentInitiated';
import { Licensee } from 'ponshine';
import NoAuth from '@/pages/NoAuth';

const { TabPane } = Tabs;

export default function index() {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab="待审核复机记录" key="1">
          <Licensee license="wait_approval_page" fallback={<NoAuth />}>
            <WaitingApprove />
          </Licensee>
        </TabPane>
        <TabPane tab="发起的涉诈骗案件复机申请" key="2">
          <Licensee license="init_approval_page" fallback={<NoAuth />}>
            <CurrentInitiated />
          </Licensee>
        </TabPane>
      </Tabs>
    </Card>
  );
}
