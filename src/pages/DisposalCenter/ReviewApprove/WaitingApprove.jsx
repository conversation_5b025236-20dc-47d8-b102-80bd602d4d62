import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
} from 'antd';

import StandardTable from '@/components/StandardTable';
import { selectPage, approve } from './services';
import { exportFile } from '@/utils/utils';

import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import ApproveModal from './ApproveModal';
import CascadeFormItem from '../components/CascadeFormItem';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;
  const pageSizeOptions = ['10', '20', '30', '50', '200'];

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 200,
      pageSizeOptions,
    },
  });
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [approveVisible, setApproveVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [currentRow, setCurrentRow] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);
  const [expand, setExpand] = useState(false);
  const localNetworkRef = useRef();

  // 获取表格数据
  const getListDatas = async ({ pageNum = 1, pageSize = 200, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      response.message !== '操作成功' && message.info(response.message);
      setSearchParams({ pageNum, pageSize, ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
          pageSizeOptions,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  // 搜索
  const handleSearch = () => {
    const values = getFieldsValue();
    const { optTime, approveTime } = values;
    getListDatas({
      ...values,
      beginApplicationTime: optTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endApplicationTime: optTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      handleTimeStart: approveTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      handleTimeEnd: approveTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      optTime: undefined,
      approveTime: undefined,
    });
  };

  // 分頁
  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  // 重置
  const onReset = () => {
    form.resetFields();
    getListDatas({
      localNetwork: localNetworkRef.current.getInitialValue(),
    });
    setSelectedRows([]);
  };
  // 重置
  const reloadTableWithFilter = () => {
    const {
      list,
      pagination: { total, pageSize, current },
    } = listData;
    const totalPage = total % pageSize === 0 ? total / pageSize : parseInt(total / pageSize) + 1;
    const isEmptyPage = listData.list.length === 1 || selectedRows.length === list.length;

    getListDatas({
      localNetwork: localNetworkRef.current.getInitialValue(),
      ...searchParams,
      // 如果是最后一页，且只有一条数据，未避免查出来总数和当前请求页对不上，改为请求当前页的上一页
      // 如果是第一页，则还是请求第一页
      pageNum: current === totalPage && isEmptyPage ? (current === 1 ? 1 : current - 1) : current,
    });
    setSelectedRows([]);
  };

  // 导出
  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/shutdownResumeApproval/exportResumeApproval',
      params: searchParams,
      title: '待审核复机记录',
      mime: 'xlsx',
      isVerifyhEncryption: true,
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  // 切换展开收起
  const handleToggle = () => {
    setExpand(!expand);
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  // 审批
  const handleAudit = (r) => {
    setCurrentRow(r);
    setApproveVisible(true);
  };

  let columns = [
    {
      title: '复机类型',
      width: 100,
      dataIndex: 'operateType',
      ellipsis: true,
      render: (v) => {
        const text = v === '2' ? '批量复机' : '涉诈骗案件复机';
        return (
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',

      ellipsis: true,
    },
    {
      title: '涉诈性质',
      width: 100,
      dataIndex: 'fraudNature',
      ellipsis: true,
    },
    {
      title: '关停备注',
      width: 100,
      dataIndex: 'shutdownRemark',
      ellipsis: true,
    },
    {
      title: '复机原因',
      width: 80,
      dataIndex: 'remark',
      ellipsis: true,
      forceShow: true,
      render: (v) => {
        return (
          <Tooltip
            placement="topLeft"
            overlayStyle={{ maxWidth: 600 }}
            title={
              <div
                style={{ whiteSpace: 'pre-wrap' }}
                dangerouslySetInnerHTML={{
                  __html: v || '',
                }}
              ></div>
            }
          >
            <span>{v}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '涉诈类型',
      dataIndex: 'fraudType',
      width: 100,
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '关停标签',
      dataIndex: 'shutdownTag',
      width: 100,
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '子标签',
      dataIndex: 'subtag',
      width: 100,
      ellipsis: true,
      forceShow: true,
    },

    {
      title: '操作人',
      width: 100,
      dataIndex: 'applicantName',
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 180,
      dataIndex: 'applicationTime',

      ellipsis: true,
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'opt',
      render: (v, r) => {
        return (
          <Tooltip title="单个复机审批">
            <a>
              <Icon type="audit" onClick={() => handleAudit(r)} />
            </a>
          </Tooltip>
        );
      },
    },
  ];

  const handleApprove = async () => {
    if (!selectedRows?.length) return message.error('至少勾选一个待审批项');
    let isNoPass =
      selectedRows.some((value) => value.operateType == 2) &&
      selectedRows.some((value) => value.operateType == 3);
    if (isNoPass) return message.error('复机类型不同,请重新选择');
    setApproveVisible(true);
  };

  return (
    <Fragment>
      <Row>
        <Form labelCol={{ span: 7 }} wrapperCol={{ span: 17 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作时间">
              {getFieldDecorator('optTime')(
                <RangePicker format={'YYYY-MM-DD'} style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="复机类型">
              {getFieldDecorator('operateType')(
                <Select placeholder="请选择" allowClear>
                  {['批量复机', '涉诈骗案件复机']?.map((ele, index) => (
                    <Select.Option value={String(index + 2)} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={6}>
            <LocalNetworkFormItem
              form={form}
              getListDatas={getListDatas}
              cref={localNetworkRef}
              // getListDatas={() => {}}
            />
          </Col>
          <div style={{ display: expand ? 'block' : 'none' }}>
            <Col span={6}>
              <Form.Item label="涉诈性质">
                {getFieldDecorator('fraudNature')(
                  <Select placeholder="请选择" allowClear>
                    {['涉诈骗案件', '涉嫌诈骗', '风险号码']?.map((ele, index) => (
                      <Select.Option value={ele} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>

            <CascadeFormItem form={form} colSpanList={[6, 6, 6]} />

            <Col span={6}>
              <Form.Item label="操作人">
                {getFieldDecorator('applicantName')(
                  <Input allowClear={true} placeholder="请输入" />,
                )}
              </Form.Item>
            </Col>
          </div>

          <Col align="right" span={24} style={{ marginBottom: 16 }}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
              重置
            </Button>
            {/* <Licensee license="outbound_export_all"> */}
            <Button type="primary" onClick={handleApprove} style={{ marginRight: 10 }}>
              审批
            </Button>
            {/* </Licensee> */}

            {/* <Licensee license="outbound_export_all"> */}
            <Button
              type="primary"
              onClick={handleExport}
              disabled={!listData?.list?.length}
              loading={exportLoading}
            >
              批量导出
            </Button>
            {/* </Licensee> */}

            <a style={{ marginLeft: 8, fontSize: 12 }} onClick={handleToggle}>
              {expand ? '收起' : '展开'} <Icon type={expand ? 'up' : 'down'} />
            </a>
          </Col>
        </Form>
      </Row>

      <StandardTable
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        isNeedAutoWidth={true}
        scroll={{
          x: 1000,
          y: 500,
        }}
      />
      <ApproveModal
        visible={approveVisible}
        reload={reloadTableWithFilter}
        cancel={() => {
          setApproveVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        currentRow={currentRow}
        selectedRows={selectedRows}
      />
    </Fragment>
  );
};
export default Form.create({})(Index);
