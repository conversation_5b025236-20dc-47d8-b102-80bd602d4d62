/*
 * @Author: zxw
 * @Date: 2023-09-04 15:21:40
 * @LastEditors: ss songsa@ponshine
 * @LastEditTime: 2024-03-14 15:59:25
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\ReviewApprove\ApproveModal.js
 * @Description:
 */
import { Modal, Form, Select, Input, Row, Col, message } from 'antd';
import React, { useEffect, useState } from 'react';
import SingleApproveInfo from './SingleApproveInfo';
import { selectFile } from '../OfflineShutdownRecords/services';

const { TextArea } = Input;

import { approve } from './services';

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const formItemLayout1 = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const approveType = [
  { value: '1', label: '审批通过' },
  { value: '2', label: '审批驳回' },
];

const ApproveModal = Form.create()(
  ({
    visible,
    cancel,
    form: { validateFields, getFieldDecorator, resetFields, setFieldsValue, getFieldValue },
    reload,
    currentRow = {},
    selectedRows,
  }) => {
    const [fileList, setFileList] = useState([]);
    const [submitLoading, setSubmitLoading] = useState(false);
    //复机类型均为涉诈骗案
    let isFraudCase = currentRow?.id
      ? currentRow.operateType === '3'
      : selectedRows?.every((ele) => ele.operateType === '3');
    useEffect(() => {
      if (visible) {
        getFileList();
      } else {
        submitLoading && setSubmitLoading(false);
      }
    }, [visible]);

    const handleOk = () => {
      validateFields(async (err, fieldsValue) => {
        if (err) return;
        setSubmitLoading(true);
        const response = await approve({
          ...fieldsValue,
          ids: currentRow.id || selectedRows?.map((ele) => ele.id)?.join(','),
        });
        setSubmitLoading(false);

        if (response.code === 200) {
          message.success(response.message);
          reload();
          cancel();
        } else {
          message.error(response.message);
        }
      });
    };

    const handleChange = (value) => {
      if (!value) return;
      setFieldsValue({ approvalRemark: approveType?.find((ele) => ele.value === value)?.label });
    };

    // 获取附件列表
    const getFileList = async () => {
      // setFileLoading(true);
      const response = await selectFile({ id: currentRow?.id });
      // setFileLoading(false);
      if (response.code === 200) {
        setFileList(response.data);
      } else {
        message.error(response.message);
      }
    };

    return (
      <Modal
        title={currentRow?.id ? '单个复机审批' : '批量复机审批'}
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
          setFileList([]);
        }}
        maskClosable={false}
        // width={currentRow?.id ? 700 : 500}
        width={700}
        confirmLoading={submitLoading}
      >
        <Form
          labelCol={currentRow?.id ? formItemLayout.labelCol : formItemLayout1.labelCol}
          wrapperCol={currentRow?.id ? formItemLayout.wrapperCol : formItemLayout1.wrapperCol}
        >
          <Row>
            {currentRow?.id && <SingleApproveInfo currentRow={currentRow} fileList={fileList} />}
            <Col span={currentRow?.id ? 12 : 24} style={{ marginRight: 16 }}>
              <Form.Item label="审批结果">
                {getFieldDecorator('approvalResult', {
                  rules: [
                    {
                      required: true,
                      message: '请选择审批结果',
                    },
                  ],
                })(
                  <Select placeholder="请选择" onChange={handleChange}>
                    {approveType.map((ele) => (
                      <Select.Option value={ele.value} key={ele.value}>
                        {ele.label}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>

            {isFraudCase && getFieldValue('approvalResult') == '1' && (
              <Col span={currentRow?.id ? 12 : 24} style={{ marginRight: 16 }}>
                <Form.Item label="审批备注">
                  {getFieldDecorator('approvalNote', {
                    // rules: [
                    //   {
                    //     required: (currentOperateTypeListPass ||currentFraudNature =='3')&& getFieldValue('approvalResult')=='1',
                    //     message: '请选择审批备注',
                    //   },
                    // ],
                  })(
                    <Select
                      placeholder="请选择"
                      getPopupContainer={(triggerNode) => triggerNode.parentElement}
                      allowClear
                    >
                      {['专班研判高风险', '专班研判中风险', '专班研判低风险', '去标拆机'].map(
                        (ele) => (
                          <Select.Option value={ele} key={ele}>
                            {ele}
                          </Select.Option>
                        ),
                      )}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            )}

            <Col span={currentRow?.id ? 12 : 24}>
              <Form.Item label="审批意见">
                {getFieldDecorator('approvalRemark', {
                  rules: [
                    {
                      required: true,
                      message: '请输入审批意见',
                    },
                    {
                      max: 200,
                      message: '最多输入200个字符',
                    },
                  ],
                })(<TextArea placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  },
);

export default ApproveModal;
