import React from 'react';
import { Icon, Table } from 'antd';
import { exportFile } from '@/utils/utils';

const FileContent = ({ loading, data }) => {
  const handleDownload = (record) => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/shutdownResumeApproval/downResumeApprovalFile',
      requestType: 'form',
      params: { id: record.id },
      decode: true,
      // title: record?.fileName?.split('.')?.[0],
      // mime: record?.fileName?.split('.')?.[1],
    });
  };
  const columns = [
    {
      title: '附件名',
      dataIndex: 'fileName',
      key: 'fileName',
      align: 'left',
    },

    {
      title: '下载',
      key: 'action',
      align: 'center',
      render: (v, r) => (
        <a>
          <Icon type="cloud-download" onClick={() => handleDownload(r)} />
        </a>
      ),
    },
  ];
  return (
    <Table
      rowKey="id"
      pagination={false}
      columns={columns}
      dataSource={data}
      loading={loading}
      locale={{ emptyText: '无附件' }}
    ></Table>
  );
};

export default FileContent;
