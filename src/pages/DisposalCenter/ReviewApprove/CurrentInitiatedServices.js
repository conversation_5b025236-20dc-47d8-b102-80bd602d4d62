import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/shutdownResumeApproval/pageInitApproval`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
// 审批
export async function approve(params) {
  return request(`/api/hn/shutdownResumeApproval/submitResumeApproval`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 撤回
export async function revocation(params) {
  return request(`/api/hn/shutdownResumeApproval/revokeApproval`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 编辑
export async function editData(params) {
  return request(`/api/hn/shutdownResumeApproval/editApproval`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 附件查询
export async function selectFile(params) {
  return request(`/api/hn/shutdownResumeApproval/getResumeApprovalFile`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
