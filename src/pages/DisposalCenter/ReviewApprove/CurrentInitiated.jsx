import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Modal,
  Popover,
} from 'antd';
import { Licensee } from 'ponshine';

import StandardTable from '@/components/StandardTable';
import { selectPage, approve, revocation, selectFile } from './CurrentInitiatedServices';
import { exportFile } from '@/utils/utils';

import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import CascadeFormItem from '../components/CascadeFormItem';
import EditModal from './EditModal';
import FileContent from './FileContent';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;
  const pageSizeOptions = ['10', '20', '30', '50'];

  const stateList = [
    {
      value: '3',
      label: '待省公司管理员审批',
    },
    {
      value: '8',
      label: '待市公司接口人审批',
    },
    {
      value: '4',
      label: '审批通过',
    },

    {
      value: '5',
      label: '审批驳回',
    },
    {
      value: '6',
      label: '无需审批',
    },
    {
      value: '7',
      label: '已撤回',
    },
  ];

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      pageSizeOptions,
    },
  });
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [editVisible, setEditVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [currentRow, setCurrentRow] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);
  const [expand, setExpand] = useState(false);
  const localNetworkRef = useRef();
  const [fileList, setFileList] = useState([]);
  const [fileLoading, setFileLoading] = useState(false);

  // 获取表格数据
  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      response.message !== '操作成功' && message.info(response.message);

      setSearchParams({ pageNum, pageSize, ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
          pageSizeOptions,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  // 搜索
  const handleSearch = () => {
    const values = getFieldsValue();
    const { optTime, approveTime } = values;
    getListDatas({
      ...values,
      beginApplicationTime: optTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endApplicationTime: optTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      handleTimeStart: approveTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      handleTimeEnd: approveTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      optTime: undefined,
      approveTime: undefined,
    });
  };

  // 分頁
  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  // 重置
  const onReset = () => {
    form.resetFields();
    getListDatas({
      localNetwork: localNetworkRef.current.getInitialValue(),
    });
    setSelectedRows([]);
  };
  // 重置
  const reloadTableWithFilter = () => {
    const {
      list,
      pagination: { total, pageSize, current },
    } = listData;
    const totalPage = total % pageSize === 0 ? total / pageSize : parseInt(total / pageSize) + 1;
    const isEmptyPage = listData.list.length === 1 || selectedRows.length === list.length;

    getListDatas({
      localNetwork: localNetworkRef.current.getInitialValue(),
      ...searchParams,
      // 如果是最后一页，且只有一条数据，未避免查出来总数和当前请求页对不上，改为请求当前页的上一页
      // 如果是第一页，则还是请求第一页
      pageNum: current === totalPage && isEmptyPage ? (current === 1 ? 1 : current - 1) : current,
    });
    setSelectedRows([]);
  };

  // 导出
  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/shutdownResumeApproval/exportInitApproval',
      params: { ...searchParams, pageSize: 1, pageNum: 1 },
      title: '发起的涉诈骗案件复机申请',
      mime: 'xlsx',
      isVerifyhEncryption: true,
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  // 切换展开收起
  const handleToggle = () => {
    setExpand(!expand);
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  // 审批
  const handleEdit = (r) => {
    setCurrentRow(r);
    setEditVisible(true);
  };

  // 撤回
  const handleRevocation = (r) => {
    Modal.confirm({
      title: '提示',
      content: '撤回后如需重新发起需从关停管理模块重新发起复机，请确认是否撤回该复机申请？',
      onOk: async () => {
        const response = await revocation({ id: r.id });
        if (response.code === 200) {
          message.success(response.message);
          onReset();
        } else {
          message.error(response.message);
        }
      },
      okText: '确认',
      cancelText: '取消',
    });
  };

  const getFileList = async (id) => {
    setFileLoading(true);
    const response = await selectFile({ id });
    setFileLoading(false);
    if (response.code === 200) {
      setFileList(response.data);
    } else {
      message.error(response.message);
    }
  };

  let columns = [
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'approvalStatus',
      ellipsis: true,
      render: (v) => {
        const str = stateList?.find((ele) => ele.value == v)?.label || '--';
        return (
          <Tooltip placement="topLeft" title={str}>
            <span>{str}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '涉诈性质',
      width: 100,
      dataIndex: 'fraudNature',
      ellipsis: true,
    },
    {
      title: '关停备注',
      width: 100,
      dataIndex: 'shutdownRemark',
      ellipsis: true,
    },
    {
      title: '复机原因',
      width: 80,
      dataIndex: 'remark',
      ellipsis: true,
      forceShow: true,
      render: (v) => {
        return (
          <Tooltip
            placement="topLeft"
            overlayStyle={{ maxWidth: 600 }}
            title={
              <div
                style={{ whiteSpace: 'pre-wrap' }}
                dangerouslySetInnerHTML={{
                  __html: v || '',
                }}
              ></div>
            }
          >
            <span>{v}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '涉诈类型',
      dataIndex: 'fraudType',
      width: 100,
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '关停标签',
      dataIndex: 'shutdownTag',
      width: 100,
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '子标签',
      dataIndex: 'subtag',
      width: 100,
      ellipsis: true,
      forceShow: true,
    },

    {
      title: '操作人',
      width: 100,
      dataIndex: 'applicantName',
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 180,
      dataIndex: 'applicationTime',
      ellipsis: true,
    },

    {
      title: '审批人',
      width: 100,
      dataIndex: 'secondProvinceApproverName',
      ellipsis: true,
    },
    {
      title: '审批时间',
      width: 180,
      dataIndex: 'secondProvinceApprovalTime',
      ellipsis: true,
    },
    {
      title: '审批备注',
      width: 200,
      dataIndex: 'approvalNote',
      ellipsis: true,
    },
    {
      title: '审批意见',
      width: 180,
      dataIndex: 'secondProvinceApprovalRemark',
      ellipsis: true,
    },

    {
      title: '变更人',
      width: 100,
      dataIndex: 'changePerson',
      ellipsis: true,
    },
    {
      title: '变更时间',
      width: 180,
      dataIndex: 'changeTime',
      ellipsis: true,
    },
    {
      title: '附件',
      width: 100,
      dataIndex: 'fileBtn',
      ellipsis: true,
      render: (v, r) => (
        <Popover
          content={<FileContent data={fileList} loading={fileLoading} />}
          title="附件"
          trigger="click"
          placement="rightTop"
        >
          <a>
            <Icon type="cloud-download" onClick={() => getFileList(r.id)} />
          </a>
        </Popover>
      ),
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'opt',
      fixed: 'right',
      render: (v, r) => {
        return (
          <Fragment>
            {['3', '5', '8']?.includes(r.approvalStatus) && (
              <Licensee license="reviewApprove_update">
                <Tooltip title="编辑">
                  <a>
                    <Icon type="edit" onClick={() => handleEdit(r)} style={{ marginRight: 8 }} />
                  </a>
                </Tooltip>
              </Licensee>
            )}
            {['3', '8']?.includes(r.approvalStatus) && (
              <Licensee license="reviewApprove_revoke">
                <Tooltip title="撤回">
                  <a>
                    <Icon
                      type="rollback"
                      onClick={() => handleRevocation(r)}
                      style={{ marginRight: 8 }}
                    />
                  </a>
                </Tooltip>
              </Licensee>
            )}
          </Fragment>
        );
      },
    },
  ];

  return (
    <Fragment>
      <Row>
        <Form labelCol={{ span: 7 }} wrapperCol={{ span: 17 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作时间">
              {getFieldDecorator('optTime')(
                <RangePicker format={'YYYY-MM-DD'} style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="状态">
              {getFieldDecorator('approvalStatus')(
                <Select placeholder="请选择" allowClear>
                  {stateList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={6}>
            <LocalNetworkFormItem
              form={form}
              getListDatas={getListDatas}
              cref={localNetworkRef}
              // getListDatas={() => {}}
            />
          </Col>
          <div style={{ display: expand ? 'block' : 'none' }}>
            <Col span={6}>
              <Form.Item label="涉诈性质">
                {getFieldDecorator('fraudNature')(
                  <Select placeholder="请选择" allowClear>
                    {['涉诈骗案件', '涉嫌诈骗', '风险号码']?.map((ele, index) => (
                      <Select.Option value={ele} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>

            <CascadeFormItem form={form} colSpanList={[6, 6, 6]} />

            <Col span={6}>
              <Form.Item label="操作人">
                {getFieldDecorator('applicantName')(
                  <Input allowClear={true} placeholder="请输入" />,
                )}
              </Form.Item>
            </Col>
          </div>

          <Col align="right" span={24} style={{ marginBottom: 16 }}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
              重置
            </Button>

            <Licensee license="reviewApprove_export">
              <Button
                type="primary"
                onClick={handleExport}
                disabled={!listData?.list?.length}
                loading={exportLoading}
              >
                批量导出
              </Button>
            </Licensee>

            <a style={{ marginLeft: 8, fontSize: 12 }} onClick={handleToggle}>
              {expand ? '收起' : '展开'} <Icon type={expand ? 'up' : 'down'} />
            </a>
          </Col>
        </Form>
      </Row>

      <StandardTable
        rowSelectionProps={false}
        showSelectCount={false}
        // selectedRows={selectedRows}
        // onSelectRow={handleSelectRows}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
          // y: 500,
        }}
      />

      <EditModal
        visible={editVisible}
        currentRow={currentRow}
        reload={onReset}
        cancel={() => {
          setEditVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
      />
    </Fragment>
  );
};
export default Form.create({})(Index);
