import React, { useEffect, useState, Fragment } from 'react';
import { But<PERSON>, Card, message, Form, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import { getIndexNumber } from '@/utils/utils';
import { HANDLE_WAY } from '../ModelConfigManage/constants';

const { RangePicker } = DatePicker;

import request from 'ponshine-request';

const Index = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 100,
      pageSizeOptions: ['10', '20', '30', '50', '100'],
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});

  const columns = [
    {
      title: '序号',
      align: 'center',
      width: 80,
      ellipsis: true,
      render: (text, record, index) => {
        const {
          pagination: { current = 1, pageSize = 100 },
        } = listData;
        return getIndexNumber(current, pageSize, index);
      },
    },
    {
      title: '处理编码',
      dataIndex: 'uuid',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '分析时间',
      dataIndex: 'analysisTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '入库时间',
      dataIndex: 'disposeTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '数据来源',
      dataIndex: 'dataSource',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '处置方式',
      dataIndex: 'disposeType',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: (text) => {
        const handleWay = HANDLE_WAY?.find((ele) => ele.value === text);
        return handleWay?.name || '--';
      },
    },
    {
      title: '模型编码',
      dataIndex: 'modelCode',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '号码',
      dataIndex: 'phoneNum',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '本地网',
      dataIndex: 'cityId',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '批次号',
      dataIndex: 'batchId',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '是否白名单',
      dataIndex: 'ifWhite',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: (text) => (text === '1' ? '是' : '否'),
    },
    {
      title: '白名单类型',
      dataIndex: 'whiteType',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '处置时间',
      dataIndex: 'disposeTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },

    {
      title: '是否处置成功',
      dataIndex: 'handleResult',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '失败原因',
      dataIndex: 'handleMsg',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '是否正式运行',
      dataIndex: 'dataType',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text) => (text === '上线' ? '是' : '否'),
    },
  ];

  const findTableDataPager = async ({ pageNum = 1, pageSize = 100, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/autoDispose/pageModelDataDisposeResult', {
      data: { pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
          pageSizeOptions: ['10', '20', '30', '50', '100'],
        },
      });
    } else {
      message.error(response.message);
    }
  };
  useEffect(() => {
    findTableDataPager();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { analyzeTime } = formValue;
    findTableDataPager({
      ...formValue,
      analysisTimeStart: analyzeTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      analysisTimeEnd: analyzeTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      analyzeTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
  };

  return (
    <Card>
      <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="处置编码">
              {getFieldDecorator('uuid')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="模型编码">
              {getFieldDecorator('modelCode')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,

            <Form.Item label="批次号">
              {getFieldDecorator('batchId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="分析日期">
              {getFieldDecorator('analyzeTime')(
                <RangePicker allowClear format="YYYY-MM-DD" style={{ width: '100%' }} />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                查询
              </Button>
              <Button style={{ marginRight: 16 }} onClick={handleReset}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
        scroll={{ y: 500 }}
      />
    </Card>
  );
};

export default Form.create()(Index);
