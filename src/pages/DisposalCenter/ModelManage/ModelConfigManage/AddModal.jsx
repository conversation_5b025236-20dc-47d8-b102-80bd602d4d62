import { Modal, Form, Input, message, Select, TreeSelect, Checkbox } from 'antd';
import React, { Fragment, useState, useEffect } from 'react';
import request from 'ponshine-request';
import { getSystemConfigListByConfigType } from '@/services/common';

const { SHOW_PARENT, SHOW_ALL } = TreeSelect;

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 14 },
};

const initFilterWhiteType = [
  {
    title: 'Node1',
    value: '0-0',
    key: '0-0',
    children: [
      {
        title: 'Child Node1',
        value: '0-0-0',
        key: '0-0-0',
      },
      {
        title: 'Child Node2',
        value: '0-0-1',
        key: '0-0-1',
      },
    ],
  },
  {
    title: 'Node2',
    value: '0-1',
    key: '0-1',
    children: [
      {
        title: 'Child Node3',
        value: '0-1-0',
        key: '0-1-0',
      },
      {
        title: 'Child Node4',
        value: '0-1-1',
        key: '0-1-1',
      },
      {
        title: 'Child Node5',
        value: '0-1-2',
        key: '0-1-2',
      },
    ],
  },
];

const AddModal = Form.create()(
  ({
    visible,
    currentRow,
    cancel,
    form: {
      validateFieldsAndScroll,
      getFieldDecorator,
      resetFields,
      getFieldValue,
      setFieldsValue,
      validateFields,
    },
    onReload,
    modelStateData,
    filterWhite,
    handleWayData,
    recordIdData,
    dataSourceData,
    modelTypeData,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);

    const [recordIdInfo, setRecordIdInfo] = useState({});
    const [filteredWhitelist, setFilteredWhitelist] = useState([]);
    const [organizationData, setOrganizationData] = useState([]);

    const handleOk = () => {
      validateFieldsAndScroll(async (err, fieldsValue) => {
        if (err) return;
        setConfirmLoading(true);
        const { disposeScope, filterWhiteType } = fieldsValue;
        const response = await request(
          currentRow?.id ? '/api/hn/autoModel/update' : '/api/hn/autoModel/add',
          {
            data: {
              ...fieldsValue,
              id: currentRow?.id,
              disposeScope: disposeScope?.join(','),
              filterWhiteType: filterWhiteType?.join(','),
            },
            method: 'POST',
            requestType: 'json',
          },
        );
        setConfirmLoading(false);
        if (response.code === 200) {
          message.success(response.message);
          cancel();
          onReload();
        } else {
          message.error(response.message);
        }
      });
    };

    const getItemValue = (value = '', data = []) => {
      if (value) {
        return value;
      }
      if (data?.length > 1) {
        return undefined;
      }
      return data?.[0];
    };

    const handleChangeRecordId = async (
      v,
      qualitative,
      shutdownType,
      relatedShutdown,
      relatedShutdownType,
    ) => {
      if (v) {
        const response = await request('/api/hn/shutdownTag/getHnFraudShutdownTagByRecordId', {
          method: 'GET',
          params: { recordId: v },
        });
        if (response.code === 200) {
          const qualitativeData = response?.data?.qualitative?.split(',') || [];
          const shutdownTypeData = response?.data?.shutdownType?.split(',') || [];
          const relatedShutdownData = response?.data?.relatedShutdown?.split(',') || [];
          const relatedShutdownTypeData = response?.data?.relatedShutdownType?.split(',') || [];
          setRecordIdInfo({
            fraudNatureData: qualitativeData,
            shutdownTypeData: shutdownTypeData,
            relatedShutdownData: relatedShutdownData,
            relatedShutdownTypeData: relatedShutdownTypeData,
          });
          setFieldsValue({
            qualitative: getItemValue(qualitative, qualitativeData),
            shutdownType: getItemValue(shutdownType, shutdownTypeData),
            relatedShutdown: getItemValue(relatedShutdown, relatedShutdownData),
            relatedShutdownType: getItemValue(relatedShutdownType, relatedShutdownTypeData),
          });
        }
      } else {
        setRecordIdInfo({});
        setFieldsValue({
          qualitative: undefined,
          shutdownType: undefined,
          relatedShutdown: undefined,
          relatedShutdownType: undefined,
        });
      }
    };

    const getWhiteList = async () => {
      const response = await request('/api/hn/autoModel/getAllWhiteType', {
        method: 'GET',
      });
      if (response.code === 200) {
        setFilteredWhitelist(response?.data || []);
      } else {
        message.error(response?.message);
      }
    };

    const getOrganizationData = async () => {
      const response = await getSystemConfigListByConfigType({
        configType: 'organization',
      });
      if (response.code === 200) {
        setOrganizationData(response?.data || []);
      } else {
        message.error(response?.message);
      }
    };
    useEffect(() => {
      if (visible) {
        getWhiteList();
        getOrganizationData();
        if (currentRow?.recordId) {
          handleChangeRecordId(
            currentRow?.recordId,
            currentRow?.qualitative,
            currentRow?.shutdownType,
            currentRow?.relatedShutdown,
            currentRow?.relatedShutdownType,
          );
        }
      }
    }, [visible, currentRow]);

    return (
      <Modal
        title={`${currentRow?.id ? '编辑' : '新增'}模型`}
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
        width={700}
      >
        <Form {...formItemLayout}>
          <Form.Item label="模型类型">
            {getFieldDecorator('modelType', {
              initialValue: currentRow?.modelType,
              rules: [
                {
                  required: true,
                  message: '请输入',
                },
              ],
            })(
              <Select
                placeholder="请选择"
                allowClear
                getPopupContainer={(triggerNode) => triggerNode.parentElement}
              >
                {modelTypeData?.map((ele) => (
                  <Select.Option value={ele.value}>{ele.name}</Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="模型编码">
            {getFieldDecorator('modelCode', {
              initialValue: currentRow?.modelCode,
              rules: [
                {
                  required: true,
                  message: '请输入',
                },
              ],
            })(<Input placeholder="请输入" allowClear />)}
          </Form.Item>
          <Form.Item label="模型名称">
            {getFieldDecorator('modelName', {
              initialValue: currentRow?.modelName,
              rules: [
                {
                  required: true,
                  message: '请输入',
                },
              ],
            })(<Input placeholder="请输入" allowClear />)}
          </Form.Item>
          <Form.Item label="数据来源">
            {getFieldDecorator('dataSource', {
              initialValue: currentRow?.dataSource,
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Select
                placeholder="请选择"
                allowClear
                getPopupContainer={(triggerNode) => triggerNode.parentElement}
              >
                {dataSourceData?.map((ele) => (
                  <Select.Option value={ele.value}>{ele.label}</Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="模型状态">
            {getFieldDecorator('modelStatus', {
              initialValue: currentRow?.modelStatus,
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Select
                placeholder="请选择"
                allowClear
                getPopupContainer={(triggerNode) => triggerNode.parentElement}
              >
                {modelStateData?.map((ele) => (
                  <Select.Option value={ele}>{ele}</Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="模型说明">
            {getFieldDecorator('modelDesc', {
              initialValue: currentRow?.modelDesc,
              rules: [
                {
                  required: true,
                  message: '请输入',
                },
              ],
            })(<Input.TextArea placeholder="请输入" allowClear rows={3} />)}
          </Form.Item>
          <Form.Item label="处置范围">
            {getFieldDecorator('disposeScope', {
              initialValue:
                currentRow?.disposeScope?.split(',') || organizationData.map((item) => item.value),
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Select
                placeholder="请选择"
                allowClear
                mode="multiple"
                getPopupContainer={(triggerNode) => triggerNode.parentElement}
                dropdownRender={(menu) => (
                  <div onMouseDown={(e) => e.preventDefault()}>
                    <div
                      style={{
                        padding: '8px 12px',
                        borderBottom: '1px solid #e8e8e8',
                      }}
                    >
                      <Checkbox
                        indeterminate={
                          getFieldValue('disposeScope')?.length > 0 &&
                          getFieldValue('disposeScope')?.length < organizationData.length
                        }
                        checked={getFieldValue('disposeScope')?.length === organizationData.length}
                        onChange={(e) => {
                          if (e.target.checked) {
                            // 全选
                            setFieldsValue({
                              disposeScope: organizationData.map((item) => item.value),
                            });
                          } else {
                            // 取消全选
                            setFieldsValue({
                              disposeScope: [],
                            });
                          }
                        }}
                      >
                        全选
                      </Checkbox>
                    </div>
                    {menu}
                  </div>
                )}
              >
                {organizationData?.map((ele) => (
                  <Select.Option value={ele.value}>{ele.name}</Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="处置方式">
            {getFieldDecorator('disposeType', {
              initialValue: currentRow?.disposeType || '不处置',
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Select
                placeholder="请选择"
                allowClear
                getPopupContainer={(triggerNode) => triggerNode.parentElement}
              >
                {handleWayData?.map((ele) => (
                  <Select.Option value={ele.value} disabled={['2', '3']?.includes(ele.value)}>
                    {ele.name}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          {/* 号码处置 */}
          {getFieldValue('disposeType') === '1' && (
            <Fragment>
              <Form.Item label="Record_id">
                {getFieldDecorator('recordId', {
                  initialValue: currentRow?.recordId,
                  rules: [
                    {
                      required: true,
                      message: '请输入',
                    },
                  ],
                })(
                  <Select
                    placeholder="请选择"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                    onChange={(v) => handleChangeRecordId(v)}
                    getPopupContainer={(triggerNode) => triggerNode.parentElement}
                  >
                    {recordIdData?.map((ele) => (
                      <Select.Option value={ele}>{ele}</Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
              <Form.Item label="涉诈性质">
                {getFieldDecorator('qualitative', {
                  initialValue: currentRow?.qualitative,
                  rules: [
                    {
                      required: true,
                      message: '请选择',
                    },
                  ],
                })(
                  <Select
                    placeholder="请选择"
                    allowClear
                    getPopupContainer={(triggerNode) => triggerNode.parentElement}
                    disabled={recordIdInfo?.fraudNatureData?.length === 1}
                  >
                    {recordIdInfo?.fraudNatureData?.map((ele) => (
                      <Select.Option value={ele}>{ele}</Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
              <Form.Item label="停机类型">
                {getFieldDecorator('shutdownType', {
                  initialValue: currentRow?.shutdownType,
                  rules: [
                    {
                      required: true,
                      message: '请选择',
                    },
                  ],
                })(
                  <Select
                    placeholder="请选择"
                    allowClear
                    getPopupContainer={(triggerNode) => triggerNode.parentElement}
                    disabled={recordIdInfo?.shutdownTypeData?.length === 1}
                  >
                    {recordIdInfo?.shutdownTypeData?.map((ele) => (
                      <Select.Option value={ele}>{ele}</Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
              <Form.Item label="是否关联关停">
                {getFieldDecorator('relatedShutdown', {
                  initialValue: currentRow?.relatedShutdown,
                  rules: [
                    {
                      required: true,
                      message: '请选择',
                    },
                  ],
                })(
                  <Select
                    placeholder="请选择"
                    allowClear
                    getPopupContainer={(triggerNode) => triggerNode.parentElement}
                    disabled={recordIdInfo?.relatedShutdownData?.length === 1}
                  >
                    {recordIdInfo?.relatedShutdownData?.map((ele) => (
                      <Select.Option value={ele}>{ele}</Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
              <Form.Item label="关联关停停机规则">
                {getFieldDecorator('relatedShutdownType', {
                  initialValue: currentRow?.relatedShutdownType,
                  rules: [
                    {
                      required: getFieldValue('relatedShutdown') === '是',
                      message: '请选择',
                    },
                  ],
                })(
                  <Select
                    placeholder="请选择"
                    allowClear
                    getPopupContainer={(triggerNode) => triggerNode.parentElement}
                    disabled={recordIdInfo?.relatedShutdownTypeData?.length === 1}
                  >
                    {recordIdInfo?.relatedShutdownTypeData?.map((ele) => (
                      <Select.Option value={ele}>{ele}</Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
              <Form.Item label="是否过滤白名单">
                {getFieldDecorator('filterWhite', {
                  initialValue: currentRow?.filterWhite,
                  rules: [
                    {
                      required: true,
                      message: '请选择',
                    },
                  ],
                })(
                  <Select
                    placeholder="请选择"
                    allowClear
                    getPopupContainer={(triggerNode) => triggerNode.parentElement}
                  >
                    {filterWhite?.map((ele, index) => (
                      <Select.Option value={ele.value}>{ele.name}</Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
              {getFieldValue('filterWhite') === '1' && (
                <Form.Item label="过滤白名单">
                  {getFieldDecorator('filterWhiteType', {
                    initialValue: currentRow?.filterWhiteType?.split(','),
                    rules: [
                      {
                        required: true,
                        message: '请选择',
                      },
                    ],
                  })(
                    <TreeSelect
                      showCheckedStrategy={SHOW_PARENT}
                      treeData={filteredWhitelist}
                      treeCheckable={true}
                      getPopupContainer={(triggerNode) => triggerNode.parentElement}
                    />,
                    // <Select
                    //   placeholder="请选择"
                    //   allowClear
                    //   getPopupContainer={(triggerNode) => triggerNode.parentElement}
                    //   mode="multiple"
                    // >
                    //   {filteredWhitelist?.map((ele) => (
                    //     <Select.Option value={ele.whiteCode}>{ele.whiteName}</Select.Option>
                    //   ))}
                    // </Select>,
                  )}
                </Form.Item>
              )}
              <Form.Item label="同模型二次关停时间间隔">
                {getFieldDecorator('retryInterval', {
                  initialValue: currentRow?.retryInterval,
                  rules: [
                    {
                      required: true,
                      message: '请输入',
                    },
                    {
                      pattern: /^[1-9]\d*$/,
                      message: '只能输入正整数',
                    },
                  ],
                })(<Input placeholder="请输入" allowClear suffix="小时" />)}
              </Form.Item>
              <Form.Item label="监控阈值上限">
                {getFieldDecorator('monitorThreshold', {
                  initialValue: currentRow?.monitorThreshold,
                  rules: [
                    {
                      required: true,
                      message: '请输入',
                    },
                    {
                      pattern: /^[1-9]\d*$/,
                      message: '只能输入正整数',
                    },
                    {
                      validator: (rule, value, callback) => {
                        if (value > 99999999) {
                          callback('不能大于99999999');
                        }
                        callback();
                      },
                    },
                  ],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
              <Form.Item label="熔断阈值上限">
                {getFieldDecorator('meltThreshold', {
                  initialValue: currentRow?.meltThreshold,
                  rules: [
                    {
                      required: true,
                      message: '请输入',
                    },
                    {
                      pattern: /^[1-9]\d*$/,
                      message: '只能输入正整数',
                    },
                    {
                      validator: (rule, value, callback) => {
                        if (value > 99999999) {
                          callback('不能大于99999999');
                        }
                        callback();
                      },
                    },
                  ],
                })(<Input placeholder="请输入正整数" allowClear />)}
              </Form.Item>
            </Fragment>
          )}
        </Form>
      </Modal>
    );
  },
);

export default AddModal;
