import React, { Fragment, useEffect, useState, useRef } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import {
  MODEL_STATE,
  HANDLE_WAY,
  CONTRACT_TYPE,
  FILTER_WHITE,
  DATA_SOURCE,
  MODEL_TYPE,
} from './constants';
import { getIndexNumber } from '@/utils/utils';

const { RangePicker } = DatePicker;

import request from 'ponshine-request';

const Index = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const serachParams = useRef();
  const [recordIdData, setRecordIdData] = useState([]);

  const columns = [
    {
      title: '序号',
      align: 'center',
      width: 80,
      ellipsis: true,
      render: (text, record, index) => {
        const {
          pagination: { current = 1, pageSize = 10 },
        } = listData;
        return getIndexNumber(current, pageSize, index);
      },
    },
    {
      title: '模型编码',
      dataIndex: 'modelCode',
      align: 'center',
      width: 140,
      ellipsis: true,
    },
    {
      title: '模型名称',
      dataIndex: 'modelName',
      align: 'center',
      width: 140,
      ellipsis: true,
    },
    {
      title: '数据来源',
      dataIndex: 'dataSource',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: (v) => {
        const obj = DATA_SOURCE?.find((ele) => ele.value === v);
        return obj?.label || '--';
      },
    },
    {
      title: '模型类型',
      dataIndex: 'modelType',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '模型状态',
      dataIndex: 'modelStatus',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '模型说明',
      dataIndex: 'modelDesc',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '维护信息',
      dataIndex: 'maintenanceInfo',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: () => <a>查看</a>,
    },
    {
      title: '管理信息',
      dataIndex: 'managementInfo',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: () => <a>查看</a>,
    },
    {
      title: '处置范围',
      dataIndex: 'disposeScopeName',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '处置方式',
      dataIndex: 'disposeType',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: (v) => {
        const handleWay = HANDLE_WAY?.find((ele) => ele.value === v);
        return handleWay?.name || '--';
      },
    },
    {
      title: 'Record_id',
      dataIndex: 'recordId',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '涉诈性质',
      dataIndex: 'qualitative',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '停机类型',
      dataIndex: 'shutdownType',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '是否关联关停',
      dataIndex: 'relatedShutdown',
      align: 'center',
      width: 140,
      ellipsis: true,
    },
    {
      title: '关联关停停机规则',
      dataIndex: 'relatedShutdownType',
      align: 'center',
      width: 160,
      ellipsis: true,
    },
    {
      title: '签约类型',
      dataIndex: 'signType',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '是否过滤白名单',
      dataIndex: 'filterWhite',
      align: 'center',
      width: 150,
      ellipsis: true,
      render: (v) => {
        const obj = FILTER_WHITE?.find((ele) => ele.value === v);
        return obj?.name || '--';
      },
    },
    {
      title: '过滤白名单类型',
      dataIndex: 'filterWhiteTypeName',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '同模型二次关停时间间隔(小时)',
      dataIndex: 'retryInterval',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '监控阈值上限',
      dataIndex: 'monitorThreshold',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '熔断阈值上限',
      dataIndex: 'meltThreshold',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '提交时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '更新用户',
      dataIndex: 'updateUser',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'gmtUpdate',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
  ];

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/autoModel/pageModelInfoRecord', {
      data: { pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };
  const getRecordIdData = async () => {
    const response = await request('/api/hn/shutdownTag/getAllRecordId', {
      method: 'GET',
    });
    if (response.code === 200) {
      setRecordIdData(response.data);
    } else {
      message.error(response.message);
    }
  };
  useEffect(() => {
    findTableDataPager();
    getRecordIdData();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { updateTime } = formValue;
    findTableDataPager({
      ...formValue,
      gmtCreateStart: updateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      gmtCreateEnd: updateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      updateTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
  };

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="模型类型">
              {getFieldDecorator('modelType')(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  {MODEL_TYPE?.map((ele) => (
                    <Select.Option value={ele.value}>{ele.name}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
            <Form.Item label="模型名称">
              {getFieldDecorator('modelName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="模型编码">
              {getFieldDecorator('modelCode')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="模型状态">
              {getFieldDecorator('modelStatus')(
                <Select placeholder="请选择" allowClear>
                  {MODEL_STATE?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
            <Form.Item label="处置方式">
              {getFieldDecorator('disposeType')(
                <Select placeholder="请选择" allowClear>
                  {HANDLE_WAY?.map((ele) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
            ...(getFieldValue('disposeType') === '1'
              ? [
                  <Form.Item label="Record_id">
                    {getFieldDecorator('recordId')(
                      <Select placeholder="请选择" allowClear>
                        {recordIdData?.map((ele) => (
                          <Select.Option value={ele} key={ele}>
                            {ele}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>,
                ]
              : []),
            ...(getFieldValue('disposeType') === '2'
              ? [
                  <Form.Item label="签约类型">
                    {getFieldDecorator('signType')(
                      <Select placeholder="请选择" allowClear>
                        {CONTRACT_TYPE?.map((ele) => (
                          <Select.Option value={ele.value} key={ele.value}>
                            {ele.name}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>,
                ]
              : []),

            <Form.Item label="更新日期">
              {getFieldDecorator('updateTime')(
                <RangePicker allowClear format="YYYY-MM-DD" style={{ width: '100%' }} />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
      />
    </Card>
  );
};

export default Form.create()(Index);
