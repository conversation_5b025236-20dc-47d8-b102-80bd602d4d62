// 模型状态
export const MODEL_STATE = ['上线', '下线', '试运行'];

// 处置方式
export const HANDLE_WAY = [
  {
    name: '不处置',
    value: '0',
  },
  {
    name: '号码关停',
    value: '1',
  },
  {
    name: '号码签约',
    value: '2',
  },
  {
    name: '外呼校验',
    value: '3',
  },
];

// 签约类型
export const CONTRACT_TYPE = [
  { name: '签入', value: '签入' },
  { name: '签出', value: '签出' },
];

// 过滤白名单
export const FILTER_WHITE = [
  { name: '过滤', value: '1' },
  { name: '不过滤', value: '2' },
];

// 数据来源
export const DATA_SOURCE = [
  {
    value: 'user_fz',
    label: '模型',
  },
  {
    value: 'aizj',
    label: 'AI质检',
  },
  {
    value: 'edc',
    label: '大数据租户',
  },
];

export const MODEL_TYPE = [
  { name: 'AI类', value: 'AI类' },
  { name: '外呼类', value: '外呼类' },
  { name: '话务类', value: '话务类' },
  { name: '终端类', value: '终端类' },
  { name: '专项类', value: '专项类' },
];
