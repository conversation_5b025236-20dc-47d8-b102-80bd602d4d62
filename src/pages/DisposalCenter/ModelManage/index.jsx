import React from 'react';
import { Card, Tabs } from 'antd';
import { Licensee } from 'ponshine';
import NoAuth from '@/pages/NoAuth';
const { TabPane } = Tabs;

import ModelConfigManage from './ModelConfigManage';
import ModelDataManage from './ModelDataManage';

export default function index() {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab="模型配置管理" key="1">
          <Licensee license="disposalCenter_modelConfigManage" fallback={<NoAuth />}>
            <ModelConfigManage />
          </Licensee>
        </TabPane>
        <TabPane tab="模型数据管理" key="2">
          <Licensee license="disposalCenter_modelDataManage" fallback={<NoAuth />}>
            <ModelDataManage />
          </Licensee>
        </TabPane>
      </Tabs>
    </Card>
  );
}
