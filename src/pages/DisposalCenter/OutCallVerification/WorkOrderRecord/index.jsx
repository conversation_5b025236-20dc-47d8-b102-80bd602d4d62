import React, { useMemo, useState, useEffect, Fragment } from 'react';
import { Card, Form, Row, Col, DatePicker, Select, Input, Button, message, Icon } from 'antd';
import moment from 'moment';
import { Licensee } from 'ponshine';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { getInitialQueryParamByUser } from '../commomServices';
import { exportFile } from '@/utils/utils';
import WorkOrderConfig from './components/WorkOrderConfig';
import DetailModal from './components/DetailModal';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [configVisible, setConfigVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState({});

  const [searchParams, setSearchParams] = useState({});
  const [searchList, setSearchList] = useState({});
  const [expand, setExpand] = useState(false);

  // 获取本地网和是否电渠下拉
  const getSearchList = async () => {
    const response = await getInitialQueryParamByUser();
    if (response.code === 200) {
      setSearchList(response?.data || {});
      getListDatas({
        localNetwork: getInitialValue(response?.data?.localNetworkList),
        ifOnline: getInitialValue(response?.data?.ifOnlineList),
      });
    } else {
      getListDatas();
      message.error(response.message);
    }
  };

  // 获取表格数据
  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getSearchList();
  }, []);

  // 搜索
  const handleSearch = () => {
    const values = getFieldsValue();
    const { createTime, handleTime } = values;
    getListDatas({
      ...values,
      createTimeStart: createTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      createTimeEnd: createTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      handleTimeStart: handleTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      handleTimeEnd: handleTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      createTime: undefined,
      handleTime: undefined,
    });
  };

  // 分頁
  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  // 重置
  const onReset = () => {
    form.resetFields();
    getListDatas({
      localNetwork: getInitialValue(searchList?.localNetworkList),
      ifOnline: getInitialValue(searchList?.ifOnlineList),
    });
  };

  // 导出
  const handleExport = () => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/outbound/exportAllOutboundWorkOrder',
      params: { ...searchParams },
      decode: true,
    });
  };

  // 切换展开收起
  const handleToggle = () => {
    setExpand(!expand);
  };

  let columns = [
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '工单号',
      width: 120,
      dataIndex: 'orderNo',
      ellipsis: true,
    },
    {
      title: '涉诈标签',
      dataIndex: 'fraudTag',
      width: 120,

      ellipsis: true,
    },
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',

      ellipsis: true,
    },
    {
      title: '是否电渠',
      width: 120,
      dataIndex: 'ifOnline',

      ellipsis: true,
    },
    {
      title: '创建时间',
      width: 180,
      dataIndex: 'gmtCreate',

      ellipsis: true,
    },
    {
      title: '状态',
      width: 120,
      dataIndex: 'orderStatus',

      ellipsis: true,
    },
    {
      title: '接单人',
      width: 100,
      dataIndex: 'receiverName',
      forceShow: true,
      ellipsis: true,
    },
    {
      title: '接单时间',
      width: 180,
      dataIndex: 'receiveTime',

      ellipsis: true,
    },
    {
      title: '报结时间',
      width: 180,
      dataIndex: 'handleTime',

      ellipsis: true,
    },
    {
      title: '是否涉诈',
      width: 100,
      dataIndex: 'ifFraud',
    },
    {
      title: '分类',
      width: 120,
      dataIndex: 'fraudType',
      forceShow: true,

      ellipsis: true,
    },
    {
      title: '外呼成功号码数',
      width: 220,
      dataIndex: 'outboundSuccessCount',
      ellipsis: true,
    },
    {
      title: '外呼失败号码数',
      width: 220,
      dataIndex: 'outboundFailedCount',
      ellipsis: true,
    },
    {
      title: '是否关联处置',
      width: 120,
      dataIndex: 'ifRelated',

      ellipsis: true,
    },
    {
      title: '关联处置结果',
      width: 120,
      dataIndex: 'relatedHandleResult',
      ellipsis: true,
    },
    {
      title: '被叫号码',
      width: 150,
      dataIndex: 'calledNum',
      ellipsis: true,
      forceShow: true,
    },
    {
      title: '是否超时',
      width: 120,
      dataIndex: 'ifTimeout',

      ellipsis: true,
      render: (v) => {
        return <span style={{ color: v === '是' ? 'red' : '' }}>{v}</span>;
      },
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'opt',
      ellipsis: true,
      fixed: 'right',
      render: (v, r) => {
        return <Icon type="read" style={{ color: '#1890ff' }} onClick={() => getDetial(r)} />;
      },
    },
  ];

  const getDetial = async (r) => {
    setCurrentRow(r);
    setDetailVisible(true);
  };

  const getInitialValue = (data) => {
    if (data?.length === 1) {
      return data?.[0];
    } else {
      return undefined;
    }
  };

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="工单号">
              {getFieldDecorator('orderNo')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="本地网">
              {getFieldDecorator('localNetwork', {
                initialValue: getInitialValue(searchList?.localNetworkList),
              })(
                <Select
                  placeholder="请选择"
                  allowClear={!Boolean(getInitialValue(searchList?.localNetworkList))}
                >
                  {searchList?.localNetworkList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="是否电渠">
              {getFieldDecorator('ifOnline', {
                initialValue: getInitialValue(searchList?.ifOnlineList),
              })(
                <Select
                  placeholder="请选择"
                  allowClear={!Boolean(getInitialValue(searchList?.ifOnlineList))}
                >
                  {searchList?.ifOnlineList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <div style={{ display: expand ? 'block' : 'none' }}>
            <Col span={6}>
              <Form.Item label="状态">
                {getFieldDecorator('orderStatus')(
                  <Select placeholder="请选择" allowClear>
                    {['待接单', '待处理', '已报结']?.map((ele, index) => (
                      <Select.Option value={ele} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="是否涉诈">
                {getFieldDecorator('ifFraud')(
                  <Select placeholder="请选择" allowClear>
                    {['是', '否']?.map((ele, index) => (
                      <Select.Option value={ele} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="涉诈标签">
                {getFieldDecorator('fraudTag')(<Input allowClear={true} placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="创建时间">
                {getFieldDecorator('createTime')(
                  <RangePicker
                    format={'YYYY-MM-DD'}
                    disabledDate={disabledDate}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="报结时间">
                {getFieldDecorator('handleTime')(
                  <RangePicker
                    format={'YYYY-MM-DD'}
                    disabledDate={disabledDate}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="分类">
                {getFieldDecorator('fraudType')(<Input allowClear={true} placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="是否关联处置">
                {getFieldDecorator('ifRelated')(
                  <Select placeholder="请选择">
                    {['是', '否']?.map((ele, index) => (
                      <Select.Option value={ele} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="关联处置结果">
                {getFieldDecorator('relatedHandleResult')(<Input placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="是否超时">
                {getFieldDecorator('ifTimeout')(
                  <Select placeholder="请选择">
                    {['是', '否']?.map((ele, index) => (
                      <Select.Option value={ele} key={index}>
                        {ele}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </div>

          <Col align="right" span={24} style={{ marginBottom: 16 }}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
              重置
            </Button>
            <Licensee license="outbound_user_config">
              <Button
                type="primary"
                style={{ marginRight: 10 }}
                onClick={() => {
                  setConfigVisible(true);
                }}
              >
                工单配置
              </Button>
            </Licensee>
            <Licensee license="outbound_export_all">
              <Button type="" onClick={handleExport} disabled={!listData?.list?.length}>
                数据导出
              </Button>
            </Licensee>

            <a style={{ marginLeft: 8, fontSize: 12 }} onClick={handleToggle}>
              {expand ? '收起' : '展开'} <Icon type={expand ? 'up' : 'down'} />
            </a>
          </Col>
        </Form>
      </Row>

      <StandardTable
        detailColumns={columns}
        tools={true}
        rowSelectionProps={false}
        showSelectCount={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        isNeedAutoWidth={true}
        scroll={{
          x: 1000,
          // y: 500,
        }}
      />
      <WorkOrderConfig
        visible={configVisible}
        cancel={() => {
          setConfigVisible(false);
        }}
      />
      <DetailModal
        visible={detailVisible}
        cancel={() => {
          setDetailVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        currentRow={currentRow}
      />
    </Card>
  );
};
export default Form.create({})(Index);
