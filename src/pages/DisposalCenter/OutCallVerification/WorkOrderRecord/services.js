/*
 * @Author: zxw
 * @Date: 2023-08-15 13:31:41
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-23 15:53:17
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\OutCallVerification\WorkOrderRecord\services.js
 * @Description:
 */
import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/outbound/pageAllOutboundWorkOrder`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取配置列表
export async function getConfigList(params) {
  return request(`/api/hn/outboundUser/getOutboundUserConfig`, {
    method: 'GET',
    params,
  });
}

// 获取可选配置人员
export async function getConfigUser(params) {
  return request(`/api/hn/outboundUser/getUserListByLocalNetwork`, {
    method: 'GET',
    params,
  });
}

// 编辑配置
export async function configEdit(params) {
  return request(`/api/hn/outboundUser/updateOutboundUserConfig`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
