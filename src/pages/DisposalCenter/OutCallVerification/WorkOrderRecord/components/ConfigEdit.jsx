import React, { useEffect, useState } from 'react';
import { Form, Modal, Select, Row, Col, message, DatePicker } from 'antd';
import { getConfigUser, configEdit } from '../services';

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const ConfigEdit = ({ visible, form, cancel, reload, currentRow }) => {
  const { getFieldDecorator, validateFields, setFieldsValue, resetFields } = form;
  const [configUserList, setConfigUserList] = useState([]);

  // 获取人员列表
  const findConfigUser = async () => {
    const response = await getConfigUser({ localNetwork: currentRow?.localNetwork });
    if (response.code === 200) {
      setConfigUserList(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  // 确认配置
  const handleOk = () => {
    validateFields(async (err, values) => {
      if (err) return;
      const response = await configEdit({ id: currentRow?.id, ...values });
      if (response.code === 200) {
        cancel();
        reload();
        message.success(response.message);
      } else {
        message.error(response.message);
      }
    });
  };

  useEffect(() => {
    if (visible) {
      findConfigUser();
    }
  }, [visible]);

  return (
    <Modal
      visible={visible}
      title="编辑"
      onOk={handleOk}
      okText="保存"
      onCancel={cancel}
      afterClose={() => {
        resetFields();
      }}
      maskClosable={false}
    >
      <Form {...formItemLayout}>
        <Row>
          <Col span={24}>
            <Form.Item label="本地网">{currentRow?.localNetwork}</Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="是否包含电渠">
              {getFieldDecorator('ifOnline', {
                initialValue: currentRow?.ifOnline || undefined,
                rules: [
                  {
                    required: true,
                    message: '请选择是否包含电渠',
                  },
                ],
              })(
                <Select
                  allowClear
                  placeholder="请选择"
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  {['是', '否']?.map((ele) => (
                    <Select.Option value={ele} key={ele}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="请选择人员">
              {getFieldDecorator('userIdList', {
                initialValue: currentRow?.userIdList || undefined,
                rules: [
                  {
                    required: true,
                    message: '请选择人员',
                  },
                ],
              })(
                <Select
                  allowClear
                  placeholder="请选择"
                  mode="multiple"
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  {configUserList?.map((ele, index) => (
                    <Select.Option value={ele.id} key={index}>
                      {ele.realName}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};
export default Form.create()(ConfigEdit);
