/*
 * @Author: zxw
 * @Date: 2023-08-17 14:05:53
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-23 15:14:44
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\OutCallVerification\WorkOrderRecord\components\DetailModal.jsx
 * @Description:
 */
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Form, Modal, message, Input, Descriptions } from 'antd';
// import { getConfigUser, configEdit } from '../services';
import BasicInfo from '../../components/BasicInfo';
import HandleInfo from '../../components/HandleInfo';
import { selectDetail } from '../../commomServices';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const DetailModal = ({ visible, cancel, currentRow = {} }) => {
  const extraInfo = [
    { label: '接单人', key: 'receiverName' },
    { label: '接单时间', key: 'receiveTime' },
    { label: '处理时间', key: 'handleTime' },
  ];

  const footerInfo = [
    { label: '是否涉诈', key: 'ifFraud' },
    { label: '分类', key: 'fraudType' },
    { label: '是否关联处置', key: 'ifRelated' },
    { label: '备注', key: 'remark' },
  ];

  const [detailData, setDetailData] = useState({});

  useEffect(() => {
    if (visible) {
      getDetial();
    }
  }, [visible]);

  const getDetial = async () => {
    const response = await selectDetail({ outboundWorkOrderId: currentRow?.id });
    if (response.code === 200) {
      setDetailData(response.data || {});
    } else {
      message.error(response.message);
    }
  };

  return (
    <Fragment>
      <Modal
        visible={visible}
        title="详情"
        onCancel={cancel}
        footer={null}
        maskClosable={false}
        width={1000}
      >
        <Form {...formItemLayout}>
          <BasicInfo detailData={detailData} />
          <Descriptions column={5}>
            {extraInfo?.map((ele, index) => (
              <Descriptions.Item label={ele.label} key={index}>
                {detailData?.[ele.key] || '--'}
              </Descriptions.Item>
            ))}
          </Descriptions>

          <HandleInfo listData={detailData?.calledNumList || []} type="detail" />
          <div style={{ marginTop: 16 }}>
            <Descriptions column={3}>
              {footerInfo?.map((ele, index) => (
                <Descriptions.Item label={ele.label} key={index}>
                  {detailData?.[ele.key] || '--'}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
        </Form>
      </Modal>
    </Fragment>
  );
};
export default Form.create()(DetailModal);
