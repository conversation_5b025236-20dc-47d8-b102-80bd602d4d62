import React, { useEffect, useState } from 'react';
import { Modal, Icon, Button, Tooltip, Spin } from 'antd';
import StandardTable from '@/components/StandardTable';
import ConfigEdit from './ConfigEdit';

import { exportFile } from '@/utils/utils';

import { getConfigList } from '../services';

export default function WorkOrderConfig({ visible, cancel }) {
  const [loading, setLoading] = useState(false);
  const [currentRow, setCurrentRow] = useState({});
  const [editVisible, setEditVisible] = useState(false);
  const [listData, setListData] = useState([]);
  const columns = [
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',
      ellipsis: true,
      
    },
    {
      title: '处理人',
      width: 130,
      dataIndex: 'userNameList',
      ellipsis: true,
      
    },
    {
      title: '是否含电渠',
      width: 100,
      dataIndex: 'ifOnline',
      ellipsis: true,
      
    },
    {
      title: '配置人',
      width: 100,
      dataIndex: 'operatorName',
      ellipsis: true,
      
    },
    {
      title: '配置时间',
      width: 140,
      dataIndex: 'operateTime',
      ellipsis: true,
      
    },
    {
      title: '操作',
      width: 50,
      dataIndex: 'opt',
      ellipsis: true,
      render: (v, r) => {
        return <Icon type="edit" style={{ color: '#1890ff' }} onClick={() => handleEdit(r)} />;
      },
    },
  ];

  useEffect(() => {
    if (visible) {
      findConfigList();
    } else {
      setListData([]);
    }
  }, [visible]);

  const handleEdit = (r) => {
    setCurrentRow(r);
    setEditVisible(true);
  };

  const findConfigList = async () => {
    setLoading(true);
    const response = await getConfigList();
    setLoading(false);
    if (response && response.code === 200) {
      setListData(response.data || []);
    } else {
      message.error(response.message);
    }
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/outboundUser/exportOutboundUserConfig',
      method: 'GET',
      decode: true,
    });
  };

  return (
    <Modal title="工单配置" width={900} visible={visible} footer={null} onCancel={cancel}>
      <Spin spinning={loading}>
        <div style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={handleExport}>
            数据导出
          </Button>
        </div>
        <StandardTable
          rowSelectionProps={false}
          showSelectCount={false}
          columns={columns}
          data={{
            list: listData,
            pagination: false,
          }}
          loading={loading}
          rowKey="id"
        />
      </Spin>
      <ConfigEdit
        visible={editVisible}
        cancel={() => {
          setEditVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        reload={findConfigList}
        currentRow={currentRow}
      />
    </Modal>
  );
}
