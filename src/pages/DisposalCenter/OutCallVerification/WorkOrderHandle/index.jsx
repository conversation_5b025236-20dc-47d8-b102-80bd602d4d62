import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Modal,
} from 'antd';

import moment from 'moment';
import { Licensee } from 'ponshine';

import StandardTable from '@/components/StandardTable';
import HandleModal from './components/HandleModal';
import { selectPage, acceptOrder } from './services';
import { selectDetail, getInitialQueryParamByUser } from '../commomServices';
import { exportFile } from '@/utils/utils';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [handleVisible, setHandleVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);
  const [currentRow, setCurrentRow] = useState({});
  const [searchList, setSearchList] = useState({});

  // 获取本地网和是否电渠下拉
  const getSearchList = async () => {
    const response = await getInitialQueryParamByUser();
    if (response && response.code === 200) {
      setSearchList(response?.data || {});
      getListDatas({
        localNetwork: getInitialValue(response?.data?.localNetworkList),
        ifOnline: getInitialValue(response?.data?.ifOnlineList),
      });
    } else {
      getListDatas();
      message.error(response.message);
    }
  };

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getSearchList();
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    const { createTime } = values;
    getListDatas({
      ...values,
      createTime: undefined,
      createTimeStart: createTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      createTimeEnd: createTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas({
      localNetwork: getInitialValue(searchList?.localNetworkList),
      ifOnline: getInitialValue(searchList?.ifOnlineList),
    });
    setSelectedRows([]);
  };

  const handleExport = () => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/outbound/exportUnhandledOutboundWorkOrder',
      params: { ...searchParams },
      decode: true,
    });
  };

  // 编辑
  const handleEdit = (r) => {
    if (r.orderStatus === '待接单') {
      confirmAcceptOrder([r.id]);
    } else if (r.orderStatus === '待处理') {
      setCurrentRow(r);
      setHandleVisible(true);
    }
  };

  // 勾选接单
  const handleAcceptOrder = () => {
    if (!selectedRows?.length) {
      return message.info('请选择要接单的工单');
    }
    if (selectedRows?.some((ele) => ele?.orderStatus === '待处理')) {
      return message.info('勾选到已接单的工单，请重新选择勾选');
    }
    confirmAcceptOrder(selectedRows?.map((ele) => ele.id));
  };

  const handleSelectRows = (newSelectRows) => {
    setSelectedRows(newSelectRows);
  };

  // 确认接单
  const confirmAcceptOrder = (ids) => {
    Modal.confirm({
      title: '是否确认接单',
      onOk: async () => {
        const response = await acceptOrder({ orderIdList: ids });
        if (response && response.code === 200) {
          onReset();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const getInitialValue = (data) => {
    if (data?.length === 1) {
      return data?.[0];
    } else {
      return undefined;
    }
  };

  let columns = [
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '工单号',
      width: 120,
      dataIndex: 'orderNo',

      ellipsis: true,
    },
    {
      title: '涉诈标签',
      dataIndex: 'fraudTag',
      width: 120,

      ellipsis: true,
    },
    {
      title: '本地网',
      width: 100,
      dataIndex: 'localNetwork',

      ellipsis: true,
    },
    {
      title: '是否电渠',
      width: 120,
      dataIndex: 'ifOnline',

      ellipsis: true,
    },
    {
      title: '创建时间',
      width: 180,
      dataIndex: 'gmtCreate',

      ellipsis: true,
    },
    {
      title: '状态',
      width: 80,
      dataIndex: 'orderStatus',
      forceShow: true,
      ellipsis: true,
    },
    {
      title: '接单人',
      width: 90,
      dataIndex: 'receiverName',
      forceShow: true,
      ellipsis: true,
    },
    {
      title: '接单时间',
      width: 180,
      dataIndex: 'receiveTime',

      ellipsis: true,
    },

    {
      title: '是否超时',
      width: 120,
      dataIndex: 'ifTimeout',

      ellipsis: true,
      render: (v) => {
        return <span style={{ color: v === '是' ? 'red' : '' }}>{v}</span>;
      },
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'opt',
      ellipsis: true,
      fixed: 'right',
      render: (v, r) => {
        return <Icon type="edit" style={{ color: '#1890ff' }} onClick={() => handleEdit(r)} />;
      },
    },
  ];

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="工单号">
              {getFieldDecorator('orderNo')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="本地网">
              {getFieldDecorator('localNetwork', {
                initialValue: getInitialValue(searchList?.localNetworkList),
              })(
                <Select
                  placeholder="请选择"
                  allowClear={!Boolean(getInitialValue(searchList?.localNetworkList))}
                >
                  {searchList?.localNetworkList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="状态">
              {getFieldDecorator('orderStatus')(
                <Select placeholder="请选择">
                  {['待接单', '待处理']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="是否电渠">
              {getFieldDecorator('ifOnline', {
                initialValue: getInitialValue(searchList?.ifOnlineList),
              })(
                <Select
                  placeholder="请选择"
                  allowClear={!Boolean(getInitialValue(searchList?.ifOnlineList))}
                >
                  {searchList?.ifOnlineList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="创建时间">
              {getFieldDecorator('createTime')(
                <RangePicker format={'YYYY-MM-DD'} disabledDate={disabledDate} />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="是否超时">
              {getFieldDecorator('ifTimeout')(
                <Select placeholder="请选择">
                  {['是', '否']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="涉诈标签">
              {getFieldDecorator('fraudTag')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>

          <Col align="right" span={24} style={{ marginBottom: 16 }}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
              重置
            </Button>
            <Licensee license="unhandled_outbound_receive">
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleAcceptOrder}>
                勾选接单
              </Button>
            </Licensee>
            <Licensee license="unhandled_outbound_export">
              <Button type="" onClick={handleExport} disabled={!listData?.list?.length}>
                数据导出
              </Button>
            </Licensee>
          </Col>
        </Form>
      </Row>

      <StandardTable
        detailColumns={columns}
        tools={true}
        onSelectRow={handleSelectRows}
        selectedRows={selectedRows}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
        isNeedAutoWidth={true}
      />
      {/* 工单处理 */}
      <HandleModal
        visible={handleVisible}
        cancel={() => {
          setHandleVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        reload={() => onReset()}
        currentRow={currentRow}
      />
    </Card>
  );
};
export default Form.create({})(Index);
