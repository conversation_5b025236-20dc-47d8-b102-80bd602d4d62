import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Form, Modal, Select, Row, Col, message, DatePicker, Input, Radio, Button } from 'antd';
import { orderHandle, rollbackOrder, saveOrder } from '../services';
import BasicInfo from '../../components/BasicInfo';
import HandleInfo from '../../components/HandleInfo';
import InstructionsModal from './InstructionsModal';

import { selectDetail } from '../../commomServices';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const ConfigEdit = ({
  visible,
  form: {
    getFieldValue,
    getFieldDecorator,
    setFieldsValue,
    getFieldsValue,
    validateFields,
    resetFields,
  },
  cancel,
  reload,
  currentRow,
}) => {
  const [instructionVisible, setInstructionVisible] = useState(false);

  const handleInfoRef = useRef();
  const [detailData, setDetailData] = useState({});

  // 保存
  const handleOk = async () => {
    let list = [];
    if (getFieldValue('orderType') === 1) {
      const result = await handleInfoRef.current.getData();
      list = result.list;
      if (
        !(
          list?.some((ele) => ele.ifOutbound === '外呼成功') ||
          list?.every((ele) => ele.ifOutbound === '外呼失败')
        )
      ) {
        return message.info('请进行外呼确认后才能进行提交');
      }
    }
    validateFields(async (err, values) => {
      if (err) return;
      let response;
      if (values.orderType === 2) {
        // 回退工单
        response = await rollbackOrder({ outboundWorkOrderId: currentRow?.id });
      } else {
        const params = {
          calledNumList: list,
          ...values,
          id: currentRow?.id,
          localNetwork: currentRow?.localNetwork,
        };
        // 处理工单
        response = await orderHandle(params);
      }
      if (response && response.code === 200) {
        message.success(response.message);
        reload();
        cancel();
      } else {
        message.error(response.message);
      }
    });
  };

  // 暂存
  const handleSave = async () => {
    let list = [];
    if (getFieldValue('orderType') === 1) {
      const result = await handleInfoRef.current.ignoreValidateGetData();
      list = result.list;
    }
    const values = getFieldsValue();
    let response;
    if (values.orderType === 2) {
      // 回退工单
      response = await rollbackOrder({ outboundWorkOrderId: currentRow?.id });
    } else {
      const params = {
        calledNumList: list,
        ...values,
        id: currentRow?.id,
        localNetwork: currentRow?.localNetwork,
      };
      response = await saveOrder(params);
    }
    if (response && response.code === 200) {
      message.success(response.message);
      reload();
      cancel();
    } else {
      message.error(response.message);
    }
  };

  // 根据是否涉诈获取分类下拉
  const getTypeList = () => {
    const value = getFieldValue('ifFraud');
    if (value === '是') {
      return [
        '冒充电商物流客服类',
        '冒充公检法及政府机关类',
        '冒充军警购物类诈骗类',
        '刷单返利类',
        '虚假贷款类',
        '虚假购物',
        '服务类',
        '虚假投资理财类',
        '虚假征信类',
        '其他',
      ];
    } else if (value === '否') {
      return ['外卖配送', '物流快递', '银行证券', '营销推广', '其他'];
    } else {
      return [];
    }
  };

  // 根据外呼结果，填入是否涉诈的内容
  const handleChangeResult = (list) => {
    let value = '';
    if (list.every((ele) => !ele.outboundResult)) {
      value = '';
    } else if (list.some((ele) => ele.outboundResult === '涉诈')) {
      value = '是';
    } else if (list.some((ele) => ele.outboundResult === '未涉诈')) {
      value = '否';
    } else {
      value = '不确定';
    }

    setFieldsValue({ ifFraud: value, fraudType: undefined });
  };

  const getDetial = async () => {
    const response = await selectDetail({ outboundWorkOrderId: currentRow?.id });
    if (response.code === 200) {
      setDetailData(response.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    if (visible) {
      getDetial();
    }
  }, [visible]);

  return (
    <Fragment>
      <Modal
        visible={visible}
        title="编辑"
        onOk={handleOk}
        okText="确认"
        onCancel={cancel}
        afterClose={() => {
          resetFields();
          handleInfoRef?.current?.clearForm();
        }}
        maskClosable={false}
        width={1000}
        footer={
          <div>
            <Button onClick={cancel}>取消</Button>
            {getFieldValue('orderType') === 1 && (
              <Button onClick={handleSave} type="primary">
                保存
              </Button>
            )}
            <Button onClick={handleOk} type="primary">
              确认
            </Button>
          </div>
        }
      >
        <Form {...formItemLayout}>
          <BasicInfo detailData={detailData} />
          <Form.Item label="">
            {getFieldDecorator('orderType', {
              initialValue: 1,
            })(
              <Radio.Group>
                <Radio value={1}>工单处理</Radio>
                <Radio value={2}>工单回退</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          {getFieldValue('orderType') === 1 && (
            <div>
              <HandleInfo
                listData={detailData?.calledNumList || []}
                cref={handleInfoRef}
                changeResult={handleChangeResult}
              />
              <Row style={{ marginTop: 16 }}>
                <Col span={6}>
                  <Form.Item label="是否涉诈">
                    {getFieldDecorator('ifFraud', {
                      initialValue: currentRow?.ifFraud,
                      // rules: [{
                      //   required: true,
                      //   message: '请填写外呼结果'
                      // }]
                    })(<Input disabled />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="分类">
                    {getFieldDecorator('fraudType', {
                      initialValue: currentRow?.fraudType || undefined,
                      rules: [
                        {
                          required:
                            getFieldValue('ifFraud') && getFieldValue('ifFraud') !== '不确定',
                          message: '请选择分类',
                        },
                      ],
                    })(
                      <Select
                        allowClear
                        placeholder="请选择"
                        getPopupContainer={(triggerNode) => triggerNode.parentElement}
                        disabled={
                          !getFieldValue('ifFraud') || getFieldValue('ifFraud') === '不确定'
                        }
                      >
                        {getTypeList()?.map((ele) => (
                          <Select.Option value={ele} key={ele}>
                            {ele}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="是否关联处置" wrapperCol={{ span: 14 }} labelCol={{ span: 10 }}>
                    {getFieldDecorator('ifRelated', {
                      initialValue: currentRow?.ifRelated || '是',
                    })(
                      <Select
                        placeholder="请选择"
                        getPopupContainer={(triggerNode) => triggerNode.parentElement}
                      >
                        {['是', '否']?.map((ele) => (
                          <Select.Option value={ele} key={ele}>
                            {ele}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item wrapperCol={{ span: 24 }}>
                    <a
                      style={{ marginLeft: 16 }}
                      onClick={() => {
                        setInstructionVisible(true);
                      }}
                    >
                      关联处置说明
                    </a>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="备注" wrapperCol={{ span: 20 }} labelCol={{ span: 4 }}>
                    {getFieldDecorator('remark', {
                      initialValue: currentRow?.remark,
                      rules: [
                        {
                          max: 200,
                          message: '最多不能超过200个字符',
                        },
                      ],
                    })(<TextArea rows={3} />)}
                  </Form.Item>
                </Col>
              </Row>
            </div>
          )}
        </Form>
      </Modal>
      <InstructionsModal
        visible={instructionVisible}
        cancel={() => {
          setInstructionVisible(false);
        }}
      />
    </Fragment>
  );
};
export default Form.create()(ConfigEdit);
