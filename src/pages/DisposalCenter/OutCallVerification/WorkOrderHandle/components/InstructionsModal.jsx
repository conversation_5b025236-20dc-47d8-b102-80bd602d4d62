/*
 * @Author: zxw
 * @Date: 2023-08-16 15:38:23
 * @LastEditors: zxw
 * @LastEditTime: 2023-09-14 04:37:37
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\OutCallVerification\WorkOrderHandle\components\InstructionsModal.jsx
 * @Description: 
 */
import React from 'react';
import { Modal } from 'antd';
import StandardTable from '@/components/StandardTable';

export default function InstructionsModal({ visible, cancel }) {
  const columns = [
    {
      title: '是否涉诈',
      width: 100,
      dataIndex: 'isSheZha',
      ellipsis: true,
    },
    {
      title: '是否关联处置',
      width: 100,
      dataIndex: 'isGuanLian',
      ellipsis: true,
    },
    {
      title: '对应关联处置方式',
      width: 120,
      dataIndex: 'way',
      ellipsis: true,
    },
  ];

  const listData = [
    {
      isSheZha: '是',
      isGuanLian: '是',
      way: '对核验为涉诈号码进行外呼核验关停',
    },
    {
      isSheZha: '否',
      isGuanLian: '是',
      way: '对号码进行强制复机',
    },
    {
      isSheZha: '不确定',
      isGuanLian: '是',
      way: '不处置',
    },
    {
      isSheZha: '是',
      isGuanLian: '否',
      way: '不处置',
    },
    {
      isSheZha: '否',
      isGuanLian: '否',
      way: '不处置',
    },
    {
      isSheZha: '不确定',
      isGuanLian: '否',
      way: '不处置',
    },
  ];
  return (
    <Modal visible={visible} title="关联处置说明" footer={null} width={800} onCancel={cancel}>
      <StandardTable
        columns={columns}
        data={{ list: listData, pagination: false }}
        rowSelectionProps={false}
        showSelectCount={false}
      />
    </Modal>
  );
}
