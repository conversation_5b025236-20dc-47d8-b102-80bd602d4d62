/*
 * @Author: zxw
 * @Date: 2023-08-15 15:41:12
 * @LastEditors: zxw
 * @LastEditTime: 2023-11-14 15:54:06
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\OutCallVerification\WorkOrderHandle\services.js
 * @Description:
 */
import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/outbound/pageUnhandledOutboundWorkOrder`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取配置人员

export async function getConfigUser(params) {
  return request(`/api/biz/modularity/project176/getNeChargingList`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 编辑配置
export async function configEdit(params) {
  return request(`/api/biz/modularity/project176/getNeChargingList`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 接单
export async function acceptOrder(params) {
  return request(`/api/hn/outbound/receivingOrders`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 处理工单
export async function orderHandle(params) {
  return request(`/api/hn/outbound/handleOrder`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 暂存工单
export async function saveOrder(params) {
  return request(`/api/hn/outbound/saveOrder`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 工单回退
export async function rollbackOrder(params) {
  return request(`/api/hn/outbound/rollbackOrder`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
