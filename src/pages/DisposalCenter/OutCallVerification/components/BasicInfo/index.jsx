import React from 'react';
import { Descriptions } from 'antd';

export default function index({ detailData = {} }) {
  const list = [
    { label: '号码', key: 'phoneNum' },
    { label: '涉诈标签', key: 'fraudTag' },
    { label: '工单状态', key: 'orderStatus' },
    { label: '创建时间', key: 'gmtCreate' },
    { label: '模型命中时间', key: 'modelHitTime' },
    { label: '关停时间', key: 'shutdownTime' },
    { label: '本地网', key: 'localNetwork' },
    { label: '是否电渠', key: 'ifOnline' },
    { label: '营业区', key: 'businessArea' },
    { label: '工单号', key: 'orderNo' },
  ];
  return (
    <Descriptions column={5}>
      {list.map((ele, index) => (
        <Descriptions.Item label={ele.label}>{detailData?.[ele.key] || '--'}</Descriptions.Item>
      ))}
    </Descriptions>
  );
}
