import React, { useImperativeHandle, forwardRef } from 'react';
import StandardTable from '@/components/StandardTable';
import { Form, Select, Input } from 'antd';

const Index = forwardRef((props, ref) => {
  const {
    listData = [],
    form: {
      getFieldDecorator,
      validateFields,
      setFieldsValue,
      resetFields,
      getFieldValue,
      getFieldsValue,
    },
    cref,
    changeResult,
    type,
  } = props;

  // 渲染表单节点
  const renderFormNode = ({ formItemType, record, dataIndex, innerProps, selectOptions }) => {
    if (formItemType === 'text') {
      return <span>{record?.[dataIndex]}</span>;
    } else {
      if (formItemType === 'Select') {
        return (
          <Select {...innerProps} placeholder="请选择" style={{ width: '100%' }}>
            {selectOptions.map((ele, index) => (
              <Select.Option value={ele?.value || ele} key={ele?.value || ele?.key || index}>
                {ele?.value || ele}
              </Select.Option>
            ))}
          </Select>
        );
      } else {
        return <Input {...innerProps} placeholder="请输入" style={{ width: '100%' }} />;
      }
    }
  };

  // 渲染表单配置项
  const renderFormItem = ({
    formItemType,
    record,
    dataIndex,
    innerProps = {},
    selectOptions,
    index,
    defaultValue = formItemType === 'Select' ? undefined : '',
    rules = [],
  }) => {
    if (!type) {
      return (
        <Form.Item style={{ margin: 0 }}>
          {getFieldDecorator(`list.${index}.${dataIndex}`, {
            initialValue: record?.[dataIndex] || defaultValue,
            rules,
          })(renderFormNode({ formItemType, record, dataIndex, innerProps, selectOptions }))}
        </Form.Item>
      );
    } else {
      return record?.[dataIndex];
    }
  };

  // 获取外呼结果下拉
  const getSelectOption = (index, type) => {
    const value = getFieldValue(`list.${index}.${type}`);
    if (value === '外呼成功') {
      return ['涉诈', '未涉诈', '不确定'];
    } else if (value === '外呼失败') {
      return ['未接听', '其他'];
    } else {
      return [];
    }
  };

  // 改变外呼结果时，赋值是否涉诈
  const handleChange = (v) => {
    changeList();
  };

  //   改变是否外呼，赋值是否涉诈
  const handleChangeWaihu = (v, index) => {
    setFieldsValue({ [`list.${index}.outboundResult`]: undefined });
    changeList();
  };

  // 获取当前表单内容
  const changeList = () => {
    setTimeout(() => {
      const list = getFieldsValue()?.list;
      changeResult(list);
    }, 0);
  };

  let columns = [
    {
      title: '被叫号码ID',
      width: 0,
      dataIndex: 'id',
      ellipsis: true,
      render: (v, record, index) => {
        return renderFormItem({
          formItemType: 'text',
          record,
          dataIndex: 'id',
          index,
        });
      },
    },
    {
      title: '被叫号码',
      width: 120,
      dataIndex: 'phoneNum',
      ellipsis: true,
      render: (v, record, index) => {
        return renderFormItem({
          formItemType: 'text',
          record,
          dataIndex: 'phoneNum',
          index,
        });
      },
    },
    {
      title: '通话开始时间',
      width: 120,
      dataIndex: 'calledTime',
      ellipsis: true,
      render: (v, record, index) => {
        return renderFormItem({
          formItemType: 'text',
          record,
          dataIndex: 'calledTime',
          index,
        });
      },
    },
    {
      title: '通话时长',
      dataIndex: 'callDuration',
      width: 120,
      ellipsis: true,
      render: (v, record, index) => {
        return renderFormItem({
          formItemType: 'text',
          record,
          dataIndex: 'callDuration',
          index,
        });
      },
    },
    {
      title: '是否外呼',
      width: 100,
      dataIndex: 'ifOutbound',
      ellipsis: true,
      render: (v, record, index) => {
        return renderFormItem({
          formItemType: 'Select',
          record,
          dataIndex: 'ifOutbound',
          selectOptions: ['外呼成功', '外呼失败', '未外呼'],
          index,
          defaultValue: '未外呼',
          innerProps: {
            onChange: (v) => handleChangeWaihu(v, index),
          },
        });
      },
    },
    {
      title: '外呼结果',
      width: 120,
      dataIndex: 'outboundResult',
      ellipsis: true,
      render: (v, record, index) => {
        return renderFormItem({
          formItemType: 'Select',
          record,
          dataIndex: 'outboundResult',
          selectOptions: getSelectOption(index, 'ifOutbound'),
          index,
          rules: [
            {
              required: getFieldValue(`list.${index}.ifOutbound`) !== '未外呼',
              message: '请选择外呼结果',
            },
          ],
          innerProps: {
            disabled: getFieldValue(`list.${index}.ifOutbound`) === '未外呼',
            onChange: handleChange,
          },
        });
      },
    },
    {
      title: '备注',
      width: 120,
      dataIndex: 'remark',

      ellipsis: true,
      render: (v, record, index) => {
        return renderFormItem({
          formItemType: 'Input',
          record,
          dataIndex: 'remark',
          index,
          rules: [
            {
              max: 200,
              message: '最多输入200个字符',
            },
          ],
        });
      },
    },
  ];

  useImperativeHandle(cref, () => ({
    getData: getData,
    ignoreValidateGetData: ignoreValidateGetData,
    clearForm: () => {
      resetFields();
    },
  }));

  const getData = () => {
    return new Promise((resolve, reject) => {
      validateFields((err, values) => {
        if (err) {
          reject(err);
          return;
        } else {
          resolve(values);
        }
      });
    });
  };
  const ignoreValidateGetData = () => {
    return new Promise((resolve, reject) => {
      resolve(getFieldsValue());
    });
  };

  return (
    <Form>
      <StandardTable
        rowSelectionProps={false}
        showSelectCount={false}
        columns={columns}
        data={{
          list: listData,
          pagination: false,
        }}
        rowKey="id"
      />
    </Form>
  );
});

export default Form.create()(Index);
