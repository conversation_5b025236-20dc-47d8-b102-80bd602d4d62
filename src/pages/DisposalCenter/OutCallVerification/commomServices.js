/*
 * @Author: zxw
 * @Date: 2023-08-23 15:53:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-28 15:36:28
 * @FilePath: \newHunanfanzha\src\pages\DisposalCenter\OutCallVerification\commomServices.js
 * @Description:
 */
import request from 'ponshine-request';

// 查询详情
export async function selectDetail(params) {
  return request(`/api/hn/outbound/getOutboundWorkOrderDetail`, {
    method: 'GET',
    params,
  });
}

// 查询本地网 是否电渠
export async function getInitialQueryParamByUser(params) {
  return request(`/api/hn/outboundUser/getInitialQueryParamByUser`, {
    method: 'GET',
    params,
  });
}
