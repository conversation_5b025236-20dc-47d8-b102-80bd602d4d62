/*
 * @Author: ss
 * @Date: 2025-07-14 16:37:34
 * @LastEditors: ss
 * @LastEditTime: 2025-07-28 16:00:25
 * @Description: 
 */
import React, { useState, useEffect, useRef, Fragment } from 'react';
import styles from './index.less';
import TopItem from '@/pages/AssessStatistics/ThematicManage/CampusCardTopic/SituationAnalysis/Top/TopItem';
import { Row, Col, message, Card, Form, DatePicker, Select, Input, Button, Modal, Tooltip } from 'antd';
import one from '@/pages/AssessStatistics/ThematicManage/CampusCardTopic/SituationAnalysis/imgs/one.png';
import two from '@/pages/AssessStatistics/ThematicManage/CampusCardTopic/SituationAnalysis/imgs/two.png';
import three from '@/pages/AssessStatistics/ThematicManage/CampusCardTopic/SituationAnalysis/imgs/three.png';
import CommonTitle from '@/components/CommonTitle';
import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import Details from './Details';
import request from 'ponshine-request';
import moment from 'moment';
import { exportFile, renderToolTip } from '@/utils/utils';
import ExportApprove from '@/components/ExportApprove';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import  highlightKeywords  from './highlightKeywords';
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const AIQualityInspectionManualVerification = (props) => {
    let apiPrefix = '/api/biz/modularity/project1752737891113331'

    const { form } = props;
    const { getFieldDecorator, getFieldsValue, resetFields } = form;
    const [data, setData] = useState({});
    const [listData, setListData] = useState({
        list: [],
        pagination: {
            total: 0,
            pageNum: 1,
            pageSize: 10,
        },
    });
    const [loading, setLoading] = useState(false);
    const [highRiskTypeList, setHighRiskTypeList] = useState([]);//高危类型
    const [exportLoading, setExportLoading] = useState(false);
    const [detailVisible, setDetailVisible] = useState(false);
    const [detailData, setDetailData] = useState({});
    const [selectedRows, setSelectedRows] = useState([]);
    const [confirmVisible, setConfirmVisible] = useState(false); // 添加二次确认弹窗状态
    const [confirmType, setConfirmType] = useState(''); // 添加操作类型状态
    const [remarkContent, setRemarkContent] = useState(''); // 添加研判记录内容
    const [keyWords, setKeyWords] = useState([])//关键字
    const serachParams = useRef();
    const timerRef = useRef(null);
    const tableRef = useRef(); // 添加表格ref以便调用表格方法

    const list = [
        {
            title: `待处理任务量`,
            img: one,
            keyStr: 'pendingProcessing',
        },
        {
            title: '已处理任务量',
            img: two,
            keyStr: 'processed',
        },
        {
            title: '任务总数',
            img: three,
            keyStr: 'taskLength',
        },
    ];
    const columns = [

        {
            title: '任务号',
            dataIndex: 'modelCode',
            align: 'center',
            width: 140,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '号码',
            dataIndex: 'phoneNum',
            align: 'center',
            width: 140,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '通话内容',
            dataIndex: 'callContent',
            key: 'callContent',
            width: 480,
            render: (text) => {
                if (!text) return '--';
                return (
                    <Tooltip
                        title={<div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>{highlightKeywords(text, keyWords)}</div>}
                        overlayStyle={{ maxWidth: 480, maxHeight: 300, overflow: 'auto' }}
                    >
                        <div
                            style={{
                                display: '-webkit-box',
                                WebkitLineClamp: 6,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                wordBreak: 'break-all',
                                whiteSpace: 'normal',
                                minWidth: 0,
                                maxWidth: 480,
                                lineHeight: '21px',
                                maxHeight: '126px',
                            }}
                        >
                            {highlightKeywords(text, keyWords)}
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            title: '高危类型',
            dataIndex: 'modelType',
            align: 'center',
            width: 100,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },

        {
            title: '模型研判',
            dataIndex: 'modelJudgment',
            align: 'center',
            width: 140,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '研判结果',
            dataIndex: 'judgmentResult',
            align: 'center',
            width: 120,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '本地网',
            dataIndex: 'localNetwork',
            align: 'center',
            width: 100,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '任务开始时间',
            dataIndex: 'startTime',
            align: 'center',
            width: 150,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        // {
        //     title: '处置标签',
        //     dataIndex: 'disposeLabel',
        //     align: 'center',
        //     width: 140,
        //     ellipsis: true,
        //     render: (v) => renderToolTip(v),
        // },

        {
            title: '主叫号码',
            dataIndex: 'callPhone',
            align: 'center',
            width: 140,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '被叫号码',
            dataIndex: 'calledPhone',
            align: 'center',
            width: 140,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '原被叫',
            dataIndex: 'originalCalledPhone',
            align: 'center',
            width: 150,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '所在地',
            dataIndex: 'location',
            align: 'center',
            width: 140,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '话务时间',
            dataIndex: 'callTime',
            align: 'center',
            width: 150,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '识别类型',
            dataIndex: 'identifyType',
            align: 'center',
            width: 100,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },

        {
            title: '研判时间',
            dataIndex: 'judgmentTime',
            align: 'center',
            width: 150,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '研判人',
            dataIndex: 'judgmentUser',
            align: 'center',
            width: 100,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '处置结果',
            dataIndex: 'disposeResult',
            align: 'center',
            width: 120,
            ellipsis: true,
            render: (v) => renderToolTip(v),
        },
        {
            title: '操作',
            dataIndex: 'operate',
            align: 'center',
            width: 100,
            fixed: 'right',
            render: (v, record) => {
                return (
                    <Button type="link" onClick={(e) => {
                        e.stopPropagation();
                        handleDetail(record);
                    }}>详情</Button>
                )
            }
        }
    ];
    useEffect(() => {
        findData();
        // 获取高危类型
        // findHighRiskType();
        findKeyWords();
        // 设置定时器，每分钟刷新一次数据
        const defaultParams = {
            disposeStartTime: moment().startOf('month').format('YYYY-MM-DD'),
            disposeEndTime: moment().format('YYYY-MM-DD'),
            responsibleUnit: '待研判',
            pageNum: 1,
            pageSize: 10,
        };
        findTableDataPager(defaultParams);
        timerRef.current = setInterval(() => {
            resetFields();
            findTableDataPager(defaultParams);
        }, 60000); // 60000毫秒 = 1分钟

        // 组件卸载时清除定时器
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, []);
    const findData = () => {

        request({
            url: `${apiPrefix}/taskCompletionStatusQuery`,
            method: "GET"
        }).then((res) => {
            if (res.status == 200) {
                setData({
                    pendingProcessing: res.data.pendingProcessing || 0,
                    processed: res.data.processed || 0,
                    taskLength: res.data.taskLength || 0,
                })

            } else {
                message.destroy()
                message.error(res.message || '查询失败')
            }
        })
    };
    // 获取高危类型
    const findHighRiskType = () => {
        // setHighRiskTypeList(['高危', '中危', '低危']);
        request({
            url: `${apiPrefix}/highRiskTypeQuery`,
            method: "GET"
        }).then((res) => {
            if (res.status == 200) {
                setHighRiskTypeList(res.data?.typeList || [])
            } else {
                message.destroy()
                message.error(res.message || '查询失败')
            }
        })
    };
    // 获取关键字
    const findKeyWords = () => {
        request({
            url: `${apiPrefix}/allwordkeysQuery`,
            method: "POST"
        }).then((res) => {
            console.log(res, 9999)
            if (res.status == 200) {
                setKeyWords(res.data?.keyworksNameList || [])
            } else {
                message.destroy()
                message.error(res.message || '查询失败')
            }
        })
    }
    const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
        console.log(props, '列表参数');
        setLoading(true);
        const response = await request('/api/hn/autoModel/pageModelInfoRecord', {
            data: { pageNum, pageSize, ...props },
            method: 'POST',
            requestType: 'json',
        });
        setLoading(false);
        if (response.code === 200) {
            serachParams.current = props;
            let data = response?.data?.items?.map((item, i) => {
                return {
                    ...item,
                    key: item.id,
                    callContent: i % 2 === 0 ? '通话内容1111' : '中奖B待审核“您好中奖5，这里是银行客服中心。恭喜您我们检测到您的账户近期有一笔异常交易中奖了，请问是您本人操作的吗？如果不是，请及时回复‘不是’。请您不要将银行卡号、中奖了验证码等信息透露给任何人恭喜您，包括自称银行工作人员的人员。我们不会通过电话要求您转账或提供验证码。请您注意账户安全，防范电信网络诈骗中奖123。您好近期有不法分子冒充支付宝银行、公安等机构，谎称账户异常、涉嫌洗钱等，诱导客户点击链接转账或泄露个人信息。请您务必提高警惕，遇到可疑情况可拨打官方客服电话核实。您的账户目前一切正常，点击链接无需支付宝操作。如有您好疑问可随时致电本行。再次提醒：银行不会通过电话、短信索要您的密码、验证码等敏感信息。请妥善保管个人信息，防止信息泄露。感谢您的配合，祝您生活愉快中奖5。中奖5近期有客户反映接到自称‘公安’的电话，称其银行卡涉嫌违法犯罪活动，要求配合调查并转账至‘安全账户’中奖B。请您牢记，公安机关不会通过电话要求转账或提供银行卡信息。遇到类似情况请立即挂断电话并报警。请您定期修改密码，避免使用过于简单的密码。不要随意点击陌生链接或下载不明软件，防止手机中病毒导致信息泄露。若发现账户有异常，请第一时间联系银行。感谢您的理解与支持。请您确认是否还有其他疑问，如无请挂断电话。祝您工作顺利，生活安康。再次提醒，保护好您的资金安全，谨防电信诈骗。您好，这里是银行客服中心。我们检测到您的账户近期有一笔异常交易，请问是您本人操作的吗？如果不是，请及时回复‘不是’。请您不要将银行卡号、验证码等信息透露给任何人，包括',
                    judgmentResult: '待研判',
                }
            }) || [];
            setListData({
                list: data || [],
                pagination: {
                    total: response.data.totalNum,
                    current: pageNum,
                    pageSize,
                },
            });
        } else {
            message.error(response.message);
        }
    };
    const handleTableChange = (pagination) => {
        findTableDataPager({
            ...serachParams.current,
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
        });
    };
    const handleSearch = () => {
        const formValue = getFieldsValue();
        const { updateTime, disposeTime } = formValue;
        let formValueObj = {
            ...formValue,
            gmtCreateStart: updateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
            gmtCreateEnd: updateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
            disposeStartTime: disposeTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
            disposeEndTime: disposeTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
        };
        delete formValueObj.disposeTime;
        delete formValueObj.updateTime;
        findTableDataPager(formValueObj);
    };

    const handleReset = () => {
        resetFields();
        let formValue = getFieldsValue();
        let formValueObj = {
            ...formValue,
            gmtCreateStart: formValue.updateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
            gmtCreateEnd: formValue.updateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
            disposeStartTime: formValue.disposeTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
            disposeEndTime: formValue.disposeTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
        };
        delete formValueObj.disposeTime;
        delete formValueObj.updateTime;

        // 使用tableRef清空表格勾选状态
        if (tableRef.current && tableRef.current.cleanSelectedKeys) {
            tableRef.current.cleanSelectedKeys();
        }

        // 重置页码并刷新列表
        findTableDataPager({
            ...formValueObj,
            pageNum: 1,
            pageSize: 10,
        });
    };
    const handleExport = () => {
        setExportLoading(true);
        console.log(serachParams.current);
        console.log(serachParams.current);
        exportFile({
            urlAPi: '/api/hn/whiteLoginIp/exportWhiteLoginIp',
            decode: true,
            method: 'POST',
            params: { ...serachParams.current },
            isVerifyhEncryption: true,
            callback: () => {
                setExportLoading(false);
            },
        });
    };
    const handleDetail = (record) => {
        setDetailData(record);
        setDetailVisible(true);
    };

    // 处理二次确认弹窗的显示
    const showConfirmModal = (type) => {
        const selectedData = tableRef.current?.getSelectedData() || [];
        if (selectedData.length === 0) {
            message.destroy();
            message.warning('请先选择数据');
            return;
        }

        // 疑似诈骗直接下发，不需要二次确认
        if (type === 'suspicious') {
            handleDirectProcess('疑似诈骗');
            return;
        }

        // 设置确认类型
        setConfirmType(type);

        // 根据类型设置默认研判记录内容
        let defaultContent = '';
        if (type === 'confirm') {
            // 提取所有选中行的高危类型，去重后用/分隔
            const modelTypes = Array.from(
                new Set(selectedData.map(item => item.modelType).filter(Boolean))
            ).join('/');
            defaultContent = `专班研判涉诈（sh-a），${modelTypes || '高危类型'}`;
        } else if (type === 'noFraud') {
            defaultContent = '人工研判复机';
        }

        setRemarkContent(defaultContent);
        setConfirmVisible(true);
    };

    // 直接处理（用于疑似诈骗，无需二次确认）
    const handleDirectProcess = async (judgmentResult) => {
        const selectedData = tableRef.current?.getSelectedData() || [];
        if (selectedData.length === 0) {
            message.destroy();
            message.warning('请先选择数据');
            return;
        }
        Modal.confirm({
            title: '提示',
            content: '是否进行操作?',
            onOk: () => {
                try {
                    setLoading(true);
                    // 获取所有选中行的ID
                    const ids = selectedData.map(item => item.id);
                    console.log(ids, 'ids,judgmentResult');
                    // 调用API进行批量更新
                    const response = {
                        code: 200,
                        message: '操作成功',
                    }
                    // await request('/api/hn/autoModel/batchUpdateJudgment', {
                    //     method: 'POST',
                    //     data: {
                    //         ids,
                    //         judgmentResult,
                    //         // 疑似诈骗无需传递备注内容
                    //     },
                    //     requestType: 'json',
                    // });


                    if (response.code === 200) {
                        message.success('操作成功');
                        // 使用tableRef清空表格勾选状态
                        if (tableRef.current && tableRef.current.cleanSelectedKeys) {
                            tableRef.current.cleanSelectedKeys();
                        }
                        // 刷新列表但保留选中状态
                        findTableDataPager({
                            ...serachParams.current,
                            pageNum: listData.pagination.current,
                            pageSize: listData.pagination.pageSize,
                        });
                    } else {
                        message.error(response.message || '操作失败');
                    }
                } catch (error) {
                    console.error('操作失败:', error);
                    message.error('操作失败，请重试');
                } finally {
                    setLoading(false);
                }
            },
        });


    };

    // 执行确认操作
    const handleConfirmAction = async () => {
        const selectedData = tableRef.current?.getSelectedData() || [];
        if (selectedData.length === 0) {
            message.destroy();
            message.warning('请先选择数据');
            return;
        }

        // 验证研判记录内容
        if (!remarkContent.trim()) {
            message.destroy();
            message.warning('请输入研判记录');
            return;
        }

        try {
            setLoading(true);
            // 获取所有选中行的ID
            const ids = selectedData.map(item => item.id);

            // 根据不同操作类型设置判断结果
            let judgmentResult = '';
            switch (confirmType) {
                case 'confirm':
                    judgmentResult = '诈骗';
                    break;
                case 'noFraud':
                    judgmentResult = '非诈骗';
                    break;
                default:
                    judgmentResult = '';
            }

            if (!judgmentResult) {
                setLoading(false);
                return;
            }

            // 调用API进行批量更新
            const response =
            {
                code: 200,
                message: '操作成功',
            }
            // await request('/api/hn/autoModel/batchUpdateJudgment', {
            // method: 'POST',
            // data: {
            //     ids,
            //     judgmentResult,
            //     remark: remarkContent, // 添加研判记录内容
            // },
            // requestType: 'json',
            // });

            if (response.code === 200) {
                message.success(response.message || '操作成功');
                // 使用tableRef清空表格勾选状态
                if (tableRef.current && tableRef.current.cleanSelectedKeys) {
                    tableRef.current.cleanSelectedKeys();
                }
                // 刷新列表但保留选中状态
                findTableDataPager({
                    ...serachParams.current,
                    pageNum: listData.pagination.current,
                    pageSize: listData.pagination.pageSize,
                });
            } else {
                message.error(response.message || '操作失败');
            }
        } catch (error) {
            console.error('操作失败:', error);
            message.error('操作失败，请重试');
        } finally {
            setLoading(false);
            setConfirmVisible(false);
        }
    };

    // 取消确认操作
    const handleCancelAction = () => {
        setConfirmVisible(false);
        setRemarkContent('');
    };

    // 确认诈骗
    const handleConfirm = () => {
        showConfirmModal('confirm');
    };

    // 疑似诈骗
    const handleSuspiciousFraud = () => {
        showConfirmModal('suspicious');
    };

    // 非诈骗
    const handleNoFraud = () => {
        showConfirmModal('noFraud');
    };

    // 研判记录内容变化
    const handleRemarkChange = (e) => {
        setRemarkContent(e.target.value);
    };

    const handleSelectRows = (newSelectedRows) => {
        setSelectedRows(newSelectedRows);
    };

    // 确认弹框的内容
    const getConfirmModalContent = () => {
        const selectedData = tableRef.current?.getSelectedData() || [];
        const count = selectedData.length;

        let title = '';
        let content = '';

        switch (confirmType) {
            case 'confirm':
                title = '确认诈骗';
                content = `您已选择${count}条数据，确定将这些数据标记为"诈骗"吗？`;
                break;
            case 'noFraud':
                title = '非诈骗';
                content = `您已选择${count}条数据，确定将这些数据标记为"非诈骗"吗？`;
                break;
            default:
                title = '确认操作';
                content = `您已选择${count}条数据，确定执行此操作吗？`;
        }

        return { title, content };
    };

    // 高亮关键字
    // const highlightKeywords = (text, keywords) => {
    //     if (!text || !keywords || !keywords.length) return text;
    
    //     // 过滤空字符串，并按长度从长到短排序
    //     const validKeywords = keywords.filter(Boolean).sort((a, b) => b.length - a.length);
    //     if (!validKeywords.length) return text;
    
    //     let highlightRanges = [];
    //     let andKeywordRanges = [];
    
    //     // 先处理所有带&的关键字，记录这些分段的区间（只在顺序完整出现时才标红）
    //     validKeywords.forEach(keyword => {
    //         if (keyword.includes('&')) {
    //             const parts = keyword.split('&').map(s => s.trim()).filter(Boolean);
    //             let searchStart = 0;
    //             while (searchStart < text.length) {
    //                 let lastIndex = searchStart;
    //                 let foundAll = true;
    //                 let tempRanges = [];
    //                 for (let part of parts) {
    //                     const idx = text.indexOf(part, lastIndex);
    //                     if (idx !== -1) {
    //                         tempRanges.push([idx, idx + part.length]);
    //                         lastIndex = idx + part.length;
    //                     } else {
    //                         foundAll = false;
    //                         break;
    //                     }
    //                 }
    //                 if (foundAll) {
    //                     andKeywordRanges.push(...tempRanges);
    //                     // 继续查找下一个顺序匹配（从第一个分段的下一个字符开始）
    //                     searchStart = tempRanges[0][0] + 1;
    //                 } else {
    //                     break;
    //                 }
    //             }
    //         }
    //     });
    
    //     // 再处理普通关键字，排除被&关键字分段覆盖的区间
    //     validKeywords.forEach(keyword => {
    //         if (!keyword.includes('&')) {
    //             let start = 0;
    //             while (true) {
    //                 const idx = text.indexOf(keyword, start);
    //                 if (idx === -1) break;
    //                 // 检查该区间是否被带&的关键字覆盖（完全重叠才排除）
    //                 const covered = andKeywordRanges.some(([s, e]) => idx >= s && (idx + keyword.length) <= e);
    //                 if (!covered) {
    //                     highlightRanges.push([idx, idx + keyword.length]);
    //                 }
    //                 start = idx + keyword.length;
    //             }
    //         }
    //     });
    
    //     // 合并所有高亮区间
    //     let allRanges = [...andKeywordRanges, ...highlightRanges];
    //     allRanges.sort((a, b) => a[0] - b[0]);
    //     let merged = [];
    //     for (let range of allRanges) {
    //         if (!merged.length || merged[merged.length - 1][1] < range[0]) {
    //             merged.push(range);
    //         } else {
    //             merged[merged.length - 1][1] = Math.max(merged[merged.length - 1][1], range[1]);
    //         }
    //     }
    
    //     // 生成高亮文本
    //     let result = [];
    //     let lastIdx = 0;
    //     merged.forEach(([start, end], i) => {
    //         if (lastIdx < start) {
    //             result.push(text.slice(lastIdx, start));
    //         }
    //         result.push(<span key={i} style={{ color: 'red', fontWeight: 600 }}>{text.slice(start, end)}</span>);
    //         lastIdx = end;
    //     });
    //     if (lastIdx < text.length) {
    //         result.push(text.slice(lastIdx));
    //     }
    //     return result;
    // };
    return (
        <Card bordered={false}>
            <div>
                <CommonTitle title="任务完成情况" />
                <Row gutter={[16, 16]}>
                    {list.map((ele, index) => (
                        <Col span={8} key={index}>
                            <TopItem {...ele} data={data} />
                        </Col>
                    ))}
                </Row>
            </div>
            <Row style={{ marginBottom: 16 }}>
                <Col span={24}>
                    <Button type="danger" onClick={handleConfirm} style={{ marginRight: 16 }} >
                        确认诈骗
                    </Button>
                    <Button type="danger" onClick={handleSuspiciousFraud} style={{ marginRight: 16, backgroundColor: '#FF9900' }} >
                        疑似诈骗
                    </Button>
                    <Button type="primary" onClick={handleNoFraud} >
                        非诈骗
                    </Button>
                </Col>
            </Row>
            <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                <ShrinkSearchForm
                    formList={[
                        <Form.Item label="质检号码">
                            {getFieldDecorator('phoneNum', {
                                initialValue: undefined,
                            })(
                                <Input placeholder="请输入" allowClear style={{ width: '100%' }} />
                            )}
                        </Form.Item>,
                        <Form.Item label="任务开始时间">
                            {getFieldDecorator('gtTime')(
                                <DatePicker
                                    format={'YYYY-MM-DD'}
                                    placeholder="请选择"
                                    allowClear
                                    style={{ width: '100%' }}
                                />,
                            )}
                        </Form.Item>,

                        <Form.Item label="研判结果">
                            {getFieldDecorator('judgmentResult', {
                                initialValue: '待研判',
                            })(
                                <Select placeholder="请选择" allowClear style={{ width: '100%' }}>
                                    {
                                        ['诈骗', '非诈骗', '疑似诈骗', '待研判']?.map((ele, index) => (
                                            <Select.Option value={ele} key={index}>
                                                {ele}
                                            </Select.Option>
                                        ))
                                    }
                                </Select>

                            )}
                        </Form.Item>,
                        <CommonSelect
                            form={form}
                            formItemKey="keywordType"
                            configType="keyWordType"
                            formItemLabel="高危类型"
                        />,
                        // <Form.Item label="高危类型">
                        //     {getFieldDecorator('modelType', {
                        //         initialValue: undefined,
                        //     })(
                        //         <Select placeholder="请选择" allowClear>
                        //             {
                        //                 highRiskTypeList?.map((ele, index) => (
                        //                     <Select.Option value={ele?.keywordType} key={index}>
                        //                         {ele?.keywordType}
                        //                     </Select.Option>
                        //                 ))
                        //             }
                        //         </Select>
                        //     )}
                        // </Form.Item>,
                        <Form.Item label="通话内容">
                            {getFieldDecorator('callContent')(<Input placeholder="请输入" allowClear style={{ width: '100%' }} />)}
                        </Form.Item>,

                        <Form.Item label="处置时间">
                            {getFieldDecorator('disposeTime', {
                                initialValue: [moment().startOf('month'), moment()],
                            })(
                                <RangePicker
                                    format={'YYYY-MM-DD'}
                                    placeholder={['开始时间', '结束时间']}
                                    allowClear
                                    style={{ width: '100%' }}

                                />
                            )}
                        </Form.Item>,

                    ]}
                    optButton={
                        <Fragment>
                            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
                                查询
                            </Button>
                            <Button onClick={handleReset} style={{ marginRight: 8 }}>
                                重置
                            </Button>
                            {/* <Licensee license="schoolCard_shutdownDetail_export"> */}
                            <Button type="primary" onClick={handleExport} loading={exportLoading} disabled={!listData?.list?.length} >
                                导出
                            </Button>
                            {/* <ExportApprove
                                // buttonStyle={{ marginRight: 10 }}
                                exportParams={{
                                    urlAPi: '/api/hn/blackAndGrayList/exportBlackAngGrayList',
                                    title: 'AI质检人工核验导出',
                                    params: { pageNum: 1, pageSize: 9999, ...serachParams?.current },
                                    method: 'POST',
                                    mime: 'xlsx',
                                    isDate: true,
                                    currentDateFormate: 'YYYYMMDD',
                                }}
                                moduleTile="AI质检人工核验"
                                // 是否校验商业秘密电子文件相关
                                isVerifyhEncryption={true}
                                disabledExport={!listData?.list?.length}
                            /> */}
                            {/* </Licensee> */}
                        </Fragment>
                    }
                    colSpan={8}
                />
            </Form>
            <StandardTable
                tableRef={tableRef}
                showSelectCount={true}
                selectedRows={selectedRows}
                columns={columns}
                data={listData}
                onChange={handleTableChange}
                onSelectRow={handleSelectRows}
                rowKey="id"
                loading={loading}
                scroll={{
                    x: '100%',
                }}
                crossPageSelect={true} // 启用跨页勾选
                preserveSelectedOnRefresh={true} // 刷新时保留选中数据
                rowSelectionProps={{
                    getCheckboxProps: (record) => ({
                        disabled: record.judgmentResult !== '待研判',
                    }),
                }}
            />
            {
                detailVisible && <Details data={detailData}
                    visible={detailVisible}
                    onCancel={() => {
                        setDetailVisible(false);
                        setDetailData({});
                    }}
                    keyWords={keyWords}
                />
            }

            {/* 二次确认弹窗 */}
            {confirmVisible && <Modal
                title={getConfirmModalContent().title}
                visible={confirmVisible}
                onOk={handleConfirmAction}
                onCancel={handleCancelAction}
                confirmLoading={loading}
            >
                <p>{getConfirmModalContent().content}</p>
                <div className={styles.remarkArea}>
                    <div className={styles.remarkContent}>
                        <TextArea
                            value={remarkContent}
                            onChange={handleRemarkChange}
                            placeholder="请输入研判记录"
                            autoSize={{ minRows: 3, maxRows: 5 }}
                        />
                    </div>
                </div>
            </Modal>}
        </Card>
    )
};

export default Form.create()(AIQualityInspectionManualVerification);