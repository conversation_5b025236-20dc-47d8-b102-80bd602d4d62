.remarkArea {
  margin-top: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
}

.remarkTitle {
  font-weight: bold;
  margin-bottom: 8px;
}

.remarkContent {
  color: #666;
}
.details{
  :global {
    .ant-descriptions-row {
      display: flex;
      // margin-bottom: 8px;
    }

    .ant-descriptions-item {
      display: flex;
      width: 33%;
      margin-right: 1%;
    }
 
    .ant-descriptions-item-content {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
