import React, { useState, useEffect } from 'react';
import { Modal, Tooltip, message } from 'antd';
import DetailItem from '@/components/DetailItem';
import styles from './index.less';
import StandardTable from '@/components/StandardTable';
import { exportFile, renderToolTip } from '@/utils/utils';
import highlightKeywords from './highlightKeywords';
import request from '@/utils/request';

const Details = (props) => {
  let apiPrefix = '/api/biz/modularity/project1752737891113331'
  const { visible, onCancel, data, keyWords } = props;
  const [basicInfoData, setBasicInfoData] = useState([]);
  const [resultInfoData, setResultInfoData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  useEffect(() => {
    getBasicInfo();
    getJudgmentResult();
    findTableDataPager();
  }, [data]);
  const columns = [
    {
      title: '呼叫id',
      dataIndex: 'callId',
      key: 'callId',
      width: 140,
      ellipsis: true,

    },
    {
      title: '通话内容',
      dataIndex: 'recordContent',
      key: 'recordContent',
      width: 480,
      render: (text) => {
        if (!text) return '--';
        return (
          <Tooltip
            title={<div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>{highlightKeywords(text, keyWords)}</div>}
            overlayStyle={{ maxWidth: 480, maxHeight: 300, overflow: 'auto' }}
          >
            <div
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 6,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                wordBreak: 'break-all',
                whiteSpace: 'normal',
                minWidth: 0,
                maxWidth: 480,
                lineHeight: '21px',
                maxHeight: '126px',
              }}
            >
              {highlightKeywords(text, keyWords)}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '高危类型',
      dataIndex: 'qualityThreeLevelType',
      key: 'qualityThreeLevelType',
      width: 140,
      ellipsis: true,

    },

    {
      title: '主叫号码',
      dataIndex: 'callingNum',
      key: 'callingNum',
      width: 140,
      ellipsis: true,

    },
    {
      title: '被叫号码',
      dataIndex: 'calledNum',
      key: 'calledNum',
      width: 140,
      ellipsis: true,

    },
    {
      title: '原被叫',
      dataIndex: 'originallyCalledNum',
      key: 'originallyCalledNum',
      width: 140,
      ellipsis: true,

    },
    {
      title: '所在地',
      dataIndex: 'location',
      key: 'location',
      width: 140,
      ellipsis: true,

    },
    {
      title: '话务时间',
      dataIndex: 'callStartTime',
      key: 'callStartTime',
      width: 140,
      ellipsis: true,

    },
    {
      title: '识别类型',
      dataIndex: 'qualityTwoLevelType',
      key: 'qualityTwoLevelType',
      width: 140,
      ellipsis: true,
      render: (text) => {
        const  types={
          0:'关键词识别',
          1:'大模型识别'
        }
        return types[text] || '--';
      }

    },


  ];
  const basicInfo = [
    {
      label: '号码',
      key: 'phoneNum',
    },
    {
      label: '入网时间',
      key: 'registerTime',
    },
    {
      label: '使用客户地址',
      key: 'customerAddress',
    },
    {
      label: '签约类型',
      key: 'contractType',
    },
    {
      label: '模型研判',
      key: 'modelJudgment',
    },
    // {
    //   label: '处置标签',
    //   key: 'disposeLabel',
    // },
    {
      label: '通话记录条数',
      key: 'callRecordCount',
    },
  ];
  const resultInfo = [
    {
      label: '研判结果',
      key: 'judgmentResult',
    },
    {
      label: '研判记录',
      key: 'judgmentRecord',
    },
  ];
  const getBasicInfo = () => {
    // setBasicInfoData({
    //   cdrRecordNum: 123,
    //   registerTime: '2025-01-01 12:00:00',
    //   customerAddress: '浙江省杭州市余杭区乐富海邦园1239',
    //   contractType: '主叫',
    //   modelJudgment: '123,可信,不可信,疑似诈骗',
    //   disposeLabel: '123,123,123,123',
    //   callRecordCount: '33',

    // });

    request({
      url: `${apiPrefix}/signedOrderDetailsQuery`,
      method: "POST",
      params: {
        phoneNum: '18267727839',
        dataTime: "2025-07-29 00:00:00"
      }
    }).then((res) => {
      console.log('res', res);
      if (res.status == 200) {
        const { informationObj, singNumInfo, cdrRecordNum } = res.data;
        setBasicInfoData({
          phoneNum: data.phoneNum,
          registerTime: informationObj.internetAccessTime,
          customerAddress: informationObj.usingCustomerAddress,
          contractType: singNumInfo.signType,
          modelJudgment: informationObj.modelJudgment,
          callRecordCount: cdrRecordNum,
        });
      } else {
        message.error(res.message);
      }
    })
  };
  // 获取研判结果
  const getJudgmentResult = () => {
    setResultInfoData({
      judgmentResult: '123',
      judgmentRecord: '123',
    });
  };
  const findTableDataPager = async (pageNum = 1, pageSize = 10,) => {
    setTableData({
      list: [
        {
          id: 1,
          key: 1,
          callId: '123',
          caller: '123',
          callee: '123',
          originalCallee: '123',
          location: '123',
          callDuration: '5分钟',
          recognitionType: '人工识别',
          modelType: '诈骗',
          callContent: '“您好，这里是银行客服中心。我们检测到您的账户近期有一笔异常交易，请问是您本人操作的吗？如果不是，请及时回复‘不是’。请您不要将银行卡号、验证码等信息透露给任何人，包括自称银行工作人员的人员。我们不会通过电话要求您转账或提供验证码。请您注意账户安全，防范电信网络诈骗。近期有不法分子冒充银行、公安等机构，谎称账户异常、涉嫌洗钱等，诱导客户转账或泄露个人信息。请您务必提高警惕，遇到可疑情况可拨打官方客服电话核实。您的账户目前一切正常，无需操作。如有疑问可随时致电本行。再次提醒：银行不会通过电话、短信索要您的密码、验证码等敏感信息。请妥善保管个人信息，防止信息泄露。感谢您的配合，祝您生活愉快。近期有客户反映接到自称‘公安’的电话，称其银行卡涉嫌违法犯罪活动，要求配合调查并转账至‘安全账户’。请您牢记，公安机关不会通过电话要求转账或提供银行卡信息。遇到类似情况请立即挂断电话并报警。请您定期修改密码，避免使用过于简单的密码。不要随意点击陌生链接或下载不明软件，防止手机中病毒导致信息泄露。若发现账户有异常，请第一时间联系银行。感谢您的理解与支持。请您确认是否还有其他疑问，如无请挂断电话。祝您工作顺利，生活安康。再次提醒，保护好您的资金安全，谨防电信诈骗。您好，这里是银行客服中心。我们检测到您的账户近期有一笔异常交易，请问是您本人操作的吗？如果不是，请及时回复‘不是’。请您不要将银行卡号、验证码等信息透露给任何人，包括',
        },
      ],
      pagination: {
        total: 10,
        current: pageNum,
        pageSize: pageSize,
      },
    });
    setLoading(true);
    const response = await request({
      url: `${apiPrefix}/manualVerificationList`,
      params: {
        pageNum,
        pageSize,
        phoneNum: data.phoneNum || '18267727839',
        dataTime: "2025-07-29 00:00:00"
      },
      method: 'GET',
    });
    setLoading(false);
    if (response.status == 200) {
      let data = response?.data?.dataList?.map((item, i) => {
        return {
          ...item,
          key: item.id,
          // callContent: i % 2 === 0 ? '通话内容1111' : '中奖B待审核“您好中奖5，这里是银行客服中心。恭喜您我们检测到您的账户近期有一笔异常交易中奖了，请问是您本人操作的吗？如果不是，请及时回复‘不是’。请您不要将银行卡号、中奖了验证码等信息透露给任何人恭喜您，包括自称银行工作人员的人员。我们不会通过电话要求您转账或提供验证码。请您注意账户安全，防范电信网络诈骗中奖123。近期有不法分子冒充银行、公安等机构，谎称账户异常、涉嫌洗钱等，诱导客户转账或泄露个人信息。请您务必提高警惕，遇到可疑情况可拨打官方客服电话核实。您的账户目前一切正常，无需操作。如有疑问可随时致电本行。再次提醒：银行不会通过电话、短信索要您的密码、验证码等敏感信息。请妥善保管个人信息，防止信息泄露。感谢您的配合，祝您生活愉快中奖5。中奖5近期有客户反映接到自称‘公安’的电话，称其银行卡涉嫌违法犯罪活动，要求配合调查并转账至‘安全账户’中奖B。请您牢记，公安机关不会通过电话要求转账或提供银行卡信息。遇到类似情况请立即挂断电话并报警。请您定期修改密码，避免使用过于简单的密码。不要随意点击陌生链接或下载不明软件，防止手机中病毒导致信息泄露。若发现账户有异常，请第一时间联系银行。感谢您的理解与支持。请您确认是否还有其他疑问，如无请挂断电话。祝您工作顺利，生活安康。再次提醒，保护好您的资金安全，谨防电信诈骗。您好，这里是银行客服中心。我们检测到您的账户近期有一笔异常交易，请问是您本人操作的吗？如果不是，请及时回复‘不是’。请您不要将银行卡号、验证码等信息透露给任何人，包括',
          // judgmentResult: '待研判',
        }
      }) || [];
      setTableData({
        list: data || [],
        pagination: {
          total: response.data.totalCount,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };
  const handleTableChange = (pagination) => {
    findTableDataPager(
      pagination.current,
      pagination.pageSize,
    );
  };
  return (
    <Modal
      title="详情"
      visible={visible}
      onCancel={onCancel}
      footer={null}
      width={'80%'}
    >
      <div className={styles.details}>
        <DetailItem
          title=""
          data={basicInfo}
          dataValue={{ ...basicInfoData }}
          column={3}
        />
      </div>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
        crossPageSelect={false}
        preserveSelectedOnRefresh={false}
        scroll={{
          x: '100%',
        }}
      />
      <div className={styles.table}>
        <DetailItem
          title=""
          data={resultInfo}
          dataValue={{ ...resultInfoData }}
          column={1}
        />
      </div>
    </Modal>
  )
};

export default Details;