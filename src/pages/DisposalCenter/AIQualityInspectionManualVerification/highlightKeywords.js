/*
 * @Author: ss
 * @Date: 2025-07-28 15:15:31
 * @LastEditors: ss
 * @LastEditTime: 2025-07-28 16:34:00
 * @Description: 高亮标红  存在&的时候 比如对于 "您好&支付宝&点击链接"：
 * 如果文本中有多个符合顺序的匹配，只会标红最紧凑的那一组
 * 其他位置的"您好"、"支付宝"、"点击链接"即使顺序正确，也不会被标红
 * 总是选择各部分之间距离最近的那组进行标记
 */
const highlightKeywords = (text, keywords) => {
    if (!text || !keywords || !keywords.length) return text;

    // 过滤空字符串，并按长度从长到短排序
    const validKeywords = keywords.filter(Boolean).sort((a, b) => b.length - a.length);
    if (!validKeywords.length) return text;

    let highlightRanges = [];

    validKeywords.forEach(keyword => {
        if (keyword.includes('&')) {
            // 处理带 & 的关键字
            const parts = keyword.split('&').map(s => s.trim()).filter(Boolean);
            if (parts.length < 2) return; // 跳过无效的 & 关键字

            let pos = 0;
            let bestSequence = null;
            let bestSequenceLength = Infinity;

            // 找到所有可能的匹配序列
            while (pos < text.length) {
                // 从当前位置开始查找第一个部分
                const firstPartIndex = text.indexOf(parts[0], pos);
                if (firstPartIndex === -1) break; // 找不到第一个部分，结束搜索

                let currentPos = firstPartIndex + parts[0].length;
                let allFound = true;
                let currentRanges = [[firstPartIndex, firstPartIndex + parts[0].length]];

                // 严格按顺序查找后续部分
                for (let i = 1; i < parts.length; i++) {
                    const nextPartIndex = text.indexOf(parts[i], currentPos);
                    
                    // 如果找不到下一个部分，或者下一个部分在前面，则匹配失败
                    if (nextPartIndex === -1 || nextPartIndex < currentPos) {
                        allFound = false;
                        break;
                    }

                    currentRanges.push([nextPartIndex, nextPartIndex + parts[i].length]);
                    currentPos = nextPartIndex + parts[i].length;
                }

                if (allFound) {
                    // 计算当前序列的总长度（最后一个结束位置减去第一个开始位置）
                    const sequenceLength = currentRanges[currentRanges.length - 1][1] - currentRanges[0][0];
                    
                    // 如果这是最紧凑的序列，保存它
                    if (sequenceLength < bestSequenceLength) {
                        bestSequence = currentRanges;
                        bestSequenceLength = sequenceLength;
                    }
                }
                
                pos = firstPartIndex + 1;
            }

            // 只添加最紧凑的序列
            if (bestSequence) {
                highlightRanges.push(...bestSequence);
            }
        } else {
            // 处理普通关键字
            let pos = 0;
            while (true) {
                const idx = text.indexOf(keyword, pos);
                if (idx === -1) break;
                highlightRanges.push([idx, idx + keyword.length]);
                pos = idx + keyword.length;
            }
        }
    });

    // 合并重叠的高亮区间
    highlightRanges.sort((a, b) => a[0] - b[0]);
    let merged = [];
    for (let range of highlightRanges) {
        if (!merged.length || merged[merged.length - 1][1] < range[0]) {
            merged.push(range);
        } else {
            merged[merged.length - 1][1] = Math.max(merged[merged.length - 1][1], range[1]);
        }
    }

    // 生成高亮文本
    let result = [];
    let lastIdx = 0;
    merged.forEach(([start, end], i) => {
        if (lastIdx < start) {
            result.push(text.slice(lastIdx, start));
        }
        result.push(<span key={i} style={{ color: 'red', fontWeight: 600 }}>{text.slice(start, end)}</span>);
        lastIdx = end;
    });
    if (lastIdx < text.length) {
        result.push(text.slice(lastIdx));
    }
    return result;
};

export default highlightKeywords;