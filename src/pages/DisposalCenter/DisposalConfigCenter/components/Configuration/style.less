@import (reference) '~antd/es/style/themes/default.less';
@import (reference) '~antd/es/form/style/index.less';

@toolbar-button-size: 16px;
@border-size: 1px;
@border-radius: 4px;
@gutter-row: 10px;
@gutter-col: 5px;
@gutter-row-conditions-rel: 5px;
@gutter-row-conditionContainer: 5px;
@gutter-level: 55px;

@group-conditions-rel-width: @gutter-level + @gutter-col;

@toolbar-width: 155px;

@condition-field-width: 170px;
@condition-rel-width: 80px;
@condition-value-width: 230px;
@condition-valueContainer-minWidth: @condition-value-width + @gutter-col;

@conditionContent-minWidth: (2 * @border-size) + @gutter-col + @condition-field-width + @gutter-col + @condition-rel-width + @gutter-col + @condition-valueContainer-minWidth;
@conditionContainer-minWidth: (2 * @border-size) + (2 * @gutter-col) + @conditionContent-minWidth;
@groupContainer-minWidth: (2 * @border-size) + (2 * @gutter-col) + @conditionContent-minWidth;
@groupOfCondition-groupContainer-minWidth: @groupContainer-minWidth - @gutter-level - ((2 * @border-size) + (2 * @gutter-col));
@formContainer-minWidth: @groupContainer-minWidth + @toolbar-width + @gutter-col;
@relContainerForGroupOfCondition-width: @gutter-level;
@relContainerForGroupOfCondition-height: @gutter-row + (2 * @border-size) + (2 * @gutter-row-conditions-rel) + (2 * @border-size) + @form-component-max-height;
@relTextForGroupOfCondition-marginTop: @gutter-row + @border-size + @gutter-row-conditions-rel + @border-size;
@relTopLineForGroupOfCondition-height: @relContainerForGroupOfCondition-height - @form-component-max-height + (@font-size-base / 2);
@relRightLineForGroupOfCondition-top: @relTopLineForGroupOfCondition-height + (@font-size-base / 2);
@relRightLineForGroupOfCondition-width: (@relContainerForGroupOfCondition-width - @font-size-base) / 2;

@border-color: #ccc;
@border-style: dashed;
@hover-color: @info-color;

.formContainer {
    position: relative;
    padding-right: @toolbar-width + @gutter-col;
    box-sizing: border-box;
    overflow-x: auto;
    display: inline-block;
    width: 100%;
    min-width: @formContainer-minWidth;

    :global {
        .ant-form-item {
            margin-bottom: 0;
            margin-right: @gutter-col;
            margin-left: 0;
        }
        .ant-form-item:first-child {
            margin-left: @gutter-col;
        }
    }

    .toolBar {
        position: absolute;
        right: 0;
        width: @toolbar-width;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        height: @form-component-max-height + (2 * @border-size);
        box-sizing: border-box;

        button {
            margin-right: @gutter-col;
            font-size: @toolbar-button-size;
            line-height: @toolbar-button-size;
        }
    }

    .groupContainer {
        width: 100%;
        display: inline-block;
        min-width: @groupContainer-minWidth;
        margin: @gutter-row 0 0 0;
        border: @border-size @border-style @border-color;
        border-radius: @border-radius;
        padding: @gutter-row-conditions-rel @gutter-col;
        box-sizing: border-box;

        & > .groupConditionsRefContainer {
            width: 100%;
            display: flex;
            align-items: center;
            border: @border-size @border-style transparent;
            box-sizing: border-box;

            .groupConditionsRel {
                width: @group-conditions-rel-width;
                box-sizing: border-box;
            }

            & > * {
                flex: 0 0 auto;
            }

            & > .lineToToolBar {
                flex: 1 1 0;
                height: 0;
                margin-left: @gutter-col;
                border: 0;
                box-sizing: border-box;
            }

            &:hover > .lineToToolBar {
                border-top: @border-size @border-style @hover-color;
            }

            & > .toolBar {
                border: @border-size @border-style transparent;
                border-radius: @border-radius;
            }

            &:hover > .toolBar {
                border-color: @hover-color;
            }

            &:hover {
                border-color: @hover-color;
            }
        }

        & > .groupConditionsContainer.groupConditionsContainerHidden {
            display: none;
        }

        & > .groupConditionsContainer {
            width: 100%;
            display: inline-block;
            padding-left: @gutter-level;
            box-sizing: border-box;

            & > .groupConditionsScroller {
                width: 100%;
                display: inline-block;
                overflow-x: auto;
                box-sizing: border-box;
                padding-right: 0.5px; // 为了使小数点宽度时子节点的边框可以显示出来
            }
        }
    }

    .conditionContainer {
        width: 100%;
        display: inline-block;
        min-width: @conditionContainer-minWidth;
        margin: @gutter-row 0 0 0;
        border: @border-size @border-style @border-color;
        border-radius: @border-radius;
        padding: @gutter-row-conditionContainer @gutter-col;
        box-sizing: border-box;

        &:hover > .groupOfCondition > .relContainerForGroupOfCondition {
            & > .relTopLineForGroupOfCondition,
            & > .relRightLineForGroupOfCondition {
                border-color: @hover-color;
            }
            & > .relTextForGroupOfCondition {
                & :global(.ant-select-selection__placeholder) {
                    color: @hover-color;
                }
            }
        }

        & > .groupOfCondition {
            width: 100%;
            display: inline-block;
            padding: 0 0 @gutter-row-conditions-rel @gutter-level;
            box-sizing: border-box;

            & > .groupContainer {
                min-width: @groupOfCondition-groupContainer-minWidth;
            }

            & > .relContainerForGroupOfCondition {
                position: absolute;
                width: @relContainerForGroupOfCondition-width;
                height: @relContainerForGroupOfCondition-height;
                display: inline-block;
                box-sizing: border-box;
                margin-left: -@gutter-level;
                overflow: hidden;

                & > .relTextForGroupOfCondition {
                    margin-top: @relTextForGroupOfCondition-marginTop;

                    & :global(.ant-select-disabled .ant-select-selection) {
                        background: transparent;
                        background-color: transparent;
                    }
                    & :global(.ant-select-selection) {
                        border-color: transparent;
                    }
                    & :global(.ant-select-selection__rendered) {
                        margin: 0;
                    }
                    & :global(.ant-select-selection-selected-value),
                    & :global(.ant-select-selection__placeholder) {
                        position: absolute;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                    & :global(.ant-select-disabled .ant-select-selection) {
                        cursor: default;
                    }
                }

                & > .relTopLineForGroupOfCondition {
                    position: absolute;
                    display: inline-block;
                    box-sizing: border-box;
                    height: @relTopLineForGroupOfCondition-height;
                    width: 0;
                    border-left: @border-size @border-style @border-color;
                    top: 0;
                    left: @relContainerForGroupOfCondition-width / 2;
                }

                & > .relRightLineForGroupOfCondition {
                    position: absolute;
                    display: inline-block;
                    box-sizing: border-box;
                    height: 0;
                    width: @relRightLineForGroupOfCondition-width;
                    border-top: @border-size @border-style @border-color;
                    right: 0;
                    top: @relRightLineForGroupOfCondition-top;
                }
            }
        }

        & > .conditionContent {
            width: 100%;
            min-width: @conditionContent-minWidth;
            display: flex;
            align-items: flex-start;
            border: @border-size @border-style transparent;
            box-sizing: border-box;

            & .conditionField {
                width: @condition-field-width;
            }
        
            & .conditionRel {
                width: @condition-rel-width;
            }
        
            & > .conditionValueContainer.valueOfINORNOTIN,
            & > .conditionValueContainer.valueOfOther {
                flex: 0 1 auto;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                min-width: @condition-valueContainer-minWidth;
                box-sizing: border-box;

                & .conditionValue {
                    width: @condition-value-width;
                }
                & .conditionValue:global(.ant-input-group-wrapper) > :global(.ant-input-wrapper.ant-input-group) > .conditionValue:global(.ant-input-affix-wrapper) {
                    width: 100%;
                }
                
                & > :global(.ant-form-item:first-child) {
                    margin-left: 0;
                }

            }

            & > * {
                flex: 0 0 auto;
            }
    
            & > .lineToToolBar {
                align-self: flex-start;
                margin-top: @form-component-max-height / 2;
                flex: 1 1 0;
                height: 0;
                margin-left: @gutter-col;
                border: 0;
                box-sizing: border-box;
            }

            &:hover > .lineToToolBar {
                border-top: @border-size @border-style @hover-color;
            }

            & > .toolBar {
                border: @border-size @border-style transparent;
                border-radius: @border-radius;
            }

            &:hover > .toolBar {
                border-color: @hover-color;
            }

            &:hover {
                border-color: @hover-color;
            }
        }
    }
}

