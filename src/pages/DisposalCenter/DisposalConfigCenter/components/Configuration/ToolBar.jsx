import React, { Component } from 'react'
import { But<PERSON>, Tooltip, Modal, Icon, message } from 'antd';
import { SYNCDATA_TYPES } from './utils';
import styles from './style.less';
import getValue from 'get-value';

export default class ToolBar extends Component {
    getSelfSyncPath = () => {
        return `${this.props.syncPath || ''}`;
    }
    getSelfButtonSyncPath = (subPath) => {
        return `${this.getSelfSyncPath()}${this.getSelfSyncPath() && subPath ? '.' : ''}${subPath || ''}`;
    }
    isRootGroup = () => this.getSelfSyncPath() === '';
    isGroupConditionsEmpty = () => !this.props.data?.group?.conditions?.length;
    isGroupEmpty = () => !this.props.data?.group;

    onButtonClick(type, path) {
        const { syncData } = this.props;
        if (type === SYNCDATA_TYPES.DELETE_CONDITION) {
            syncData(type, path);
        } else if (type === SYNCDATA_TYPES.CLEAR_GROUP) {
            Modal.confirm({
                title: '删除',
                content: '该层关系下存在条件/条件关系组，是否确认删除',
                okText: '确定',
                okType: 'danger',
                cancelText: '取消',
                icon: <Icon type="delete" />,
                onOk() {
                    syncData(type, path);
                    message.success('删除成功');
                },
                onCancel() {
                    message.warning('取消删除');
                },
            });
        } else if (type === SYNCDATA_TYPES.DELETE_GROUP) {
            syncData(type, path);
        }
        else {
            syncData(type, path);
        }
    }

    renderToolBar = () => {
        const toolBarType = this.props.type;
        let node = null;
        switch (toolBarType) {
            case 'group':
                node = this.renderToolBarGroupConditionsRel();
                break;
            case 'condition':
                node = this.renderToolBarCondition();
                break;
        }
        return <div className={styles.toolBar}>{node}</div>;
    }
    renderToolBarGroupConditionsRelDelButton = () => {
        return this.isRootGroup() ?
            <Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : (this.isGroupConditionsEmpty() ? '不可删除' : '删除')}>
                <Button
                    type="link"
                    icon="delete"
                    onClick={() => this.onButtonClick(SYNCDATA_TYPES.CLEAR_GROUP, this.getSelfButtonSyncPath())}
                    disabled={this.isGroupConditionsEmpty() || !!this.props.disabled}
                />
            </Tooltip>
            :
            <Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : '删除'}>
                <Button
                    type="link"
                    icon="delete"
                    onClick={() => this.onButtonClick(SYNCDATA_TYPES.DELETE_GROUP, this.getSelfButtonSyncPath())}
                    disabled={!!this.props.disabled}
                />
            </Tooltip>
            ;
    }
    renderToolBarGroupConditionsRel = () => {
        const isEmptyGroupConditions = this.isGroupConditionsEmpty();
        const isExpandedGroupConditions = this.props.groupCoditionsIsExpandedStatus;
        const toggleStatusGroupConditionsFn = this.props.groupConditionsToggleStatus;
        return (
            <>
                <Tooltip placement="top" title={isEmptyGroupConditions ? '该关系下无任何条件时不可展开' : (isExpandedGroupConditions ? '点击可收起' : '点击可展开')}>
                    <Button
                        type="link"
                        icon={isExpandedGroupConditions && !isEmptyGroupConditions ? 'down' : 'left'}
                        disabled={isEmptyGroupConditions}
                        onClick={toggleStatusGroupConditionsFn}
                    />
                </Tooltip>
                <Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : '添加单个条件'}>
                    <Button
                        type="link"
                        icon="plus-circle"
                        onClick={() => {
                            if (!isExpandedGroupConditions) {
                                toggleStatusGroupConditionsFn();
                            }
                            this.onButtonClick(SYNCDATA_TYPES.APPEND_CONDITION_GROUP_CONDITIONS, this.getSelfButtonSyncPath('group.conditions'));
                        }}
                        disabled={!!this.props.disabled}
                    />
                </Tooltip>
                <Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : '添加条件关系组'}>
                    <Button
                        type="link"
                        icon="plus-square"
                        onClick={() => {
                            if (!isExpandedGroupConditions) {
                                toggleStatusGroupConditionsFn();
                            }
                            this.onButtonClick(SYNCDATA_TYPES.APPEND_GROUP_GROUP_CONDITIONS, this.getSelfButtonSyncPath('group.conditions'));
                        }}
                        disabled={!!this.props.disabled}
                    />
                </Tooltip>
                {this.renderToolBarGroupConditionsRelDelButton()}
            </>
        );
    }
    renderToolBarCondition = () => {
        return (
            <>
                {/* 子条件关系组不太好理解，且不用自条件关系组已经可以配置出各种与或关系了，所以注释掉 */}
                {/*<Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : (!this.isGroupEmpty() ? '当前单行条件不允许继续添加子条件关系组' : '添加子条件关系组')}>*/}
                {/*    <Button type="link" icon="plus-square" disabled={!this.isGroupEmpty() || !!this.props.disabled} onClick={() => this.onButtonClick(SYNCDATA_TYPES.ADD_GROUP, this.getSelfButtonSyncPath())}></Button>*/}
                {/*</Tooltip>*/}
                <Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : '删除'}>
                    <Button type="link" icon="delete" disabled={!!this.props.disabled} onClick={() => this.onButtonClick(SYNCDATA_TYPES.DELETE_CONDITION, this.getSelfButtonSyncPath())}></Button>
                </Tooltip>
            </>
        );
    }
    render() {
        return this.renderToolBar();
    }
}



