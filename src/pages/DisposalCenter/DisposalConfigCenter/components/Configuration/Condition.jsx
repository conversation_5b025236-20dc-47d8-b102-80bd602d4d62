
import React, { Component } from 'react';
import { Button, Form, Input, Select, Divider, Modal, Tooltip, message, Icon } from 'antd';
import classnames from 'classnames';
import cloneDeep from 'lodash/cloneDeep';
import styles from './style.less';
import Group from './Group';
import ToolBar from './ToolBar';
import { formatConditionValue, CONDITIONRELMap, VALID_CONDITION_RELS, COLON_CONDITION_REL, isValidConditionRel, isConditionRelOfMultiValue, isConditionRelOfREG, SYNCDATA_TYPES, matchConditionValueFormatterTriggeredBy } from './utils';
import { connect } from 'dva';
const { Option } = Select;
@connect(({ stepModal }) => ({
    stepModal
}))
export default class Condition extends Component {
    constructor(props) {
        super(props)
    }

    getSelfPath = () => {
        return `${this.props.path}.condition`;
    }
    getSelfFormItemPath = (attrName) => {
        return `${this.getSelfPath()}.${attrName}`;
    }
    getSelfSyncPath = () => {
        return `${this.props.syncPath}.condition`;
    }
    getSelfFormItemSyncPath = (attrName) => {
        return `${this.getSelfSyncPath()}.${attrName}`;
    }
    getSelfToolbarSyncPath = () => {
        return this.props.syncPath;
    }
    getGroupOfConditionPath = () => {
        return this.props.path;
    }
    getGroupOfConditionSyncPath = () => {
        return this.props.syncPath;
    }

    onChangeConditionField = (value) => {
        this.props.syncData(SYNCDATA_TYPES.CHANGE_CONDITION_FIELD, this.getSelfFormItemSyncPath('field'), value);
        if (typeof this.props.conditionValueFormatter === 'function' && matchConditionValueFormatterTriggeredBy(this.props.conditionValueFormatterTriggeredBy, 'field')) {
           let valueOfConditionValue = this.props.conditionValueFormatter(value, this.props.data?.condition?.rel, cloneDeep(this.props.data?.condition?.value));
           if (typeof valueOfConditionValue !== 'undefined') {
               valueOfConditionValue = formatConditionValue(this.props.data?.condition?.rel, valueOfConditionValue, false, '');
               valueOfConditionValue = cloneDeep(valueOfConditionValue);
               this.props.syncData(SYNCDATA_TYPES.CHANGE_CONDITION_VALUE, this.getSelfFormItemSyncPath('value'), valueOfConditionValue);
           }
        }
    }
    onChangeConditionRel = (value) => {
        this.props.syncData(SYNCDATA_TYPES.CHANGE_CONDITION_REL, this.getSelfFormItemSyncPath('rel'), value);
        if (typeof this.props.conditionValueFormatter === 'function' && matchConditionValueFormatterTriggeredBy(this.props.conditionValueFormatterTriggeredBy, 'rel')) {
            let valueOfConditionValue = this.props.conditionValueFormatter(this.props.data?.condition?.field, value, cloneDeep(this.props.data?.condition?.value));
            if (typeof valueOfConditionValue !== 'undefined') {
                valueOfConditionValue = formatConditionValue(value, valueOfConditionValue, false, '');
                valueOfConditionValue = cloneDeep(valueOfConditionValue);
                this.props.syncData(SYNCDATA_TYPES.CHANGE_CONDITION_VALUE, this.getSelfFormItemSyncPath('value'), valueOfConditionValue);
            }
        }
    }
    onChangeConditionValue = (value, index) => {
        // .0 的原因详见 Condition 中的 renderValueOfOther 方法
        this.props.syncData(SYNCDATA_TYPES.CHANGE_CONDITION_ITEM_VALUE, this.getSelfFormItemSyncPath(`value${typeof index === 'number' ? `.${index}` : '.0'}`), value);
        if (typeof this.props.conditionValueFormatter === 'function' && matchConditionValueFormatterTriggeredBy(this.props.conditionValueFormatterTriggeredBy, 'value')) {
          let holeValue = cloneDeep(this.props.data?.condition?.value);
          if (!Array.isArray(holeValue)) {
            holeValue = [value];
          }
          if (typeof index !== 'undefined') {
            holeValue[index] = value;
          }
          let valueOfConditionValue = this.props.conditionValueFormatter(this.props.data?.condition?.field, this.props.data?.condition?.rel, cloneDeep(holeValue), index);
          if (typeof valueOfConditionValue !== 'undefined') {
            valueOfConditionValue = formatConditionValue(this.props.data?.condition?.rel, valueOfConditionValue, false, '');
            valueOfConditionValue = cloneDeep(valueOfConditionValue);
            this.props.syncData(SYNCDATA_TYPES.CHANGE_CONDITION_VALUE, this.getSelfFormItemSyncPath('value'), valueOfConditionValue);
          }
        }
    }
    addConditionValue = (index) => {
        this.props.syncData(SYNCDATA_TYPES.ADD_CONDITION_ITEM_VALUE, this.getSelfFormItemSyncPath(`value.${index + 1}`));
    }
    deleteConditionValue = (index) => {
        Modal.confirm({
            title: '删除',
            content: '您确定要删除吗？',
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            icon: <Icon type="delete" />,
            onOk: () => {
                this.props.syncData(SYNCDATA_TYPES.DELETE_CONDITION_ITEM_VALUE, this.getSelfFormItemSyncPath(`value.${index}`));
                message.success('删除成功');
            },
            onCancel() {
                message.warning('取消删除');
            },
        });
    }

    renderGroupOfCondition = () => {
        const hasGroup = !!this.props.data?.group;
        return (hasGroup &&
            <div className={styles.groupOfCondition}>
                <div className={styles.relContainerForGroupOfCondition}>
                    <Form.Item className={styles.relTextForGroupOfCondition}>
                        <Select placeholder="与" disabled showArrow={false} />
                    </Form.Item>
                    <div className={styles.relTopLineForGroupOfCondition} />
                    <div className={styles.relRightLineForGroupOfCondition} />
                </div>
                <Group
                    path={this.getGroupOfConditionPath()}
                    syncPath={this.getGroupOfConditionSyncPath()}
                    form={this.props.form}
                    data={this.props.data}
                    syncData={this.props.syncData}
                    optionList={this.props.optionList}
                    colonRelMode={this.props.colonRelMode}
                    conditionValueFormatter={this.props.conditionValueFormatter}
                    conditionValueFormatterTriggeredBy={this.props.conditionValueFormatterTriggeredBy}
                    disabled={!!this.props.disabled}
                    state={this.props.data}
                />
            </div>
        );
    }
    renderValue = () => {
        const rel = this.props.data?.condition?.rel;
        const isColonRelMode = !!this.props.colonRelMode;
        const isRelOfINORNOTIN = !isColonRelMode && isConditionRelOfMultiValue(rel);
        return (
            <div className={classnames(styles.conditionValueContainer, isRelOfINORNOTIN ? styles.valueOfINORNOTIN : styles.valueOfOther)}>
                {isRelOfINORNOTIN ? this.renderValueOfINORNOTIN() : this.renderValueOfOther()}
            </div>
        );
    }
    renderValueOfINORNOTIN = () => {
        const value = formatConditionValue(this.props.data?.condition?.rel, this.props.data?.condition?.value, false, '');
        return value.map((item, index) => {
            return (
                <Form.Item key={index}>{this.props.form.getFieldDecorator(this.getSelfFormItemPath(`value[${index}]`), {
                    initialValue: item,
                    rules: [{ required: true, message: '请填写必填信息!' }],
                    validateTrigger: false,
                })(
                    <Input
                        onChange={(e) => {
                            // allowClear 时的处理逻辑，保证 state 与 form 中的数据是一致的
                            if (e.target.value === '') {
                                this.onChangeConditionValue(e.target.value, index);
                            }
                        }}
                        onBlur={(e) => this.onChangeConditionValue(e.target.value, index)}
                        className={styles.conditionValue}
                        placeholder="请输入常量内容"
                        allowClear
                        disabled={!!this.props.disabled}
                        addonAfter={
                            <>
                                <Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : '删除常量输入框'}>
                                    {value.length > 1 ? <Button type="link" icon='minus' size="small" disabled={!!this.props.disabled} onClick={() => this.deleteConditionValue(index)} /> : null}
                                </Tooltip>
                                {value.length > 1 ? <Divider type="vertical" /> : null}
                                <Tooltip placement="top" title={!!this.props.disabled ? '已禁用，不可操作' : '新增常量输入框'}>
                                    <Button type="link" icon="plus" size="small" disabled={!!this.props.disabled} onClick={() => this.addConditionValue(index)} />
                                </Tooltip>
                            </>
                        }
                    />
                )}</Form.Item>
            );
        });
    }
    renderValueOfOther = () => {
        const value = formatConditionValue(this.props.data?.condition?.rel, this.props.data?.condition?.value, false, '');
        // value[0] 是因为 getFieldDecorator 不支持从 value 和 value[] 之间的转化，会报：One field name cannot be part of another, e.g. `a` and `a.b`
        return (
            <Form.Item>{this.props.form.getFieldDecorator(this.getSelfFormItemPath('value[0]'), {
                initialValue: value,
                rules: [{ required: true, message: '请填写必填信息!' }],
                validateTrigger: false,
            })(
                <Input
                    onChange={(e) => {
                        // allowClear 时的处理逻辑，保证 state 与 form 中的数据是一致的
                        if (e.target.value === '') {
                            this.onChangeConditionValue(e.target.value, 0);
                        }
                    }}
                    onBlur={(e) => this.onChangeConditionValue(e.target.value, 0)}
                    className={styles.conditionValue}
                    placeholder={isConditionRelOfREG(this.props.data?.condition?.rel) ? '请输入正则表达式' : '请输入常量内容'}
                    allowClear
                    disabled={!!this.props.disabled}
                />
            )}</Form.Item>
        );
    }
    renderLineToToolBar = () => {
        return <div className={styles.lineToToolBar} />;
    }
    renderToolBar = () => {
        return <ToolBar
            type="condition"
            syncPath={this.getSelfToolbarSyncPath()}
            syncData={this.props.syncData}
            data={this.props.data}
            state={this.props.data}
            disabled={!!this.props.disabled}
        />;
    }
    renderCondition = () => {
        const { form } = this.props;
        return (
            <div className={styles.conditionContainer}>
                <div className={styles.conditionContent}>
                    <Form.Item>{form.getFieldDecorator(this.getSelfFormItemPath('field'), {
                        initialValue: this.props.data?.condition?.field || undefined,
                        rules: [{ required: true, message: '请填写必填信息!' }],
                        normalize: (value, prevValue, allValues) => {
                            if (!value) {
                                return undefined;
                            }
                            return value;
                        },
                        validateTrigger: false,
                    })(
                        <Select
                            className={styles.conditionField}
                            onChange={this.onChangeConditionField}
                            placeholder="请选择"
                            showSearch
                            allowClear
                            disabled={!!this.props.disabled}
                            optionFilterProp="children"
                            filterOption={(input, option) =>
                                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }>
                            {this.props.optionList?.map(item => {
                              return <Option key={item.field} value={item.field}><Tooltip placement="left" title={`${item.fieldName}(${item.field})`}>{`${item.fieldName}(${item.field})`}</Tooltip></Option>;
                            })}
                        </Select>
                    )}</Form.Item>
                    <Form.Item label={!!this.props.colonRelMode ? ' ' : undefined} colon={!!this.props.colonRelMode}>{!this.props.colonRelMode ? (
                      form.getFieldDecorator(this.getSelfFormItemPath('rel'), {
                        initialValue: isValidConditionRel(this.props.data?.condition?.rel) ? this.props.data?.condition?.rel : undefined,
                        rules: [{ required: true, message: '请填写必填信息!' }],
                        normalize: (value, prevValue, allValues) => {
                            if (!isValidConditionRel(value)) {
                                return undefined;
                            }
                            return value;
                        },
                        validateTrigger: false,
                      })(
                        <Select className={styles.conditionRel} placeholder="请选择" disabled={!!this.props.disabled} onChange={this.onChangeConditionRel}>
                          {VALID_CONDITION_RELS.map(item => <Option key={item} value={item}>{CONDITIONRELMap[item]}</Option>)}
                        </Select>
                      )
                    ) : (
                      form.getFieldDecorator(this.getSelfFormItemPath('rel'), {
                        initialValue: COLON_CONDITION_REL,
                        normalize: (value, prevValue, allValues) => {
                            return COLON_CONDITION_REL;
                        }
                      })(<Input type="hidden" disabled={!!this.props.disabled}/>)
                    )}</Form.Item>
                    {this.renderValue()}
                    {this.renderLineToToolBar()}
                    {this.renderToolBar()}
                </div>
                {this.renderGroupOfCondition()}
            </div>
        );
    }
    render() {
        return this.renderCondition()
    }
}
