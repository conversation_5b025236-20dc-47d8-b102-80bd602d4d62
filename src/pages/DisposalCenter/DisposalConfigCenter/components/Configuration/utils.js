const NODETYPE = {
    condition: Symbol('condition'),
    group: Symbol('group'),
    conditionWithGroup: Symbol('conditionWithGroup'),
}

export const getNodeType = (nodeData) => {
    let nodeType;
    if (nodeData?.condition && !nodeData?.group) {
        nodeType = NODETYPE.condition;
    } else if (!nodeData?.condition && nodeData?.group) {
        nodeType = NODETYPE.group;
    } else if (nodeData?.condition && nodeData?.group) {
        nodeType = NODETYPE.conditionWithGroup;
    }
    return nodeType;
}

export const isGroup = (nodeType) => nodeType === NODETYPE.group;

export const isCondition = (nodeType) => nodeType === NODETYPE.condition;

export const isConditionWithGroup = (nodeType) => nodeType === NODETYPE.conditionWithGroup;

export const isValidConditionItemValue = (itemValue, canNotEmptyString) =>
    ['number', 'string'].includes(typeof itemValue) &&
    (!canNotEmptyString || typeof itemValue !== 'string' || itemValue !== '');

export const formatConditionValue = (rel, value, canNotEmptyString, defaultItemValue) => {
    if (isConditionRelOfMultiValue(rel)) {
        if (Array.isArray(value)) {
            const t = value.filter(item => isValidConditionItemValue(item, canNotEmptyString));
            if (t.length > 0) {
                return t;
            }
            return [(!canNotEmptyString || ['number', 'string'].includes(typeof defaultItemValue)) ? defaultItemValue : ''];
        }
        if (isValidConditionItemValue(value, canNotEmptyString)) {
            return [value];
        }
        return [(!canNotEmptyString || ['number', 'string'].includes(typeof defaultItemValue)) ? defaultItemValue : ''];
    } else {
        if (Array.isArray(value)) {
            const t = value.find(item => isValidConditionItemValue(item, canNotEmptyString));
            if (typeof t !== 'undefined') {
                return t;
            }
            return (!canNotEmptyString || ['number', 'string'].includes(typeof defaultItemValue)) ? defaultItemValue : '';
        }
        if (isValidConditionItemValue(value, canNotEmptyString)) {
            return value;
        }
        return (!canNotEmptyString || ['number', 'string'].includes(typeof defaultItemValue)) ? defaultItemValue : '';
    }
}

export const formatConditionItemValue = (value, canNotEmptyString, defaultItemValue) => formatConditionValue(null, value, canNotEmptyString, defaultItemValue);
export const formatConditionArrayValue = (value, canNotEmptyString, defaultItemValue) => formatConditionValue(/* 此处传 IN 和 NOT_IN 都可以 */'IN', value, canNotEmptyString, defaultItemValue);

export const CONDITIONRELMap = {
    EQ: '=',
    GREATER: '>',
    GREATER_EQ: '>=',
    LESS: '<',
    LESS_EQ: '<=',
    IN: 'in',
    NOT_IN: 'not in',
    REG: '正则',
    CONTAIN:"包含",
    NOTINCLUDED:"不包含"
};
export const CONDITIONRELMap1 = {
    EQ: '=',
    GREATER: '>',
    GREATER_EQ: '>=',
    LESS: '<',
    LESS_EQ: '<=',
    IN: 'in',
    NOT_IN: 'not in',
    REG: '正则',
};
export const VALID_CONDITION_RELS = ['EQ', 'GREATER', 'GREATER_EQ', 'LESS', 'LESS_EQ', 'IN', 'NOT_IN', 'REG','CONTAIN','NOTINCLUDED'];
export const COLON_CONDITION_REL = ':';
export const VALID_CONDITION_RELS1 = ['EQ', 'GREATER', 'GREATER_EQ', 'LESS', 'LESS_EQ', 'IN', 'NOT_IN', 'REG',];
export const isValidConditionRel = (rel) => VALID_CONDITION_RELS.includes(rel);
export const isConditionRelOfMultiValue = (rel) => ['IN', 'NOT_IN','CONTAIN','NOTINCLUDED'].includes(rel);
export const isConditionRelOfREG = (rel) => rel === 'REG';

export const CONDITIONS_RELMap = {
    AND: '与',
    OR: '或',
};
export const VALID_GROUP_CONDITIONS_RELS = ['AND', 'OR'];
export const isValidGroupConditionsRel = (rel) => VALID_GROUP_CONDITIONS_RELS.includes(rel);

// # 号后面的字符串可以直观的看到调用时相对于目标对象的 path 参数
export const SYNCDATA_TYPES = {
    CHANGE_GROUP_CONDITIONREL: 'change#group.conditionsRel',
    CHANGE_CONDITION_REL: 'change#condition.rel',
    CHANGE_CONDITION_FIELD: 'change#condition.field',
    CHANGE_CONDITION_VALUE: 'change#condition.value',
    CHANGE_CONDITION_ITEM_VALUE: 'change#condition.value[?]',
    ADD_CONDITION_ITEM_VALUE: 'add#condition.value[?]',
    DELETE_CONDITION_ITEM_VALUE: 'delete#condition.value[?]',

    APPEND_CONDITION_GROUP_CONDITIONS: 'append-condition#group.conditions',
    APPEND_GROUP_GROUP_CONDITIONS: 'append-group#group.conditions',
    DELETE_GROUP: 'delete-group#',
    CLEAR_GROUP: 'clear-group#',

    ADD_GROUP: 'add-group#',
    DELETE_CONDITION: 'delete-condition#',
};

export const matchConditionValueFormatterTriggeredBy = function (currentValue, matchValue) {
  // 如果 matchValue 不是字符串，直接返回 false
  if (typeof matchValue !== 'string') {
    return false;
  }

  // 默认值
  let values = ['init', 'field', 'rel', 'value'];

  // 判断 currentValue 类型
  if (Array.isArray(currentValue)) {
    // 如果是数组，使用数组中的值
    values = currentValue;
  } else if (typeof currentValue === 'string') {
    // 如果是字符串，包装成单元素数组
    values = [currentValue];
  }
  // 其它非法情况使用默认值

  // 检查 matchValue 是否在 values 中
  return values.includes(matchValue);
};
