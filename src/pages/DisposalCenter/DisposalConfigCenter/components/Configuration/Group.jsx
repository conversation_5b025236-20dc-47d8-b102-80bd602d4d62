import React, { Component } from 'react';
import { Form, Select } from 'antd';
import classnames from 'classnames';
import styles from './style.less';
import Condition from './Condition';
import ToolBar from './ToolBar';
import {
  getNodeType,
  isGroup,
  isCondition,
  isConditionWithGroup,
  isValidGroupConditionsRel,
  SYNCDATA_TYPES,
  VALID_GROUP_CONDITIONS_RELS,
  CONDITIONS_RELMap,
} from './utils';
const { Option } = Select;

// 根节点一定是 Group
export default class Group extends Component {
  state = {
    isExpandedStatus: true,
  };

  groupConditionsToggleStatus = () => {
    this.setState({
      isExpandedStatus: !this.state.isExpandedStatus,
    });
  };

  getSelfPath = () => {
    return `${this.props.path || ''}${this.props.path ? '.' : ''}group`;
  };
  getSelfFormItemPath = attrName => {
    return `${this.getSelfPath()}.${attrName}`;
  };
  getSelfSyncPath = () => {
    return `${this.props.syncPath || ''}${this.props.syncPath ? '.' : ''}group`;
  };
  getSelfItemSyncPath = attrName => {
    return `${this.getSelfSyncPath()}.${attrName}`;
  };
  getSelfGroupConditionsRelSyncPath = () => {
    return `${this.getSelfSyncPath()}.conditionsRel`;
  };
  getSelfToolbarSyncPath = () => {
    return `${this.props.syncPath || ''}`;
  };

  onChangeGroupConditionsRel = value => {
    this.props.syncData(
      SYNCDATA_TYPES.CHANGE_GROUP_CONDITIONREL,
      this.getSelfGroupConditionsRelSyncPath(),
      value,
    );
  };

  renderGroup = () => {
    return <div className={styles.groupContainer}>{this.renderBody()}</div>;
  };
  renderBody = () => {
    return (
      <>
        {this.renderBodyConditionsRef()}
        {this.renderBodyConditions()}
      </>
    );
  };

  renderBodyConditionsRef = () => {
    const { form } = this.props;
    return (
      <div className={styles.groupConditionsRefContainer}>
        <Form.Item>
          {form.getFieldDecorator(this.getSelfFormItemPath('conditionsRel'), {
            initialValue: isValidGroupConditionsRel(this.props.data?.group?.conditionsRel)
              ? this.props.data?.group?.conditionsRel
              : undefined,
            rules: [{ required: true, message: '请填写必填信息!' }],
            normalize: (value, prevValue, allValues) => {
              if (!isValidGroupConditionsRel(value)) {
                return undefined;
              }
              return value;
            },
            validateTrigger: false,
          })(
            <Select
              placeholder="请选择"
              className={styles.groupConditionsRel}
              onChange={this.onChangeGroupConditionsRel}
              disabled={!!this.props.disabled}
            >
              {VALID_GROUP_CONDITIONS_RELS.map((item, index) => {
                return (
                  <Option key={index} value={item}>
                    {CONDITIONS_RELMap[item]}
                  </Option>
                );
              })}
            </Select>,
          )}
        </Form.Item>
        {this.renderLineToToolBar()}
        {this.renderToolBar()}
      </div>
    );
  };
  renderLineToToolBar = () => {
    return <div className={styles.lineToToolBar} />;
  };
  renderToolBar = () => {
    return (
      <ToolBar
        type="group"
        syncPath={this.getSelfToolbarSyncPath()}
        syncData={this.props.syncData}
        data={this.props.data}
        groupCoditionsIsExpandedStatus={this.state.isExpandedStatus}
        groupConditionsToggleStatus={this.groupConditionsToggleStatus}
        state={this.props.data}
        disabled={!!this.props.disabled}
      />
    );
  };
  renderBodyConditions = () => {
    const conditions = this.props.data?.group?.conditions;
    // console.log(conditions, 'conditions');
    return (
      Array.isArray(conditions) &&
      conditions.length > 0 && (
        <div
          className={classnames(
            styles.groupConditionsContainer,
            !this.state.isExpandedStatus ? styles.groupConditionsContainerHidden : null,
          )}
        >
          <div className={styles.groupConditionsScroller}>
            {conditions.map((conditionData, conditionIndex) =>
              this.renderBodyConditionItem(conditionData, conditionIndex),
            )}
          </div>
        </div>
      )
    );
  };
  renderBodyConditionItem = (conditionData, conditionIndex) => {
    const nodeType = getNodeType(conditionData);
    let node = null;
    if (isGroup(nodeType)) {
      node = (
        <Group
          key={conditionIndex}
          path={this.getSelfFormItemPath(`conditions[${conditionIndex}]`)}
          syncPath={this.getSelfItemSyncPath(`conditions.${conditionIndex}`)}
          data={conditionData}
          form={this.props.form}
          syncData={this.props.syncData}
          optionList={this.props.optionList}
          colonRelMode={this.props.colonRelMode}
          conditionValueFormatter={this.props.conditionValueFormatter}
          conditionValueFormatterTriggeredBy={this.props.conditionValueFormatterTriggeredBy}
          disabled={!!this.props.disabled}
          state={this.props.data}
        />
      );
    } else if (isCondition(nodeType)) {
      node = (
        <Condition
          key={conditionIndex}
          path={this.getSelfFormItemPath(`conditions[${conditionIndex}]`)}
          syncPath={this.getSelfItemSyncPath(`conditions.${conditionIndex}`)}
          data={conditionData}
          form={this.props.form}
          syncData={this.props.syncData}
          optionList={this.props.optionList}
          colonRelMode={this.props.colonRelMode}
          conditionValueFormatter={this.props.conditionValueFormatter}
          conditionValueFormatterTriggeredBy={this.props.conditionValueFormatterTriggeredBy}
          disabled={!!this.props.disabled}
          state={this.props.data}
        />
      );
    } else if (isConditionWithGroup(nodeType)) {
      node = (
        <Condition
          key={conditionIndex}
          path={this.getSelfFormItemPath(`conditions[${conditionIndex}]`)}
          syncPath={this.getSelfItemSyncPath(`conditions.${conditionIndex}`)}
          data={conditionData}
          form={this.props.form}
          syncData={this.props.syncData}
          optionList={this.props.optionList}
          colonRelMode={this.props.colonRelMode}
          conditionValueFormatter={this.props.conditionValueFormatter}
          conditionValueFormatterTriggeredBy={this.props.conditionValueFormatterTriggeredBy}
          disabled={!!this.props.disabled}
          state={this.props.data}
        />
      );
    }
    return node;
  };

  render() {
    return this.renderGroup();
  }
}
