import React, { Component } from 'react';
import { Form, Modal, message, Icon } from 'antd';
import setValue from 'set-value';
import getValue from 'get-value';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash.isequal';
import Group from './Group';
import styles from './style.less';
import {
  VALID_CONDITION_RELS,
  COLON_CONDITION_REL,
  VALID_GROUP_CONDITIONS_RELS,
  SYNCDATA_TYPES,
  isConditionRelOfMultiValue,
  formatConditionValue,
  formatConditionItemValue,
  formatConditionArrayValue,
  isValidConditionItemValue,
  matchConditionValueFormatterTriggeredBy,
} from './utils';

const defaultConditionRel = VALID_CONDITION_RELS[0];
const defaultGroupConditionsRel = VALID_GROUP_CONDITIONS_RELS[0];
const generateDefaultCondition = (colonRelMode) => ({
  condition: {
    field: undefined,
    rel: colonRelMode ? COLON_CONDITION_REL : defaultConditionRel,
    value: [''],
  },
});
const generateDefaultGroup = () => ({
  group: {
    conditionsRel: defaultGroupConditionsRel,
    conditions: [],
  },
});

const formateDataForRender = data => {
  if (!data) {
    return {
      group: {
        conditionsRel: defaultGroupConditionsRel,
        conditions: [],
      },
    };
  }
  return { ...data };
};

const Cache = {};

export function createConfiguration(formCreateOptions) {
  const { name } = formCreateOptions || {};
  const needBeCache = !!(typeof name === 'string' && name);
  const isCached = !!(needBeCache && Cache[name]);
  if (isCached) {
    return Cache[name];
  }
  const NewConfiguration = Form.create(formCreateOptions)(
    class Configuration extends Component {
      constructor(props) {
        super(props);
        this.props?.onRef?.(this);
        this.state = {
          data: this.props.data ? this.props.data : generateDefaultGroup(),
          valifyStatus: null,
        };
      }
      componentWillUnmount() {
        if (needBeCache) {
          delete Cache[name];
        }
      }
      componentDidMount() {
        this.props.form.setFieldsValue(this.state.data);
      }
      componentWillReceiveProps(nextProps) {
        if (!!this.props.colonRelMode === !!nextProps.colonRelMode && !isEqual(this.props.data, nextProps.data)) {
          this.setState(formateDataForRender(nextProps.data), () => {
            this.props.form.setFieldsValue(this.state.data);
          });
        }
        if (!!this.props.colonRelMode !== !!nextProps.colonRelMode) {
          this.handleReset();
        }
      }
      setStateAndFormFieldsValue = () => {
        this.setState({ data: { ...this.state.data } }, () => {
          this.props.form.setFieldsValue(this.state.data);
          this.setState({
            valifyStatus: Date.now(),
          });
          if (typeof this.props.syncData === 'function') {
            this.props.syncData(cloneDeep(this.state.data), this.props.syncIndex);
          }
        });
      };
      handleChangeCommon = (type, path, value) => {
        if (
          [
            SYNCDATA_TYPES.CHANGE_CONDITION_ITEM_VALUE,
            SYNCDATA_TYPES.CHANGE_CONDITION_REL,
            SYNCDATA_TYPES.CHANGE_CONDITION_VALUE,
          ].includes(type)
        ) {
          let conditionRel = null;
          let conditionValue = null;
          let conditionValuePath = null;
          if (SYNCDATA_TYPES.CHANGE_CONDITION_VALUE === type) {
            conditionValuePath = path;
            const conditionRelPath = conditionValuePath.replace(/\.value$/, '.rel');
            conditionRel = getValue(this.state.data, conditionRelPath);
            conditionValue = value;
          } else if (SYNCDATA_TYPES.CHANGE_CONDITION_ITEM_VALUE === type) {
            conditionValuePath = path.replace(/\.\d+$/, '');
            const conditionRelPath = conditionValuePath.replace(/\.value$/, '.rel');
            conditionRel = getValue(this.state.data, conditionRelPath);
          } else {
            conditionRel = value;
            conditionValuePath = path.replace(/\.rel$/, '.value');
          }
          if (SYNCDATA_TYPES.CHANGE_CONDITION_VALUE === type) {
            if (isConditionRelOfMultiValue(conditionRel)) {
              if (!Array.isArray(conditionValue)) {
                conditionValue = formatConditionValue(conditionRel, conditionValue, true, '')
              }
            } else {
              // 此处转为数组的原因详见 Condition 中的 renderValueOfOther 方法
              conditionValue = [
                formatConditionValue(conditionRel, conditionValue, true, ''),
              ];
            }
            // eslint-disable-next-line no-param-reassign
            value = conditionValue;
          } else {
            const oldConditionValue = getValue(this.state.data, conditionValuePath);
            if (isConditionRelOfMultiValue(conditionRel)) {
              if (!Array.isArray(oldConditionValue)) {
                setValue(
                  this.state.data,
                  conditionValuePath,
                  formatConditionValue(conditionRel, oldConditionValue, true, ''),
                );
              }
            } else {
              // 此处转为数组的原因详见 Condition 中的 renderValueOfOther 方法
              setValue(this.state.data, conditionValuePath, [
                formatConditionValue(conditionRel, oldConditionValue, true, ''),
              ]);
            }
          }
        }
        setValue(this.state.data, path, value);
        this.setStateAndFormFieldsValue();
      };
      handleAddConditionItemValue = (path, value) => {
        const conditionValuePath = path.replace(/\.\d+$/, '');
        const conditionItemValueIndex = parseInt(path.replace(`${conditionValuePath}.`, ''), 10);
        const oldConditionValue = getValue(this.state.data, conditionValuePath);
        const newConditionValue = formatConditionArrayValue(
          Array.isArray(oldConditionValue) ? oldConditionValue : [],
          false,
          '',
        );
        const validValue = formatConditionItemValue(value, false, '');
        if (!isValidConditionItemValue(newConditionValue[conditionItemValueIndex])) {
          newConditionValue[conditionItemValueIndex] = validValue;
        } else {
          newConditionValue.splice(conditionItemValueIndex, 0, validValue);
        }
        setValue(this.state.data, conditionValuePath, newConditionValue);
        this.setStateAndFormFieldsValue();
      };
      handleDeleteConditionItemValue = path => {
        const conditionValuePath = path.replace(/\.\d+$/, '');
        setValue(this.state.data, path, undefined);
        const newConditionValue = formatConditionArrayValue(
          getValue(this.state.data, conditionValuePath),
          false,
          '',
        );
        setValue(this.state.data, conditionValuePath, newConditionValue);
        this.setStateAndFormFieldsValue();
      };
      handleAppendConditionGroupConditions = path => {
        const oldGroupConditions = getValue(this.state.data, path);
        const newGroupConditions = Array.isArray(oldGroupConditions) ? oldGroupConditions : [];
        let tData= generateDefaultCondition(this.props.colonRelMode);
        if (typeof this.props.conditionValueFormatter === 'function' && matchConditionValueFormatterTriggeredBy(this.props.conditionValueFormatterTriggeredBy, 'init')) {
          let tValue = this.props.conditionValueFormatter(tData.condition.field, tData.condition.rel, cloneDeep(tData.condition.value));
          if (typeof tValue !== 'undefined') {
            tValue = formatConditionValue(tData.condition.rel, tValue, false, '');
            // 此处转为数组的原因详见 Condition 中的 renderValueOfOther 方法
            if (!Array.isArray(tValue)) {
              tValue = [tValue];
            }
            tData.condition.value = cloneDeep(tValue);
          }
        }
        newGroupConditions.push(tData);
        setValue(this.state.data, path, newGroupConditions);
        this.setStateAndFormFieldsValue();
      };
      handleAppendGroupGroupConditions = path => {
        const oldGroupConditions = getValue(this.state.data, path);
        const newGroupConditions = Array.isArray(oldGroupConditions) ? oldGroupConditions : [];
        newGroupConditions.push(generateDefaultGroup());
        setValue(this.state.data, path, newGroupConditions);
        this.setStateAndFormFieldsValue();
      };
      handleDeleteGroup = path => {
        const conditionsItem = getValue(this.state.data, path);
        Modal.confirm({
          title: '删除',
          content:
            conditionsItem.group.conditions.length > 0
              ? '该层关系下存在条件/条件关系组，是否确认删除'
              : '是否确认删除',
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',
          icon: <Icon type="delete" />,
          onOk: () => {
            if (!conditionsItem.condition) {
              const conditionsPath = path.replace(/\.\d+$/, '');
              const oldConditions = getValue(this.state.data, conditionsPath);
              setValue(this.state.data, path, undefined);
              const newConditions = oldConditions.filter(Boolean);
              setValue(this.state.data, conditionsPath, newConditions);
            } else {
              delete conditionsItem.group;
            }
            this.setStateAndFormFieldsValue();
            message.success('删除成功');
          },
          onCancel() {
            message.warning('取消删除');
          },
        });
        //
      };
      handleClearGroup = path => {
        if (!path) {
          // 这里就是 root
          this.state.data = generateDefaultGroup();
        } else {
          setValue(this.state.data, path, generateDefaultGroup());
        }
        this.setStateAndFormFieldsValue();
      };
      handleAddGroup = path => {
        const obj = getValue(this.state.data, path);
        obj.group = generateDefaultGroup().group;
        obj.group.conditions = Array.isArray(obj.group.conditions)
          ? obj.group.conditions.filter(Boolean)
          : [];
        setValue(this.state.data, path, obj);
        this.setStateAndFormFieldsValue();
      };
      handleDeleteCondition = path => {
        const flagConditions = getValue(this.state.data, path);
        Modal.confirm({
          title: '删除',
          content: flagConditions.group
            ? '该单个条件下存在子条件关系组，是否确认删除！'
            : '是否确认删除',
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',
          icon: <Icon type="delete" />,
          onOk: () => {
            const conditionsPath = path.replace(/\.\d+$/, '');
            const oldConditions = getValue(this.state.data, conditionsPath);
            setValue(this.state.data, path, undefined);
            const newConditions = oldConditions.filter(Boolean);
            setValue(this.state.data, conditionsPath, newConditions);
            this.setStateAndFormFieldsValue();
            message.success('删除成功');
          },
          onCancel() {
            message.warning('取消删除');
          },
        });
      };
      syncData = (type, path, value) => {
        switch (type) {
          case SYNCDATA_TYPES.CHANGE_GROUP_CONDITIONREL:
          case SYNCDATA_TYPES.CHANGE_CONDITION_REL:
          case SYNCDATA_TYPES.CHANGE_CONDITION_FIELD:
          case SYNCDATA_TYPES.CHANGE_CONDITION_VALUE:
          case SYNCDATA_TYPES.CHANGE_CONDITION_ITEM_VALUE:
            this.handleChangeCommon(type, path, value);
            break;
          case SYNCDATA_TYPES.ADD_CONDITION_ITEM_VALUE:
            this.handleAddConditionItemValue(path, value); // value 其实是 undefined，但没关系，handleAddConditionItemValue 可以处理这种情况
            break;
          case SYNCDATA_TYPES.DELETE_CONDITION_ITEM_VALUE:
            this.handleDeleteConditionItemValue(path);
            break;

          case SYNCDATA_TYPES.APPEND_CONDITION_GROUP_CONDITIONS:
            this.handleAppendConditionGroupConditions(path);
            break;
          case SYNCDATA_TYPES.APPEND_GROUP_GROUP_CONDITIONS:
            this.handleAppendGroupGroupConditions(path);
            break;
          case SYNCDATA_TYPES.DELETE_GROUP:
            this.handleDeleteGroup(path);
            break;
          case SYNCDATA_TYPES.CLEAR_GROUP:
            this.handleClearGroup(path);
            break;

          case SYNCDATA_TYPES.ADD_GROUP:
            this.handleAddGroup(path);
            break;
          case SYNCDATA_TYPES.DELETE_CONDITION:
            this.handleDeleteCondition(path);
            break;
        }
      };

      recursion = conditions => {
        let vaState = 'SUCCESS';
        if (conditions.length > 0) {
          for (let index = 0; index < conditions.length; index++) {
            const item = conditions[index];
            if (item.group && item.group.conditions) {
              vaState = this.recursion(item.group.conditions);
            }
            if (vaState === 'EMPTY') {
              return vaState;
            }
          }
        } else {
          vaState = 'EMPTY';
        }
        return vaState;
      };

      valify = () => {
        let valifyStatus = 'SUCCESS';
        const va = this.recursion(this.state.data.group.conditions);
        if (va === 'EMPTY') {
          valifyStatus = 'EMPTY';
        } else {
          this.props.form.validateFieldsAndScroll(err => {
            if (err) {
              valifyStatus = 'FAIL';
            }
          });
        }
        this.setState({
          valifyStatus,
        });
        return valifyStatus;
      };

      handleSubmit = () => {
        return {
          valifyStatus: this.valify(),
          data: cloneDeep(this.state.data),
        };
      };
      handlePrev = () => {
        return {
          data: cloneDeep(this.state.data),
        };
      };
      handleReset = () => {
        this.setState(
          {
            data: generateDefaultGroup(),
          },
          () => {
            this.props.form.setFieldsValue(this.state.data);
          },
        );
      };
      render() {
        return (
          <Form className={styles.formContainer}>
            <Group
              form={this.props.form}
              state={this.state.data}
              data={this.state.data}
              syncData={this.syncData}
              optionList={this.props.optionList}
              colonRelMode={this.props.colonRelMode}
              conditionValueFormatter={this.props.conditionValueFormatter}
              conditionValueFormatterTriggeredBy={this.props.conditionValueFormatterTriggeredBy}
              disabled={!!this.props.disabled}
            />
          </Form>
        );
      }
    },
  );
  class ConfigurationWrapper extends Component {
    constructor(props) {
      super(props);
    }
    shouldComponentUpdate(nextProps, nextState) {
      return !isEqual(nextProps, this.props) || !isEqual(nextState, this.state);
    }
    render() {
      return <NewConfiguration {...this.props} />;
    }
  }
  if (needBeCache) {
    Cache[name] = ConfigurationWrapper;
  }
  return ConfigurationWrapper;
}

export default createConfiguration();
