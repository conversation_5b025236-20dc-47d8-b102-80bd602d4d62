import React, { Fragment, useState, useEffect } from 'react';
import { Form, Select } from 'antd';
import request from 'ponshine-request';
import { getSystemConfigListByConfigType } from '@/services/common';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';

const IDNameCascader = ({
  form,
  idKey = 'idKey',
  nameKey = 'nameKey',
  nameLabel = '',
  idLabel = '',
  disabled = false,
  idInitialValue = '',
  nameInitialValue = '',
  onChange,
  // 数据接入ID和名称
  api = 'listAllDataAccessIdsAndNames',
}) => {
  const { getFieldDecorator } = form;
  const [idNameData, setIdNameData] = useState([]);

  const getIDNameData = async () => {
    const response = await request(`${apiPrefix}/${api}`, {
      method: 'GET',
    });
    if (response.status === 200) {
      setIdNameData(response?.data?.list || []);
    } else {
      message.error(response?.msg);
    }
  };

  const handleChangeId = (value) => {
    if (!value) {
      form.setFieldsValue({
        [nameKey]: undefined,
      });
      return;
    }
    const name = idNameData.find((ele) => ele.value === value)?.label;
    form.setFieldsValue({
      [nameKey]: name,
    });
    onChange?.(value);
  };

  const handleChangeName = (value) => {
    if (!value) {
      form.setFieldsValue({
        [idKey]: undefined,
      });
      return;
    }
    const id = idNameData.find((ele) => ele.label === value)?.value;
    form.setFieldsValue({
      [idKey]: id,
    });
  };

  useEffect(() => {
    getIDNameData();
  }, []);

  return (
    <Fragment>
      <Form.Item label={idLabel}>
        {getFieldDecorator(idKey, {
          rules: [{ required: true, message: '请选择' }],
          initialValue: idInitialValue,
        })(
          <Select
            placeholder="请选择"
            allowClear
            getPopupContainer={(triggerNode) => triggerNode.parentElement}
            onChange={handleChangeId}
            disabled={disabled}
          >
            {idNameData?.map((ele) => (
              <Select.Option value={ele.value} key={ele.value}>
                {ele.value}
              </Select.Option>
            ))}
          </Select>,
        )}
      </Form.Item>

      <Form.Item label={nameLabel}>
        {getFieldDecorator(nameKey, {
          rules: [{ required: true, message: '请选择' }],
          initialValue: nameInitialValue,
        })(
          <Select
            placeholder="请选择"
            allowClear
            getPopupContainer={(triggerNode) => triggerNode.parentElement}
            onChange={handleChangeName}
            disabled={disabled}
          >
            {idNameData?.map((ele) => (
              <Select.Option value={ele.label} key={ele.value}>
                {ele.label}
              </Select.Option>
            ))}
          </Select>,
        )}
      </Form.Item>
    </Fragment>
  );
};

export default IDNameCascader;
