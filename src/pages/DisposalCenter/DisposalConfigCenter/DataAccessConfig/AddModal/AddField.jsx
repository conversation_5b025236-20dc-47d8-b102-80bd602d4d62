import React, { useState, useEffect, Fragment } from 'react';
import { Form, Input, Row, Col, Icon, message } from 'antd';

const AddField = ({ form, initialFields = [], isDetail, isEdit }) => {
  const { getFieldDecorator, getFieldValue, setFieldsValue, resetFields } = form;
  const [fields, setFields] = useState([]);

  useEffect(() => {
    // 初始化表单数据
    if (initialFields && initialFields.length > 0) {
      let fieldList = initialFields;
      if (initialFields.length <= 4) {
        fieldList = fieldList.concat([{}]);
      }
      setFields(fieldList);
      // setFieldsValue({ fieldList });
    }
  }, [JSON.stringify(initialFields)]);

  const handleAdd = (index) => {
    if (fields.length >= 30) {
      message.error('最多添加30个字段');
      return;
    }

    // 获取当前所有的表单值
    const values = form.getFieldsValue()?.fieldList;
    values.splice(index + 1, 0, {});
    setFields(values);
    resetFields('fieldList');
    setTimeout(() => {
      setFieldsValue({ fieldList: values });
    }, 0);
  };

  const handleRemove = (index) => {
    // 获取当前所有的表单值
    const values = form.getFieldsValue()?.fieldList;
    values.splice(index, 1);
    setFields(values);
    resetFields('fieldList');
    setTimeout(() => {
      setFieldsValue({ fieldList: values });
    }, 0);
  };

  return (
    <div>
      <Row style={{ marginBottom: 16 }}>
        <Col span={6} offset={3} style={{ paddingLeft: 8, textAlign: 'center' }}>
          <span>字段名</span>
        </Col>
        <Col span={6} style={{ paddingLeft: 8, textAlign: 'center' }}>
          <span>含义</span>
        </Col>
        <Col span={6} style={{ paddingLeft: 8, textAlign: 'center' }}>
          <span>样例</span>
        </Col>
      </Row>
      <div style={{ maxHeight: 300, overflow: 'auto' }}>
        {fields.map((field, index) => {
          // 前四行、详情、编辑时原有的字段不可以编辑
          const isDisabled = index < 4 || isDetail || (isEdit && field?.id);
          return (
            <Row key={index} style={{ marginBottom: 16 }}>
              <Col span={3} style={{ textAlign: 'right', lineHeight: '32px' }}>
                <span>字段{index + 1} :</span>
              </Col>
              <Col span={6} style={{ paddingLeft: 8, paddingRight: 8 }}>
                {/* field?.id为真代表已入库的字段， 不可以编辑 */}
                <Form.Item
                  style={{ marginBottom: 0 }}
                  labelCol={{ span: 0 }}
                  wrapperCol={{ span: 24 }}
                >
                  {getFieldDecorator(`fieldList[${index}].fieldName`, {
                    initialValue: field.fieldName,
                    rules: [
                      { required: true, message: '请输入字段名' },
                      { pattern: /^[A-Za-z]+$/, message: '只能输入英文字母' },
                      {
                        max: 30,
                        message: '最多不能超过30个字符',
                      },
                    ],
                    getValueFromEvent: (e) => e?.target?.value?.trim(),
                  })(<Input placeholder="请输入英文字母" disabled={isDisabled} />)}
                </Form.Item>
              </Col>
              <Col span={6} style={{ paddingLeft: 8, paddingRight: 8 }}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  labelCol={{ span: 0 }}
                  wrapperCol={{ span: 24 }}
                >
                  {getFieldDecorator(`fieldList[${index}].fieldMean`, {
                    initialValue: field.fieldMean,
                    rules: [
                      { required: true, message: '请输入含义' },
                      {
                        max: 30,
                        message: '最多不能超过30个字符',
                      },
                    ],
                    getValueFromEvent: (e) => e?.target?.value?.trim(),
                  })(<Input placeholder="含义" disabled={isDisabled} />)}
                </Form.Item>
              </Col>
              <Col span={6} style={{ paddingLeft: 8, paddingRight: 8 }}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  labelCol={{ span: 0 }}
                  wrapperCol={{ span: 24 }}
                >
                  {getFieldDecorator(`fieldList[${index}].description`, {
                    initialValue: field.description,
                    rules: [
                      { required: true, message: '请输入样例' },
                      {
                        max: 2000,
                        message: '最多不能超过2000个字符',
                      },
                    ],
                    getValueFromEvent: (e) => e?.target?.value?.trim(),
                  })(<Input placeholder="样例" disabled={isDisabled} />)}
                </Form.Item>
              </Col>
              {getFieldDecorator(`fieldList[${index}].id`, {
                initialValue: field.id || '',
              })(<Input type="hidden" />)}
              <Col span={3} style={{ paddingLeft: 8 }}>
                {/* 详情不展示 */}
                {!isDetail && (
                  <Fragment>
                    {/* 编辑时不能删除原有字段(新增时，第 5 行数据才能出现删除) */}
                    {((!isEdit && index > 3) || (isEdit && !field?.id)) && (
                      <Icon
                        type="minus-square"
                        onClick={() => handleRemove(index)}
                        style={{ marginRight: 8, color: index < 2 ? '#ccc' : '#1890ff' }}
                      />
                    )}
                    {/* 新增或（编辑时， 如果当前字段是最后一个或者是新添加的字段），则可以点击添加 */}
                    {((index > 2 && !isEdit) || (isEdit && index >= initialFields?.length - 1)) && (
                      <Icon
                        type="plus-square"
                        onClick={() => handleAdd(index)}
                        style={{ marginRight: 8, color: '#1890ff' }}
                      />
                    )}
                  </Fragment>
                )}
              </Col>
            </Row>
          );
        })}
      </div>
    </div>
  );
};

export default AddField;
