import { Modal, Form, Input, message, Select, Radio, Button, Upload } from 'antd';
import React, { Fragment, useState, useEffect } from 'react';
import request from 'ponshine-request';
import AddField from './AddField';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';

import { v4 as uuidv4 } from 'uuid';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
};

// 校验字段名是否重复
const validateFieldNameDuplicate = (fieldList) => {
  const fieldNameMap = new Map();

  for (const field of fieldList || []) {
    if (field.fieldName) {
      if (fieldNameMap.has(field.fieldName)) {
        message.error(`字段名 "${field.fieldName}" 重复，请修改后重试`);
        return false;
      }
      fieldNameMap.set(field.fieldName, true);
    }
  }
  return true;
};

const initialFixedFields = [
  {
    fieldName: 'dataAccessId',
    fieldMean: '数据接入ID',
    description: 'DA_fe6ec966-273c-e8dc-3d',
  },
  {
    fieldName: 'platformName',
    fieldMean: '数据接入平台名称',
    description: '模型分析平台',
  },
  {
    fieldName: 'phoneNumber',
    fieldMean: '号码',
    description: '15100000000',
  },
  {
    fieldName: 'createTime',
    fieldMean: '数据产生时间',
    description: '1753669930',
  },
];

const AddModal = Form.create()(
  ({
    visible,
    currentRow,
    onCancel,
    form,
    form: { validateFieldsAndScroll, getFieldDecorator, resetFields, getFieldsValue },
    onReload,
    type,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [saveDraftLoading, setSaveDraftLoading] = useState(false);
    const [initialFields, setInitialFields] = useState(initialFixedFields);
    const [detailData, setDetailData] = useState({});
    const [initDataAccessId, setInitDataAccessId] = useState('');

    const isDetail = currentRow?.title === '详情';
    const isEdit = currentRow?.title === '编辑';

    const handleSaveDraft = async () => {
      // 获取表单数据并过滤 fieldList
      const formValues = getFieldsValue();
      const { fieldList, ...rest } = formValues;
      const filteredFieldList =
        fieldList?.filter((field) =>
          // 只要有一个属性有值就保留
          Object.values(field).some(
            (value) => value !== undefined && value !== null && value !== '',
          ),
        ) || [];

      if (!validateFieldNameDuplicate(fieldList)) return;
      setSaveDraftLoading(true);
      handleSubmitRequest(1, { ...rest, fieldList: filteredFieldList }, () => {
        setSaveDraftLoading(false);
      });
    };

    const handleSubmit = () => {
      validateFieldsAndScroll(async (err, fieldsValue) => {
        if (err) return;
        if (!validateFieldNameDuplicate(fieldsValue?.fieldList)) return;
        setConfirmLoading(true);
        handleSubmitRequest(0, fieldsValue, () => {
          setConfirmLoading(false);
        });
      });
    };

    // 修改 handleSubmitRequest 函数，增加数据参数
    const handleSubmitRequest = async (drafted, params, callback) => {
      const response = await request(`${apiPrefix}/dataAccessAdd`, {
        data: {
          ...params,
          drafted,
        },
        method: 'POST',
        requestType: 'json',
      });
      callback?.();
      if (response.status === 200) {
        message.success(response.msg);
        onCancel();
        onReload();
      } else {
        message.error(response.msg);
      }
    };

    // 获取配置记录的动态字段
    const getConfigRecordFieldData = async () => {
      const response = await request(`${apiPrefix}/configurationRecordDetails`, {
        method: 'POST',
        data: {
          configId: currentRow?.id,
        },
        requestType: 'json',
      });
      if (response.status === 200) {
        setDetailData(currentRow);
        setInitialFields(response?.data?.dataList);
      } else {
        message.error(response?.message);
      }
    };

    // 获取动态字段
    const getDetailFieldData = async () => {
      const response = await request(`${apiPrefix}/dataAccessDetails`, {
        method: 'POST',
        data: {
          dataAccessId: currentRow?.dataAccessId,
        },
        requestType: 'json',
      });
      if (response.status === 200) {
        setDetailData(currentRow);
        setInitialFields(response?.data?.dataList || initialFixedFields);
      } else {
        message.error(response?.msg);
      }
    };

    // 新增时，默认查询是否有草稿数据
    const getDraftData = async () => {
      const response = await request(`${apiPrefix}/searchDraftQuery`, {
        method: 'POST',
      });
      if (response.status === 200) {
        if (JSON.stringify(response?.data) === '{}') {
          setInitDataAccessId('DA_' + uuidv4());
        }
        setDetailData(response?.data?.draftData || {});
        setInitialFields(response?.data?.draftData?.fleldList || initialFixedFields);
      } else {
        message.error(response?.message);
      }
    };

    useEffect(() => {
      if (visible) {
        if (currentRow?.id) {
          // 如果是配置记录打开的详情
          if (type === 'configRecordDetail') {
            getConfigRecordFieldData();
          } else {
            getDetailFieldData();
          }
        } else {
          getDraftData();
        }
      }
    }, [visible]);

    const uploadProps = {
      beforeUpload: (file) => {
        const formData = new FormData();
        formData.append('file', file);
        request('/api/business/automation/uploadFile', {
          method: 'POST',
          data: formData,
          requestType: 'form',
        }).then((res) => {
          if (res.code == 200) {
            message.success('上传成功');
            const originData = getFieldsValue()?.fieldList?.slice(0, 4);
            const newData = res.data;
            const newFields = originData.concat(newData);
            setInitialFields(newFields);
          } else {
            message.error(res?.msg);
          }
        });
        return false;
      },
    };
    return (
      <Modal
        title={`${currentRow?.title || '新增'}`}
        visible={visible}
        onCancel={onCancel}
        afterClose={() => {
          resetFields();
          setDetailData({});
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
        width={800}
        footer={
          <div>
            {!isEdit && !isDetail && (
              <Button type="primary" onClick={handleSaveDraft} loading={saveDraftLoading}>
                保存草稿
              </Button>
            )}
            {!isDetail && (
              <Fragment>
                <Button onClick={onCancel}>取消</Button>
                <Button type="primary" onClick={handleSubmit} loading={confirmLoading}>
                  确认提交
                </Button>
              </Fragment>
            )}
          </div>
        }
      >
        <Form {...formItemLayout}>
          <Form.Item label="数据接入ID">
            {getFieldDecorator('dataAccessId', {
              initialValue: detailData?.dataAccessId || initDataAccessId,
              rules: [
                {
                  required: true,
                  message: '请输入',
                },
              ],
            })(<Input placeholder="请输入" allowClear disabled />)}
          </Form.Item>
          <Form.Item label="数据名称">
            {getFieldDecorator('dataName', {
              initialValue: detailData?.dataName,
              rules: [
                {
                  required: true,
                  message: '请输入',
                },
                { max: 20, message: '最多输入20个字符' },
              ],
              getValueFromEvent: (e) => e?.target?.value?.trim(),
            })(<Input placeholder="请输入" allowClear disabled={isEdit || isDetail} />)}
          </Form.Item>
          {/* <Form.Item label="数据接入IP">
            {getFieldDecorator('dataAccessIp', {
              initialValue: detailData?.dataAccessIp,
              rules: [
                {
                  required: true,
                  message: '请输入',
                },
                {
                  max: 255,
                  message: '最多输入255个字符',
                },
              ],
              getValueFromEvent: (e) => e?.target?.value?.trim(),
            })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
          </Form.Item> */}
          <CommonSelect
            configType="dataAccessType"
            form={form}
            formItemKey="dataType"
            formItemLabel="数据类型"
            rules={[{ required: true, message: '请选择' }]}
            initialValue={!detailData?.dataType ? undefined : String(detailData?.dataType)}
            disabled={isDetail || isEdit}
          />

          <Form.Item label="数据说明">
            {getFieldDecorator('description', {
              initialValue: detailData?.description,
              rules: [{ max: 1000, message: '最多输入1000个字符' }],
              getValueFromEvent: (e) => e?.target?.value?.trim(),
            })(
              <Input.TextArea
                placeholder="请输入"
                allowClear
                rows={3}
                disabled={isEdit || isDetail}
              />,
            )}
          </Form.Item>
          <Form.Item label="接收状态">
            {getFieldDecorator('receiveState', {
              initialValue: detailData?.receiveState ?? 1,
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Radio.Group disabled={isDetail}>
                <Radio value={0}>关闭</Radio>
                <Radio value={1}>开启</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          {/* <Form.Item label="传输内容与格式">
            {getFieldDecorator('transferContent', {
              initialValue: detailData?.transferContent,
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Upload {...uploadProps}>
                <Button type="primary" disabled={isDetail}>
                  导入模板解析以下字段
                </Button>
              </Upload>,
            )}
          </Form.Item> */}
          <AddField form={form} initialFields={initialFields} isDetail={isDetail} isEdit={isEdit} />
        </Form>
      </Modal>
    );
  },
);

export default AddModal;
