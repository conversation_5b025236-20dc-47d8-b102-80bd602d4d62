import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import AddModal from './AddModal';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';
import { getSystemConfigListByConfigType } from '@/services/common';

import { Licensee, router } from 'ponshine';
import request from 'ponshine-request';

const RECEIVE_STATUS = [
  { name: '关闭', value: 0 },
  { name: '开启', value: 1 },
];

const Index = (props) => {
  const {
    form,
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [addVisible, setAddVisible] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [dataTypeData, setDataTypeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentRow, setCurrentRow] = useState({});

  const serachParams = useRef();

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`${apiPrefix}/dataAccessTablequery`, {
      data: { pageNum, pageSize, drafted: 0, ...props },
      method: 'POST',
      requestType: 'json',
    });

    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.dataList || [],
        pagination: {
          total: response?.data?.totalCount,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      setListData({
        list: [],
        pagination: false,
      });
      message.error(response.message);
    }
  };

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { updateTime } = formValue;
    findTableDataPager({
      ...formValue,
      updateDateStart: updateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      updateDateEnd: updateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      updateTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager({
      receiveState: 1,
    });
  };

  const getDataTypeData = async () => {
    const response = await getSystemConfigListByConfigType({ configType: 'dataAccessType' });
    if (response.code === 200) {
      setDataTypeData(response.data);
    } else {
      message.error(response.message);
    }
  };

  const columns = [
    {
      title: '数据接入ID',
      dataIndex: 'dataAccessId',
      align: 'center',
      width: 220,
      ellipsis: true,
    },
    {
      title: '数据名称',
      dataIndex: 'dataName',
      align: 'center',
      width: 140,
      ellipsis: true,
    },
    // {
    //   title: '数据接入IP',
    //   dataIndex: 'dataAccessIp',
    //   align: 'center',
    //   width: 140,
    //   ellipsis: true,
    // },
    {
      title: '数据类型',
      dataIndex: 'dataType',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text) => {
        const option = dataTypeData.find((item) => item.value === text);
        return option?.name || '';
      },
    },
    {
      title: '数据说明',
      dataIndex: 'description',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '接收状态',
      dataIndex: 'receiveState',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text) => (text === 1 ? '开启' : '关闭'),
    },
    {
      title: '操作',
      dataIndex: 'opt',
      align: 'center',
      fixed: 'right',
      width: 100,
      ellipsis: true,
      render: (v, r) => (
        <Fragment>
          <a
            style={{ marginRight: 8 }}
            onClick={() => {
              setCurrentRow({ ...r, title: '详情' });
              setAddVisible(true);
            }}
          >
            详情
          </a>
          <a
            onClick={() => {
              setCurrentRow({ ...r, title: '编辑' });
              setAddVisible(true);
            }}
          >
            编辑
          </a>
        </Fragment>
      ),
    },
  ];

  useEffect(() => {
    findTableDataPager({
      receiveState: 1,
    });
    getDataTypeData();
  }, []);

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="数据接入ID">
              {getFieldDecorator('dataAccessId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据名称">
              {getFieldDecorator('dataName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <CommonSelect
              configType="dataAccessType"
              form={form}
              formItemKey="dataType"
              formItemLabel="数据类型"
              data={dataTypeData}
            />,

            <Form.Item label="接收状态">
              {getFieldDecorator('receiveState', {
                initialValue: 1,
              })(
                <Select placeholder="请选择" allowClear>
                  {RECEIVE_STATUS?.map((ele) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
              {/* <Licensee license="schoolCard_shutdownDetail_export"> */}
              <Button
                type="primary"
                onClick={() => {
                  setAddVisible(true);
                }}
                style={{ marginLeft: 8 }}
              >
                新增
              </Button>
              {/* </Licensee> */}
              {/* <Licensee license="schoolCard_shutdownDetail_export"> */}
              <Button
                onClick={() => {
                  router.push('/disposalCenter/DataAccessConfigRecord');
                }}
                style={{ marginLeft: 8 }}
              >
                配置记录
              </Button>
              {/* </Licensee> */}
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
      />
      {addVisible && (
        <AddModal
          visible={addVisible}
          onReload={handleReset}
          onCancel={() => {
            currentRow?.id && setCurrentRow({});
            setAddVisible(false);
          }}
          currentRow={currentRow}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
