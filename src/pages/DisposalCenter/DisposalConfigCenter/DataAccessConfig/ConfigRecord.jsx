import React, { Fragment, useEffect, useState, useRef } from 'react';
import { Button, Card, message, Form, Input, DatePicker, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import AddModal from './AddModal';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import { getSystemConfigListByConfigType } from '@/services/common';

import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';

import request from 'ponshine-request';

const Index = (props) => {
  const {
    form,
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const serachParams = useRef();

  const [detailVisible, setDetailVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState({});
  const [dataTypeData, setDataTypeData] = useState([]);

  const getDataTypeData = async () => {
    const response = await getSystemConfigListByConfigType({ configType: 'dataAccessType' });
    if (response.code === 200) {
      setDataTypeData(response.data);
    } else {
      message.error(response.message);
    }
  };

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`${apiPrefix}/configurationRecordquery`, {
      data: { pageNum, pageSize, drafted: 0, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.dataList || [],
        pagination: {
          total: response.data.totalCount,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.msg);
    }
  };

  useEffect(() => {
    findTableDataPager({
      receiveState: 1,
    });
    getDataTypeData();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { operateTime } = formValue;
    findTableDataPager({
      ...formValue,
      operateStartTime: operateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      operateEndTime: operateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      operateTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager({
      receiveState: 1,
    });
  };

  const handleDetail = (record) => {
    setCurrentRow({ ...record, title: '详情' });
    setDetailVisible(true);
  };

  const columns = [
    {
      title: '数据接入ID',
      dataIndex: 'dataAccessId',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '数据名称',
      dataIndex: 'dataName',
      align: 'center',
      width: 140,
      ellipsis: true,
    },
    {
      title: '数据接入IP',
      dataIndex: 'dataAccessIp',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '数据类型',
      dataIndex: 'dataType',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text) => {
        const option = dataTypeData.find((item) => item.value == text);
        return option?.name || '--';
      },
    },
    {
      title: '数据说明',
      dataIndex: 'description',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '接收状态',
      dataIndex: 'receiveState',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text) => (text === 1 ? '开启' : '关闭'),
    },
    {
      title: '操作',
      dataIndex: 'opt',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text, record) => <a onClick={() => handleDetail(record)}>详情</a>,
    },
    {
      title: '操作时间',
      dataIndex: 'operateTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '操作用户',
      dataIndex: 'operator',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作内容',
      dataIndex: 'action',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text) => {
        const obj = {
          0: '新增',
          1: '删除',
          2: '修改',
        };
        return obj?.[text] || '';
      },
    },
  ];

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="数据接入ID">
              {getFieldDecorator('dataAccessId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据名称">
              {getFieldDecorator('dataName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,

            <CommonSelect
              configType="dataAccessType"
              form={form}
              formItemKey="dataType"
              formItemLabel="数据类型"
              data={dataTypeData}
            />,
            <Form.Item label="接收状态">
              {getFieldDecorator('receiveState', {
                initialValue: 1,
              })(
                <Select placeholder="请选择" allowClear>
                  <Select.Option value={1}>开启</Select.Option>
                  <Select.Option value={0}>关闭</Select.Option>
                </Select>,
              )}
            </Form.Item>,
            <Form.Item label="操作时间">
              {getFieldDecorator('operateTime')(
                <DatePicker.RangePicker placeholder="请选择" allowClear format="YYYY-MM-DD" />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
      />
      {detailVisible && (
        <AddModal
          visible={detailVisible}
          onReload={handleReset}
          onCancel={() => {
            currentRow?.id && setCurrentRow({});
            setDetailVisible(false);
          }}
          currentRow={currentRow}
          type="configRecordDetail"
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
