import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import AddModal from './AddModal';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';

import { getIndexNumber } from '@/utils/utils';

const { RangePicker } = DatePicker;

import request from 'ponshine-request';

const Index = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const serachParams = useRef();
  const [configColumns, setConfigColumns] = useState([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState({});

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`${apiPrefix}/getConfigRecordsByPage`, {
      params: { current: pageNum, pageSize, ...props },
      method: 'GET',
    });
    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.list || [],
        pagination: {
          total: response.data.total,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.msg);
    }
  };

  useEffect(() => {
    findTableDataPager();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { operateTime } = formValue;
    findTableDataPager({
      ...formValue,
      operateStartTime: operateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      operateEndTime: operateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      operateTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
  };

  const handleDetail = (record) => {
    setCurrentRow({ ...record, title: '详情' });
    setDetailVisible(true);
  };

  const columns = useMemo(() => {
    return [
      {
        title: '数据分类ID',
        dataIndex: 'dataClassifyId',
        align: 'center',
        width: 200,
        ellipsis: true,
      },
      {
        title: '数据分类名称',
        dataIndex: 'dataClassifyName',
        align: 'center',
        width: 180,
        ellipsis: true,
      },
      {
        title: '数据接入ID',
        dataIndex: 'dataAccessId',
        align: 'center',
        width: 200,
        ellipsis: true,
      },
      {
        title: '数据名称',
        dataIndex: 'dataAccessName',
        align: 'center',
        width: 180,
        ellipsis: true,
      },
      {
        title: '数据分类说明',
        dataIndex: 'description',
        align: 'center',
        width: 180,
        ellipsis: true,
      },
      {
        title: '优先级',
        dataIndex: 'priority',
        align: 'center',
        width: 100,
        ellipsis: true,
      },
      {
        title: '分类规则',
        dataIndex: 'ruleContext',
        align: 'center',
        width: 120,
        ellipsis: true,
        render: (text, record) => <a onClick={() => handleDetail(record)}>查看</a>,
      },
      {
        title: '处理状态',
        dataIndex: 'handleState',
        align: 'center',
        width: 120,
        ellipsis: true,
        render: (text) => (text === 1 ? '开启' : '关闭'),
      },
      {
        title: '操作时间',
        dataIndex: 'operateTime',
        align: 'center',
        width: 180,
        ellipsis: true,
      },
      {
        title: '操作用户',
        dataIndex: 'operator',
        align: 'center',
        width: 120,
        ellipsis: true,
      },
      {
        title: '操作内容',
        dataIndex: 'action',
        align: 'center',
        width: 120,
        ellipsis: true,
        render: (text) => {
          const obj = {
            0: '新增',
            1: '删除',
            2: '修改',
          };
          return obj?.[text] || '';
        },
      },
    ];
  }, [configColumns]);

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="数据接入ID">
              {getFieldDecorator('dataAccessId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据名称">
              {getFieldDecorator('dataAccessName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据分类ID">
              {getFieldDecorator('dataClassifyId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据分类名称">
              {getFieldDecorator('dataClassifyName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="处理状态">
              {getFieldDecorator('handleState')(
                <Select placeholder="请选择" allowClear>
                  <Select.Option value={1}>开启</Select.Option>
                  <Select.Option value={0}>关闭</Select.Option>
                </Select>,
              )}
            </Form.Item>,
            <Form.Item label="操作时间">
              {getFieldDecorator('operateTime')(
                <DatePicker.RangePicker placeholder="请选择" allowClear format="YYYY-MM-DD" />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
      />
      {detailVisible && (
        <AddModal
          visible={detailVisible}
          onReload={handleReset}
          onCancel={() => {
            currentRow?.id && setCurrentRow({});
            setDetailVisible(false);
          }}
          currentRow={currentRow}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
