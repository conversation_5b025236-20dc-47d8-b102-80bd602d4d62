import React from 'react';
import { DndProvider, DragSource, DropTarget } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import arrayMove from 'array-move';
import { Table } from 'antd';
import styles from './index.less';

let dragingIndex = -1;

const BodyRow = (props) => {
  const { isOver, connectDragSource, connectDropTarget, moveRow, ...restProps } = props;
  const style = { ...restProps.style, cursor: 'move' };

  let { className } = restProps;
  if (isOver) {
    if (restProps.index > dragingIndex) {
      className += ' drop-over-downward';
    }
    if (restProps.index < dragingIndex) {
      className += ' drop-over-upward';
    }
  }

  return connectDragSource(
    connectDropTarget(<tr {...restProps} className={className} style={style} />),
  );
};

const sourceSpec = {
  beginDrag(props) {
    dragingIndex = props.index;
    return {
      index: props.index,
    };
  },
};

const dragSource = DragSource('row', sourceSpec, (connect) => {
  return {
    connectDragSource: connect.dragSource(),
  };
});

const bodyRow = dragSource(BodyRow);

const targetSpec = {
  drop(props, monitor) {
    const sourceIndex = monitor.getItem().index;
    const targetIndex = props.index;
    if (sourceIndex === targetIndex) return;
    props.moveRow(sourceIndex, targetIndex);
    monitor.getItem().index = targetIndex;
  },
};

const dropTarget = DropTarget('row', targetSpec, (connect, monitor) => {
  return { connectDropTarget: connect.dropTarget(), isOver: monitor.isOver() };
});

const DragableBodyRow = dropTarget(bodyRow);

const SortableTable = ({ columns, data, rowKey, loading, setListData }) => {
  const moveRow = (dragIndex, hoverIndex) => {
    if (dragIndex !== hoverIndex) {
      const newData = arrayMove([].concat(data), dragIndex, hoverIndex).filter((el) => !!el);
      setListData({
        list: newData,
        pagination: false,
      });
    }
  };
  return (
    <div className={styles.sortableTable}>
      <DndProvider backend={HTML5Backend}>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          rowKey={rowKey}
          loading={loading}
          size="small"
          components={{
            body: {
              row: DragableBodyRow,
            },
          }}
          onRow={(record, index) => ({
            index,
            moveRow: moveRow,
          })}
        />
      </DndProvider>
    </div>
  );
};

export default SortableTable;
