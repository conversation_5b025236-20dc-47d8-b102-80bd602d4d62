import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { Button, Card, message, Form, Input, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import AddModal from './AddModal';
import SortableTable from './SortableTable';

import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';
import { v4 as uuidv4 } from 'uuid';

import { Licensee, router } from 'ponshine';
import request from 'ponshine-request';

import { HANDLE_STATUS } from './constants';

const Index = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue, setFieldsValue },
  } = props;

  const [addVisible, setAddVisible] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [currentRow, setCurrentRow] = useState({});
  const [editPriorityVisible, setEditPriorityVisible] = useState(false);

  const serachParams = useRef();

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);

    const response = await request(`${apiPrefix}/getDataCategoryConfigurationsByPage`, {
      params: { current: pageNum, pageSize, ...props },
      method: 'GET',
    });

    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list:
          response?.data?.list?.map((ele) => {
            return {
              ...ele,
              uuid: uuidv4(),
            };
          }) || [],
        pagination: {
          total: response.data.total,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      setListData({
        list: [],
        pagination: false,
      });
      message.error(response.msg);
    }
  };

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { updateTime } = formValue;
    findTableDataPager({
      ...formValue,
      updateDateStart: updateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      updateDateEnd: updateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      updateTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
  };

  const handleDetail = (record, title) => {
    setCurrentRow({ ...record, title });
    setAddVisible(true);
  };

  const columns = useMemo(
    () => [
      {
        title: '数据分类ID',
        dataIndex: 'dataClassifyId',
        align: 'center',
        width: 200,
        ellipsis: true,
      },
      {
        title: '数据分类名称',
        dataIndex: 'dataClassifyName',
        align: 'center',
        width: 140,
        ellipsis: true,
      },
      {
        title: '数据接入ID',
        dataIndex: 'dataAccessId',
        align: 'center',
        width: 200,
        ellipsis: true,
      },
      {
        title: '数据名称',
        dataIndex: 'dataAccessName',
        align: 'center',
        width: 120,
        ellipsis: true,
      },
      {
        title: '数据分类说明',
        dataIndex: 'description',
        align: 'center',
        width: 150,
        ellipsis: true,
      },
      {
        title: '优先级',
        dataIndex: 'priority',
        align: 'center',
        width: 100,
        ellipsis: true,
        render: (v, r) => {
          return r.handleState === 0 ? '--' : v;
        },
      },
      {
        title: '分类规则',
        dataIndex: 'categoryRule',
        align: 'center',
        width: 100,
        ellipsis: true,
        render: (v, r) => (
          <a disabled={editPriorityVisible} onClick={() => handleDetail(r, '详情')}>
            查看
          </a>
        ),
      },
      {
        title: '处理状态',
        dataIndex: 'handleState',
        align: 'center',
        width: 100,
        ellipsis: true,
        render: (v, r) => {
          return HANDLE_STATUS.find((ele) => ele.value === v)?.name;
        },
      },
      {
        title: '操作',
        dataIndex: 'opt',
        align: 'center',
        // fixed: 'right',
        width: 100,
        ellipsis: true,
        render: (v, r) => (
          <a disabled={editPriorityVisible} onClick={() => handleDetail(r, '编辑')}>
            编辑
          </a>
        ),
      },
    ],
    [editPriorityVisible],
  );

  const handleEditPriority = () => {
    const { dataAccessId, handleState, ...rest } = getFieldsValue();

    if (!dataAccessId) {
      message.warning('请输入数据接入ID');
      return;
    }

    resetFields([...Object.keys(rest)]);
    setFieldsValue({
      handleState: 1,
    });
    message.info('编辑优先级只能操作处理状态为开启的数据，其他多余查询条件已为您重置');

    findTableDataPager({
      dataAccessId,
      handleState: 1,
    });
    setEditPriorityVisible(true);
  };
  const handleConfirmEditPriority = async () => {
    const response = await request(`${apiPrefix}/updatePriority`, {
      method: 'POST',
      data: { sortFields: listData?.list?.map((ele) => ele.id) },
      requestType: 'json',
    });
    if (response?.status === 200) {
      message.success(response?.msg);
      setEditPriorityVisible(false);
      findTableDataPager(serachParams?.current);
    } else {
      message.error(response?.msg);
    }
  };

  const handleCancelEditPriority = () => {
    findTableDataPager(serachParams.current);
    setEditPriorityVisible(false);
  };

  useEffect(() => {
    findTableDataPager();
  }, []);

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="数据接入ID">
              {getFieldDecorator('dataAccessId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据名称">
              {getFieldDecorator('dataAccessName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据分类ID">
              {getFieldDecorator('dataClassifyId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据分类名称">
              {getFieldDecorator('dataClassifyName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="处理状态">
              {getFieldDecorator('handleState')(
                <Select placeholder="请选择" allowClear>
                  {HANDLE_STATUS?.map((ele) => (
                    <Select.Option value={ele.value}>{ele.name}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch} disabled={editPriorityVisible}>
                查询
              </Button>
              <Button
                onClick={handleReset}
                style={{ marginLeft: 8 }}
                disabled={editPriorityVisible}
              >
                重置
              </Button>
              {/* <Licensee license="schoolCard_shutdownDetail_export"> */}
              <Button
                type="primary"
                onClick={() => {
                  setAddVisible(true);
                }}
                style={{ marginLeft: 8 }}
                disabled={editPriorityVisible}
              >
                新增
              </Button>
              {/* </Licensee> */}

              {/* <Licensee license="schoolCard_shutdownDetail_export"> */}
              <Button
                onClick={() => {
                  router.push('/disposalCenter/DataCategoryConfigRecord');
                }}
                style={{ marginLeft: 8 }}
                disabled={editPriorityVisible}
              >
                配置记录
              </Button>
              {/* </Licensee> */}
            </Fragment>
          }
        />
      </Form>
      <div style={{ textAlign: 'right', marginBottom: 16 }}>
        {editPriorityVisible ? (
          <Fragment>
            操作提示：选中要调整的数据并拖动，调整上下顺序
            <Button style={{ margin: '0 8px' }} onClick={handleCancelEditPriority}>
              取消
            </Button>
            <Button type="primary" onClick={handleConfirmEditPriority}>
              确认
            </Button>
          </Fragment>
        ) : (
          <Button
            type="primary"
            style={{ marginLeft: 8 }}
            onClick={handleEditPriority}
            disabled={!listData.list.length}
          >
            编辑优先级
          </Button>
        )}
      </div>
      {editPriorityVisible ? (
        <SortableTable
          columns={columns}
          data={listData.list}
          rowKey="uuid"
          loading={loading}
          setListData={setListData}
        />
      ) : (
        <StandardTable
          columns={columns}
          data={listData}
          onChange={handleTableChange}
          rowKey="uuid"
          loading={loading}
          showSelectCount={false}
          rowSelectionProps={false}
        />
      )}

      {addVisible && (
        <AddModal
          visible={addVisible}
          onReload={handleReset}
          onCancel={() => {
            currentRow?.id && setCurrentRow({});
            setAddVisible(false);
          }}
          currentRow={currentRow}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
