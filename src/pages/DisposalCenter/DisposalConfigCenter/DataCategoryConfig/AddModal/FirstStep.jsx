import React, {
  Fragment,
  useEffect,
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { Form, Input, Select, message } from 'antd';
import request from 'ponshine-request';
import IDNameCascader from '@/pages/DisposalCenter/DisposalConfigCenter/components/IDNameCascader';
import { HANDLE_STATUS } from '../constants';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
};

const FirstStep = forwardRef(
  (
    {
      form,
      detailValues,
      isEdit,
      isDetail,
      initDataAccessId,
      setFlowQuery,
      handleReasetConfiguration,
    },
    ref,
  ) => {
    useImperativeHandle(ref, () => form);
    const { getFieldDecorator, getFieldValue } = form;

    useEffect(() => {
      if (!isDetail && !isEdit) {
        if (!getFieldValue('dataAccessId')) {
          setFlowQuery({ group: null });
        } else {
          // 草稿状态下，第一次进来不需要清空，当改变后，再清空；新增的时候，只要改变就清空
          if (detailValues?.dataAccessId) {
            if (getFieldValue('dataAccessId') !== detailValues?.dataAccessId) {
              setFlowQuery({ group: null });
            }
          } else {
            setFlowQuery({});
            handleReasetConfiguration();
          }
        }
      }
    }, [getFieldValue('dataAccessId')]);

    return (
      <Form {...formItemLayout}>
        <Form.Item label="数据分类ID">
          {getFieldDecorator('dataClassifyId', {
            initialValue: detailValues?.dataClassifyId || initDataAccessId,
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(<Input placeholder="请输入" allowClear disabled />)}
        </Form.Item>
        <Form.Item label="数据分类名称">
          {getFieldDecorator('dataClassifyName', {
            initialValue: detailValues?.dataClassifyName || '',
            rules: [
              {
                required: true,
                message: '请输入',
              },
              {
                max: 20,
                message: '最多输入20个字符',
              },
            ],
            getValueFromEvent: (e) => e?.target?.value?.trim(),
          })(<Input placeholder="请输入" allowClear disabled={isDetail || isEdit} />)}
        </Form.Item>

        <IDNameCascader
          form={form}
          idKey="dataAccessId"
          nameKey="dataAccessName"
          nameLabel="数据名称"
          idLabel="数据接入ID"
          disabled={isDetail || isEdit}
          idInitialValue={detailValues?.dataAccessId}
          nameInitialValue={detailValues?.dataAccessName}
        />

        <Form.Item label="数据分类说明">
          {getFieldDecorator('description', {
            initialValue: detailValues?.description || '',
            getValueFromEvent: (e) => e?.target?.value?.trim(),
            rules: [{ max: 1000, message: '最多输入1000个字符' }],
          })(
            <Input.TextArea
              placeholder="请输入"
              allowClear
              rows={3}
              disabled={isDetail || isEdit}
            />,
          )}
        </Form.Item>
        <Form.Item label="处理状态">
          {getFieldDecorator('handleState', {
            initialValue: detailValues?.handleState ?? 1,
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              allowClear
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              disabled={isDetail}
            >
              {HANDLE_STATUS?.map((ele) => (
                <Select.Option value={ele.value}>{ele.name}</Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </Form>
    );
  },
);

export default Form.create()(FirstStep);
