import { Modal, Form, Input, message, Select, Radio, Button, Upload, Steps } from 'antd';
import React, { Fragment, useState, useEffect, useRef } from 'react';
import request from 'ponshine-request';
const { Step } = Steps;
import FirstStep from './FirstStep';
import Configuration from '@/pages/DisposalCenter/DisposalConfigCenter/components/Configuration';
import SecondStep from './SecondStep';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';
import { v4 as uuidv4 } from 'uuid';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
};

const AddModal = Form.create()(
  ({
    visible,
    currentRow,
    onCancel,
    form,
    form: {
      validateFieldsAndScroll,
      getFieldDecorator,
      resetFields,
      getFieldsValue,
      getFieldValue,
      setFieldsValue,
    },
    onReload,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [draftLoading, setDraftLoading] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);
    const [detailValues, setDetailValues] = useState({});
    const [flowQuery, setFlowQuery] = useState(null);
    const [initDataAccessId, setInitDataAccessId] = useState('');

    const configurationRef = useRef(null);

    const [categoryFieldData, setCategoryFieldData] = useState([
      {
        name: '字段1',
        key: 'field1',
      },
      {
        name: '字段2',
        key: 'field2',
      },
      {
        name: '字段3',
        key: 'field3',
      },
    ]);
    const firstStepForm = useRef(null);
    const secondStepForm = useRef(null);

    const stepValuesRef = useRef({});

    const isEdit = currentRow?.title === '编辑';
    const isDetail = currentRow?.title === '详情';

    // 保存和提交的公共方法
    const saveOrSubmitRequest = async (configurationData, drafted, type, callback) => {
      const response = await request(`${apiPrefix}/saveDataCategoryConfig`, {
        data: {
          ruleContext: JSON.stringify(configurationData || {}),
          ...firstStepForm?.current?.getFieldsValue(),
          drafted,
          id: currentRow?.id,
        },
        method: 'POST',
        requestType: 'json',
      });
      callback?.();
      if (response.status === 200) {
        message.success(response.msg);
        onCancel();
        onReload();
        if (type === 'submit') {
          // handleCache(response.data);
        }
      } else {
        message.error(response.msg);
      }
    };

    // 保存
    const handleSaveDraft = async () => {
      setDraftLoading(true);
      let result = {};
      try {
        result = await configurationRef?.current?.handleSubmit();
      } catch (error) {}

      saveOrSubmitRequest(result?.data || {}, 1, 'save', () => {
        setDraftLoading(false);
      });
    };

    // 校验所有配置条数
    const checkOperationalLength = () => {
      const list = getFieldsValue()?.fieldConfig;
      const total = list?.reduce((pre, cur) => {
        pre += cur?.length;
        return pre;
      }, 0);
      if (total > 100) {
        message.error('最多配置100条');
        return;
      }
    };

    // 提交
    const handleSubmit = async () => {
      let result = {};
      try {
        result = await configurationRef?.current?.handleSubmit();
      } catch (error) {}

      saveOrSubmitRequest(result?.data || {}, 0, 'submit', () => {
        setConfirmLoading(false);
      });
      // try {
      //   const result = await configurationRef?.current?.handleSubmit();
      //   if (!result) {
      //     message.error('请添加条件！');

      //     return;
      //   }
      //   if (result?.valifyStatus === 'EMPTY') {
      //     message.error('请添加条件！');
      //   } else if (result?.valifyStatus === 'SUCCESS') {
      //     saveOrSubmitRequest(result?.data, 0, 'submit', () => {
      //       setConfirmLoading(false);
      //     });
      //   } else if (result.valifyStatus === 'FAIL') {
      //     message.error('请填写必填信息！');
      //   }
      // } catch (error) {
      //   message.error('请添加条件！');
      // }
    };
    // const handleSubmit = () => {
    //   secondStepForm?.current?.validateFields(async (errs, values) => {
    //     if (errs) return;
    //     checkOperationalLength();
    //     setConfirmLoading(true);
    //     saveOrSubmitRequest(0, 'submit', () => {
    //       setConfirmLoading(false);
    //     });
    //   });
    // };

    // 下一步
    const handleNextStep = () => {
      firstStepForm?.current?.validateFields((errs, values) => {
        if (errs) return;
        stepValuesRef.current.step1Values = values;
        getCategoryField(values?.dataAccessId, () => {
          // 改变数据接入 ID 后，将与或置空
          if (detailValues?.dataAccessId !== values?.dataAccessId) {
            // setFlowQuery({ group: {} });
          }
          setCurrentStep(currentStep + 1);
        });
      });
    };

    // 上一步
    const handlePreviousStep = () => {
      setCurrentStep(currentStep - 1);
    };

    // 获取分类下拉字段
    const getCategoryField = async (dataAccessId, callback) => {
      if (!dataAccessId) {
        setCategoryFieldData([]);
        return;
      }
      const response = await request(`${apiPrefix}/dataAccessDetails`, {
        method: 'POST',
        data: {
          dataAccessId,
        },
        requestType: 'json',
      });
      if (response.status === 200) {
        setCategoryFieldData(
          response?.data?.dataList?.map((ele) => {
            return {
              fieldName: ele.fieldMean,
              field: ele.fieldName,
            };
          }) || [],
        );
        callback?.();
      } else {
        message.error(response?.msg);
      }
    };

    // 清空与或组件
    const handleReasetConfiguration = () => {
      configurationRef?.current?.handleReset();
    };

    // 获取保存数据
    const getDraftData = async () => {
      const response = await request(`${apiPrefix}/getUserCategoryDraft`, {
        method: 'GET',
      });
      if (response.status === 200) {
        if (!response?.data?.draftData) {
          setInitDataAccessId('DS_' + uuidv4());
        }
        setDetailValues(response?.data?.draftData || {});
        const ruleContext = response?.data?.draftData?.ruleContext;
        try {
          const parsedData = ruleContext ? JSON.parse(ruleContext) : { group: null };

          setFlowQuery(parsedData);
        } catch (error) {
          setFlowQuery({ group: null });
        }
      } else {
        message.error(response?.msg);
      }
    };

    useEffect(() => {
      if (visible) {
        // 新增的时候  先获取草稿数据
        if (!currentRow?.title) {
          getDraftData();
        } else {
          setDetailValues(currentRow);
          setFlowQuery(JSON.parse(currentRow?.ruleContext || '{}'));
          getCategoryField(currentRow?.dataAccessId);
        }
      }
    }, [visible]);

    return (
      <Modal
        title={`${currentRow?.title ?? '新增'}`}
        visible={visible}
        onCancel={onCancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
        width={900}
        bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
        footer={
          <div>
            {currentStep === 0 && (
              <Button type="primary" onClick={handleNextStep}>
                下一步
              </Button>
            )}
            {currentStep === 1 && (
              <Fragment>
                <Button type="primary" onClick={handlePreviousStep}>
                  上一步
                </Button>
                {!isEdit && !isDetail && (
                  <Button type="primary" onClick={handleSaveDraft} loading={draftLoading}>
                    保存草稿
                  </Button>
                )}
                {!isDetail && (
                  <Fragment>
                    <Button onClick={onCancel}>取消</Button>
                    <Button type="primary" onClick={handleSubmit} loading={confirmLoading}>
                      确认提交
                    </Button>
                  </Fragment>
                )}
              </Fragment>
            )}
          </div>
        }
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          <Step title="配置分类规则信息" />
          <Step title="配置分类规则" />
        </Steps>
        <div
          style={{
            width: '100%',
            display: currentStep === 0 ? 'block' : 'none',
            justifyContent: 'center',
          }}
        >
          <FirstStep
            wrappedComponentRef={firstStepForm}
            detailValues={detailValues}
            currentRow={currentRow}
            isEdit={isEdit}
            isDetail={isDetail}
            setFlowQuery={setFlowQuery}
            initDataAccessId={initDataAccessId}
            handleReasetConfiguration={handleReasetConfiguration}
          />
        </div>

        {currentStep === 1 && (
          <Configuration
            key={JSON.stringify(flowQuery)}
            wrappedComponentRef={configurationRef}
            data={flowQuery}
            optionList={categoryFieldData}
            disabled={isDetail}
          />

          // // 下面是处置策略里的写法示例：
          // <Configuration
          //   wrappedComponentRef={configurationRef}
          //   data={flowQuery}
          //   optionList={categoryFieldData}
          //   colonRelMode
          //   /*
          //     conditionValueFormatter 初始化时、value[?] 的元素离开焦点或清空操作以及 field、rel 任意一个值改变时才会被触发
          //     conditionValueFormatter 如果不返回或者返回值为 undefined 则忽略返回值（保留原值），否则该返回值还会再次经过内部的格式化器（比如当返回值是 rel 不支持的数据时会自动处理）
          //     conditionValueFormatter 只有 value[?] 时 triggerValueIndex 参数才会有值（表示是从哪个索引的 value 元素触发的），但 value 参数依然是整体的值
          //     conditionValueFormatter 的 rel 入参枚举值： 'EQ', 'GREATER', 'GREATER_EQ', 'LESS', 'LESS_EQ', 'IN', 'NOT_IN', 'REG', 'CONTAIN', 'NOTINCLUDED', ':'
          //   */
          //   conditionValueFormatter={(field, rel, value, triggerValueIndex) => {
          //     if (value === '' || (Array.isArray(value) && !value.join(''))) { // 这两种情况都可以认为当前值为空
          //       if (field === 'dateAccessId') {
          //         return 'hello';
          //       }
          //       if (rel === 'EQ') {
          //         return '123';
          //       } else if (rel === 'GREATER') {
          //         return 'aaa';
          //       }
          //       return 'bbb';
          //     }
          //   }}
          //   // conditionValueFormatterTriggeredBy 配置 conditionValueFormatter 触发时机，默认值：['init', 'field', 'rel', 'value']
          //   conditionValueFormatterTriggeredBy={['init', 'field', 'rel'/*, 'value'*/]}
          // />
        )}

        {/* <div
         
        >
          <SecondStep
            currentRow={currentRow}
            wrappedComponentRef={secondStepForm}
            detailValues={detailValues}
            isEdit={isEdit}
            isDetail={isDetail}
            categoryFieldData={categoryFieldData}
          />
        </div> */}
      </Modal>
    );
  },
);

export default AddModal;
