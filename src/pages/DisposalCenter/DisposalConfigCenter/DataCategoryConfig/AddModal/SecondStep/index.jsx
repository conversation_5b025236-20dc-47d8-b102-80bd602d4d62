import React, { forwardRef, useImperativeHandle, useState, useMemo, useEffect } from 'react';
import { Button, Card, Form, Icon, Select } from 'antd';
import AndFormItem from './AndFormItem';
import request from 'ponshine-request';
import { getSystemConfigListByConfigType } from '@/services/common';
const Index = forwardRef((props, ref) => {
  const { form, detailValues, current, isDetail, categoryFieldData } = props;

  useImperativeHandle(ref, () => {
    return {
      ...form,
    };
  });

  const [formList, setFormList] = useState([]);
  const [operationalList, setOperationalList] = useState(undefined);

  const handleAdd = () => {
    const newList = form.getFieldsValue()?.fieldConfig || [];
    newList.push([{}]);
    setFormList(newList);
  };

  const handleDelete = (index) => {
    const newList = form.getFieldsValue().fieldConfig;
    newList.splice(index, 1);
    form.resetFields(`fieldConfig`);
    form.setFieldsValue({ fieldConfig: newList });
    setFormList(newList);
  };

  const getOperationalList = async () => {
    const response = await getSystemConfigListByConfigType({ configType: 'operational' });
    if (response.code === 200) {
      setOperationalList(response?.data || []);
    } else {
      message.error(response?.message);
    }
  };

  const safeJSONParse = (jsonString, defaultValue = []) => {
    try {
      const parsed = JSON.parse(jsonString);
      return Array.isArray(parsed) ? parsed : defaultValue;
    } catch (error) {
      console.error('JSON parse error:', error);
      return defaultValue;
    }
  };

  useEffect(() => {
    current === 2 && getOperationalList();
  }, [current]);

  useEffect(() => {
    if (detailValues?.ruleContext) {
      setFormList(safeJSONParse(detailValues.ruleContext));
    }
  }, [detailValues]);

  return (
    <div>
      <Form layout="inline">
        {formList?.map((ele, index) => (
          <div key={index}>
            <div style={{ position: 'relative', marginLeft: 24 }}>
              {/* formList?.length > 1 &&  */}
              {!isDetail && (
                <Icon
                  type="delete"
                  style={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    zIndex: 999,
                    color: '#1890ff',
                  }}
                  onClick={() => handleDelete(index)}
                />
              )}

              {formList?.length && (
                <div style={{ padding: '16px', border: '1px dashed #1890ff' }}>
                  <AndFormItem
                    form={form}
                    formItemList={ele}
                    setFormList={setFormList}
                    index={index}
                    formList={formList}
                    operationalList={operationalList}
                    isDetail={isDetail}
                    categoryFieldData={categoryFieldData}
                  />
                </div>
              )}
            </div>
            {formList.length > 1 && index !== formList.length - 1 && (
              <div style={{ color: '#1890ff' }}>
                <strong>或</strong>
              </div>
            )}
          </div>
        ))}
      </Form>
      <div style={{ margin: '24px 0 24px 24px' }}>
        <Button
          icon="plus"
          type="dashed"
          style={{ width: '100%' }}
          onClick={handleAdd}
          disabled={isDetail}
        >
          添加新的条件（或）
        </Button>
      </div>
    </div>
  );
});
export default Form.create()(Index);
