import React, { Fragment, useState } from 'react';
import { Form, Input, Select, Icon } from 'antd';
import GroupCondition from './GroupCondition';

const initOperationalList = [
  '等于',
  '大于',
  '大于等于',
  '小于',
  '小于等于',
  '包含',
  '不包含',
  'IN',
  'NOT IN',
  // '正则',
];
export default function index({
  form,
  form: { getFieldDecorator, resetFields, setFieldsValue, getFieldsValue, getFieldValue },
  formItemList,
  formList,
  setFormList,
  index,
  operationalList = initOperationalList,
  isDetail,
  categoryFieldData,
}) {
  const handleAdd = (i) => {
    const newList = [...formList];
    const list = getFieldsValue().fieldConfig?.[index];
    list.splice(i + 1, 0, {});
    newList[index] = list;
    resetFields(`fieldConfig.${index}`);
    setFieldsValue({ fieldConfig: newList });
    setFormList(newList);
  };

  const handleRemove = (i) => {
    const newList = [...formList];
    const list = getFieldsValue().fieldConfig[index];
    list.splice(i, 1);
    newList[index] = list;
    resetFields(`fieldConfig.${index}`);
    setFieldsValue({ fieldConfig: newList });
    setFormList(newList);
  };

  return (
    <Fragment>
      {formItemList?.map((ele, i) => (
        <div key={i}>
          <div style={{ padding: '0 24px' }}>
            <Form.Item>
              {getFieldDecorator(`fieldConfig.${index}.${i}.fieldValue`, {
                initialValue: formItemList[i]?.fieldValue || undefined,
                rules: [{ required: true, message: '请选择字段' }],
              })(
                <Select placeholder="请选择" allowClear style={{ width: 250 }}>
                  {categoryFieldData?.map((ele, index) => (
                    <Select.Option value={ele.key} key={ele.key}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item>
              {getFieldDecorator(`fieldConfig.${index}.${i}.operational`, {
                initialValue: formItemList[i]?.operational || undefined,
                rules: [{ required: true, message: '请选择条件' }],
              })(
                <Select
                  placeholder="请选择"
                  allowClear
                  style={{ width: 150 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  {operationalList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>

            {['包含', '不包含', 'IN', 'NOT IN']?.includes(
              getFieldValue(`fieldConfig.${index}.${i}.operational`),
            ) ? (
              <Form.Item>
                <GroupCondition
                  form={form}
                  parentKey={`fieldConfig.${index}.${i}.inputValue`}
                  groupList={
                    Array.isArray(formItemList[i]?.inputValue)
                      ? formItemList[i]?.inputValue
                      : [formItemList[i]?.inputValue]
                  }
                  setFormList={setFormList}
                  formList={formList}
                  isDetail={isDetail}
                />
              </Form.Item>
            ) : (
              <Form.Item>
                {getFieldDecorator(`fieldConfig.${index}.${i}.inputValue`, {
                  initialValue: formItemList[i]?.inputValue || '',
                  rules: [{ required: true, message: '请输入阈值' }],
                })(<Input placeholder="请输入阈值" allowClear style={{ width: 200 }} />)}
              </Form.Item>
            )}

            <Form.Item>
              {!isDetail && (
                <Icon
                  type="plus-circle"
                  style={{ marginRight: 16, color: '#1890ff' }}
                  onClick={() => handleAdd(i)}
                />
              )}

              {formItemList?.length > 1 && !isDetail && (
                <Icon
                  type="minus-circle"
                  style={{ color: '#1890ff' }}
                  onClick={() => handleRemove(i)}
                />
              )}
            </Form.Item>
          </div>
          {formItemList.length > 1 && i !== formItemList.length - 1 && (
            <div style={{ color: '#1890ff' }}>且</div>
          )}
        </div>
      ))}
    </Fragment>
  );
}
