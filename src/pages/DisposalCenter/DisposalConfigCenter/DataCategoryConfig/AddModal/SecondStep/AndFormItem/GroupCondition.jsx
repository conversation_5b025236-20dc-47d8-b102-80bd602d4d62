import React, { useState, useEffect } from 'react';
import { Form, Input, Button } from 'antd';

export default function GroupCondition({ form, parentKey, groupList, isDetail }) {
  const { getFieldDecorator, getFieldValue, setFieldsValue, resetFields } = form;
  const [groupConditionList, setGroupConditionList] = useState(['']);

  useEffect(() => {
    setGroupConditionList(groupList.length > 0 ? groupList : ['']);
  }, [JSON.stringify(groupList)]);

  const handleAdd = () => {
    const newGroupConditionList = [...groupConditionList, ''];
    setGroupConditionList(newGroupConditionList);
    setFieldsValue({ [parentKey]: newGroupConditionList });
  };

  const handleDelete = (index) => {
    const currentGroupConditionList = getFieldValue(parentKey);
    delete currentGroupConditionList[index];
    setGroupConditionList(Object.values(currentGroupConditionList));
    resetFields(parentKey);
    setTimeout(() => {
      setFieldsValue({ [parentKey]: currentGroupConditionList });
    }, 0);
  };

  return (
    <div>
      {groupConditionList?.map((ele, index) => (
        <div style={{ display: 'flex', alignItems: 'center' }} key={index}>
          <Form.Item label="">
            {getFieldDecorator(`${parentKey}.${index}`, {
              initialValue: ele,
              rules: [{ required: true, message: '请输入阈值' }],
            })(<Input placeholder="请输入阈值" allowClear style={{ width: 120 }} />)}
          </Form.Item>
          {index === 0 ? (
            <Button size="small" icon="plus" onClick={() => handleAdd()} disabled={isDetail}>
              添加
            </Button>
          ) : (
            <Button
              size="small"
              icon="delete"
              onClick={() => handleDelete(index)}
              disabled={isDetail}
            >
              删除
            </Button>
          )}
        </div>
      ))}
    </div>
  );
}
