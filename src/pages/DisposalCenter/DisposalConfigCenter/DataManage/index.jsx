import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';

import request from 'ponshine-request';

const { RangePicker } = DatePicker;

const fixedColumns = [
  {
    title: '序号',
    dataIndex: 'id',
    key: 'id',
    width: 60,
  },
  {
    title: '数据名称',
    dataIndex: 'dataName',
    key: 'dataName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '数据接入平台名称',
    dataIndex: 'platformName',
    key: 'platformName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '数据分类ID',
    dataIndex: 'dataClassifyId',
    key: 'dataClassifyId',
    width: 150,
    ellipsis: true,
  },
  {
    title: '入库时间',
    dataIndex: 'storageTime',
    key: 'storageTime',
    width: 180,
    ellipsis: true,
  },
];

const Index = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [batchNoData, setBatchNoData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [columns, setColumns] = useState(fixedColumns);

  const serachParams = useRef();

  const findTableDataPager = async ({
    pageNum = 1,
    pageSize = 10,
    reloadColumn = true,
    ...props
  } = {}) => {
    setLoading(true);
    const response = await request(`${apiPrefix}/dataManagementQueryDetails`, {
      data: { pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });

    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      // 切换分页时，不刷新表头
      reloadColumn && getTableColumns(response?.data?.headerList);
      setListData({
        list: response?.data?.dataList || [],
        pagination: {
          total: response.data.totalCount,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      setListData({
        list: [],
        pagination: false,
      });
      message.error(response.msg);
    }
  };
  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      reloadColumn: false,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();

    const { createTime, dataAccessId, dataName } = formValue;
    if (!dataAccessId && !dataName) {
      return message.error('请输入数据接入ID或数据名称');
    }

    findTableDataPager({
      dataAccessId,
      dataName,
      ...formValue,
      dataProduceStartTime: createTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      dataProduceEndTime: createTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      createTime: undefined,
    });
  };
  const getTableColumns = async (data) => {
    const newColumns = data?.map((ele) => {
      return {
        title: ele.fieldMean,
        dataIndex: ele.fieldVlaue,
        key: ele.fieldVlaue,
        width: ele?.fieldMean?.includes('时间') ? 180 : 120,
        ellipsis: true,
      };
    });
    setColumns([...fixedColumns, ...newColumns]);
  };

  const handleReset = () => {
    resetFields();
    setListData({
      list: [],
      pagination: false,
    });
    setColumns(fixedColumns);
  };

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="数据接入ID">
              {getFieldDecorator('dataAccessId', {
                normalize: (value) => value?.trim(), // 自动去除空格
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据接入IP">
              {getFieldDecorator('dataAccessIp', {
                normalize: (value) => value?.trim(), // 自动去除空格
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据名称">
              {getFieldDecorator('dataName', {
                normalize: (value) => value?.trim(), // 自动去除空格
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="号码">
              {getFieldDecorator('number', {
                normalize: (value) => value?.trim(), // 自动去除空格
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据分类ID">
              {getFieldDecorator('dataClassifyId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,

            <Form.Item label="数据产生时间">
              {getFieldDecorator('createTime')(
                <RangePicker allowClear format="YYYY-MM-DD" style={{ width: '100%' }} />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
        columns={columns}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default Form.create()(Index);
