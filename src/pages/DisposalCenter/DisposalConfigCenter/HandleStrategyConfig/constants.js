//列表和配置记录表头
const commonColumns = [
  {
    title: '处置策略ID',
    dataIndex: 'policyId',
    key: 'policyId',
    width: 180,
    ellipsis: true,
  },
  {
    title: '处置策略名称',
    dataIndex: 'policyName',
    key: 'policyName',
    width: 140,
    ellipsis: true,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 100,
    ellipsis: true,
    render: (v, r) => {
      return r.handleState === '关闭' ? '--' : v;
    },
  },
  {
    title: '处置方式',
    dataIndex: 'handleType',
    key: 'handleType',
    width: 120,
    ellipsis: true,
  },
  {
    title: '数据来源',
    dataIndex: 'dataSource',
    key: 'dataSource',
    width: 120,
    ellipsis: true,
    render: () => {
      return '自动';
    },
  },
  {
    title: '数据分类ID',
    dataIndex: 'dataClassifyId',
    key: 'dataClassifyId',
    width: 170,
    ellipsis: true,
  },
  {
    title: '数据分类名称',
    dataIndex: 'dataClassifyName',
    key: 'dataClassifyName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '处置状态',
    dataIndex: 'handleState',
    key: 'handleState',
    width: 120,
    ellipsis: true,
  },
];

export { commonColumns };
