import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
  Button,
  Card,
  message,
  Form,
  Modal,
  Input,
  DatePicker,
  Row,
  Col,
  Select,
  Table,
} from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import AddModal from './AddModal';
import EditTable from './EditTable';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import { commonColumns } from './constants';

import { Licensee, router } from 'ponshine';
import request from 'ponshine-request';

const Index = (props) => {
  const {
    form,
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue, setFieldsValue },
  } = props;

  const initialParams = {
    handleType: '号码签约',
  };
  const [addVisible, setAddVisible] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [currentRow, setCurrentRow] = useState({});
  const [editPriorityVisible, setEditPriorityVisible] = useState(false);

  const serachParams = useRef();
  const editTableRef = useRef();

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`${apiPrefix}/getStrategyPageByDisposalMethod`, {
      params: { current: pageNum, pageSize, ...props },
      method: 'GET',
    });

    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.list || [],
        pagination: {
          total: response.data.total,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      setListData({
        list: [],
        pagination: false,
      });
      message.error(response.message);
    }
  };

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    findTableDataPager(formValue);
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager(initialParams);
  };

  const handleCancelEditPriority = () => {
    setEditPriorityVisible(false);
    findTableDataPager({ ...serachParams.current, pageSize: 10 });
  };

  const handleEditPriority = async () => {
    try {
      const editTableData = await editTableRef?.current?.getEditTableData();
      const response = await request(`${apiPrefix}/updatePriorityByListId`, {
        data: {
          handleType: '号码签约',
          policyListIdSortMap: editTableData,
        },
        method: 'POST',
        requestType: 'json',
      });
      if (response.status === 200) {
        message.success(response.msg);
        setEditPriorityVisible(false);
        findTableDataPager(serachParams.current);
      } else {
        message.error(response.msg);
      }
    } catch (err) {
      // 校验未通过
    }
  };

  const handleEditPriorityConfig = () => {
    const { handleState, handleType, ...rest } = getFieldsValue();

    resetFields([...Object.keys(rest)]);
    setFieldsValue({
      handleState: ['开启', '试运行'],
    });
    message.info('编辑优先级只能操作处理状态为上线和试运行的数据，其他多余查询条件已为您重置');

    findTableDataPager({
      handleState: ['开启', '试运行'],
      current: 1,
      pageSize: 100,
      ...initialParams,
    });
    setEditPriorityVisible(true);
  };

  useEffect(() => {
    setFieldsValue({
      handleType: '号码签约',
    });

    findTableDataPager(initialParams);
  }, []);

  const columns = [
    ...commonColumns,
    {
      title: '操作',
      dataIndex: 'opt',
      align: 'center',
      fixed: 'right',
      width: 100,
      ellipsis: true,
      render: (v, r) => (
        <Fragment>
          <a
            onClick={() => {
              setCurrentRow({ ...r, title: '详情' });
              setAddVisible(true);
            }}
            disabled={editPriorityVisible}
            style={{ marginRight: 8 }}
          >
            详情
          </a>
          <a
            type="link"
            onClick={() => {
              setCurrentRow({ ...r, title: '编辑' });
              setAddVisible(true);
            }}
            disabled={editPriorityVisible}
          >
            编辑
          </a>
        </Fragment>
      ),
    },
  ];

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="处置策略ID">
              {getFieldDecorator('policyId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="处置策略名称">
              {getFieldDecorator('policyName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,

            <CommonSelect
              configType="handleMethod"
              form={form}
              formItemKey="handleType"
              formItemLabel="处置方式"
              valueKey="name"
              availableData={['号码签约', '号码质检人工核验']}
              initialValue={initialParams.handleType}
            />,

            <Form.Item label="数据分类ID">
              {getFieldDecorator('dataClassifyId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据分类名称">
              {getFieldDecorator('dataClassifyName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="处置状态">
              {getFieldDecorator('handleState')(
                <Select placeholder="请选择" allowClear mode="multiple">
                  {['关闭', '开启', '试运行']?.map((ele) => (
                    <Select.Option key={ele} value={ele}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch} disabled={editPriorityVisible}>
                查询
              </Button>
              <Button
                onClick={handleReset}
                style={{ marginLeft: 8 }}
                disabled={editPriorityVisible}
              >
                重置
              </Button>
              {/* <Licensee license="schoolCard_shutdownDetail_export"> */}
              <Button
                type="primary"
                onClick={() => {
                  setAddVisible(true);
                }}
                style={{ marginLeft: 8 }}
                disabled={editPriorityVisible}
              >
                新增
              </Button>
              {/* </Licensee> */}

              {/* <Licensee license="schoolCard_shutdownDetail_export"> */}
              <Button
                onClick={() => {
                  router.push('/disposalCenter/HandleStrategyConfigRecord');
                }}
                style={{ marginLeft: 8 }}
                disabled={editPriorityVisible}
              >
                配置记录
              </Button>
              {/* </Licensee> */}
            </Fragment>
          }
        />
      </Form>
      <div style={{ textAlign: 'right', marginBottom: 16 }}>
        {editPriorityVisible ? (
          <Fragment>
            操作提示：请在表格优先级输入框中输入优先级，并点击确认按钮
            <Button style={{ margin: '0 8px' }} onClick={handleCancelEditPriority}>
              取消
            </Button>
            <Button type="primary" onClick={handleEditPriority}>
              确认
            </Button>
          </Fragment>
        ) : (
          <Fragment>
            {getFieldValue('handleType') === '号码签约' && (
              <Button type="primary" style={{ marginLeft: 8 }} onClick={handleEditPriorityConfig}>
                优先级配置
              </Button>
            )}
          </Fragment>
        )}
      </div>
      {editPriorityVisible ? (
        <EditTable
          columns={columns}
          dataSource={listData.list}
          wrappedComponentRef={editTableRef}
        />
      ) : (
        <StandardTable
          columns={columns}
          data={listData}
          onChange={handleTableChange}
          rowKey="policyId"
          loading={loading}
          scroll={{ x: 1200 }}
          showSelectCount={false}
          rowSelectionProps={false}
        />
      )}

      {addVisible && (
        <AddModal
          visible={addVisible}
          onReload={() => findTableDataPager(serachParams.current)}
          onCancel={() => {
            currentRow?.id && setCurrentRow({});
            setAddVisible(false);
          }}
          currentRow={currentRow}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
