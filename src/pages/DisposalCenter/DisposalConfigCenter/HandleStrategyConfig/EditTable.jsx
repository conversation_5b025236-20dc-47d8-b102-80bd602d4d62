import React, { useImperativeHandle, forwardRef } from 'react';
import { Table, Form, Input, message } from 'antd';
import _ from 'lodash';

const EditTable = forwardRef(({ columns, dataSource, form }, ref) => {
  const { getFieldDecorator } = form;
  const newColumns = columns.map((child) => {
    if (child.title === '优先级') {
      return {
        ...child,
        render: (text, record, index) => (
          <Form.Item style={{ marginBottom: 0 }}>
            {getFieldDecorator(`priorityList.${index}`, {
              initialValue: String(text),
              rules: [
                { required: true, message: '请输入优先级' },
                { pattern: /^[1-9]\d*$/, message: '请输入正整数' },
              ],
            })(<Input placeholder="请输入" size="small" />)}
          </Form.Item>
        ),
      };
    }
    return child;
  });

  const getEditTableData = () => {
    return new Promise((resolve, reject) => {
      form.validateFields((err, values) => {
        if (err) {
          reject(err);
          return;
        }
        // const priorityList = values?.priorityList || [];
        // // 校验是否有重复
        // const uniqList = _.uniq(priorityList);
        // console.log(uniqList, '---uniqList');
        // if (uniqList.length !== priorityList.length) {
        //   message.warning('有重复的优先级');
        //   return reject(new Error('有重复的优先级'));
        // }
        let priorityObj = {};
        console.log(values?.priorityList, '---values?.priorityList');
        values?.priorityList.forEach((item, index) => {
          priorityObj[dataSource[index].id] = item;
        });
        resolve(priorityObj);
      });
    });
  };

  useImperativeHandle(ref, () => {
    return {
      getEditTableData: getEditTableData,
    };
  });

  return (
    <Form>
      <Table columns={newColumns} dataSource={dataSource} size="small" pagination={false} />
    </Form>
  );
});

export default Form.create()(EditTable);
