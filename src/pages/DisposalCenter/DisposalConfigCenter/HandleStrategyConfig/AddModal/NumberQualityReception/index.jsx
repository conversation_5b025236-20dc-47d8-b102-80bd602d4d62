import React, { useContext } from 'react';
import { Form, Radio } from 'antd';

import Context from '../context';

const NumberQualityReception = ({ form: { getFieldDecorator } }) => {
  const { isDetail, currentRow, defaultConfigData, getDefaultConfigData } = useContext(Context);
  return (
    <Form.Item label="处置状态" required>
      {getFieldDecorator('handleState', {
        rules: [{ required: true, message: '请选择处置状态' }],
        initialValue:
          currentRow?.handleState || getDefaultConfigData(defaultConfigData, '处置状态') || '开启',
      })(
        <Radio.Group disabled={isDetail}>
          {['关闭', '开启'].map((ele) => (
            <Radio value={ele}>{ele}</Radio>
          ))}
        </Radio.Group>,
      )}
    </Form.Item>
  );
};

export default Form.create()(NumberQualityReception);
