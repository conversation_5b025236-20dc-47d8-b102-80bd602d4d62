import { Modal, Form, Input, message, Select, Button } from 'antd';
import React, { Fragment, useState, useEffect, useRef, useMemo } from 'react';
import request from 'ponshine-request';
import NumberSign from './NumberSign';
import NumberQualityReception from './NumberQualityReception';
import IDNameCascader from '@/pages/DisposalCenter/DisposalConfigCenter/components/IDNameCascader';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';
import { getDefaultConfigData } from './utils';
import Context from './context';

import { v4 as uuidv4 } from 'uuid';
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const AddModal = ({
  visible,
  currentRow,
  onCancel,
  form,
  form: { validateFieldsAndScroll, getFieldDecorator, resetFields, getFieldValue },
  onReload,
}) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [defaultConfigData, setDefaultConfigData] = useState([]);
  const [initHandleStrategyId, setInitHandleStrategyId] = useState('');

  const isDetail = currentRow?.title === '详情';
  const numberSignRef = useRef();

  const handleSubmit = () => {
    validateFieldsAndScroll(async (errs, values) => {
      let customValues = {};
      if (getFieldValue('handleType') === '号码签约') {
        customValues = await numberSignRef.current?.getValues();

        if (errs) return;
      }

      setConfirmLoading(true);
      const response = await request(
        `${apiPrefix}/${
          currentRow?.id ? 'updateDisposalStrategyConfig' : 'createDisposalStrategyConfig'
        } `,
        {
          data: {
            ...values,
            ...customValues,
            id: currentRow?.id,
          },
          method: 'POST',
          requestType: 'json',
        },
      );
      setConfirmLoading(false);
      if (response.status === 200) {
        message.success(response.msg);
        onCancel();
        onReload();
      } else {
        message.error(response.msg);
      }
    });
  };

  // 获取全局默认配置
  const getGlobalDefaultConfig = async (handleType) => {
    const response = await request(`${apiPrefix}/currentlyAllocatedQuery`, {
      method: 'GET',
      params: {
        configType: handleType,
      },
    });
    if (response.status === 200) {
      setDefaultConfigData(response?.data?.configTypeList);
    } else {
      message.error(response?.msg);
    }
  };

  useEffect(() => {
    if (visible) {
      !currentRow?.id && setInitHandleStrategyId('HS_' + uuidv4());
    }
  }, [visible]);

  const HandleWayComponent = {
    号码签约: <NumberSign wrappedComponentRef={numberSignRef} />,
    号码质检人工核验: <NumberQualityReception form={form} />,
  };

  // 使用 useMemo 来获取当前的处置方式，确保有默认值
  const currentHandleType = useMemo(() => {
    getGlobalDefaultConfig(currentRow?.handleType || getFieldValue('handleType'));
    return getFieldValue('handleType') || currentRow?.handleType || '号码签约';
  }, [getFieldValue('handleType'), currentRow?.handleType]);

  const getHandleWayComponent = (type) => {
    return HandleWayComponent[type];
  };

  return (
    <Modal
      title={`${currentRow?.title ?? '新增'}`}
      visible={visible}
      onCancel={onCancel}
      afterClose={() => {
        resetFields();
      }}
      maskClosable={false}
      // confirmLoading={confirmLoading}
      width={900}
      // onOk={handleSubmit}
      bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
      footer={
        currentRow?.title === '详情' ? null : (
          <Fragment>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" onClick={handleSubmit} loading={confirmLoading}>
              确认
            </Button>
          </Fragment>
        )
      }
    >
      <Context.Provider value={{ isDetail, currentRow, defaultConfigData, getDefaultConfigData }}>
        <Form {...formItemLayout}>
          <Form.Item label="处置策略ID">
            {getFieldDecorator('policyId', {
              initialValue: currentRow?.policyId ?? initHandleStrategyId,
              rules: [{ required: true, message: '请输入' }],
            })(<Input placeholder="请输入" allowClear disabled />)}
          </Form.Item>
          <Form.Item label="处置策略名称">
            {getFieldDecorator('policyName', {
              initialValue: currentRow?.policyName ?? '',
              rules: [
                { required: true, message: '请输入' },
                {
                  max: 20,
                  message: '最多不能超过20个字符',
                },
              ],
              getValueFromEvent: (e) => e?.target?.value?.trim(),
            })(<Input placeholder="请输入" allowClear disabled={!!currentRow?.id} />)}
          </Form.Item>
          <IDNameCascader
            form={form}
            idKey="dataClassifyId"
            nameKey="dataClassifyName"
            idLabel="数据分类ID"
            nameLabel="数据分类名称"
            idInitialValue={currentRow?.dataClassifyId ?? undefined}
            nameInitialValue={currentRow?.dataClassifyName ?? undefined}
            api="listAllDataClassifyIdsAndNames"
            disabled={!!currentRow?.id}
          />
          <CommonSelect
            configType="handleMethod"
            form={form}
            formItemKey="handleType"
            formItemLabel="处置方式"
            valueKey="name"
            rules={[{ required: true, message: '请选择' }]}
            initialValue={currentRow?.handleType ?? '号码签约'}
            availableData={['号码签约', '号码质检人工核验']}
            disabled={!!currentRow?.id}
          />

          {getHandleWayComponent(currentHandleType)}
        </Form>
      </Context.Provider>
    </Modal>
  );
};

export default Form.create()(AddModal);
