import React, { useContext, Fragment, useState, useEffect } from 'react';
import { Form, Radio, Select, Input, InputNumber, TreeSelect, message } from 'antd';

import request from '@/utils/request';
const { SHOW_PARENT, SHOW_ALL } = TreeSelect;
const NumberStop = ({ form, form: { getFieldDecorator, getFieldValue }, configData }) => {
  const [filteredWhitelist, setFilteredWhitelist] = useState([]);

  const getWhiteList = async () => {
    const response = await request('/api/hn/autoModel/getAllWhiteType', {
      method: 'GET',
    });
    if (response.code === 200) {
      setFilteredWhitelist(response?.data || []);
    } else {
      message.error(response?.message);
    }
  };

  useEffect(() => {
    getWhiteList();
  }, []);

  return (
    <Form>
      <IDNameCascader
        form={form}
        idKey="dataCategoryId"
        nameKey="dataCategoryName"
        idLabel="数据分类ID"
        nameLabel="数据分类名称"
        idInitialValue={''}
        nameInitialValue=""
      />
      <Form.Item label="处置状态" required>
        {getFieldDecorator('SignOutHandleStatus', {
          rules: [{ required: true, message: '请选择处置状态' }],
          initialValue: configData?.SignOutHandleStatus ?? 1,
        })(
          <Radio.Group>
            <Radio value="0">关闭</Radio>
            <Radio value="1">开启</Radio>
            <Radio value="2">试运行</Radio>
          </Radio.Group>,
        )}
      </Form.Item>
      <Form.Item label="是否过滤白名单">
        {getFieldDecorator('filterWhite', {
          initialValue: configData?.filterWhite,
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        })(
          <Select
            placeholder="请选择"
            allowClear
            getPopupContainer={(triggerNode) => triggerNode.parentElement}
          >
            {[
              { name: '过滤', value: '1' },
              { name: '不过滤', value: '2' },
            ].map((ele, index) => (
              <Select.Option value={ele.value}>{ele.name}</Select.Option>
            ))}
          </Select>,
        )}
      </Form.Item>
      {getFieldValue('filterWhite') === '1' && (
        <Form.Item label="过滤白名单">
          {getFieldDecorator('filterWhiteType', {
            // initialValue: currentRow?.filterWhiteType?.split(','),
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          })(
            <TreeSelect
              showCheckedStrategy={SHOW_PARENT}
              treeData={filteredWhitelist}
              treeCheckable={true}
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
            />,
          )}
        </Form.Item>
      )}

      <Form.Item label="二次处置时间间隔 (小时)">
        {getFieldDecorator('processInterval', {
          initialValue: configData?.processInterval,
        })(
          <InputNumber min={1} max={999} style={{ width: '100%' }} placeholder="请输入时间间隔" />,
        )}
      </Form.Item>

      <Form.Item label="每日熔断阈值">
        {getFieldDecorator('dailyThreshold', {
          initialValue: configData?.dailyThreshold,
        })(<InputNumber min={0} style={{ width: '100%' }} placeholder="请输入熔断阈值" />)}
      </Form.Item>
    </Form>
  );
};

export default Form.create()(NumberStop);
