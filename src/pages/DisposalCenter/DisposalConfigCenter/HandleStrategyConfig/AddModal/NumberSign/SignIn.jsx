import React, { useContext, Fragment, useState, useEffect } from 'react';
import { Form, Radio, Select, Input, InputNumber, TreeSelect, message } from 'antd';

import request from '@/utils/request';
import Context from '../context';
const { SHOW_PARENT, SHOW_ALL } = TreeSelect;

const filterStateData = [
  {
    label: '过滤',
    value: 0,
  },
  {
    label: '不过滤',
    value: 1,
  },
];

const SignIn = ({ form: { getFieldDecorator, getFieldValue, setFieldsValue } }) => {
  const { isDetail, currentRow, defaultConfigData, getDefaultConfigData } = useContext(Context);
  const [filteredWhitelist, setFilteredWhitelist] = useState([]);

  // 获取所有子节点的路径
  const getAllLeafPaths = (node, parentTitles = []) => {
    // 如果没有子节点，返回当前节点路径
    if (!node.children || node.children.length === 0) {
      // 从第二层开始的父节点名称加上当前节点名称
      const relevantParents = parentTitles.slice(1);
      const path =
        relevantParents.length > 0 ? `${relevantParents.join('-')}-${node.title}` : node.title;
      return [{ value: node.value, path }];
    }

    // 如果有子节点，递归获取所有子节点的路径
    return node.children.reduce((paths, child) => {
      return paths.concat(getAllLeafPaths(child, [...parentTitles, node.title]));
    }, []);
  };

  // 在树中查找节点并获取完整路径
  const findNodeAndGetPath = (nodes, targetValue, parentTitles = []) => {
    for (const node of nodes) {
      const currentParentTitles = [...parentTitles];
      if (node.value === targetValue) {
        // 如果是叶子节点，返回完整路径
        if (!node.children || node.children.length === 0) {
          // 从第二层开始的父节点名称加上当前节点名称
          const relevantParents = currentParentTitles.slice(1);
          const path =
            relevantParents.length > 0 ? `${relevantParents.join('-')}-${node.title}` : node.title;
          return { found: true, path, value: node.value };
        }
        // 如果是父节点，获取所有子节点的路径，并传入当前的父节点路径
        const leafPaths = getAllLeafPaths(node, currentParentTitles);
        return { found: true, leafPaths };
      }

      if (node.children) {
        currentParentTitles.push(node.title);
        const result = findNodeAndGetPath(node.children, targetValue, currentParentTitles);
        if (result.found) {
          return result;
        }
      }
    }
    return { found: false };
  };

  // 处理树选择变化
  const handleTreeSelectChange = (values) => {
    if (!values) {
      setFieldsValue({ whiteType: '' });
      return;
    }

    // 获取所有选中节点的最深层子节点路径
    const allPaths = new Set();
    const allValues = new Set();

    // 遍历每个选中的值
    values.forEach((value) => {
      const result = findNodeAndGetPath(filteredWhitelist, value);
      if (result.found) {
        if (result.leafPaths) {
          // 处理父节点的情况
          result.leafPaths.forEach(({ value, path }) => {
            if (!allValues.has(value)) {
              allValues.add(value);
              allPaths.add(path);
            }
          });
        } else {
          // 处理叶子节点的情况
          if (!allValues.has(result.value)) {
            allValues.add(result.value);
            allPaths.add(result.path);
          }
        }
      }
    });

    // 将所有路径合并
    const pathsArray = Array.from(allPaths);
    setFieldsValue({ whiteType: pathsArray.join(',') });
  };

  const getWhiteList = async () => {
    const response = await request('/api/hn/autoModel/getAllWhiteType', {
      method: 'GET',
    });
    if (response.code === 200) {
      setFilteredWhitelist(response?.data || []);
    } else {
      message.error(response?.message);
    }
  };

  useEffect(() => {
    getWhiteList();
  }, []);

  return (
    <Fragment>
      <Form.Item label="处置状态" required>
        {getFieldDecorator('handleState', {
          rules: [{ required: true, message: '请选择处置状态' }],
          initialValue:
            currentRow?.handleState ?? getDefaultConfigData(defaultConfigData, '签入配置-处置状态'),
        })(
          <Radio.Group disabled={isDetail}>
            {['关闭', '开启', '试运行'].map((ele) => (
              <Radio value={ele}>{ele}</Radio>
            ))}
          </Radio.Group>,
        )}
      </Form.Item>

      <Form.Item label="是否过滤白名单">
        {getFieldDecorator('filterState', {
          initialValue: currentRow?.filterState
            ? Number(currentRow?.filterState)
            : getDefaultConfigData(defaultConfigData, '签入配置-是否过滤白名单'),
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        })(
          <Select
            placeholder="请选择"
            allowClear
            getPopupContainer={(triggerNode) => triggerNode.parentElement}
            disabled={isDetail}
          >
            {filterStateData.map((ele, index) => (
              <Select.Option value={ele.value}>{ele.label}</Select.Option>
            ))}
          </Select>,
        )}
      </Form.Item>
      {getFieldValue('filterState') === 0 && (
        <Form.Item label="过滤白名单">
          {/* currentRow?.filterWhiteType?.split(','） */}
          {getFieldDecorator('filterWhiteType', {
            initialValue: currentRow?.filterWhiteType?.split(','),
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          })(
            <TreeSelect
              disabled={isDetail}
              showCheckedStrategy={SHOW_PARENT}
              treeData={filteredWhitelist}
              treeCheckable={true}
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              onChange={handleTreeSelectChange}
            />,
          )}
          {/* 隐藏字段，用于存储完整的白名单路径名称 */}
          {getFieldDecorator('whiteType', {
            initialValue: '',
          })(<Input type="hidden" />)}
        </Form.Item>
      )}

      <Form.Item label="二次处置时间间隔 (小时)">
        {getFieldDecorator('secondHandleHour', {
          initialValue:
            currentRow?.secondHandleHour ??
            getDefaultConfigData(defaultConfigData, '签入配置-二次处置时间间隔（小时）'),
          rules: [
            {
              required: true,
              message: '请输入',
            },
            {
              pattern: /^[0-9]*$/,
              message: '请输入大于等于0的整数',
            },
          ],
        })(<Input placeholder="请输入" disabled={isDetail} />)}
      </Form.Item>

      <Form.Item label="每日熔断阈值">
        {getFieldDecorator('checkInBreak', {
          initialValue:
            currentRow?.checkInBreak ??
            getDefaultConfigData(defaultConfigData, '签入配置-每日熔断阈值'),
          rules: [
            {
              required: true,
              message: '请输入',
            },
            {
              pattern: /^[0-9]*$/,
              message: '请输入大于等于0的整数',
            },
          ],
        })(<Input placeholder="请输入" disabled={isDetail} />)}
      </Form.Item>
    </Fragment>
  );
};

export default SignIn;
