import React, { useContext, Fragment, useState } from 'react';
import { Form, Radio } from 'antd';
import Context from '../context';

const SignInAndOut = ({ form, form: { getFieldDecorator, getFieldValue } }) => {
  const { isDetail, currentRow, getDefaultConfigData, defaultConfigData } = useContext(Context);
  return (
    <Fragment>
      <Form.Item label="签约类型" required>
        {getFieldDecorator('checkType', {
          rules: [{ required: true, message: '请选择签约类型' }],
          initialValue:
            currentRow?.checkType ?? getDefaultConfigData(defaultConfigData, '签约类型'),
        })(
          <Radio.Group disabled={isDetail}>
            <Radio value="主叫签约">主叫签约</Radio>
            <Radio value="被叫签约">被叫签约</Radio>
            <Radio value="主叫签约+被叫签约">主叫签约+被叫签约</Radio>
          </Radio.Group>,
        )}
      </Form.Item>
    </Fragment>
  );
};

export default SignInAndOut;
