import React, { Fragment, useState, useContext } from 'react';
import { Form, Input, Select, Icon, Row, Col } from 'antd';
import Context from '../../../context';

const initialOperationalList = [
  '等于',
  '大于',
  '大于等于',
  '小于',
  '小于等于',
  '包含',
  '不包含',
  'IN',
  'NOT IN',
  // '正则',
];
export default function index({
  formList,
  setFormList,
  index,
  formItemList,
  operationalList = initialOperationalList,
  form,
  configData,
}) {
  const { getFieldDecorator, resetFields, setFieldsValue, getFieldsValue, getFieldValue } = form;
  const [fieldList, setFieldList] = useState([
    {
      name: '签入到签出时间间隔(小时)',
      key: '签入到签出时间间隔(小时)',
    },
    {
      name: '主叫质检次数阈值',
      key: '主叫质检次数阈值',
    },
    {
      name: '被叫质检次数阈值',
      key: '被叫质检次数阈值',
    },
    {
      name: '主叫+被叫质检次数阈值',
      key: '主叫+被叫质检次数阈值',
    },
    {
      name: '主叫质检正常次数阈值',
      key: '主叫质检正常次数阈值',
    },
    {
      name: '被叫质检正常次数阈值主叫',
      key: '被叫质检正常次数阈值主叫',
    },
    {
      name: '主叫+被叫质检正常次数阈值',
      key: '主叫+被叫质检正常次数阈值',
    },
  ]);
  const [conditionList, setConditionList] = useState([]);
  const handleAdd = (i) => {
    const newList = [...formList];
    const list = getFieldsValue().fieldConfig?.[index];
    list.splice(i + 1, 0, {});
    newList[index] = list;
    resetFields(`fieldConfig.${index}`);
    setFieldsValue({ fieldConfig: newList });
    setFormList(newList);
  };

  const handleRemove = (i) => {
    const newList = [...formList];
    const list = getFieldsValue().fieldConfig[index];
    list.splice(i, 1);
    newList[index] = list;
    resetFields(`fieldConfig.${index}`);
    setFieldsValue({ fieldConfig: newList });
    setFormList(newList);
  };

  const config = {
    '签入到签出时间间隔(小时)': 10,
    主叫质检次数阈值: 20,
    被叫质检次数阈值: 30,
    '主叫+被叫质检次数阈值': 40,
    主叫质检正常次数阈值: 50,
    被叫质检正常次数阈值: 60,
    '主叫+被叫质检正常次数阈值': 70,
  };

  const handleChangeField = (v, key) => {
    setFieldsValue({
      [key]: v ? config[v] : undefined,
    });
  };

  return (
    <Fragment>
      {formItemList?.map((ele, i) => (
        <div key={i}>
          <div style={{ padding: '0 24px', display: 'flex', alignItems: 'center' }}>
            <Form.Item style={{ marginBottom: 0 }}>
              {getFieldDecorator(`fieldConfig.${index}.${i}.fieldValue`, {
                initialValue: formItemList[i]?.fieldValue || undefined,
                rules: [{ required: true, message: '请选择字段' }],
              })(
                <Select
                  placeholder="请选择"
                  allowClear
                  style={{ width: 250 }}
                  onChange={(v) => handleChangeField(v, `fieldConfig.${index}.${i}.inputValue`)}
                >
                  {fieldList?.map((ele, index) => (
                    <Select.Option value={ele.key} key={ele.key}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <span>：</span>

            <Form.Item style={{ marginBottom: 0 }}>
              {getFieldDecorator(`fieldConfig.${index}.${i}.inputValue`, {
                initialValue: formItemList[i]?.inputValue || '',
                rules: [{ required: true, message: '请输入阈值' }],
              })(<Input placeholder="请输入阈值" allowClear style={{ width: 200 }} />)}
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <div style={{ width: 60, marginLeft: 16 }}>
                <Icon
                  type="plus-circle"
                  style={{ marginRight: 16, color: '#1890ff' }}
                  onClick={() => handleAdd(i)}
                />
                {formItemList?.length > 1 && (
                  <Icon
                    type="minus-circle"
                    style={{ color: '#1890ff' }}
                    onClick={() => handleRemove(i)}
                  />
                )}
              </div>
            </Form.Item>
          </div>
          {formItemList.length > 1 && i !== formItemList.length - 1 && (
            <div style={{ color: '#1890ff' }}>且</div>
          )}
        </div>
      ))}
    </Fragment>
  );
}
