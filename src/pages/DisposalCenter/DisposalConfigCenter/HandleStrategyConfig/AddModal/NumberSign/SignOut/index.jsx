import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useMemo,
  useEffect,
  useRef,
  useContext,
} from 'react';
import { Button, Card, Form, Icon, Select, message, Input, Radio } from 'antd';
import { getSystemConfigListByConfigType } from '@/services/common';
import Configuration from '@/pages/DisposalCenter/DisposalConfigCenter/components/Configuration';
import Context from '../../context';

const Index = forwardRef(
  (
    {
      form,
      form: { getFieldsValue, validateFields, setFieldsValue, resetFields, getFieldDecorator },
    },
    ref,
  ) => {
    const { isDetail, currentRow, defaultConfigData, getDefaultConfigData } = useContext(Context);
    const [operationalList, setOperationalList] = useState([]);
    const [flowQuery, setFlowQuery] = useState(null);

    const configurationRef = useRef(null);

    const getOperationalList = async () => {
      const response = await getSystemConfigListByConfigType({
        configType: 'checkOutConfig',
      });
      if (response.code === 200) {
        setOperationalList(
          response?.data?.map((ele) => {
            return {
              fieldName: ele.name,
              field: ele.value,
            };
          }) || [],
        );
      } else {
        message.error(response?.message);
      }
    };

    const getFieldConfig = (field) => {
      const currentFieldName = operationalList?.find((ele) => ele.field === field)?.fieldName;

      return getDefaultConfigData(defaultConfigData, `签出配置-${currentFieldName}`) || '';
    };

    useImperativeHandle(ref, () => {
      return {
        getFlowQuery: () => {
          return new Promise(async (resolve, reject) => {
            try {
              const result = configurationRef?.current?.handleSubmit();
              if (!result) {
                message.error('获取配置数据失败');
                reject();
                return;
              }
              if (result.valifyStatus === 'EMPTY') {
                message.error('请添加条件！');
                reject();
              } else if (result.valifyStatus === 'SUCCESS') {
                resolve(result.data);
              } else if (result.valifyStatus === 'FAIL') {
                message.error('请填写必填信息！');
                reject();
              }
            } catch (error) {
              message.error('获取配置数据出错');
              reject(error);
            }
          });
        },
      };
    });

    useEffect(() => {
      getOperationalList();
    }, []);

    useEffect(() => {
      if (currentRow?.id) {
        const ruleContext = currentRow?.checkOutRule;
        setFlowQuery(JSON.parse(ruleContext || '{}'));
      }
    }, [currentRow]);

    return (
      <div>
        <Form.Item label="处置状态" required>
          {getFieldDecorator('checkOutState', {
            rules: [{ required: true, message: '请选择处置状态' }],
            initialValue:
              currentRow?.checkOutState ??
              getDefaultConfigData(defaultConfigData, '签出配置-处置状态') ??
              '开启',
          })(
            <Radio.Group disabled={isDetail}>
              {['关闭', '开启', '试运行'].map((ele) => (
                <Radio value={ele}>{ele}</Radio>
              ))}
            </Radio.Group>,
          )}
        </Form.Item>
        <Configuration
          key={JSON.stringify(flowQuery)}
          wrappedComponentRef={configurationRef}
          data={flowQuery}
          optionList={operationalList}
          disabled={isDetail}
          colonRelMode
          /*
              conditionValueFormatter 初始化时、value[?] 的元素离开焦点或清空操作以及 field、rel 任意一个值改变时才会被触发
              conditionValueFormatter 如果不返回或者返回值为 undefined 则忽略返回值（保留原值），否则该返回值还会再次经过内部的格式化器（比如当返回值是 rel 不支持的数据时会自动处理）
              conditionValueFormatter 只有 value[?] 时 triggerValueIndex 参数才会有值（表示是从哪个索引的 value 元素触发的），但 value 参数依然是整体的值
              conditionValueFormatter 的 rel 入参枚举值： 'EQ', 'GREATER', 'GREATER_EQ', 'LESS', 'LESS_EQ', 'IN', 'NOT_IN', 'REG', 'CONTAIN', 'NOTINCLUDED', ':'
            */
          conditionValueFormatter={(field, rel, value, triggerValueIndex) => {
            if (field) {
              return getFieldConfig(field);
            } else {
              return '';
            }
          }}
          conditionValueFormatterTriggeredBy={['init', 'field', 'rel' /*, 'value'*/]}
        />

        <Form.Item label="每日熔断值">
          {getFieldDecorator('checkOutBreak', {
            initialValue:
              currentRow?.checkOutBreak ??
              getDefaultConfigData(defaultConfigData, '签出配置-每日熔断阈值'),
            rules: [
              {
                required: true,
                message: '请输入',
              },
              {
                pattern: /^[0-9]*$/,
                message: '请输入大于等于0的整数',
              },
            ],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
      </div>
    );
  },
);
export default Index;
