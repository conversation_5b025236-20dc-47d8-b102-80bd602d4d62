import React, {
  Fragment,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
  useContext,
} from 'react';
import { Form, Input, message, Select, Collapse } from 'antd';
import SignInAndOut from './SignInAndOut';
import SignIn from './SignIn';
import SignOut from './SignOut';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const { Panel } = Collapse;

const NumberSign = forwardRef(({ form }, ref) => {
  const signOutRef = useRef();

  useImperativeHandle(ref, () => {
    return {
      form,
      getValues: async () => {
        return new Promise((resolve, reject) => {
          form.validateFieldsAndScroll(async (err, values) => {
            if (err) return reject();
            const flowQuery = await signOutRef?.current?.getFlowQuery();
            const { filterWhiteType, ...rest } = values;
            resolve({
              ...rest,
              filterWhiteType: filterWhiteType?.join(','),
              checkOutRule: JSON.stringify(flowQuery),
            });
          });
        });
      },
    };
  });

  return (
    <Form {...formItemLayout}>
      <Collapse defaultActiveKey={['1', '2', '3']}>
        <Panel header="签入签出统一配置" key="1">
          <SignInAndOut form={form} />
        </Panel>
        <Panel header="签入配置" key="2">
          <SignIn form={form} />
        </Panel>
        <Panel header="签出配置" key="3">
          <SignOut ref={signOutRef} form={form} />
        </Panel>
      </Collapse>
    </Form>
  );
});

export default Form.create()(NumberSign);
