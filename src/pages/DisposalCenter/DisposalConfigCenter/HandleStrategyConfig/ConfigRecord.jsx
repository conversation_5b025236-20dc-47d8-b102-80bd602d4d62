import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import { apiPrefix } from '@/pages/DisposalCenter/DisposalConfigCenter/common';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import AddModal from './AddModal';

import { commonColumns } from './constants';

import request from 'ponshine-request';

const Index = (props) => {
  const {
    form,
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState({});
  const serachParams = useRef();

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`${apiPrefix}/numberSigningConfigRecordQuery`, {
      data: { current: pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.dataList || [],
        pagination: {
          total: response.data.totalCount,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      setListData({
        list: [],
        pagination: false,
      });
      message.error(response.msg);
    }
  };

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { updateTime, ...rest } = formValue;
    findTableDataPager({
      ...rest,
      operateStartTime: updateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      operateEndTime: updateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
  };

  useEffect(() => {
    findTableDataPager();
  }, []);

  const columns = [
    ...commonColumns,
    {
      title: '操作',
      dataIndex: 'opt',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: (v, r) => (
        <a
          onClick={() => {
            setCurrentRow({ ...r, title: '详情' });
            setDetailVisible(true);
          }}
        >
          详情
        </a>
      ),
    },
    {
      title: '操作时间',
      dataIndex: 'operateTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '操作用户',
      dataIndex: 'operator',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作内容',
      dataIndex: 'action',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (text) => {
        const obj = {
          0: '新增',
          1: '删除',
          2: '修改',
        };
        return obj?.[text] || '';
      },
    },
  ];

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <Form.Item label="处置策略ID">
              {getFieldDecorator('policyId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="处置策略名称">
              {getFieldDecorator('policyName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,

            <CommonSelect
              configType="handleMethod"
              form={form}
              formItemKey="handleType"
              formItemLabel="处置方式"
              valueKey="name"
              availableData={['号码签约', '号码质检人工核验']}
            />,

            <Form.Item label="数据分类ID">
              {getFieldDecorator('dataClassifyId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="数据分类名称">
              {getFieldDecorator('dataClassifyName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="处置状态">
              {getFieldDecorator('handleState')(
                <Select placeholder="请选择" allowClear mode="multiple">
                  {['关闭', '开启', '试运行']?.map((ele) => (
                    <Select.Option key={ele} value={ele}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
            <Form.Item label="更新日期">
              {getFieldDecorator('updateTime')(
                <DatePicker.RangePicker placeholder="请选择" allowClear format="YYYY-MM-DD" />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
        scroll={{ x: 1200 }}
      />
      {detailVisible && (
        <AddModal
          visible={detailVisible}
          onCancel={() => {
            setDetailVisible(false);
          }}
          currentRow={currentRow}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
