import React from 'react';
import { Tabs, Card } from 'antd';
const { TabPane } = Tabs;
import DataManage from './DataManage';
import DataAccessConfig from './DataAccessConfig';
import DataCategoryConfig from './DataCategoryConfig';
import HandleStrategyConfig from './HandleStrategyConfig';

export default function index() {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab="数据管理" key="1">
          <DataManage />
        </TabPane>
        <TabPane tab="数据接入配置" key="2">
          <DataAccessConfig />
        </TabPane>
        <TabPane tab="数据分类配置" key="3">
          <DataCategoryConfig />
        </TabPane>
        <TabPane tab="处置策略配置" key="4">
          <HandleStrategyConfig />
        </TabPane>
      </Tabs>
    </Card>
  );
}
