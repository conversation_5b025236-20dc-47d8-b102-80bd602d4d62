import {
  pagePhoneShutdownDetail,
  getAllFraudType,
  getShutdownTagListByFraudType,
  getShutdownSubTagListByShutdownTag,
  getOrganizationByUser,
  shutdownSinglePhoneNum,
  resumeSinglePhoneNum,
  queryUserRole,
  forceResumeSinglePhoneNum,
  getAllFraudTypeAll,
  getShutdownTagListByFraudTypeAll,getShutdownSubTagListByShutdownTagAll
} from '@/services/DisposalCenter/fraudPreventionManage';
const defaultState = {};

export default {
  namespace: 'fraudPreventionManage',
  state: defaultState,
  effects: {
    *pagePhoneShutdownDetail({ payload, callback }, { call, put }) {
      const response = yield call(pagePhoneShutdownDetail, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getAllFraudType({ payload, callback }, { call, put }) {
      const response = yield call(getAllFraudType, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getAllFraudTypeAll({ payload, callback }, { call, put }) {
      const response = yield call(getAllFraudTypeAll, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getShutdownTagListByFraudType({ payload, callback }, { call, put }) {
      const response = yield call(getShutdownTagListByFraudType, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getShutdownTagListByFraudTypeAll({ payload, callback }, { call, put }) {
      const response = yield call(getShutdownTagListByFraudTypeAll, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getShutdownSubTagListByShutdownTag({ payload, callback }, { call, put }) {
      const response = yield call(getShutdownSubTagListByShutdownTag, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getShutdownSubTagListByShutdownTagAll({ payload, callback }, { call, put }) {
      const response = yield call(getShutdownSubTagListByShutdownTagAll, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *shutdownSinglePhoneNum({ payload, callback }, { call, put }) {
      const response = yield call(shutdownSinglePhoneNum, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *resumeSinglePhoneNum({ payload, callback }, { call, put }) {
      const response = yield call(resumeSinglePhoneNum, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *queryUserRole({ payload, callback }, { call, put }) {
      const response = yield call(queryUserRole, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *forceResumeSinglePhoneNum({ payload, callback }, { call, put }) {
      const response = yield call(forceResumeSinglePhoneNum, payload);
      if (!response) return;
      if (callback) callback(response);
    },

  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
