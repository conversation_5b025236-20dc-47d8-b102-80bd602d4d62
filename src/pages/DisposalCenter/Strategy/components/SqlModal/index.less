.wrapSql {
  padding: 0;
  margin: 0;
  list-style: none;
  border: 1px solid #ccc;
  border-radius: 2px;
  li {
    padding: 0;
    margin: 0;
    list-style: none;
    min-height: 48px;
    display: flex;
    &:not(:nth-last-child(1)) {
      border-bottom: 1px solid #ccc;
    }
    .label {
      width: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1px solid #ccc;
    }
    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      .status {
        border: 1px solid #333;
        width: 80px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 4px;
      }
    }
    div {
      box-sizing: border-box;
      padding: 8px;
    }
  }
}
