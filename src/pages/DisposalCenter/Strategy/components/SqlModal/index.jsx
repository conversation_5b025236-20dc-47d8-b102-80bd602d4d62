import React, { Component } from "react";
import { Button, Modal } from "antd";
import styled from "./index.less";

export default class SqlModal extends Component {
  handleOk = () => {
    this.props.handleBack();
  };
  render() {
    const { isModalVisible, record } = this.props;
    return (
      <Modal
        title="查看筛选SQL"
        visible={isModalVisible}
        width="720px"
        closable={false}
        footer={
          <Button type="primary" onClick={() => this.handleOk()}>
            返回
          </Button>
        }
      >
        <ul className={styled.wrapSql}>
          <li>
            <div className={styled.label}>策略id</div>
            <div className={styled.content}>{record.strategyId}</div>
          </li>
          <li>
            <div className={styled.label}>策略名称</div>
            <div className={styled.content}>{record.strategyName}</div>
          </li>
          <li>
            <div className={styled.label}>筛选SQL</div>
            <div
              style={{
                background: record.state == 0 ? "#ffbfbf" : "#bfffbf",
              }}
              className={styled.content}
            >
              <span className={styled.status}>
                {record.state == 0 ? "未启用" : "已启用"}
              </span>
              <div className={styled.sql}>{record.strategyCondition}</div>
            </div>
          </li>
        </ul>
      </Modal>
    );
  }
}
