import React from "react";

import { Form, Input, Radio, Switch, InputNumber } from "antd";

class MySwitch extends React.Component {
  handleChange = checked => {
    const { onChange } = this.props;
    if (checked) {
      onChange(1);
    } else {
      onChange(0);
    }
  };
  render() {
    const { value } = this.props;
    const checked = Number(value) !== 0;

    return <Switch checked={checked} onChange={this.handleChange} />;
  }
}

const NewStrategyForm = Form.create()(
  class extends React.Component {
    constructor(props) {
      super(props);

      const { editingStrategy } = props;

      let strategyType = editingStrategy
        ? editingStrategy.probability
          ? "byPossibility"
          : editingStrategy.noIntercept
          ? "all"
          : "byCount"
        : "byCount";

      this.state = {
        strategyType
      };
    }

    // shouldComponentUpdate(nextProps, nextState) {
    //   // props不会改变
    //   if (this.state !== nextState) {
    //     return true;
    //   }

    //   return false;
    // }

    handleChangeType = e => {
      this.setState({
        strategyType: e.target.value
      });
    };

    render() {
      const { handleChangeType } = this;
      const { strategyType } = this.state;
      const { mode } = this.props;
      const editingStrategy = this.props.editingStrategy || {};
      const { getFieldDecorator, getFieldValue } = this.props.form;

      const formItemLayout = {
        labelCol: {
          span: 10,
          offset: 4
        },
        wrapperCol: {
          span: 6
        }
      };

      return (
        <React.Fragment>
          <Radio.Group
            buttonStyle="solid"
            value={strategyType}
            style={{ marginLeft: 105, marginBottom: 10 }}
            onChange={handleChangeType}
          >
            <Radio.Button value="byCount">按次数限呼</Radio.Button>
            <Radio.Button value="byPossibility">按概率限呼</Radio.Button>
            <Radio.Button value="all">全放通</Radio.Button>
          </Radio.Group>
          <Form {...formItemLayout} labelAlign="left" colon={false}>
            <Form.Item label="策略名称">
              {getFieldDecorator("name", {
                initialValue: editingStrategy.name || "",
                rules: [
                  {
                    required: true,
                    whitespace: true,
                    message: "请输入策略名称！"
                  },
                  {
                    max: 20,
                    message: "策略名称的长度不能超过20！"
                  }
                ]
              })(<Input disabled={mode==="auto"} />)}
            </Form.Item>

            {strategyType === "byPossibility" ? (
              <Form.Item label="每个主叫号码拦截概率">
                {getFieldDecorator("probability", {
                  initialValue:
                    editingStrategy.probability &&
                    editingStrategy.probability !== -1
                      ? editingStrategy.probability
                      : 100,
                  rules: [
                    {
                      required: true,
                      message: "请输入拦截概率！"
                    }
                  ]
                })(
                  <InputNumber
                    min={0}
                    max={100}
                    formatter={value => `${value}%`}
                    parser={value => value.replace("%", "")}
                  />
                )}
              </Form.Item>
            ) : strategyType === "all" ? null : (
              <React.Fragment>
                <Form.Item label="每个主叫号码单日限呼次数">
                  {getFieldDecorator("dayNum", {
                    initialValue:
                      editingStrategy.dayNum &&
                      editingStrategy.dayNum !== -1 &&
                      editingStrategy.dayNum
                  })(<InputNumber min={0} max={999} />)}
                </Form.Item>

                <Form.Item label="每个主叫号码单小时限呼次数">
                  {getFieldDecorator("hourNum", {
                    initialValue:
                      editingStrategy.hourNum &&
                      editingStrategy.hourNum !== -1 &&
                      editingStrategy.hourNum
                  })(<InputNumber min={0} max={999} />)}
                </Form.Item>
                <Form.Item label="每个主叫号码同一被叫单日限呼次数">
                  {getFieldDecorator("sameNum", {
                    initialValue:
                      editingStrategy.sameNum &&
                      editingStrategy.sameNum !== -1 &&
                      editingStrategy.sameNum
                  })(<InputNumber min={0} max={999} />)}
                </Form.Item>
              </React.Fragment>
            )}
            {strategyType !== "all" && (
              <React.Fragment>
                <Form.Item label="被叫是浙江联通号码放通">
                  {getFieldDecorator("zjlt", {
                    // valuePropName: "checked",
                    initialValue: editingStrategy.zjlt === 1 ? 1 : 0
                  })(<MySwitch />)}
                </Form.Item>
                <Form.Item label="被叫是浙江移动号码放通">
                  {getFieldDecorator("zjyd", {
                    // valuePropName: "checked",
                    initialValue: editingStrategy.zjyd === 1 ? 1 : 0
                  })(<MySwitch />)}
                </Form.Item>
                <Form.Item label="被叫是浙江电信号码放通">
                  {getFieldDecorator("zjdx", {
                    // valuePropName: "checked",
                    initialValue: editingStrategy.zjdx === 1 ? 1 : 0
                  })(<MySwitch />)}
                </Form.Item>
              </React.Fragment>
            )}
          </Form>
        </React.Fragment>
      );
    }
  }
);

export default NewStrategyForm;
