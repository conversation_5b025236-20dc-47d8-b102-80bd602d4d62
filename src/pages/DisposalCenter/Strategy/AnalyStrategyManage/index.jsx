import React, { Component } from 'react';
import { Table, But<PERSON>, Card, message, Modal } from 'antd';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { DndProvider, DragSource, DropTarget } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import arrayMove from 'array-move';

import SqlModal from '../components/SqlModal';
import AddStrategyModal from './childComp/AddStrategyModal';
import UpdateStrategyModal from './childComp/UpdateStrategyModal';
import Details from './detail';
import styled from './index.less';
import { Licensee } from 'ponshine';

let dragingIndex = -1;

const infoColumns = [
  {
    dataIndex: 'strategyName',
    key: 'strategyName',
    title: '策略名称',
    ellipsis: true,
    width: 200,
    align: 'center',
  },
  {
    dataIndex: 'strategyId',
    key: 'strategyId',
    title: '模型运行周期',
    ellipsis: true,
    width: 200,
    align: 'center',
    render: (text) => {
      return { BH: '分钟', BM: '小时', BD: '天' }[text.slice(0, 2)];
    },
  },
  {
    dataIndex: 'phoneGrade',
    key: 'phoneGrade',
    title: '策略处置方式',
    ellipsis: true,
    width: 200,
    align: 'center',
    render: (text) => {
      return ['语音短信单停', '非实名双停', '非实名单停', '不处置'][text - 1];
    },
  },
  {
    dataIndex: 'cleanDay',
    key: 'cleanDay',
    title: '数据加白周期',
    ellipsis: true,
    width: 200,
    align: 'center',
  },
  {
    dataIndex: 'content',
    key: 'content',
    title: '策略说明',
    ellipsis: true,
    width: 200,
    align: 'center',
  },
];

class BodyRow extends Component {
  render() {
    const { isOver, connectDragSource, connectDropTarget, moveRow, ...restProps } = this.props;
    const style = { ...restProps.style, cursor: 'move' };

    let { className } = restProps;
    if (isOver) {
      if (restProps.index > dragingIndex) {
        className += ' drop-over-downward';
      }
      if (restProps.index < dragingIndex) {
        className += ' drop-over-upward';
      }
    }

    return connectDragSource(
      connectDropTarget(<tr {...restProps} className={className} style={style} />),
    );
  }
}

const sourceSpec = {
  beginDrag(props) {
    dragingIndex = props.index;
    return {
      index: props.index,
    };
  },
};

const dragSource = DragSource('row', sourceSpec, (connect) => {
  return {
    connectDragSource: connect.dragSource(),
  };
});

const bodyRow = dragSource(BodyRow);

const targetSpec = {
  drop(props, monitor) {
    const sourceIndex = monitor.getItem().index;
    const targetIndex = props.index;
    if (sourceIndex === targetIndex) return;
    props.moveRow(sourceIndex, targetIndex);
    monitor.getItem().index = targetIndex;
  },
};

const dropTarget = DropTarget('row', targetSpec, (connect, monitor) => {
  return { connectDropTarget: connect.dropTarget(), isOver: monitor.isOver() };
});

const DragableBodyRow = dropTarget(bodyRow);

class AnalyStrategyManage extends Component {
  state = {
    isEqual: true,
    isCanSort: false,
    addVisibleStrategy: false,
    updateVisibleStrategy: false,
    isVisibleSql: false,
    strategyRecord: {},
  };

  componentDidMount() {
    this.getAnalyStrategyList();
  }
  getAnalyStrategyList() {
    this.props.dispatch({
      type: 'analyStrategyManage/getAnalyStrategyList',
      callback: () => {
        this.setState({
          isEqual: true,
          isCanSort: false,
        });
      },
    });
  }
  handleSortStrtegy = () => {
    let isCanSort = this.state.isCanSort;
    if (isCanSort) {
      this.handleReset();
    }
    this.setState({
      isCanSort: !this.state.isCanSort,
    });
  };
  moveRow = (dragIndex, hoverIndex) => {
    const { strategyList } = this.props;
    if (dragIndex !== hoverIndex) {
      const newData = arrayMove([].concat(strategyList), dragIndex, hoverIndex).filter(
        (el) => !!el,
      );

      this.props.dispatch({
        type: 'analyStrategyManage/saveStrategyList',
        payload: newData,
      });

      this.compareData();
    }
  };
  handleReset = () => {
    this.props.dispatch({
      type: 'analyStrategyManage/saveStrategyList',
      payload: this.props.catchStrategy,
    });
    this.setState({ isEqual: true });
  };
  handleSave = () => {
    Modal.confirm({
      title: '提示',
      content: '确定变更优先级?',
      onOk: () => {
        const strategyIds = this.props.strategyList.reduce((pre, cur) => {
          pre.push(cur.strategyId);
          return pre;
        }, []);

        this.props.dispatch({
          type: 'analyStrategyManage/updateLevel',
          payload: strategyIds,
          callback: (response) => {
            if (response.code !== 200) {
              return message.error(response.message);
            }
            message.success(response.message);
            this.getAnalyStrategyList();
          },
        });
      },
    });
  };
  // 比对数据
  compareData = () => {
    const { strategyList, catchStrategy } = this.props;

    let isEqual = true;
    for (let i = 0; i < strategyList.length; i++) {
      let source = strategyList[i];
      let origin = catchStrategy[i];

      if (source.strategyId !== origin.strategyId) {
        isEqual = false;
        break;
      }
    }
    this.setState({
      isEqual,
    });
  };
  showAddStrategy = () => {
    this.addRef.resetStrategyFrom();
    this.setState({
      addVisibleStrategy: true,
    });
  };
  addStrategy = (values) => {
    this.props.dispatch({
      type: 'analyStrategyManage/add',
      payload: values,
      callback: (response) => {
        if (response.code !== 200) {
          return message.error(response.message);
        }
        message.success(response.message);
        this.setState({
          addVisibleStrategy: false,
        });
        this.getAnalyStrategyList();
      },
    });
  };
  showInfo = (record) => {
    this.setState({
      infoVisible: true,
      infoRecord: record,
    });
  };
  showUpdateStrategy = (record) => {
    this.updateRef.updateStrategyFrom(record);
    this.setState({
      updateVisibleStrategy: true,
    });
  };
  updateStrategy = (values) => {
    this.props.dispatch({
      type: 'analyStrategyManage/update',
      payload: values,
      callback: (response) => {
        if (response.code !== 200) {
          return message.error(response.message);
        }
        message.success(response.message);
        this.setState({
          updateVisibleStrategy: false,
        });
        this.getAnalyStrategyList();
      },
    });
  };
  handleToHistory = (record) => {
    this.props.dispatch(
      routerRedux.push({
        pathname: '/disposalCenter/strategyHistory',
        query: {
          strategyId: record.strategyId,
        },
      }),
    );
  };
  showCheckSql = (record) => {
    this.setState({
      isVisibleSql: true,
      strategyRecord: record,
    });
  };
  columns = [
    {
      title: '优先级',
      align: 'center',
      dataIndex: 'index',
      key: 'index',
      width: '80px',
      render: (text, record) => <span style={{ fontWeight: '600' }}>{`${record.index + 1}`}</span>,
    },
    {
      title: '策略ID',
      align: 'center',
      dataIndex: 'strategyId',
      key: 'strategyId',
    },
    {
      title: '策略名称',
      align: 'center',
      dataIndex: 'strategyName',
      key: 'strategyName',
    },
    {
      title: '策略处置方式',
      align: 'center',
      dataIndex: 'phoneGrade',
      key: 'phoneGrade',
      render: (text, record) => {
        return ['语音短信单停', '非实名双停', '非实名单停', '不处置'][record.phoneGrade - 1];
      },
    },
    {
      title: '数据加白周期(天)',
      align: 'center',
      dataIndex: 'cleanDay',
      key: 'cleanDay',
      render(text) {
        return text === 0 ? '无需清除' : text;
      },
    },
    // {
    //   title: '策略白名单组',
    //   align: 'center',
    //   dataIndex: 'whitelistId',
    //   key: 'whitelistId',
    // },
    {
      title: '创建时间',
      align: 'center',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '最近更新时间',
      align: 'center',
      dataIndex: 'updateTime',
      key: 'updateTime',
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'action',
      key: 'action',
      width: '280px',
      render: (text, record) => {
        // const { buttonsPermission } = this.props;
        // const buttonsPermission = [];
        // const recordPermission = buttonsPermission.some((item) => item.nodeId === 401202);
        const recordPermission = true;
        // const updatePermission = buttonsPermission.some((item) => item.nodeId === 401203);
        const updatePermission = true;
        return (
          <>
            {/* <Button onClick={() => this.showCheckSql(record)} type="link">
              查看筛选SQL
            </Button> */}
            <Licensee license="AnalyStrategyManage_getAllStrategy">
              <Button onClick={() => this.showInfo(record)} type="link">
                详情
              </Button>
            </Licensee>
            <Licensee license="AnalyStrategyManage_changeStrategy">
              {updatePermission && (
                <Button onClick={() => this.showUpdateStrategy(record)} type="link">
                  变更
                </Button>
              )}
            </Licensee>
            <Licensee license="AnalyStrategyManage_getStrategyChangeRecord">
              {recordPermission && (
                <Button onClick={() => this.handleToHistory(record)} type="link">
                  变更记录
                </Button>
              )}
            </Licensee>
          </>
        );
      },
    },
  ];
  render() {
    const {
      isEqual,
      isCanSort,
      isVisibleSql,
      strategyRecord,
      addVisibleStrategy,
      updateVisibleStrategy,
    } = this.state;

    const {
      strategyList,
      strategyLoading,
      addLoading,
      updateLoading,
      // buttonsPermission,
      // menusPermission,
    } = this.props;
    // const buttonsPermission = [];
    // const menusPermission = [];

    // const addPermission = buttonsPermission.some((item) => item.nodeId === 401204);
    const addPermission = true;
    // const indexPermission = buttonsPermission.some((item) => item.nodeId === 401205);
    const indexPermission = true;
    // const whiteGroupManagement = menusPermission.some((item) => item.nodeId === 401206);
    const whiteGroupManagement = false;

    return (
      <Card>
        <div className={styled.wrapControllerRow}>
          {isCanSort && (
            <>
              <span>操作提示：选中要调整的策略并拖动，调整上下顺序</span>
              <Button disabled={isEqual} onClick={() => this.handleReset()}>
                放弃变更
              </Button>
              <Button disabled={isEqual} onClick={() => this.handleSave()}>
                确认变更
              </Button>
            </>
          )}
          {/* {indexPermission && ( */}
          <Licensee license="AnalyStrategyManage_updateStrategyPriorityLevel">
            <Button
              type={isCanSort ? 'primary' : 'default'}
              onClick={() => this.handleSortStrtegy()}
            >
              调整优先级
            </Button>
          </Licensee>
          {/* )} */}
          {whiteGroupManagement && (
            <Button
              type="primary"
              onClick={() =>
                this.props.dispatch(
                  routerRedux.push({
                    pathname: '/strategy/whiteGroupManagement',
                  }),
                )
              }
            >
              策略白名单组管理
            </Button>
          )}
          <Licensee license="AnalyStrategyManage_addStrategy">
            {addPermission && (
              <Button type="primary" onClick={() => this.showAddStrategy()}>
                策略添加
              </Button>
            )}
          </Licensee>
          <span style={{ marginLeft: '12px' }}>
            共<span className={styled.count}>{strategyList.length}</span>
            条策略
          </span>
        </div>
        {isCanSort ? (
          <DndProvider backend={HTML5Backend}>
            <Table
              className={styled.wrapSortableTable}
              loading={strategyLoading}
              bordered
              size="small"
              pagination={false}
              dataSource={strategyList}
              columns={this.columns}
              rowKey="strategyId"
              components={{
                body: {
                  row: DragableBodyRow,
                },
              }}
              onRow={(record, index) => ({
                index,
                moveRow: this.moveRow,
              })}
            />
          </DndProvider>
        ) : (
          <Table
            className={styled.wrapSortableTable}
            loading={strategyLoading}
            bordered
            size="small"
            pagination={false}
            dataSource={strategyList}
            columns={this.columns}
            rowKey="strategyId"
          />
        )}

        <AddStrategyModal
          wrappedComponentRef={(modal) => {
            this.addRef = modal;
          }}
          onCancel={() => this.setState({ addVisibleStrategy: false })}
          onConfirmOk={this.addStrategy}
          isModalVisible={addVisibleStrategy}
          loading={addLoading}
        ></AddStrategyModal>

        <UpdateStrategyModal
          wrappedComponentRef={(modal) => {
            this.updateRef = modal;
          }}
          onCancel={() => this.setState({ updateVisibleStrategy: false })}
          onConfirmOk={this.updateStrategy}
          isModalVisible={updateVisibleStrategy}
          loading={updateLoading}
        ></UpdateStrategyModal>

        <Details
          visible={this.state.infoVisible}
          onClose={() => {
            this.setState({
              infoVisible: false,
              infoRecord: {},
            });
          }}
          columns={infoColumns}
          details={this.state.infoRecord || {}}
        ></Details>

        <SqlModal
          record={strategyRecord}
          isModalVisible={isVisibleSql}
          handleBack={() => this.setState({ isVisibleSql: false })}
        ></SqlModal>
      </Card>
    );
  }
}
const mapStateToProps = ({ user, analyStrategyManage, loading }) => {
  return {
    // buttonsPermission: user.buttonsPermission,
    menusPermission: user.menusPermission,
    strategyList: analyStrategyManage.strategyList,
    catchStrategy: analyStrategyManage.catchStrategy,
    strategyLoading: loading.effects['analyStrategyManage/getAnalyStrategyList'],
    addLoading: loading.effects['analyStrategyManage/add'],
    updateLoading: loading.effects['analyStrategyManage/update'],
  };
};
export default connect(mapStateToProps)(AnalyStrategyManage);
