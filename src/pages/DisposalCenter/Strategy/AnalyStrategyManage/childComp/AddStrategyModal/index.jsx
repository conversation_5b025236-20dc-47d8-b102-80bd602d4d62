import React, { Component } from 'react';
import { Modal, Form, Input, Select, InputNumber, Button } from 'antd';
import StandardTable from '@/components/StandardTable';
import styled from './index.less';
import { connect } from 'dva';
import { reject } from 'lodash';

const particleList = [
  {
    label: '分钟',
    value: 'BM',
  },
  {
    label: '小时',
    value: 'BH',
  },
  {
    label: '天',
    value: 'BD',
  },
];
const phoneGradeList = [
  {
    label: '语音短信单停',
    value: '1',
  },
  {
    label: '非实名双停',
    value: '2',
  },
  {
    label: '非实名单停',
    value: '3',
  },
  {
    label: '不处置',
    value: '4',
  },
  // {
  //   label: '外呼核验',
  //   value: '5',
  // },
];

class StrategyModal extends Component {
  state = {
    isVisibleConfirmModal: false,
    confirmValues: {},
    isSelectModalVisible: false,
    selectedRows: [],
  };

  handleFromModalOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.setState({
          isVisibleConfirmModal: true,
          confirmValues: values,
        });
      }
    });
  };
  // 最后的确认添加
  handleConfirmOk = () => {
    this.props.onConfirmOk(this.state.confirmValues);
  };

  componentWillReceiveProps = (nextProps) => {
    if (!nextProps.isModalVisible) {
      this.setState({
        isVisibleConfirmModal: false,
      });
    }
  };

  // 重置
  resetStrategyFrom = () => {
    this.props.form.resetFields();
  };

  // 赋值
  updateStrategyFrom = (record) => {
    this.props.form.setFieldsValue({
      strategyName: record.strategyName,
      particle: 'BH',
      phoneGrade: record.phoneGrade,
      cleanDay: record.cleanDay,
    });
  };
  columns = [
    {
      title: '策略白名单组号',
      dataIndex: 'whitelistId',
      key: 'whitelistId',
      align: 'center',
      width: 150,
    },
    {
      title: '策略白名单组名',
      dataIndex: 'whitelistName',
      key: 'whitelistName',
      align: 'center',
      width: 150,
    },
    {
      title: '策略ID',
      dataIndex: 'strategyIds',
      key: 'strategyIds',
      align: 'center',
      width: 150,
    },
  ];
  handleSelectOk = () => {
    this.setState({ isSelectModalVisible: false });
    this.props.form.setFieldsValue({
      whitelistId: this.state.selectedRows.whitelistId,
    });
  };
  getTable = (params) => {
    this.props.dispatch({
      type: 'analyStrategyManage/getWhiteGroup',
      payload: {
        currentPage: 1,
        pageSize: 10,
        ...params,
      },
    });
  };
  handleChoose = () => {
    this.setState({ isSelectModalVisible: true });
    this.getTable();
  };
  /**分页 */
  handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    this.getTable({ currentPage: current, pageSize });
  };
  render() {
    const { isVisibleConfirmModal, confirmValues, isSelectModalVisible } = this.state;
    const {
      isModalVisible,
      onCancel,
      form,
      loading,
      tableLoading,
      analyStrategyManage: { getWhiteGroup },
    } = this.props;
    const { getFieldDecorator } = form;
    return (
      <>
        <Modal
          visible={isModalVisible}
          closable={false}
          okText="确认添加"
          onCancel={onCancel}
          onOk={() => this.handleFromModalOk()}
          width={560}
          destroyOnClose
        >
          <Form labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
            <Form.Item label="策略名称">
              {getFieldDecorator('strategyName', {
                rules: [
                  {
                    required: true,
                    message: '请输入策略名称!',
                  },
                  {
                    min: 1,
                    max: 30,
                    message: '策略名称最短1个字符，最长30个字符！',
                  },
                ],
              })(<Input maxLength={30} placeholder="请输入策略名称" />)}
            </Form.Item>
            <Form.Item label="数据运行周期">
              {getFieldDecorator('particle', {
                rules: [
                  {
                    required: true,
                    message: '请选择数据运行周期',
                  },
                ],
              })(
                <Select placeholder="请选择数据运行周期">
                  {particleList.map((item) => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item label="策略处置方式">
              {getFieldDecorator('phoneGrade', {
                rules: [
                  {
                    required: true,
                    message: '请选择策略处置方式',
                  },
                ],
              })(
                <Select placeholder="请选择策略处置方式">
                  {phoneGradeList.map((item) => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>

            <Form.Item label="数据加白周期(天)">
              {getFieldDecorator('cleanDay', {
                rules: [
                  {
                    required: true,
                    message: '请输入数据加白周期',
                  },
                ],
              })(
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={9999}
                  placeholder="请输入1-9999,不清除填0"
                ></InputNumber>,
              )}
            </Form.Item>
            <Form.Item label="策略说明">
              {getFieldDecorator('content', {
                rules: [
                  {
                    required: true,
                    message: '策略说明',
                  },
                ],
              })(
                <Input.TextArea
                  style={{ width: '100%' }}
                  autoSize={{ minRows: 3 }}
                  placeholder="请输入策略说明"
                ></Input.TextArea>,
              )}
            </Form.Item>
            {/* <Form.Item label="策略白名单组">
              {getFieldDecorator('whitelistId', {
                rules: [
                  {
                    required: true,
                    message: '请选择策略白名单组!',
                  },
                ],
              })(<Input readOnly placeholder="请选择策略白名单组" style={{ width: '70%' }} />)}
              <Button type="primary" style={{ marginLeft: 10 }} onClick={this.handleChoose}>
                选择
              </Button>
            </Form.Item> */}
          </Form>
        </Modal>
        <Modal
          visible={isSelectModalVisible}
          closable={false}
          title="策略名单组"
          okText="确认选择"
          onCancel={() => this.setState({ isSelectModalVisible: false })}
          onOk={() => this.handleSelectOk()}
          width={1000}
          destroyOnClose
        >
          <StandardTable
            columns={this.columns}
            bordered
            loading={tableLoading}
            showSelectCount={false}
            rowSelection={{
              columnTitle: '选择',
              columnWidth: 40,
              type: 'radio',
              // getCheckboxProps: (record) => {
              //   return {
              //     disabled:
              //       record.whitelistName === "仅过滤本策略" &&
              //       record.whitelistId === "GW01",
              //   };
              // },
              onChange: (t, record) => this.setState({ selectedRows: record[0] }),
            }}
            tableAlert={false}
            data={{
              list: getWhiteGroup?.items || [],
              pagination: {
                total: getWhiteGroup?.totalNum,
                pageSizeOptions: ['5', '10', '15', '20'],
                showSizeChanger: true,
                showQuickJumper: true,
                defaultPageSize: 5,
                showTotal: (total, range) => {
                  return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
                },
              },
            }}
            scroll={{
              x: '100%',
            }}
            onChange={this.handleTableChange}
          />
        </Modal>
        <Modal
          okText="确认添加"
          title="即将添加如下策略"
          closable={false}
          visible={isVisibleConfirmModal}
          onOk={() => this.handleConfirmOk()}
          okButtonProps={{
            loading: loading,
          }}
          width={560}
          onCancel={() => this.setState({ isVisibleConfirmModal: false })}
        >
          <ul className={styled.wrapStrategyList}>
            <li>策略名称：{confirmValues.strategyName}</li>
            <li>
              数据生成颗粒：
              {confirmValues.particle == 'BD'
                ? '天'
                : confirmValues.particle == 'BH'
                ? '小时'
                : '分钟'}
            </li>
            <li>
              策略处置方式：
              {phoneGradeList.find((item) => item.value === confirmValues.phoneGrade)?.label}
            </li>

            {confirmValues.cleanDay == 0 ? (
              <li>无需清除</li>
            ) : (
              <li>数据加白周期：{confirmValues.cleanDay}</li>
            )}
            <li style={{ display: 'flex' }}>
              策略说明：
              <span style={{ whiteSpace: 'pre-wrap' }}>{confirmValues.content}</span>
            </li>

            {/* <li>策略白名单组：{confirmValues.whitelistId}</li> */}
          </ul>
          <div className={styled.confirmTips}>确认添加吗？</div>
        </Modal>
      </>
    );
  }
}
export default connect(({ loading, analyStrategyManage }) => ({
  analyStrategyManage,
  tableLoading: loading.effects['analyStrategyManage/getWhiteGroup'],
}))(Form.create()(StrategyModal));
