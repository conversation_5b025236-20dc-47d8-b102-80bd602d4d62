import React, { Component } from 'react';
import { Card, Table, Button, Icon, Empty } from 'antd';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { onEnterPage } from '@/utils/openTab';

import SqlModal from '../components/SqlModal';

let unlistenHistory = null;

class AnalyStrategyManage extends Component {
  state = {
    strategyId: '',
    isVisibleSql: false,
    strategyRecord: {},
    pagination: {
      total: 0,
      pageSize: 10,
      current: 1,
      pageSizeOptions: ['5', '10', '15', '20'],
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => {
        return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
      },
    },
  };
  componentDidMount() {
    const initTable = () => {
      const strategyId = this.props.location?.query?.strategyId;
      if (!strategyId) {
        return this.handleBack();
      }
      this.setState(
        {
          strategyId,
        },
        () => {
          this.getStrategyHistory();
        },
      );
    };
    initTable();
    unlistenHistory = onEnterPage(this.props, () => {
      initTable();
    });
  }
  componentWillUnmount() {
    if (unlistenHistory) unlistenHistory();
  }
  getStrategyHistory = () => {
    const {
      strategyId,
      pagination: { current, pageSize },
    } = this.state;
    this.props.dispatch({
      type: 'analyStrategyManage/getStrategyHistory',
      payload: {
        strategyId,
        rows: pageSize,
        page: current,
      },
      callback: (response) => {
        this.setState((preState) => {
          return {
            pagination: {
              ...preState.pagination,
              total: response.totalNum,
            },
          };
        });
      },
    });
  };
  handleCheckSql = (record) => {
    this.setState({
      isVisibleSql: true,
      strategyRecord: record,
    });
  };
  handleBack = () => {
    this.props.dispatch({
      type: 'analyStrategyManage/resetStrategyHistory',
    });
    this.props.dispatch(routerRedux.goBack());
  };

  handleTableChange = (pagination) => {
    this.setState(
      {
        pagination,
      },
      this.getStrategyHistory,
    );
  };

  columns = [
    {
      title: '策略ID',
      dataIndex: 'strategyId',
      key: 'strategyId',
      align: 'center',
    },
    {
      title: '策略名称',
      dataIndex: 'strategyName',
      key: 'strategyName',
      align: 'center',
    },
    {
      title: '策略处置方式',
      dataIndex: 'phoneGrade',
      key: 'phoneGrade',
      align: 'center',
      render: (text, record) => {
        return ['语音短信单停', '非实名双停', '非实名单停', '不处置'][record.phoneGrade - 1];
      },
    },
    {
      title: '数据加白周期(天)',
      dataIndex: 'cleanDay',
      key: 'cleanDay',
      align: 'center',
      render(text) {
        return text === 0 ? '无需清除' : text;
      },
    },
    // {
    //   title: '筛选SQL',
    //   dataIndex: 'filterSql',
    //   key: 'filterSql',
    //   align: 'center',
    //   render: (text, record) => {
    //     return (
    //       <Button onClick={() => this.handleCheckSql(record)} type="link">
    //         查看筛选sql
    //       </Button>
    //     );
    //   },
    // },
    // {
    //   title: '策略白名单组',
    //   dataIndex: 'whitelistId',
    //   key: 'whitelistId',
    //   align: 'center',
    // },
    {
      title: '操作时间',
      dataIndex: 'changeTime',
      key: 'changeTime',
      align: 'center',
    },
    {
      title: '操作账号',
      dataIndex: 'changeUser',
      key: 'changeUser',
      align: 'center',
    },
    {
      title: '操作类型',
      dataIndex: 'changeType',
      key: 'changeType',
      align: 'center',
      width: '280px',
      render: (text, recored) => {
        return text === 0 ? '修改' : '新增';
      },
    },
  ];
  render() {
    const { isVisibleSql, strategyRecord, pagination, strategyId } = this.state;
    const { changeRecord, loading } = this.props;
    return (
      <>
        <Card>
          <Button onClick={() => this.handleBack()} style={{ marginBottom: '12px' }}>
            <Icon type="left" />
            返回
          </Button>
          <Table
            bordered
            size="middle"
            loading={loading}
            rowKey="changeTime"
            columns={this.columns}
            dataSource={changeRecord}
            pagination={pagination}
            onChange={this.handleTableChange}
            locale={{
              emptyText: <Empty description={`未查询到策略${strategyId}的历史记录`} />,
            }}
          ></Table>
        </Card>
        <SqlModal
          record={strategyRecord}
          isModalVisible={isVisibleSql}
          handleBack={() => this.setState({ isVisibleSql: false })}
        ></SqlModal>
      </>
    );
  }
}

const mapStateToProps = ({ analyStrategyManage, loading }) => {
  return {
    changeRecord: analyStrategyManage.changeRecord,
    loading: loading.effects['analyStrategyManage/getStrategyHistory'],
  };
};
export default connect(mapStateToProps)(AnalyStrategyManage);
