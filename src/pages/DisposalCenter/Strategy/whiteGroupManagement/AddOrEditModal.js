import React from "react";
import { Modal, Form, Input, Button, Checkbox, Spin, message } from "antd";
import styles from "./index.less";
import { connect } from "dva";

const CheckboxGroup = Checkbox.Group;

function AddOrEditModal(props) {
  const {
    visible,
    onCancel,
    type,
    data,
    form: { getFieldDecorator, setFieldsValue },
    analyStrategyManage,
    tableLoading,
  } = props;
  const [visibleChoose, setVisibleChoose] = React.useState(false);
  const [checkedList, setCheckedList] = React.useState([]);
  const onCheckAllChange = (index) => {
    setCheckedList(
      index === "all"
        ? analyStrategyManage?.strategyIds?.data
        : index === "noall"
        ? []
        : analyStrategyManage?.strategyIds?.data?.filter(
            (v) => !checkedList.some((x) => x === v)
          )
    );
  };
  const onChange = (list) => {
    setCheckedList(list);
  };
  React.useEffect(() => {
    setCheckedList(data?.strategyIds?.split("|"));
  }, [visible, data]);
  const handleSubmit = (e) => {
    e.preventDefault();
    props.form.validateFields((err, values) => {
      if (err) return;
      Modal.confirm({
        title: "即将添加如下策略白名单组名",
        okText: type === "ADD" ? "确认添加" : "确认变更",
        cancelText: "取消",
        content: (
          <div>
            <div style={{ marginBottom: 10 }}>
              策略白名单组名：{values.whitelistName}
            </div>
            <div>策略ID：{values.strategyIds}</div>
          </div>
        ),
        onOk: async () => {
          const params = {
            ...values,
            strategyIds: values?.strategyIds,
            whitelistId: type === "ADD" ? undefined : data?.whitelistId,
          };
          props.dispatch({
            type: `analyStrategyManage/${
              type === "ADD" ? "saveWhiteGroup" : "updateWhiteGroup"
            }`,
            payload: params,
            callback: (res) => {
              if (res.code === 200) {
                message.success(res.message);
                props.Refresh();
              } else {
                message.error(res.message);
                props.Refresh();
              }
            },
          });
          onCancel();
          setCheckedList([]);
        },
      });
    });
  };
  return (
    <React.Fragment>
      <Modal
        visible={visible}
        closable={false}
        okText={type === "ADD" ? "确认添加" : "确认变更"}
        onCancel={() => {
          onCancel();
          setCheckedList([]);
        }}
        destroyOnClose
        onOk={handleSubmit}
        width={500}
      >
        <Form
          labelCol={{ span: 7 }}
          wrapperCol={{ span: 17 }}
          labelAlign="left"
        >
          <Form.Item label="策略白名单组名">
            {getFieldDecorator("whitelistName", {
              initialValue:
                type === "ADD" ? undefined : data?.whitelistName || undefined,
              rules: [
                {
                  required: true,
                  message: "请输入策略白名单组名!",
                },
                {
                  max: 30,
                  message: "输入超限,最多输入30个字符!",
                },
                {
                  pattern: /^[^\s]*$/,
                  message: "策略白名单组名不允许输入空格",
                },
              ],
            })(<Input placeholder="请输入策略白名单组名" allowClear />)}
          </Form.Item>
          <Form.Item label="策略ID">
            {getFieldDecorator("strategyIds", {
              initialValue:
                type === "ADD" ? undefined : data?.strategyIds || undefined,
              rules: [
                {
                  required: true,
                  message: "请选择策略白名单组!",
                },
                {
                  max: 1000,
                  message: "长度最大1000个字符",
                },
              ],
            })(
              <Input.TextArea
                placeholder="请选择策略白名单组"
                readOnly
                style={{ width: "76%" }}
              />
            )}
            <Button
              type="primary"
              style={{ marginLeft: 10 }}
              onClick={() => {
                setVisibleChoose(true);
                props.dispatch({
                  type: "analyStrategyManage/getStrategyIds",
                });
              }}
            >
              选择
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      {visibleChoose && (
        <Modal
          visible={visibleChoose}
          closable={false}
          className={styles.chooseStrategy}
          destroyOnClose
          title={
            <div style={{ display: "flex", justifyContent: "space-evenly" }}>
              {[
                { label: "全选", value: "all" },
                { label: "全不选", value: "noall" },
                { label: "反选", value: "反选" },
              ].map((v) => (
                <Button onClick={() => onCheckAllChange(v.value)}>
                  {v.label}
                </Button>
              ))}
            </div>
          }
          onCancel={() => {
            setVisibleChoose(false);
            setCheckedList(data?.strategyIds?.split("|"));
          }}
          onOk={() => {
            setVisibleChoose(false);
            setFieldsValue({
              strategyIds:
                checkedList?.length !== 0 ? checkedList.join("|") : undefined,
            });
          }}
          width={400}
        >
          <Spin spinning={tableLoading}>
            <div style={{ display: "flex" }}>
              <div style={{ width: "50%", borderRight: "1px solid #e8e8e8" }}>
                <div style={{ marginBottom: 10, fontWeight: "bold" }}>
                  全部策略
                </div>
                <CheckboxGroup
                  options={analyStrategyManage?.strategyIds?.data}
                  onChange={onChange}
                  value={checkedList?.length && checkedList}
                />
              </div>
              <div style={{ width: "50%", paddingLeft: 24 }}>
                <div style={{ marginBottom: 10, fontWeight: "bold" }}>
                  已选策略
                </div>
                {checkedList?.length !== 0 &&
                  checkedList?.map((v, i) => {
                    return <div key={i}>{v}</div>;
                  })}
              </div>
            </div>
          </Spin>
        </Modal>
      )}
    </React.Fragment>
  );
}
export default connect(({ loading, analyStrategyManage }) => ({
  analyStrategyManage,
  tableLoading: loading.effects["analyStrategyManage/getStrategyIds"],
}))(Form.create()(AddOrEditModal));
