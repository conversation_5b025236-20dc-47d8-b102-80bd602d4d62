import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Tooltip, Icon, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import { routerRedux } from 'dva/router';
import { connect } from 'dva';
import styles from './index.less';
import AddOrEditModal from './AddOrEditModal';

function WhitelistGroupManagement(props) {
  const [visible, setVisible] = React.useState(false);
  const [type, setType] = React.useState('ADD');
  const [data, setData] = React.useState(null);
  const columns = [
    {
      title: '策略白名单组号',
      dataIndex: 'whitelistId',
      key: 'whitelistId',
      width: '25%',
      align: 'center',
      render: (t) =>
        t ? (
          <Tooltip title={t} placement="topLeft">
            {t}
          </Tooltip>
        ) : (
          '--'
        ),
    },
    {
      title: '策略白名单组名',
      dataIndex: 'whitelistName',
      key: 'whitelistName',
      width: '25%',
      align: 'center',
      render: (t) =>
        t ? (
          <Tooltip title={t} placement="topLeft">
            {t}
          </Tooltip>
        ) : (
          '--'
        ),
    },
    {
      title: '策略ID',
      dataIndex: 'strategyIds',
      key: 'strategyIds',
      width: '25%',
      align: 'center',
      ellipsis: true,
      render: (t) =>
        t ? (
          <Tooltip title={t} placement="topLeft">
            {t}
          </Tooltip>
        ) : (
          '--'
        ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: '25%',
      align: 'center',

      render: (t, record) => (
        <Button
          type="link"
          onClick={() => handleChange(record)}
          disabled={record.whitelistId === 'GW01'}
        >
          变更
        </Button>
      ),
    },
  ];
  const handleChange = (val) => {
    setVisible(true);
    setType('EDIT');
    setData(val);
  };
  const getTable = (parmas) => {
    dispatch({
      type: 'analyStrategyManage/getWhiteGroup',
      payload: {
        currentPage: 1,
        pageSize: 10,
        ...parmas,
      },
    });
  };
  React.useEffect(() => {
    getTable();
  }, []);
  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    getTable({ currentPage: current, pageSize });
  };
  const { tableLoading, analyStrategyManage, dispatch } = props;
  return (
    <div className={styles.whiteGroupManagement}>
      <Row>
        <Col span={12} align="left" style={{ marginBottom: 20 }}>
          <Button
            onClick={() => props.dispatch(routerRedux.goBack())}
            style={{ marginBottom: '12px' }}
          >
            <Icon type="left" />
            返回
          </Button>
        </Col>
        <Col span={12} align="right" style={{ marginBottom: 20 }}>
          <Button
            type="primary"
            onClick={() => {
              setVisible(true);
              setType('ADD');
            }}
          >
            增加策略白名单组
          </Button>
        </Col>
      </Row>
      <StandardTable
        columns={columns}
        bordered
        loading={tableLoading}
        showSelectCount={false}
        rowSelection={null}
        rowSelectionProps={false}
        tableAlert={false}
        data={{
          list: analyStrategyManage?.getWhiteGroup?.items,
          pagination: {
            total: analyStrategyManage?.getWhiteGroup?.totalNum,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSize: analyStrategyManage?.getWhiteGroup?.pageSize,
            current: analyStrategyManage?.getWhiteGroup?.currentPage,
            defaultPageSize: 10,
            showTotal: (total, range) => {
              return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
            },
          },
        }}
        scroll={{
          x: '100%',
        }}
        onChange={handleTableChange}
      />
      {visible && (
        <AddOrEditModal
          visible={visible}
          onCancel={() => setVisible(false)}
          type={type}
          data={type === 'ADD' ? null : data}
          Refresh={getTable}
        />
      )}
    </div>
  );
}
export default connect(({ loading, analyStrategyManage }) => ({
  analyStrategyManage,
  tableLoading: loading.effects['analyStrategyManage/getWhiteGroup'],
}))(WhitelistGroupManagement);
