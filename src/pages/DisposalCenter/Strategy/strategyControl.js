import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Modal, Button, Input, Form, DatePicker, TimePicker, Select, Cascader, message, Card } from 'antd';
import FormItem from 'antd/lib/form/FormItem';
import styles from './strategyControl.less';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

@connect()
@Form.create()
class StrategyControl extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      value: '',
      value1: 5,
      value2: 5,
      value3: 5,
      visible: false,
    };
    this.textMap = {
      text1: '每个主叫号码单日试呼次数限呼',
      text2: '每个主叫号码单小时试呼次数限呼',
      text3: '每个主叫号码同一被叫单日次数限呼',
    };
  }

  componentDidMount() {
    this.getNum();
  }

  getNum = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'managementControl/getControlStrategy',
      payload: {},
      callback: (res) => {
        const { data: [{ dayNum, hourNum, calledNum }] = [{}] } = res;
        this.setState({
          value1: dayNum,
          value2: hourNum,
          value3: calledNum,
        });
      },
    });
  };

  showModal = (value, key, text) => {
    this.key = key;
    this.setState({
      value,
      visible: true,
      text,
    });
  };
  handleOk = (e) => {
    const { form, dispatch } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const value = fieldsValue.inputContent;
      const keyMap = {
        1: 'dayNum',
        2: 'hourNum',
        3: 'calledNum',
      };
      dispatch({
        type: 'managementControl/outOfStrategy',
        payload: {
          [keyMap[this.key]]: value,
        },
        callback: () => {
          message.success('修改成功');
          this.getNum();
          this.setState({
            visible: false,
          });
          form.resetFields();
        },
      });
    });
  };

  handleCancel = (e) => {
    const { form } = this.props;
    this.setState({
      visible: false,
    });
    form.resetFields();
  };

  render() {
    const { value, value1, value2, value3, text } = this.state;
    const { text1, text2, text3 } = this.textMap;
    const { form } = this.props;
    return (
      <Card>
        <div className={styles.box}>
          <div className={styles.list}>
            <p>{text1}<span className={styles.span1}>{value1}</span></p>
            <Button onClick={() => this.showModal(value1, 1, text1)}>修改</Button>
          </div>
          <div className={styles.list}>
            <p>{text2}<span className={styles.span2}>{value2}</span></p>
            <Button onClick={() => this.showModal(value2, 2, text2)}>修改</Button>
          </div>
          <div className={styles.list}>
            <p>{text3}<span>{value3}</span></p>
            <Button onClick={() => this.showModal(value3, 3, text3)}>修改</Button>
          </div>
          <Modal
            visible={this.state.visible}
            onOk={this.handleOk}
            onCancel={this.handleCancel}
          >
            <p>{text}</p>
            <Form {...formItemLayout}>
              <FormItem label="请输入0-999数字">
                {form.getFieldDecorator('inputContent', {
                  rules: [
                    { required: true, message: '请输入0-999数字' },
                    { message: '请输入0-999数字', pattern: /^[0-9]{1,3}$/ },
                  ],
                })(<Input placeholder="请输入0-999数字" id="warning" />)}
              </FormItem>
            </Form>
          </Modal>
        </div>
      </Card>
    );
  }
}

export default StrategyControl;
