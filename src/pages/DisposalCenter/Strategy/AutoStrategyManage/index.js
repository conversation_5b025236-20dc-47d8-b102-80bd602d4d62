import React from "react";
import {Card, Table, Button, Modal, message, Descriptions} from "antd";

import StrategyHistory from "./StrategyHistory";
import NewStrategyForm from "./NewStrategyForm";

import PermissionButton from 'components/PermissionButton';

import withTableData from "hocs/withTableData";
import request from "../../../utils/request";
import {basePath} from "../../../services/basePath";

class AutoStrategyManage extends React.Component {
  state = {
    strategyEditingModalVisible: false,
    editingStrategy: null,
    savingStrategy: false
  };
  handleEditStrategy = record => () => {
    this.setState({
      editingStrategy: record,
      strategyEditingModalVisible: true
    });
  };
  columns = [
    {
      title: "策略ID",
      align: "center",
      dataIndex: "sid"
    },
    {
      title: "策略名称",
      align: "center",
      dataIndex: "name"
    },
    {
      title: "策略内容",
      align: "center",
      key: "strategyContent",
      render: (_, record) => {
        // return (
        //   <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
        //     {[
        //       "dayNum",
        //       "hourNum",
        //       "sameNum",
        //       "probability",
        //       "zjlt",
        //       "zjyd"
        //     ].map(f => <li>{record[f]}</li>)}
        //   </ul>
        // );
        if (record.noIntercept) {
          return "全放通";
        }
        else if (record.probability) {
          return (
            <ul style={{listStyle: "none", padding: 0, margin: 0}}>
              <li>每个主叫号码拦截概率{record.probability}%</li>
              {Number(record.zjlt) === 1 && <li>被叫是浙江联通号码放通</li>}
              {Number(record.zjyd) === 1 && <li>被叫是浙江移动号码放通</li>}
              {Number(record.zjdx) === 1 && <li>被叫是浙江电信号码放通</li>}
            </ul>
          );
        } else {
          return (
            <ul style={{listStyle: "none", padding: 0, margin: 0}}>
              {![null, undefined].includes(record.dayNum) && <li>每个主叫号码单日限呼{record.dayNum}次</li>}
              {![null, undefined].includes(record.hourNum) && <li>每个主叫号码单小时限呼{record.hourNum}次</li>}
              {![null, undefined].includes(record.sameNum) && <li>每个主叫号码同一被叫单日限呼{record.sameNum}次</li>}
              {Number(record.zjlt) === 1 && <li>被叫是浙江联通号码放通</li>}
              {Number(record.zjyd) === 1 && <li>被叫是浙江移动号码放通</li>}
              {Number(record.zjdx) === 1 && <li>被叫是浙江电信号码放通</li>}
            </ul>
          );
        }
      }
    },
    {
      title: "最近更新时间",
      align: "center",
      dataIndex: "input_time"
    },
    {
      title: "对应分析策略",
      align: "center",
      dataIndex: "name"
    },
    {
      title: "操作",
      align: "center",
      key: "action",
      render: (_, record) => {
        return (
          // <PermissionButton location={this.props.location} buttonKey="updateStrategys">
          <Button
            size="small"
            type="primary"
            onClick={this.handleEditStrategy(record)}
          >
            变更
          </Button>
          // </PermissionButton>

        );
      }
    }
  ];
  handleNewStrategy = () => {
    this.setState({
      editingStrategy: null,
      strategyEditingModalVisible: true
    });
  };

  handleCloseStrategyEditingModal = () => {
    this.setState({
      strategyEditingModalVisible: false
    });
  };

  saveStrategy = fields => {
    const {reloadTable} = this.props;
    const {editingStrategy} = this.state;
    const isEditing = Boolean(this.state.editingStrategy);

    let strategyType = fields
      ? 'probability' in fields
        ? "byPossibility"
        : 'dayNum' in fields
          ? "byCount"
          : "all"
      : "byCount";


    Modal.confirm({
      title: isEditing ? "确认变更为如下策略？" : "确认添加如下策略？",
      content: (
        <Descriptions column={1}>
          <Descriptions.Item label="策略名称">{fields.name}</Descriptions.Item>
          <Descriptions.Item label="策略类型">
            {
              {
                byCount: "按次数限呼",
                byPossibility: "按概率限呼",
                all: "全放通"
              }[strategyType]
            }
          </Descriptions.Item>
          {strategyType === "byCount" ? (
            [
              [null, undefined].includes(fields.dayNum) || (
                <Descriptions.Item key="dayNum" label="每个主叫号码单日限呼次数">
                  {fields.dayNum}
                </Descriptions.Item>
              ),
              [null, undefined].includes(fields.hourNum) || (
                <Descriptions.Item key="hourNum" label="每个主叫号码单小时限呼次数">
                  {fields.hourNum}
                </Descriptions.Item>
              ),
              [null, undefined].includes(fields.sameNum) || (
                <Descriptions.Item key="sameNum" label="每个主叫号码同一被叫单日限呼次数">
                  {fields.sameNum}
                </Descriptions.Item>
              )
            ]
          ) : strategyType === "byPossibility" ? (
            <Descriptions.Item label="每个主叫号码拦截概率">
              {fields.probability + "%"}
            </Descriptions.Item>
          ) : null}
          {strategyType !== "all" && [
            <Descriptions.Item key="zjlt" label="被叫是浙江联通号码放通">
              {fields.zjlt ? "是" : "否"}
            </Descriptions.Item>,
            <Descriptions.Item key="zjyd" label="被叫是浙江移动号码放通">
              {fields.zjyd ? "是" : "否"}
            </Descriptions.Item>,
            <Descriptions.Item key="zjdx" label="被叫是浙江电信号码放通">
              {fields.zjdx ? "是" : "否"}
            </Descriptions.Item>
          ]}
        </Descriptions>
      ),
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        if (isEditing) {
          // 变更
          this.setState({
            savingStrategy: true
          });


          // const body = strategyType === 'byCount' ? {
          //   sid: editingStrategy.sid,
          //   name: fields.name,
          //   probability: -1,
          //   dayNum: [undefined, null].includes(fields.dayNum) ? -1 : fields.dayNum,
          //   hourNum: [undefined, null].includes(fields.hourNum) ? -1 : fields.hourNum,
          //   sameNum: [undefined, null].includes(fields.sameNum) ? -1: fields.sameNum,
          //   zjlt: fields.zjlt,
          //   zjyd: fields.zjyd,
          //   zydx: fields.zjdx,
          //   noIntercept: strategyType === "all" ? 1 : 0
          // } : {
          //   sid: editingStrategy.sid,
          //   dayNum: -1,
          //   hourNum: -1,
          //   sameNum: -1,
          //   probability: [undefined, null].includes(fields.probability) ? -1 : fields.probability,
          //   noIntercept: strategyType === "all" ? 1 : 0
          // }

          const body = {
            sid: editingStrategy.sid,
            name: encodeURIComponent(fields.name),
            probability: [undefined, null].includes(fields.probability) ? -1 : fields.probability,
            dayNum: [undefined, null].includes(fields.dayNum) ? -1 : fields.dayNum,
            hourNum: [undefined, null].includes(fields.hourNum) ? -1 : fields.hourNum,
            sameNum: [undefined, null].includes(fields.sameNum) ? -1 : fields.sameNum,
            zjlt: [undefined, null].includes(fields.zjlt) ? -1 : fields.zjlt,
            zjyd: [undefined, null].includes(fields.zjyd) ? -1 : fields.zjyd,
            zjdx: [undefined, null].includes(fields.zjdx) ? -1 : fields.zjdx,
            noIntercept: strategyType === "all" ? 1 : 0
          }

          request(`${basePath}zjlt/updateStrategys`, {
            method: "POST",
            body,
          })
            .then(response => {
              if (!response) {
                return Promise.reject(new Error());
              }
              if (response.success) {
                message.success("操作成功！");
                reloadTable();
                this.setState({
                  strategyEditingModalVisible: false
                });
              } else {
                message.error(response.message);
              }
            })
            .catch(e => {
            }).finally(() => {
            this.setState({
              savingStrategy: false,
            })
          });
        } else {
          // 新增

          this.setState({
            savingStrategy: true
          });

          // const body = strategyType === 'byCount' ? {
          //   name: fields.name,
          //   probability: -1,
          //   dayNum: fields.dayNum || -1,
          //   hourNum: fields.hourNum || -1,
          //   sameNum: fields.sameNum || -1,
          //   zjlt: fields.zjlt,
          //   zjyd: fields.zjyd,
          //   zjdx: fields.zjdx,
          //   noIntercept: strategyType === "all" ? 1 : 0
          // } : {
          //   ...fields,
          //   dayNum: -1,
          //   hourNum: -1,
          //   sameNum: -1,
          //   probability: fields.probability || -1,
          //   noIntercept: strategyType === "all" ? 1 : 0
          // }

          const body = {
            name: encodeURIComponent(fields.name),
            probability: [undefined, null].includes(fields.probability) ? -1 : fields.probability,
            dayNum: [undefined, null].includes(fields.dayNum) ? -1 : fields.dayNum,
            hourNum: [undefined, null].includes(fields.hourNum) ? -1 : fields.hourNum,
            sameNum: [undefined, null].includes(fields.sameNum) ? -1 : fields.sameNum,
            zjlt: [undefined, null].includes(fields.zjlt) ? -1 : fields.zjlt,
            zjyd: [undefined, null].includes(fields.zjyd) ? -1 : fields.zjyd,
            zjdx: [undefined, null].includes(fields.zjdx) ? -1 : fields.zjdx,
            noIntercept: strategyType === "all" ? 1 : 0
          }

          request(`${basePath}zjlt/addStrategys`, {
            method: "POST",
            body,
          })
            .then(response => {
              if (!response) {
                return Promise.reject(new Error());
              }
              if (response.success) {
                message.success("操作成功！");
                reloadTable();
                this.setState({
                  savingStrategy: false,
                  strategyEditingModalVisible: false
                });
              } else {
                message.error(response.message);
              }
            })
            .catch(e => {
            }).finally(() => {
            this.setState({
              savingStrategy: false,
            })
          });
        }
      }
    });
  };

  handleSaveStrategy = () => {
    if (this.strategyEdittingForm) {
      const {validateFields} = this.strategyEdittingForm.props.form;

      validateFields((err, values) => {
        if (err) return;

        if ('dayNum' in values) {
          if (['dayNum', 'hourNum', 'sameNum'].every(f => [undefined, null].includes(values[f]))) {
            message.error("请至少输入一种限制次数的策略！")
            return
          }
        }

        this.saveStrategy(values);
      });
    }
  };

  handleTableChange = (pagination, filters, sorters) => {
    const {fetchTableData, params} = this.props;

    fetchTableData({
      ...params,
      page: pagination.current,
      length: pagination.pageSize
    });
  };

  render() {
    const {
      strategyEditingModalVisible,
      editingStrategy,
      savingStrategy,
    } = this.state;
    const {
      columns,
      // handleOpenStrategyEditingModal,
      handleNewStrategy,
      handleCloseStrategyEditingModal,
      handleSaveStrategy,
      handleTableChange
    } = this;

    const {
      loading,
      tableData,
      params: {length: pageSize, page: current},
      total,
      location
    } = this.props;

    return (
      <React.Fragment>
        <Card>
          {/* <PermissionButton location={location} buttonKey="getStrategysHis"> */}
          <StrategyHistory/>
          {/* </PermissionButton> */}

          <Table
            columns={columns}
            loading={loading}
            dataSource={tableData}
            rowKey="sid" // TODO
            onChange={handleTableChange}
            pagination={{
              current,
              pageSize,
              total,
              size: "small",
              pageSizeOptions: ["5", "10", "15", "20"],
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => {
                return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
              }
            }}
          ></Table>
        </Card>
        <Modal
          width={670}
          visible={strategyEditingModalVisible}
          confirmLoading={savingStrategy}
          title="策略编辑"
          onCancel={handleCloseStrategyEditingModal}
          maskClosable={false}
          onOk={handleSaveStrategy}
          destroyOnClose={true}
        >
          <NewStrategyForm
            editingStrategy={editingStrategy}
            wrappedComponentRef={form => (this.strategyEdittingForm = form)}
          />
        </Modal>
      </React.Fragment>
    );
  }
}

export default withTableData({
  url: `${basePath}zjlt/getStrategys`,
  initialParams: {
    page: 1,
    length: 15
  }
})(AutoStrategyManage);
