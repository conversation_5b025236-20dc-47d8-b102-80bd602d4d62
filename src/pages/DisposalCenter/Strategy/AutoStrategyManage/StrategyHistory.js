import React from "react";
import {Form, Button, Input, Modal, Table, message, Empty} from "antd";

import withTableData from "hocs/withTableData";

import {basePath} from "../../../services/basePath";

class StrategyHistory extends React.Component {
  columns = [
    {
      title: "策略ID",
      align: "center",
      dataIndex: "sid"
    },
    {
      title: "策略名称",
      align: "center",
      dataIndex: "name"
    },
    {
      title: "策略内容",
      align: "center",
      render: (_, record) => {
        // return (
        //   <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
        //     {[
        //       "dayNum",
        //       "hourNum",
        //       "sameNum",
        //       "probability",
        //       "noIntercept",
        //       "zjlt",
        //       "zjyd"
        //     ].map(f => <li>{record[f]}</li>)}
        //   </ul>
        // );
        if (record.noIntercept) {
          return "全放通";
        }
        else if (record.probability) {
          return (
            <ul style={{listStyle: "none", padding: 0, margin: 0}}>
              <li>每个主叫号码拦截概率{record.probability}%</li>
              {Number(record.zjlt) === 1 && <li>被叫是浙江联通号码放通</li>}
              {Number(record.zjyd) === 1 && <li>被叫是浙江移动号码放通</li>}
              {Number(record.zjdx) === 1 && <li>被叫是浙江电信号码放通</li>}
            </ul>
          );
        } else {
          return (
            <ul style={{listStyle: "none", padding: 0, margin: 0}}>
              {![null, undefined].includes(record.dayNum) && <li>每个主叫号码单日限呼{record.dayNum}次</li>}
              {![null, undefined].includes(record.hourNum) && <li>每个主叫号码单小时限呼{record.hourNum}次</li>}
              {![null, undefined].includes(record.sameNum) && <li>每个主叫号码同一被叫单日限呼{record.sameNum}次</li>}
              {Number(record.zjlt) === 1 && <li>被叫是浙江联通号码放通</li>}
              {Number(record.zjyd) === 1 && <li>被叫是浙江移动号码放通</li>}
              {Number(record.zjdx) === 1 && <li>被叫是浙江电信号码放通</li>}
            </ul>
          );
        }
      }
    },
    {
      title: "操作时间",
      align: "center",
      dataIndex: "input_time"
    },
    {
      title: "操作账号",
      align: "center",
      dataIndex: "userName"
    },
    {
      title: "操作类型",
      align: "center",
      dataIndex: "type"
    },
  ];

  state = {
    searchText: "",
    showHistoryModal: false
  };

  handleClick = () => {
    const {fetchTableData} = this.props;
    const {searchText} = this.state;
    if (searchText === '') {
      message.warn("请输入策略ID！");
      return;
    }

    if (!/^\d{4}$/.test(searchText)) {
      message.warn("请输入正确的策略ID！");
      return;
    }

    // const { form, onSearch } = this.props;
    // if (onSearch && typeof onSearch === 'function') {
    //   onSearch(form.getFieldsValue());
    // }

    // TODO
    fetchTableData({
      sid: this.state.searchText,
      page: 1,
      length: 15
    }).then(() => {
      this.setState({
        showHistoryModal: true
      });
    }).catch(err => {
      message.error(err.message);
    });
  };

  handleSearchTextChange = e => {
    const value = e.target.value;

    if (/^\d{0,4}$/.test(value)) {
      this.setState({
        searchText: e.target.value
      });
    }
  };

  handleCloseHistoryModal = () => {
    this.setState({
      showHistoryModal: false
    });
  };

  handleTableChange = (pagination, filters, sorters) => {
    const {fetchTableData, params} = this.props;

    fetchTableData({
      ...params,
      page: pagination.current,
      length: pagination.pageSize
    });
  };

  render() {
    const {
      columns,
      handleClick,
      handleSearchTextChange,
      handleCloseHistoryModal,
      handleTableChange
    } = this;
    const {showHistoryModal, searchText} = this.state;

    const {
      loading,
      tableData,
      params: {length: pageSize, page: current},
      total
    } = this.props;

    return (
      <React.Fragment>
        <Form layout="inline">
          <Form.Item label="策略ID">
            <Input
              size="small"
              placeholder="请输入策略ID"
              value={searchText}
              onChange={handleSearchTextChange}
            ></Input>
          </Form.Item>
          <Form.Item>
            <Button size="small" onClick={handleClick}>
              历史记录查询
            </Button>
          </Form.Item>
        </Form>
        <Modal
          width={780}
          title="历史记录查询"
          visible={showHistoryModal}
          onCancel={handleCloseHistoryModal}
          footer={null}
          destroyOnClose={true}
        >
          <Table
            size="small"
            columns={columns}
            loading={loading}
            dataSource={tableData}
            rowKey={record => `${record.sid}-${record.input_time}`} // TODO
            onChange={handleTableChange}
            pagination={{
              current,
              pageSize,
              total,
              size: "small",
              pageSizeOptions: ["5", "10", "15", "20"],
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => {
                return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
              }
            }}
            locale={{
              // emptyText: "未查询到相关的历史记录"
              emptyText: <Empty description="未查询到相关的历史记录"/>
            }}
          ></Table>
        </Modal>
      </React.Fragment>
    );
  }
}

export default withTableData({
  url: `${basePath}zjlt/getStrategysHis`,
  first: false
})(StrategyHistory);
