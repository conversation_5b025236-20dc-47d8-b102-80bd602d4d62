import { Modal, Form, Input, message, Select } from 'antd';
import React, { useState } from 'react';
import request from 'ponshine-request';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const AddModal = Form.create()(
  ({
    visible,
    cancel,
    selectedRows,
    form: { validateFields, getFieldDecorator, resetFields, setFieldsValue },
    onReload,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const handleOk = () => {
      validateFields(async (err, values) => {
        if (err) return;
        setConfirmLoading(true);
        const response = await request('/api/hn/userAccountDelay/approveDelay', {
          data: { ...values, delayId: selectedRows?.map((ele) => ele.id)?.join() },
          method: 'POST',
          requestType: 'form',
        });
        setConfirmLoading(false);
        if (response.code === 200) {
          message.success(response.message);
          cancel();
          onReload();
        } else {
          message.error(response.message);
        }
      });
    };

    const handleChange = (v) => {
      if (v) {
        setFieldsValue({ approveRemark: v });
      }
    };

    return (
      <Modal
        title="账号延期审批"
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
      >
        <Form {...formItemLayout}>
          <Form.Item label="审批结果">
            {getFieldDecorator('approveResult', {
              rules: [
                {
                  required: true,
                  message: '请输入审批结果',
                },
              ],
            })(
              <Select placeholder="请选择" onChange={handleChange}>
                {['审批通过', '审批驳回']?.map((ele, index) => (
                  <Select.Option value={ele} key={index}>
                    {ele}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="审批意见">
            {getFieldDecorator('approveRemark', {
              rules: [
                {
                  required: true,
                  message: '请输入审批意见',
                },
              ],
            })(<Input.TextArea placeholder="请输入" allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default AddModal;
