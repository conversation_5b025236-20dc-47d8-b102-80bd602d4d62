import React, { useRef, useState, useEffect } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, But<PERSON>, Card } from 'antd';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import request from 'ponshine-request';
import StandardTable from '@/components/StandardTable';
import ApproveModal from './ApproveModal';

const { RangePicker } = DatePicker;

const Index = ({ form, form: { getFieldDecorator, validateFields } }) => {
  const [loading, setLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [approveVisible, setApproveVisible] = useState(false);

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const serachParams = useRef();

  let columns = [
    {
      title: '申请人',
      width: 100,
      dataIndex: 'userName',
      ellipsis: true,
    },
    {
      title: '申请时间',
      width: 100,
      dataIndex: 'gmtCreate',
      ellipsis: true,
    },

    {
      title: '申请账号延期时间',
      width: 120,
      dataIndex: 'delayTime',
      ellipsis: true,
    },
    {
      title: '延期原因',
      width: 120,
      dataIndex: 'delayReason',
      ellipsis: true,
    },
    {
      title: '审批状态',
      width: 120,
      dataIndex: 'approvalStatus',
      ellipsis: true,
      render: (v) => {
        const obj = {
          1: '待审批',
          2: '审批通过',
          3: '审批驳回',
        };
        return obj?.[v] || '--';
      },
    },
    {
      title: '审批人',
      width: 120,
      dataIndex: 'approver',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/userAccountDelay/pageUserAccountDelay', {
      method: 'POST',
      requestType: 'json',
      data: {
        pageNum,
        pageSize,
        ...props,
      },
    });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      const { applyTime } = values;
      getListDatas({
        ...values,
        gmtCreateStart: applyTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
        gmtCreateEnd: applyTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
        applyTime: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleReset = () => {
    form.resetFields();
    setSelectedRows([]);
    getListDatas();
  };

  const handleApprove = async () => {
    if (!selectedRows?.length) return message.warning('请选择要审批的数据');
    if (selectedRows?.some((ele) => ele.approvalStatus !== '1'))
      return message.warning('请选择待审批的数据，选中的数据中包含审批通过或审批驳回的数据');
    setApproveVisible(true);
  };

  const onSelectChange = (_selectRows) => {
    setSelectedRows(_selectRows);
  };

  useEffect(() => {
    getListDatas();
  }, []);

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <Row>
          <Col span={8}>
            <Form.Item label="申请人">
              {getFieldDecorator('userName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="申请时间">
              {getFieldDecorator('applyTime', {})(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>

          <Col span={8} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>
            <Button onClick={handleReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Button type="primary" onClick={handleApprove}>
              审批
            </Button>
          </Col>
        </Row>
      </Form>
      <StandardTable
        onSelectRow={onSelectChange}
        selectedRows={selectedRows}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
      {approveVisible && (
        <ApproveModal
          visible={approveVisible}
          onReload={handleReset}
          cancel={() => {
            setApproveVisible(false);
          }}
          selectedRows={selectedRows}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
