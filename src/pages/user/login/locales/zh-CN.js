export default {
  'user-login.login.userName': '用户名',
  'user-login.login.password': '密码',
  'user-login.login.message-invalid-credentials': '账户或密码错误',
  'user-login.login.message-invalid-verification-code': '验证码错误',
  'user-login.login.tab-login-credentials': '账户密码登录',
  'user-login.login.tab-login-mobile': '手机号登录',
  'user-login.login.remember-me': '自动登录',
  'user-login.login.forgot-password': '忘记密码',
  'user-login.login.sign-in-with': '其他登录方式',
  'user-login.login.signup': '注册账户',
  'user-login.login.login': '登录',
  'user-login.register.register': '注册',
  'user-login.register.get-verification-code': '获取验证码',
  'user-login.register.sign-in': '使用已有账户登录',
  'user-login.register-result.msg': '你的账户：{email} 注册成功',
  'user-login.register-result.activation-email':
    '激活邮件已发送到你的邮箱中，邮件有效期为24小时。请及时登录邮箱，点击邮件中的链接激活帐户。',
  'user-login.register-result.back-home': '返回首页',
  'user-login.register-result.view-mailbox': '查看邮箱',
  'user-login.email.required': '请输入邮箱地址！',
  'user-login.email.wrong-format': '邮箱地址格式错误！',
  'user-login.userName.required': '请输入用户名!',
  'user-login.password.required': '请输入密码！',
  'user-login.password.twice': '两次输入的密码不匹配!',
  'user-login.strength.msg': '请至少输入 6 个字符。请不要使用容易被猜到的密码。',
  'user-login.strength.strong': '强度：强',
  'user-login.strength.medium': '强度：中',
  'user-login.strength.short': '强度：太短',
  'user-login.confirm-password.required': '请确认密码！',
  'user-login.phone-number.required': '请输入手机号！',
  'user-login.phone-number.wrong-format': '手机号格式错误！',
  'user-login.verification-code.required': '请输入验证码！',
  'user-login.verification-code.refresh': '换一张',
  'user-login.verification-code.succeeded': '验证成功！',
  'user-login.verification-code.failed': '验证失败！',
  'user-login.verification-code.slide-jigsaw.on-drag-timeout':
    '滑动超时了，用了 {duration} 毫秒，请在 {timeout} 毫秒内完成滑动！',
  'user-login.verification-code.slide-jigsaw.verify-errored': '验证异常',
  'user-login.verification-code.slide-jigsaw.verify-failed': '验证失败',
  'user-login.verification-code.slide-jigsaw.verify-succeeded': '验证成功',
  'user-login.verification-code.slide-jigsaw.ready': '向右滑动填充拼图',
  'user-login.verification-code.slide-jigsaw.image-ing': '加载中...',
  'user-login.verification-code.slide-jigsaw.image-errored': '加载失败',
  'user-login.verification-code.slide-jigsaw.drag-timeout': '滑动超时',
  'user-login.verification-code.dev-info': '请开发人员自行选择验证码类型',
  'user-login.title.required': '请输入标题',
  'user-login.date.required': '请选择起止日期',
  'user-login.goal.required': '请输入目标描述',
  'user-login.standard.required': '请输入衡量标准',
  'user-login.form.get-captcha': '获取验证码',
  'user-login.captcha.second': '秒',
  'user-login.form.optional': '（选填）',
  'user-login.form.submit': '提交',
  'user-login.form.save': '保存',
  'user-login.email.placeholder': '邮箱',
  'user-login.password.placeholder': '至少6位密码，区分大小写',
  'user-login.confirm-password.placeholder': '确认密码',
  'user-login.phone-number.placeholder': '手机号',
  'user-login.verification-code.placeholder': '验证码',
  'user-login.title.label': '标题',
  'user-login.title.placeholder': '给目标起个名字',
  'user-login.date.label': '起止日期',
  'user-login.placeholder.start': '开始日期',
  'user-login.placeholder.end': '结束日期',
  'user-login.goal.label': '目标描述',
  'user-login.goal.placeholder': '请输入你的阶段性工作目标',
  'user-login.standard.label': '衡量标准',
  'user-login.standard.placeholder': '请输入衡量标准',
  'user-login.client.label': '客户',
  'user-login.label.tooltip': '目标的服务对象',
  'user-login.client.placeholder': '请描述你服务的客户，内部客户直接 @姓名／工号',
  'user-login.invites.label': '邀评人',
  'user-login.invites.placeholder': '请直接 @姓名／工号，最多可邀请 5 人',
  'user-login.weight.label': '权重',
  'user-login.weight.placeholder': '请输入',
  'user-login.public.label': '目标公开',
  'user-login.label.help': '客户、邀评人默认被分享',
  'user-login.radio.public': '公开',
  'user-login.radio.partially-public': '部分公开',
  'user-login.radio.private': '不公开',
  'user-login.publicUsers.placeholder': '公开给',
  'user-login.option.A': '同事甲',
  'user-login.option.B': '同事乙',
  'user-login.option.C': '同事丙',
  'user-login.navBar.lang': '语言',
  'user-login.forgotPassword.username': '用户名',
  'user-login.forgotPassword.username.placeholder': '请输入用户名！',
  'user-login.forgotPassword.username.required': '该字段为必填项！',
  'user-login.forgotPassword.captcha': '验证码',
  'user-login.forgotPassword.captcha.required': '该字段为必填项！',
  'user-login.forgotPassword.captcha.button': '获取验证码',
  'user-login.forgotPassword.captcha.success.message': '验证码发送成功！',
  'user-login.forgotPassword.captcha.error.message': '获取验证码失败！',
  'user-login.forgotPassword.password': '密码',
  'user-login.forgotPassword.password.compareToFirstPassword': '与上一次密码不符，请确认！',
  'user-login.forgotPassword.password.tooltip':
    '密码必须符合下列最低要求： 不能包含用户的账号名， 至少包含以下四类字符中的三类字符： 英文大写字母（A-Z） 英文小写字母（a-z） 数字（0-9） 特殊字符（如：@、#、￥、%）',
  'user-login.forgotPassword.password.required': '该字段不能为空！',
  'user-login.forgotPassword.password.minLength': '密码不能小于{minimumLength}位！',
  'user-login.forgotPassword.password.pattern': '密码复杂度不符合要求！',
  'user-login.forgotPassword.confirm': '确认密码',
  'user-login.forgotPassword.confirm.tooltip': '请确保2次新密码相同！',
  'user-login.forgotPassword.confirm.required': '该字段不能为空！',
  'user-login.forgotPassword.submit': '提交',
  'user-login.forgotPassword.cancel': '返回',
  'user-login.forgotPassword.submit.success.message': '密码修改成功！',
  'user-login.forgotPassword.submit.success.description':
    '为避免您的账号无法正常使用，还请你使用新密码进行平台登录。',
  'user-login.forgotPassword.submit.success.goToLogin': '返回登录页',
  'user-login.forgotPassword.submit.error.message': '密码修改失败！',
};
