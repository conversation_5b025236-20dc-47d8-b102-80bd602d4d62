import {
  getFakeNormalCaptcha,
  getFakeSlideJigsawCaptcha,
  verifyFakeSlideJigsawCaptcha,
  sendBackPasswordCaptcha,
  updateBackPassword,
} from '@/services/login';
import { Alert, Checkbox, Icon, Modal, message as antdMessage, message } from 'antd'; // eslint-disable-next-line import/no-extraneous-dependencies
import { connect } from 'dryad';
import { Link } from 'ponshine';
import { formatMessage, FormattedMessage } from 'ponshine-plugin-react/locale';
import request from 'ponshine-request';
import ForgotPasswordComponents from './components/ForgotPassword';
import React, { Component } from 'react';
import LoginComponents from './components/Login';
import styles from './style.less';

const { Tab, UserName, Password, Mobile, Captcha, NormalCaptcha, SlideJigsawCaptcha, Submit } =
  LoginComponents;

class Login extends Component {
  loginForm = undefined;
  state = {
    type: 'account',
    autoLogin: true,
    visible: true,
  };
  captchaRef = React.createRef();
  captchaImgHeraldRule = (rs) => {
    // 此处写法老版和新版后端兼容
    this.captchaLastImgToken = this.captchaImgToken;
    this.captchaLastImgSecretKey = this.captchaImgSecretKey;
    this.captchaImgToken = rs?.repData?.token || rs?.imageToken;
    this.captchaImgSecretKey = rs?.repData?.secretKey;
    return {
      // 当前是滑动拼图验证码，所以设置 bgImgSrc 和 frontImgSrc
      bgImgSrc: rs?.oripic || `data:image/png;base64,${rs?.repData?.originalImageBase64}`,
      frontImgSrc: rs?.newpic || `data:image/png;base64,${rs?.repData?.jigsawImageBase64}`,
    };
  };
  captchaVerifyService = (newInputValue) => {
    this.captchaImgTrack = newInputValue;
    this.captchaLastSize = this.captchaRef.current?.getSize?.();
    return verifyFakeSlideJigsawCaptcha({
      size: this.captchaLastSize,
      track: this.captchaImgTrack,
      token: this.captchaImgToken,
      secretKey: this.captchaImgSecretKey,
    });
  };
  captchaVerifyRule = (rs) => {
    // 此处写法老版和新版后端兼容
    const isVerifyOk = rs?.success === true || rs?.state === 'SUCCESS';
    const msg = rs?.repMsg || rs?.message;

    if (isVerifyOk) {
      antdMessage.success(
        `${
          msg ||
          formatMessage({
            id: 'user-login.verification-code.succeeded',
          })
        }`,
      );
    } else {
      antdMessage.warn(
        `${
          msg ||
          formatMessage({
            id: 'user-login.verification-code.failed',
          })
        }`,
      );
    }

    return isVerifyOk;
  };
  changeAutoLogin = (e) => {
    this.setState({
      autoLogin: e.target.checked,
    });
  };
  changeVisible = (e = true) => {
    this.setState({
      visible: e,
    });
  };
  handleSubmit = (err, values) => {
    const { type } = this.state;

    if (!err) {
      const { dispatch } = this.props;

      if (dispatch) {
        dispatch({
          type: 'login/login',
          payload: {
            ...values,
            size: this.captchaLastSize,
            track: this.captchaImgTrack,
            token: this.captchaLastImgToken,
            secretKey: this.captchaLastImgSecretKey,
            type,
          },
          callback: () => {
            // 从老平台登录的账号根据登录IP限制登录，提示“根据安全管理要求，原反诈运营管理平台由于无法满足安全管理要求，请从反诈运营管理平台新地址https://**************:31006登录”(地址需可复制)；
            request('/api/hn/whiteLoginIp/checkLoginIp', {
              method: 'POST',
            }).then((res) => {
              if (res?.code === 500) {
                Modal.confirm({
                  title: '提示',
                  content: res?.message,
                });
              }
            });
          },
        });
      }
    }
  };
  onTabChange = (type) => {
    this.setState({
      type,
    });
  };
  onGetCaptcha = () =>
    new Promise((resolve, reject) => {
      if (!this.loginForm) {
        return;
      }

      this.loginForm.validateFields(['userName', 'password'], {}, async (err, values) => {
        if (err) {
          reject(err);
        } else {
          const { dispatch } = this.props;

          try {
            if (dispatch) {
              const success = await dispatch({
                type: 'login/sendLoginVerificationCode',
                payload: { userName: values.userName, password: values.password },
                callback: (res) => {
                  if (res.code == 200) {
                    message.success('发送成功');
                    resolve();
                  } else {
                    message.error(res.message);
                    reject();
                  }
                },
              });
            } else {
              resolve(false);
            }
          } catch (error) {
            reject(error);
          }
        }
      });
    });
  renderMessage = (content) => (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );

  render() {
    const { userLogin = {}, submitting } = this.props;
    const { status, message, type: loginType } = userLogin;
    const { type, autoLogin, visible } = this.state;
    return (
      <div className={styles.main}>
        <div className={styles.login_title}>登录</div>
        {visible ? (
          <LoginComponents
            defaultActiveKey={type}
            onTabChange={this.onTabChange}
            onSubmit={this.handleSubmit}
            onCreate={(form) => {
              this.loginForm = form;
            }}
          >
            {/* <Tab
              key="account"
              tab={formatMessage({
                id: 'user-login.login.tab-login-credentials',
              })}
            > */}
            {['ERROR', 'FAIL'].includes(`${status}`.toUpperCase()) &&
              loginType === 'account' &&
              !submitting &&
              this.renderMessage(
                message ||
                  formatMessage({
                    id: 'user-login.login.message-invalid-credentials',
                  }),
              )}
            <UserName
              name="userName"
              placeholder={`${formatMessage({
                id: 'user-login.login.userName',
              })}`}
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: 'user-login.userName.required',
                  }),
                },
              ]}
            />
            <Password
              name="password"
              placeholder={`${formatMessage({
                id: 'user-login.login.password',
              })}`}
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: 'user-login.password.required',
                  }),
                },
              ]}
              onPressEnter={(e) => {
                e.preventDefault();

                if (this.loginForm) {
                  this.loginForm.validateFields(this.handleSubmit);
                }
              }}
            />
            <Captcha
              name="verificationCode"
              placeholder={formatMessage({
                id: 'user-login.verification-code.placeholder',
              })}
              countDown={120}
              onGetCaptcha={this.onGetCaptcha}
              getCaptchaButtonText={formatMessage({
                id: 'user-login.form.get-captcha',
              })}
              getCaptchaSecondText={formatMessage({
                id: 'user-login.captcha.second',
              })}
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: 'user-login.verification-code.required',
                  }),
                },
              ]}
            />
            {/* <NormalCaptcha
                forwardRef={this.captchaRef}
                name="captcha"
                inputProps={{
                  placeholder: formatMessage({
                    id: 'user-login.verification-code.placeholder',
                  }),
                }}
                imgProps={getFakeNormalCaptcha}
                refreshBtnProps={{
                  children: formatMessage({
                    id: 'user-login.verification-code.refresh',
                  }),
                }}
                rules={[
                  {
                    required: true,
                    message: formatMessage({
                      id: 'user-login.verification-code.required',
                    }),
                  },
                ]}
              /> */}
            {/* <SlideJigsawCaptcha // @ts-ignore
                key={this.props.locale}
                forwardRef={this.captchaRef}
                name="captcha"
                imgHeraldService={getFakeSlideJigsawCaptcha}
                imgHeraldRule={this.captchaImgHeraldRule}
                verifyService={this.captchaVerifyService}
                verifyRule={this.captchaVerifyRule}
              /> */}
            {/* <Alert
                style={{
                  marginBottom: 24,
                }}
                type="warning"
                message={formatMessage({
                  id: 'user-login.verification-code.dev-info',
                })}
              /> */}
            {/* </Tab> */}
            {/* <Tab
              key="mobile"
              tab={formatMessage({
                id: 'user-login.login.tab-login-mobile',
              })}
            >
              {['ERROR', 'FAIL'].includes(`${status}`.toUpperCase()) &&
                loginType === 'mobile' &&
                !submitting &&
                this.renderMessage(
                  message ||
                    formatMessage({
                      id: 'user-login.login.message-invalid-verification-code',
                    }),
                )}
              <Mobile
                name="mobile"
                placeholder={formatMessage({
                  id: 'user-login.phone-number.placeholder',
                })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({
                      id: 'user-login.phone-number.required',
                    }),
                  },
                  {
                    pattern: /^1\d{10}$/,
                    message: formatMessage({
                      id: 'user-login.phone-number.wrong-format',
                    }),
                  },
                ]}
              />
              <Captcha
                name="mobileCaptcha"
                placeholder={formatMessage({
                  id: 'user-login.verification-code.placeholder',
                })}
                countDown={120}
                onGetCaptcha={this.onGetCaptcha}
                getCaptchaButtonText={formatMessage({
                  id: 'user-login.form.get-captcha',
                })}
                getCaptchaSecondText={formatMessage({
                  id: 'user-login.captcha.second',
                })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({
                      id: 'user-login.verification-code.required',
                    }),
                  },
                ]}
              />
            </Tab> */}
            <div>
              <Checkbox checked={autoLogin} onChange={this.changeAutoLogin}>
                <FormattedMessage id="user-login.login.remember-me" />
              </Checkbox>
              <a
                style={{
                  float: 'right',
                }}
                onClick={(e) => {
                  this.changeVisible(false);
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <FormattedMessage id="user-login.login.forgot-password" />
              </a>
            </div>
            <Submit loading={submitting}>
              <FormattedMessage id="user-login.login.login" />
            </Submit>
            {/* <div className={styles.other}>
              <FormattedMessage id="user-login.login.sign-in-with" />
              <Icon type="alipay-circle" className={styles.icon} theme="outlined" />
              <Icon type="taobao-circle" className={styles.icon} theme="outlined" />
              <Icon type="weibo-circle" className={styles.icon} theme="outlined" />
              <Link className={styles.register} to="/user/register">
                <FormattedMessage id="user-login.login.signup" />
              </Link>
            </div> */}
          </LoginComponents>
        ) : (
          <ForgotPasswordComponents
            sendBackPasswordCaptcha={sendBackPasswordCaptcha}
            updateBackPassword={updateBackPassword}
            changeVisible={this.changeVisible}
          ></ForgotPasswordComponents>
        )}
      </div>
    );
  }
}

export default connect(({ login, loading }) => ({
  userLogin: login,
  submitting: loading.effects['login/login'],
}))(Login);
