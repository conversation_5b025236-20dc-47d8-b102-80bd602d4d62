import { Icon, message } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import styles from './index.less';
import * as SlideJigsawCaptchaDefaultRender from './SlideJigsawCaptchaDefaultRender';

export default {
  UserName: {
    props: {
      size: 'large',
      id: 'userName',
      prefix: <Icon type="user" className={styles.prefixIcon} />,
      placeholder: 'admin',
    },
    rules: [
      {
        required: true,
        message: 'Please enter username!',
      },
    ],
  },
  Password: {
    props: {
      size: 'large',
      prefix: <Icon type="lock" className={styles.prefixIcon} />,
      type: 'password',
      id: 'password',
      placeholder: '888888',
    },
    rules: [
      {
        required: true,
        message: 'Please enter password!',
      },
    ],
  },
  Mobile: {
    props: {
      size: 'large',
      prefix: <Icon type="mobile" className={styles.prefixIcon} />,
      placeholder: 'mobile number',
    },
    rules: [
      {
        required: true,
        message: 'Please enter mobile number!',
      },
      {
        pattern: /^1\d{10}$/,
        message: 'Wrong mobile number format!',
      },
    ],
  },
  // 手机验证码
  Captcha: {
    props: {
      size: 'large',
      prefix: <Icon type="mail" className={styles.prefixIcon} />,
      placeholder: 'captcha',
    },
    rules: [
      {
        required: true,
        message: 'Please enter Captcha!',
      },
    ],
  },
  // @demasia-rc/captcha 常规验证码
  NormalCaptcha: {
    props: {
      type: 'normal',
      mode: 'embed',
      inputVerifyMode: 'none',
      imgClickRefresh: true,
      inputProps: {
        size: 'large',
        prefix: <Icon type="code" className={styles.prefixIcon} />,
      },
      inputColProps: {
        sm: 12,
        xs: 15,
      },
      imgColProps: {
        sm: 12,
        xs: 9,
      },
    },
    rules: [
      {
        required: true,
        message: 'Please enter Captcha!',
      },
    ],
  },
  // @demasia-rc/captcha 滑动拼图验证码
  SlideJigsawCaptcha: {
    props: {
      type: 'slide-jigsaw',
      mode: 'popover',
      onlySliderPopover: true,
      frontImgDisabledDraggable: true,
      dragStopPopoverHidden: true,
      dragStopVerify: true,
      dragTimeoutRefreshImg: true,
      dragTimeout: 5000,
      onDragTimeout: (duration, timeout) => {
        message.warn(
          formatMessage(
            {
              id: 'user-login.verification-code.slide-jigsaw.on-drag-timeout',
            },
            {
              duration,
              timeout,
            },
          ),
        );
      },
      refreshImgWhenVerify: ['verifyFailed', 'verifyErrored', 'verifySucceeded'],
      slideIndicatorRender: SlideJigsawCaptchaDefaultRender.defaultSlideIndicatorRender,
      sliderRender: SlideJigsawCaptchaDefaultRender.defaultSliderRender,
      ctlTipRender: SlideJigsawCaptchaDefaultRender.defaultCtlTipRender,
      ctlStatusIndicatorRender: SlideJigsawCaptchaDefaultRender.defaultCtlStatusIndicatorRender,
      imgCtlTipRender: SlideJigsawCaptchaDefaultRender.defaultImgCtlTipRender,
      imgCtlStatusIndicatorRender:
        SlideJigsawCaptchaDefaultRender.defaultImgCtlStatusIndicatorRender,
    },
  },
};
