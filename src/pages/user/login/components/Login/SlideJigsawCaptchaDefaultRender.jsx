import { <PERSON><PERSON>, <PERSON> } from 'antd';
// eslint-disable-next-line import/no-extraneous-dependencies
import { formatMessage } from 'ponshine-plugin-react/locale';
export const defaultSlideIndicatorRender = (status) => {
  const { verifyStatus, dragStatus, isDragTimeout } = status;

  if (verifyStatus.verifyFailed || verifyStatus.verifyErrored || isDragTimeout) {
    return (
      <div
        style={{
          boxSizing: 'border-box',
          display: 'block',
          width: 'calc(100% + 6px)',
          height: '100%',
          marginLeft: 6,
          border: '1px solid #ffa39e',
          borderRadius: 4,
          background: '#fff1f0',
          backgroundColor: '#fff1f0',
        }}
      />
    );
  }

  if (verifyStatus.verifySucceeded) {
    return (
      <div
        style={{
          boxSizing: 'border-box',
          display: 'block',
          width: 'calc(100% + 6px)',
          height: '100%',
          marginLeft: 6,
          border: '1px solid #b7eb8f',
          borderRadius: 4,
          background: '#f6ffed',
          backgroundColor: '#f6ffed',
        }}
      />
    );
  }

  if (dragStatus.dragging || dragStatus.dragStop) {
    return (
      <div
        style={{
          boxSizing: 'border-box',
          display: 'block',
          width: 'calc(100% + 6px)',
          height: '100%',
          marginLeft: 6,
          border: '1px solid #1991fa',
          borderRadius: 4,
          background: '#d1e9fe',
          backgroundColor: '#d1e9fe',
        }}
      />
    );
  }

  return null;
};
export const defaultSliderRender = (status, ref) => {
  const {
    verifyStatus,
    imgHeraldStatus,
    bgImageStatus,
    frontImageStatus,
    dragStatus,
    isHover,
    isDragTimeout,
  } = status;

  if (
    verifyStatus.verifyFailed ||
    verifyStatus.verifyErrored ||
    imgHeraldStatus.imgHeraldErrored ||
    bgImageStatus.imgErrored ||
    frontImageStatus.imgErrored ||
    isDragTimeout
  ) {
    if (
      verifyStatus.verifying ||
      imgHeraldStatus.imgHeralding ||
      bgImageStatus.imgLoading ||
      frontImageStatus.imgLoading
    ) {
      return <Button type="danger" size="large" loading />;
    }

    return (
      <Button
        type="danger"
        size="large"
        icon="close"
        onClick={() => {
          ref?.current?.refreshImg();
        }}
      />
    );
  }

  if (
    verifyStatus.verifying ||
    imgHeraldStatus.imgHeralding ||
    bgImageStatus.imgLoading ||
    frontImageStatus.imgLoading
  ) {
    return <Button type="primary" size="large" loading />;
  }

  if (verifyStatus.verifySucceeded) {
    return (
      <Button
        type="primary"
        size="large"
        icon="check"
        style={{
          background: '#b7eb8f',
          backgroundColor: '#b7eb8f',
          borderColor: '#b7eb8f',
        }}
        onClick={() => {
          ref?.current?.refreshImg();
        }}
      />
    );
  }

  if (isHover || dragStatus.dragging) {
    return (
      <Button
        type="primary"
        size="large"
        icon="arrow-right"
        loading={
          verifyStatus.verifying ||
          imgHeraldStatus.imgHeralding ||
          bgImageStatus.imgLoading ||
          frontImageStatus.imgLoading
        }
      />
    );
  }

  return <Button size="large" icon="arrow-right" />;
};
export const defaultCtlTipRender = (status, ref) => {
  const {
    verifyStatus,
    imgHeraldStatus,
    bgImageStatus,
    frontImageStatus,
    dragStatus,
    isDragTimeout,
  } = status;

  if (dragStatus.dragging || verifyStatus.verifying) {
    return null;
  }

  if (verifyStatus.verifyErrored) {
    return (
      <span
        onClick={() => ref?.current?.refreshImg()}
        style={{
          cursor: 'pointer',
        }}
      >
        {formatMessage({
          id: 'user-login.verification-code.slide-jigsaw.verify-errored',
        })}
      </span>
    );
  }

  if (verifyStatus.verifyFailed) {
    return (
      <span
        onClick={() => ref?.current?.refreshImg()}
        style={{
          cursor: 'pointer',
        }}
      >
        {formatMessage({
          id: 'user-login.verification-code.slide-jigsaw.verify-failed',
        })}
      </span>
    );
  }

  if (verifyStatus.verifySucceeded) {
    return (
      <span
        onClick={() => ref?.current?.refreshImg()}
        style={{
          cursor: 'pointer',
        }}
      >
        {formatMessage({
          id: 'user-login.verification-code.slide-jigsaw.verify-succeeded',
        })}
      </span>
    );
  }

  if (
    (imgHeraldStatus.imgHeraldSucceeded ||
      (!imgHeraldStatus.imgHeraldErrored &&
        !imgHeraldStatus.imgHeraldSucceeded &&
        !imgHeraldStatus.imgHeralding)) &&
    bgImageStatus.imgLoaded &&
    frontImageStatus.imgLoaded &&
    !dragStatus.dragging &&
    !dragStatus.dragStart &&
    !dragStatus.dragStop &&
    !isDragTimeout
  ) {
    return formatMessage({
      id: 'user-login.verification-code.slide-jigsaw.ready',
    });
  }

  if (imgHeraldStatus.imgHeralding || bgImageStatus.imgLoading || frontImageStatus.imgLoading) {
    return formatMessage({
      id: 'user-login.verification-code.slide-jigsaw.image-ing',
    });
  }

  if (imgHeraldStatus.imgHeraldErrored || bgImageStatus.imgErrored || frontImageStatus.imgErrored) {
    return (
      <span
        onClick={() => ref?.current?.refreshImg()}
        style={{
          cursor: 'pointer',
        }}
      >
        {formatMessage({
          id: 'user-login.verification-code.slide-jigsaw.image-errored',
        })}
      </span>
    );
  }

  if (isDragTimeout) {
    return (
      <span
        onClick={() => ref?.current?.refreshImg()}
        style={{
          cursor: 'pointer',
        }}
      >
        {formatMessage({
          id: 'user-login.verification-code.slide-jigsaw.drag-timeout',
        })}
      </span>
    );
  }

  return null;
};
export const defaultCtlStatusIndicatorRender = (status) => {
  const { verifyStatus, imgHeraldStatus, bgImageStatus, frontImageStatus } = status;

  if (
    imgHeraldStatus.imgHeralding ||
    bgImageStatus.imgLoading ||
    frontImageStatus.imgLoading ||
    verifyStatus.verifying
  ) {
    return (
      <Spin
        size="small"
        spinning
        className="demasia-rc-slideJigsawCaptcha-controller-statusIndicator-loading-transparent"
      />
    );
  }

  return null;
};
export const defaultImgCtlTipRender = (status) => {
  const { verifyStatus, imgHeraldStatus, bgImageStatus, frontImageStatus, dragStatus } = status;

  if (dragStatus.dragging || verifyStatus.verifying) {
    return null;
  }

  if (imgHeraldStatus.imgHeralding || bgImageStatus.imgLoading || frontImageStatus.imgLoading) {
    return formatMessage({
      id: 'user-login.verification-code.slide-jigsaw.image-ing',
    });
  }

  return null;
};
export const defaultImgCtlStatusIndicatorRender = (status) => {
  const { verifyStatus, imgHeraldStatus, bgImageStatus, frontImageStatus } = status;

  if (imgHeraldStatus.imgHeralding || bgImageStatus.imgLoading || frontImageStatus.imgLoading) {
    return (
      <Spin
        size="small"
        spinning
        className="demasia-rc-slideJigsawCaptcha-controller-statusIndicator-loading-bg"
      />
    );
  }

  if (verifyStatus.verifying) {
    return (
      <Spin
        size="small"
        spinning
        className="demasia-rc-slideJigsawCaptcha-controller-statusIndicator-loading-transparent"
      />
    );
  }

  if (imgHeraldStatus.imgHeraldErrored || bgImageStatus.imgErrored || frontImageStatus.imgErrored) {
    return (
      <div className="demasia-rc-slideJigsawCaptcha-controller-statusIndicator-errored-bg">
        {formatMessage({
          id: 'user-login.verification-code.slide-jigsaw.image-errored',
        })}
      </div>
    );
  }

  return null;
};
