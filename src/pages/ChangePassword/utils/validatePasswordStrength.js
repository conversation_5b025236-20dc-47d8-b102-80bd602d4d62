export default (password, userName) => {
  const hasNumberRegex = /\d/m;
  const hasLowerLetterRegex = /[a-z]/m;
  const hasUpperLetterRegex = /[A-Z]/m;
  const hasSpecialLetterRegex =
    /[_!@#$%^&*`~()\-+=[\]{}:;'"<>,.?/|\\：；‘’“”，。…￥（）？—！、·《》＇＂；：＜＞．｛｝［］＼｜／＿－＝＋＊＆＾％＄＃＠！～【】「」]/m;
  const regexs = [hasNumberRegex, hasLowerLetterRegex, hasUpperLetterRegex, hasSpecialLetterRegex];
  const matchResults = [false, false, false, false];
  let atLeast3CategoriesMatched = false;
  let i = 0;

  while (i < regexs.length) {
    const regex = regexs[i];
    matchResults[i] = regex.test(password);

    if (matchResults.filter(Boolean).length === 3) {
      atLeast3CategoriesMatched = true;
      break;
    }

    i += 1;
  }

  let flag = true;

  if (!atLeast3CategoriesMatched) {
    // 应包括数字、小写字母、大写字母、特殊符号4类中至少3类；
    flag = false;
  } else if (password && userName) {
    // 应与用户名无相关性，不得包含用户名的完整字符串、大小写变位
    const userNameVal1 = userName
      .toLocaleLowerCase()
      .replace(/\s+/gm, '')
      .replace(
        /[_!@#$%^&*`~()\-+=[\]{}:;'"<>,.?/|\\：；‘’“”，。…￥（）？—！、·《》＇＂；：＜＞．｛｝［］＼｜／＿－＝＋＊＆＾％＄＃＠！～【】「」]+/gm,
        '',
      );
    const passwordVal1 = password
      .toLocaleLowerCase()
      .replace(/\s+/gm, '')
      .replace(
        /[_!@#$%^&*`~()\-+=[\]{}:;'"<>,.?/|\\：；‘’“”，。…￥（）？—！、·《》＇＂；：＜＞．｛｝［］＼｜／＿－＝＋＊＆＾％＄＃＠！～【】「」]+/gm,
        '',
      );
    const userNameVal2 = userName
      .toLowerCase()
      .replace(/\s+/gm, '')
      .replace(
        /[_!@#$%^&*`~()\-+=[\]{}:;'"<>,.?/|\\：；‘’“”，。…￥（）？—！、·《》＇＂；：＜＞．｛｝［］＼｜／＿－＝＋＊＆＾％＄＃＠！～【】「」]+/gm,
        '',
      );
    const passwordVal2 = password
      .toLowerCase()
      .replace(/\s+/gm, '')
      .replace(
        /[_!@#$%^&*`~()\-+=[\]{}:;'"<>,.?/|\\：；‘’“”，。…￥（）？—！、·《》＇＂；：＜＞．｛｝［］＼｜／＿－＝＋＊＆＾％＄＃＠！～【】「」]+/gm,
        '',
      );

    if (
      userNameVal1.indexOf(passwordVal1) !== -1 ||
      userNameVal1.indexOf(passwordVal2) !== -1 ||
      userNameVal2.indexOf(passwordVal1) !== -1 ||
      userNameVal2.indexOf(passwordVal2) !== -1 ||
      passwordVal1.indexOf(userNameVal1) !== -1 ||
      passwordVal1.indexOf(userNameVal2) !== -1 ||
      passwordVal2.indexOf(userNameVal1) !== -1 ||
      passwordVal2.indexOf(userNameVal2) !== -1
    ) {
      flag = false;
    }
  }

  return flag;
};
