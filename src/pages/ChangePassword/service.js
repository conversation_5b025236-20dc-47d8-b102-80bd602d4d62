import request from '@/utils/request';
import Jsencrypt from 'jsencryptNew';
const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzaCrNH49tWuCmTxnVivfIgg8OSljmE3Lx9+KZIBQxfXZhLsj7uLdXRB3Q9MQ5sUhS7dQ+XfUaczNizMv+gQ8AYsPW9P9VTYUNRug47KhPgU28oOlkI5qm5RPQg06B5S7SGYpihhmTjzFWNTlshFjVZTV4QyfuX5hNrigJ4ddFmwIDAQAB'; // 首次登录修改密码接口

export async function changePassword(params) {
  const encrypt = new Jsencrypt();
  encrypt.setPublicKey(publicKey);
  const { password, ...rest } = params;
  return request('/api/user/changePassword', {
    method: 'POST',
    data: { ...rest, password: password ? encrypt.encryptLong(password) : '' },
    requestType: 'form',
  });
}
