export default {
  'changepassword.password.compareToFirstPassword':
    'Does not match the last password, please confirm!',
  'changepassword.password.placeholder': 'Password',
  'changepassword.password.tooltip':
    'The password must meet the following requirements: It cannot contain the username, and at least contains three of the following four types of characters: English uppercase letters (A-Z) English lowercase letters (a-z) Numbers (0-9) Special characters (such as: @,#,￥,%)',
  'changepassword.password.required': 'This field cannot be empty！',
  'changepassword.password.minLength':
    'The length of the password cannot be less than {minimumLength}！',
  'changepassword.password.pattern': 'Password complexity does not meet the requirements！',
  'changepassword.confirm.placeholder': 'Confirm password',
  'changepassword.confirm.tooltip':
    'Please make sure that the confirm password is the same as the last password！',
  'changepassword.confirm.required': 'This field cannot be empty！',
  'changepassword.submit': 'Submit',
  'changepassword.cancel': 'Cancel',
  'changepassword.submit.success.message': 'Password reset complete！',
  'changepassword.submit.success.description':
    'In order to avoid your account cannot be used normally, please use the new password to login to the platform.',
  'changepassword.submit.success.goToLogin': 'Return to login page',
  'changepassword.submit.error.message': 'Password modification failed！',
};
