import React, { useState, useRef } from 'react';
import { Form, Button, Input, Tooltip, Icon, notification, message, Spin } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { router, useAuth, refreshAuth } from 'ponshine';
import { withSilence } from 'demasia-pro-layout';
import { useInterval } from 'phooks';
import { changePassword } from './service';
import { validatePasswordStrength } from './utils';
import styles from './index.less';
const FormItem = Form.Item;

const CountDown = () => {
  const [count, setCount] = useState(5);
  useInterval(
    () => {
      setCount(count - 1);
    },
    count <= 0 ? null : 1000,
  );
  return <span>{count}</span>;
};

const ChangePassword = (props) => {
  const [confirmDirty, setConfirmDirty] = useState(false);
  const [loading, setLoading] = useState(false);
  const { authState } = useAuth() || {};
  const username = authState?.user?.username;
  const passwordPolicy = authState?.passwordPolicy || {};
  const myRef = useRef();
  const {
    form: { validateFieldsAndScroll, getFieldDecorator, getFieldValue, validateFields },
  } = props;
  const formItemLayout = {
    labelCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 0,
      },
    },
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 24,
      },
    },
  };

  const compareToFirstPassword = (rule, value, callback) => {
    if (value && value !== getFieldValue('password')) {
      callback(
        formatMessage({
          id: 'changepassword.password.compareToFirstPassword',
        }),
      );
    } else {
      callback();
    }
  };

  const validateToNextPassword = (rule, value, callback) => {
    if (!validatePasswordStrength(value, username)) {
      callback(
        formatMessage({
          id: 'changepassword.password.pattern',
        }),
      );
      return;
    }

    if (value && confirmDirty) {
      validateFields(['confirm'], {
        force: true,
      });
    }

    callback();
  };

  const handleConfirmBlur = (e) => {
    const { value } = e.target;
    setConfirmDirty(confirmDirty || !!value);
  };

  const handleToLogin = async () => {
    if (myRef.current) clearInterval(myRef.current);
    await refreshAuth(); // TODO 下方跳转路由在非单点登录时只是一个双保险（因为 refreshAuth() 后在 auth.[j|t]sx? 文件中有对回包头中的 session-status 进行特殊处理），而如果是单点登录，则不需要跳转路由，完全由 refreshAuth() 后通过 auth.[j|t]sx? 文件中的逻辑去控制

    router.push({
      pathname: '/user/login',
    });
    notification.close('changePassword');
  };

  const handleOk = () => {
    validateFieldsAndScroll(async (err, values) => {
      if (err) return;
      setLoading(true);
      const response = await changePassword(values);
      setLoading(false);

      if (response?.state === 'SUCCESS') {
        if (myRef.current) clearInterval(myRef.current);
        myRef.current = setTimeout(() => {
          handleToLogin();
        }, 1000 * 5);
        notification.success({
          key: 'changePassword',
          message:
            response.message ||
            formatMessage({
              id: 'changepassword.submit.success.message',
            }),
          duration: 5,
          description: (
            <div>
              <div>
                {formatMessage({
                  id: 'changepassword.submit.success.description',
                })}
              </div>
              <a onClick={handleToLogin}>
                {formatMessage({
                  id: 'changepassword.submit.success.goToLogin',
                })}
                （<CountDown />
                s）
              </a>
            </div>
          ),
        });
      } else {
        message.error(
          response.message ||
            formatMessage({
              id: 'changepassword.submit.error.message',
            }),
        );
      }
    });
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.changePassword}>
        <div className={styles.title}>修改密码</div>
        <Form {...formItemLayout}>
          <FormItem hasFeedback>
            {getFieldDecorator('password', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'changepassword.password.required',
                  }),
                },
                {
                  min: passwordPolicy?.minimumLengthEnable ? passwordPolicy?.minimumLength : -1,
                  message: formatMessage(
                    {
                      id: 'changepassword.password.minLength',
                    },
                    {
                      minimumLength: passwordPolicy?.minimumLength,
                    },
                  ),
                },
                {
                  validator: validateToNextPassword,
                },
              ],
            })(
              <Input.Password
                size="large"
                prefix={
                  <Icon
                    type="lock"
                    style={{
                      color: 'rgba(0,0,0,.25)',
                    }}
                  />
                }
                placeholder={formatMessage({
                  id: 'changepassword.password.placeholder',
                })}
                addonBefore={
                  <Tooltip
                    title={formatMessage({
                      id: 'changepassword.password.tooltip',
                    })}
                  >
                    <Icon
                      type="info-circle"
                      style={{
                        color: 'rgba(0,0,0,.45)',
                      }}
                    />
                  </Tooltip>
                }
              />,
            )}
          </FormItem>

          <FormItem>
            {getFieldDecorator('confirm', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'changepassword.confirm.required',
                  }),
                },
                {
                  validator: compareToFirstPassword,
                },
              ],
            })(
              <Input.Password
                size="large"
                prefix={
                  <Icon
                    type="lock"
                    style={{
                      color: 'rgba(0,0,0,.25)',
                    }}
                  />
                }
                onBlur={handleConfirmBlur}
                placeholder={formatMessage({
                  id: 'changepassword.confirm.placeholder',
                })}
                addonBefore={
                  <Tooltip
                    title={formatMessage({
                      id: 'changepassword.confirm.tooltip',
                    })}
                  >
                    <Icon
                      type="info-circle"
                      style={{
                        color: 'rgba(0,0,0,.45)',
                      }}
                    />
                  </Tooltip>
                }
              />,
            )}
          </FormItem>
          <div
            style={{
              textAlign: 'center',
              width: '100%',
              marginTop: '30px',
            }}
          >
            <Button type="primary" onClick={handleOk}>
              {formatMessage({
                id: 'changepassword.submit',
              })}
            </Button>

            <Button
              style={{
                marginLeft: 20,
              }}
              onClick={() => {
                handleToLogin();
              }}
            >
              {formatMessage({
                id: 'changepassword.cancel',
              })}
            </Button>
          </div>
        </Form>
      </div>
    </Spin>
  );
};

const ChangePasswordForm = Form.create()(ChangePassword);
export default withSilence(ChangePasswordForm);
