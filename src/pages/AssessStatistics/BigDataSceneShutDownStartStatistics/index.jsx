import React, { useState, useEffect } from 'react';
import { Form, Card, DatePicker, Button, message, Upload, Table } from 'antd';
const { MonthPicker } = DatePicker;
import { exportFile } from '@/utils/utils';

import request from '@/utils/request';
import moment from 'moment';
import { Licensee } from 'ponshine';

const Index = ({ form: { getFieldDecorator, getFieldsValue, resetFields } }) => {
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const defaultDate = moment().subtract(1, 'month');

  const columns = [
    {
      title: '归属',
      dataIndex: 'belongingPlace',
      width: 100,
      key: 'belongingPlace',
      fixed: 'left',
    },
    {
      title: '分类',
      dataIndex: 'classification',
      key: 'classification',
      width: 100,
      fixed: 'left',
    },
    {
      title: '模型',
      width: 150,
      dataIndex: 'model',
      key: 'model',
      fixed: 'left',
    },
    {
      title: '模型场景',
      key: 'modelScene',
      dataIndex: 'modelScene',
      width: 200,
      fixed: 'left',
    },
    {
      title: '关停量',
      dataIndex: 'shutdownCount',
      ellipsis: true,
      width: 100,
    },
    {
      title: '命中号码数',
      width: 120,
      ellipsis: true,
      dataIndex: 'hitPhoneNumCount',
    },
    {
      title: '命中率',
      dataIndex: 'hitRate',
      ellipsis: true,
      width: 100,
    },
    {
      title: '模型关停占比(模型/模型大类)',
      dataIndex: 'modelShutdownRate',
      // ellipsis: true,
      width: 115,
    },
    {
      title: '场景关停占比(场景/模型)',
      dataIndex: 'sceneShutdownRate',
      // ellipsis: true,
      width: 100,
    },
    {
      title: '外呼号码数',
      dataIndex: 'outboundPhoneNumCount',
      ellipsis: true,
      width: 120,
    },
    {
      title: '外呼结果诈骗号码数',
      dataIndex: 'outboundResultFraudPhoneNumCount',
      ellipsis: true,
      width: 160,
    },
    {
      title: '外呼结果非诈骗号码数',
      dataIndex: 'outboundResultNotFraudPhoneNumCount',
      ellipsis: true,
      width: 180,
    },
    {
      title: '外呼结果待定号码数',
      dataIndex: 'outboundResultUndeterminedPhoneNumCount',
      ellipsis: true,
      width: 160,
    },
    {
      title: '外呼诈骗率',
      dataIndex: 'outboundFraudRate',
      width: 120,
    },
    {
      title: '外呼非诈骗率',
      dataIndex: 'outboundNotFraudRate',
      ellipsis: true,
      width: 120,
    },
    {
      title: '外呼待定率',
      dataIndex: 'outboundUndeterminedRate',
      ellipsis: true,
      width: 120,
    },
    // {
    //   title: '申告量',
    //   dataIndex: 'applicationCount',
    //   ellipsis: true,
    //   width: 120,
    // },
    // {
    //   title: '申告率(申告量/总申告量)',
    //   dataIndex: 'applicationRate',
    //   // ellipsis: true,
    //   width: 190,
    // },
    {
      title: '当日复机量',
      dataIndex: 'resumeCountToday',
      ellipsis: true,
      width: 120,
    },
    {
      title: '当日复机率',
      dataIndex: 'resumeRateToday',
      ellipsis: true,
      width: 120,
    },
    {
      title: '近七天复机量',
      dataIndex: 'resumeCountWeek',
      ellipsis: true,
      width: 120,
    },
    {
      title: '近七天复机率',
      dataIndex: 'resumeRateWeek',
      ellipsis: true,
      width: 120,
    },
  ];

  const findTableDataPager = async (props) => {
    const params = {
      countMonth: props?.date?.format('YYYYMM') || defaultDate?.format('YYYYMM'),
      countMonthStr: props?.date?.format('YYYY年MM月') || defaultDate?.format('YYYY年MM月'),
    };
    setLoading(true);
    const response = await request('/api/hn/bigModelSceneCount/pageBigModelSceneShutdownCount', {
      method: 'POST',
      requestType: 'json',
      data: params,
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(params);
      setTableData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    findTableDataPager();
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    findTableDataPager({ ...values });
  };

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/hn/bigModelSceneCount/exportBigModelSceneShutdownCount',
      decode: true,
      method: 'POST',
      requestType: 'json',
      params: { ...searchParams },
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const handleBeforUpload = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    setImportLoading(true);
    const response = await request('/api/hn/bigModelSceneCount/importBigModelSceneCountData', {
      method: 'POST',
      requestType: 'form',
      data: formData,
    });
    setImportLoading(false);
    if (response.code === 200) {
      message.success(response.message);
      resetFields();
      findTableDataPager();
    } else {
      message.error(response.message);
    }
    return new Promise.reject();
  };

  return (
    <Card>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="统计年月">
          {getFieldDecorator('date', {
            initialValue: defaultDate,
          })(<MonthPicker allowClear={false} format={'YYYYMM'} />)}
        </Form.Item>
        <Form.Item>
          <Button type="primary" onClick={handleSearch} style={{ marginRight: 16 }}>
            查询
          </Button>

          <Licensee license="bigDataSceneShutDownStartStatistics_exportData">
            <Button
              onClick={handleExport}
              loading={exportLoading}
              disabled={!tableData?.length}
              style={{ marginRight: 16 }}
            >
              导出
            </Button>
          </Licensee>
          <Licensee license="bigDataSceneShutDownStartStatistics_importData">
            <Upload beforeUpload={handleBeforUpload} showUploadList={false}>
              <Button icon="upload" type="primary" loading={importLoading}>
                导入
              </Button>
            </Upload>
          </Licensee>
        </Form.Item>
      </Form>
      <h3>大数据模型分场景关停复机统计({searchParams?.countMonthStr})</h3>
      <Table
        columns={columns}
        dataSource={tableData}
        pagination={false}
        rowKey="id"
        scroll={{ x: 2200, y: 560 }}
        loading={loading}
      />
    </Card>
  );
};

export default Form.create()(Index);
