import { Form, Card } from 'antd';
import { connect } from 'dryad';
import { useEffect, useState } from 'react';
import moment from 'moment';
import { exportFile } from '@/utils/utils';
import ReportProvinceTable from './components/ReportProvinceTable';
import ReportCityTable from './components/ReportCityTable';
import CommonForm from './components/CommonForm';

const defaultPagination = {
  // pageSize: 10,
  // currentPage: 1,
  dataType: 'report',
  startMonth: moment().subtract(2, 'month').format('YYYY-MM'),
  endMonth: moment().subtract(1, 'month').format('YYYY-MM'),
  province: '全省',
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const ReportData = (props) => {
  const {
    dispatch,
    assessMonthStatistics: { reportTableData, cityList },
    form,
    form: { validateFields, resetFields, getFieldValue },
    loading,
  } = props;
  const [params, setParams] = useState(defaultPagination);

  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    setExportLoading(true)
    const newParams = {
      ...params,
      // currentPage: 1,
      // pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/monthlyExamine/exportCityMonthData',
      decode: true,
      params: newParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const getPageList = (params) => {
    dispatch({
      type: 'assessMonthStatistics/getReportCityMonthlyData',
      payload: params,
    });
    setParams(params);
  };

  //查询
  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const newParams = {
        ...params,
        ...defaultPagination,
        startMonth: months && months.length > 0 ? moment(months[0]).format('YYYY-MM') : '',
        endMonth: months && months.length > 0 ? moment(months[1]).format('YYYY-MM') : '',
        ...rest,
      };
      getPageList(newParams);
    });
  };

  //表格切换
  const handleChangeTable = (pagination) => {
    const newParms = { ...params, currentPage: pagination.current, pageSize: pagination.pageSize };
    getPageList(newParms);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultPagination });
    getPageList({ ...defaultPagination });
  };

  useEffect(() => {
    getPageList({ ...params });
  }, []);

  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <CommonForm
          form={form}
          handleSearch={handleSearch}
          handleReset={handleReset}
          handleExport={handleExport}
          cityList={cityList}
          params={params}
          exportLoading={exportLoading}
        />
      </Form>
      {params.province == '全省' ? (
        <ReportProvinceTable
          params={params}
          tableData={reportTableData}
          loading={loading}
          handleChangeTable={handleChangeTable}
        />
      ) : (
        <ReportCityTable
          params={params}
          tableData={reportTableData}
          loading={loading}
          handleChangeTable={handleChangeTable}
        />
      )}
    </Card>
  );
};

export default connect(({ assessMonthStatistics, loading }) => ({
  assessMonthStatistics,
  loading: loading.effects['assessMonthStatistics/getReportCityMonthlyData'],
}))(Form.create()(ReportData));
