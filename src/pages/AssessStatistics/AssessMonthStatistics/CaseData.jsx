import { Form, Card } from 'antd';
import { connect } from 'dryad';
import { useEffect, useState } from 'react';
import moment from 'moment';
import { exportFile } from '@/utils/utils';
import ProvinceTable from './components/CaseProvinceTable';
import CityTable from './components/CaseCityTable';
import CommonForm from './components/CommonForm';

const defaultPagination = {
  // pageSize: 10,
  // currentPage: 1,
  dataType: 'involved',
  startMonth: moment().subtract(2, 'month').format('YYYY-MM'),
  endMonth: moment().subtract(1, 'month').format('YYYY-MM'),
  province: '全省',
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const CaseData = (props) => {
  const {
    dispatch,
    assessMonthStatistics: { involvedTableData, cityList },
    form,
    form: { validateFields, resetFields, getFieldValue },
    loading,
  } = props;
  const [params, setParams] = useState(defaultPagination);
  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    setExportLoading(true);
    const newParams = {
      ...params,
      // currentPage: 1,
      // pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/monthlyExamine/exportCityMonthData',
      decode: true,
      params: newParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const getPageList = (params) => {
    dispatch({
      type: 'assessMonthStatistics/getInvolvedCityMonthlyData',
      payload: params,
    });
    setParams(params);
  };

  const getCityList = () => {
    dispatch({
      type: 'assessMonthStatistics/getSystemConfigListByConfigType',
      payload: { configType: 'unit' },
    });
  };
  //查询
  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const newParams = {
        ...params,
        ...defaultPagination,
        startMonth: months && months.length > 0 ? moment(months[0]).format('YYYY-MM') : '',
        endMonth: months && months.length > 0 ? moment(months[1]).format('YYYY-MM') : '',
        ...rest,
      };
      getPageList(newParams);
    });
  };

  //表格切换
  const handleChangeTable = (pagination) => {
    const newParms = { ...params, currentPage: pagination.current, pageSize: pagination.pageSize };
    getPageList(newParms);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultPagination });
    getPageList({ ...defaultPagination });
  };

  useEffect(() => {
    getPageList({ ...params });
    getCityList();
  }, []);

  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <CommonForm
          form={form}
          handleSearch={handleSearch}
          handleReset={handleReset}
          handleExport={handleExport}
          cityList={cityList}
          params={params}
          exportLoading={exportLoading}
        />
      </Form>
      {params.province == '全省' ? (
        <ProvinceTable
          params={params}
          tableData={involvedTableData}
          loading={loading}
          handleChangeTable={handleChangeTable}
        />
      ) : (
        <CityTable
          params={params}
          tableData={involvedTableData}
          loading={loading}
          handleChangeTable={handleChangeTable}
        />
      )}
    </Card>
  );
};

export default connect(({ assessMonthStatistics, loading }) => ({
  assessMonthStatistics,
  loading: loading.effects['assessMonthStatistics/getInvolvedCityMonthlyData'],
}))(Form.create()(CaseData));
