import { connect } from 'dryad';
import { useEffect, useState } from "react";
import StandardTable from "@/components/StandardTable";
import { exportFile } from '@/utils/utils';
import LineChart from './LineChart'


const ReportProvinceTable = (props) => {
    const { tableData, loading, handleChangeTable, params } = props;

    const columns = [
        {
            title: "时间",
            dataIndex: "countMonth",
            width: 120,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "12321涉诈举报量",
            dataIndex: "dataCount",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "12321涉诈举报量环比涨幅（件次）",
            dataIndex: "monthIncrease",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "百万用户12321涉诈举报率（件次）",
            dataIndex: "millionUserRate",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "12321涉诈举报量超考核值百分比",
            dataIndex: "exceedExamineRate",
            width: 120,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "12321涉诈举报日均量",
            dataIndex: "dailyCount",
            width: 100,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "12321涉诈举报量日均量环比涨幅",
            dataIndex: "dailyCountIncrease",
            width: 120,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "12321涉诈举报量集团排名",
            dataIndex: "countOrderInProvince",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "百万用户12321涉诈举报率集团排名",
            dataIndex: "rateOrderInProvince",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        }
    ];

    //表格切换
    const handlePaginationTable = (pagination) => {
        handleChangeTable(pagination)
    };

    return (
        <>
            <LineChart pid={'1401'} params={params}/>
            <StandardTable
                columns={columns}
                loading={loading}
                data={{
                    list: tableData,
                    pagination:false
                    // pagination: {
                    //     current: params.currentPage,
                    //     pageSize: params.pageSize,
                    //     total: 0
                    // }
                }}
                onChange={handlePaginationTable}
                rowKey="id"
                showSelectCount={false}
                rowSelectionProps={false}
                scroll={{ x: true }}
            />
        </>
    );
}

export default ReportProvinceTable;
