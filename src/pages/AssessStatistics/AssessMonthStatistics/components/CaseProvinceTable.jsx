import { connect } from 'dryad';
import { useEffect, useState } from "react";
import StandardTable from "@/components/StandardTable";
import LineChart from './LineChart'


const CaseProvinceTable = (props) => {
    const { tableData, loading, handleChangeTable, params } = props;

    const columns = [
        {
            title: "时间",
            dataIndex: "countMonth",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "涉案量",
            dataIndex: "dataCount",
            width: 110,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "涉案量环比涨幅（件次）",
            dataIndex: "monthIncrease",
            width: 170,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "百万用户涉案率（件次）",
            dataIndex: "millionUserRate",
            width: 170,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "超考核值百分比",
            dataIndex: "exceedExamineRate",
            width: 120,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "涉案日均量",
            dataIndex: "dailyCount",
            width: 100,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "涉案日均量环比涨幅",
            dataIndex: "dailyCountIncrease",
            width: 140,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "涉诈量集团排名",
            dataIndex: "countOrderInProvince",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "百万用户涉诈率集团排名",
            dataIndex: "rateOrderInProvince",
            width: 170,
            align: "center",
            render: (text) => text || '--'
        }
    ];

    //表格切换
    const handlePaginationTable = (pagination) => {
        handleChangeTable(pagination)
    };

    return (
        <>
            <LineChart pid={'1400'} params={params}/>
            <StandardTable
                columns={columns}
                loading={loading}
                data={{
                    list: tableData,
                    pagination:false
                    // pagination: {
                    //     current: params.currentPage,
                    //     pageSize: params.pageSize,
                    //     total: 0
                    // }
                }}
                onChange={handlePaginationTable}
                rowKey="id"
                showSelectCount={false}
                rowSelectionProps={false}
                scroll={{ x: true }}
            />
        </>
    );
}

export default CaseProvinceTable;
