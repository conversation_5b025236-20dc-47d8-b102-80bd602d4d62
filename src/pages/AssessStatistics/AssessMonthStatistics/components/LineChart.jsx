import React, { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';
import { Row, Col, Checkbox, Empty } from 'antd'
import { connect } from 'dryad';

const CheckboxGroup = Checkbox.Group;

const LineChart = (props) => {
    const { dispatch, pid, params } = props
    const [checkedList, setCheckedList] = useState([])
    const [indeterminate, setIndeterminate] = useState(false)
    const [checkAll, setCheckAll] = useState(false)
    const [lineChartData, setLineChartData] = useState({})
    const [phoneCloseSubtag, setPhoneCloseSubtag] = useState([])
    const domRef = useRef(null);
    const myChartRef = useRef(null);

    const initCharts = (dom, data) => {
        myChartRef.current = dom && echarts.init(dom);
        myChartRef.current.clear()
        let seriesData = []
        let xAxisData = []
        for (let key in data) {
            seriesData.push({
                name: key,
                type: 'line',
                data: data?.[key]?.map((({ indicatorValue, ...rest }) => {
                    return { value: indicatorValue, ...rest }
                })),
            })
            xAxisData.push(data?.[key]?.map((item => item.countMonth)))
        }
        const options = {
            tooltip: {
                trigger: 'axis',
                extraCssText: 'z-index:9999;',
                appendToBody: true,
                position: function (point, params, dom, rect, size) {
                    // 固定在顶部
                    return [point[0] + 10, point[1] - 150];
                },
                formatter: (params) => {
                    let str = ''
                    params.forEach((item) => {
                        const unit = item.data.ifPercent ? '%' : ''
                        str +=
                            '<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:' + item.color + '"></span>' + item.seriesName + " : " + item.value + unit + "<br />";
                    });
                    return str;
                }
            },
            legend: {
                show: false,
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xAxisData?.[0]
            },
            yAxis: {
                type: 'value'
            },
            series: seriesData
        };
        myChartRef.current.setOption({
            ...options,
        });
    };

    const filterLineData = (checkedList) => {
        let newObj = {}
        checkedList.map((item => {
            for (let key in lineChartData) {
                if (item == key) {
                    newObj[item] = lineChartData[item]
                }
            }
        }))
        if (domRef.current) {
            initCharts(domRef.current, newObj);
        }
    }

    const onChange = checkedList => {
        setCheckAll(checkedList.length === phoneCloseSubtag.length)
        setIndeterminate(!!checkedList.length && checkedList.length < phoneCloseSubtag.length)
        setCheckedList(checkedList)
        filterLineData(checkedList)
    };

    const onCheckAllChange = e => {
        setCheckAll(e.target.checked)
        setIndeterminate(false)
        setCheckedList(e.target.checked ? phoneCloseSubtag.map((item => item.name)) : [])
        filterLineData(e.target.checked ? phoneCloseSubtag.map((item => item.name)) : [])
    };

    const getPhoneCloseTrend = () => {
        dispatch({
            type: 'assessMonthStatistics/getLineChartData',
            payload: params,
            callback: (res) => {
                const data = res.data || {}
                setLineChartData(data)
            }
        });
    }

    useEffect(() => {
        dispatch({
            type: 'assessMonthStatistics/getConfigTypeByPidAndConfigType',
            payload: { pid, configType: 'province_examine_indicator' },
            callback: (res) => {
                const tagData = res.data || []
                setPhoneCloseSubtag(tagData)
                setCheckAll(true)
                setIndeterminate(false)
                setCheckedList(tagData?.map((item => item.name)))
            }
        });
        return () => {
            if (domRef.current) {
                myChartRef.current = echarts.init(domRef.current);
                echarts.dispose(myChartRef.current);
            }
        };
    }, [pid]);

    useEffect(() => {
        getPhoneCloseTrend()
        return () => {
            if (domRef.current) {
                myChartRef.current = echarts.init(domRef.current);
                echarts.dispose(myChartRef.current);
            }
        };
    }, [params]);

    useEffect(() => {
        if (domRef.current) {
            initCharts(domRef.current, lineChartData);
        }
    }, [domRef.current, JSON.stringify(lineChartData) !== '{}', lineChartData])

    return (
        <div>
            {
                JSON.stringify(lineChartData) !== '{}' ? <div
                    className="commonCharts"
                    ref={domRef}
                    style={{ width: '100%', height: 300 }}
                ></div> : <Empty style={{ marginTop: 50 }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
            }
            <Row>
                {phoneCloseSubtag?.length > 0 && <Col span={2}>
                    <Checkbox
                        indeterminate={indeterminate}
                        onChange={onCheckAllChange}
                        checked={checkAll}>全部</Checkbox>
                </Col>}
                <Col span={22}>
                    <CheckboxGroup
                        value={checkedList}
                        onChange={onChange}
                        style={{ width: '100%' }}
                    >
                        <Row gutter={6}>
                            {
                                phoneCloseSubtag?.map(((item, index) => {
                                    return (
                                        <Col span={5}  key={index}>
                                            <Checkbox value={item.name}>{item?.name}</Checkbox>
                                        </Col>
                                    )
                                }))
                            }
                        </Row>
                    </CheckboxGroup>
                </Col>
            </Row>
        </div>
    )
}

export default connect(({ assessMonthStatistics }) => ({
    assessMonthStatistics
}))(LineChart)