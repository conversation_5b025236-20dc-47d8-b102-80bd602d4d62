import { connect } from 'dryad';
import { useEffect, useState } from "react";
import StandardTable from "@/components/StandardTable";


const ReportCityTable = (props) => {
  const { tableData, loading, handleChangeTable, params } = props;


  const columns = [
    {
      title: "时间",
      dataIndex: "countMonth",
      width: 130,
      align: "center",
      render(_, row, Tindex) {
        let rowSpan = 1;
        let arrIndex = 0;
        tableData.forEach((item, Dindex) => {
          if (item.id === row.id) {
            arrIndex = Dindex;// 先获取countMonth所在的arr的Dindex
          }
        });
        if (Tindex === 0) { // 当值在多个页面出现时重新记录rowSpan
          tableData.forEach((item, Dindex) => {
            if (Dindex > arrIndex) {
              if (item.countMonth=== row.countMonth) {
                rowSpan += 1;
              }
            }
          });
        } else if (tableData[arrIndex].countMonth=== tableData[arrIndex - 1].countMonth) {
            rowSpan = 0;
          } else {
            tableData.forEach((item, Dindex) => {
              if (Dindex > arrIndex) {
                if (item.countMonth=== row.countMonth) {
                  rowSpan += 1;
                }
              }
            });
          }
          return {
            children: row.countMonth,
            props: {
              rowSpan,
            },
          }
        }
    },
    {
      title: "地市",
      dataIndex: "city",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "12321涉诈举报量",
      dataIndex: "dataCount",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "12321涉诈举报量环比涨幅（件次）",
      dataIndex: "monthIncrease",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "百万用户12321涉诈举报率（件次）",
      dataIndex: "millionUserRate",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "12321涉诈举报量考核值",
      dataIndex: "examineCount",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "12321涉诈举报量超考核值百分比",
      dataIndex: "exceedExamineRate",
      width: 120,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "12321涉诈举报日均量",
      dataIndex: "dailyCount",
      width: 100,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "12321涉诈举报量日均量环比涨幅",
      dataIndex: "dailyCountIncrease",
      width: 120,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "12321涉诈举报量全省排名",
      dataIndex: "countOrderInProvince",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "百万用户12321涉诈举报率全省排名",
      dataIndex: "rateOrderInProvince",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    }
  ];

  //表格切换
  const handlePaginationTable = (pagination) => {
    handleChangeTable(pagination)
  };
  return (
    <div style={{ marginTop: 24 }}>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: tableData,
          pagination:false
          // pagination: {
          //   current: params.currentPage,
          //   pageSize: params.pageSize,
          //   total: 0
          // }
        }}
        onChange={handlePaginationTable}
        rowKey="id"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ x: true }}
      />
    </div>
  );
}

export default ReportCityTable;
