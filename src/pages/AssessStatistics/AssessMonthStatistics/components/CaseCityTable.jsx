import StandardTable from "@/components/StandardTable";


const CaseCityTable = (props) => {
  const { tableData, loading, handleChangeTable, params } = props;

  const columns = [
    {
      title: "时间",
      dataIndex: "countMonth",
      width: 130,
      align: "center",
      render(_, row, Tindex) {
        let rowSpan = 1;
        let arrIndex = 0;
        tableData.forEach((item, Dindex) => {
          if (item.id === row.id) {
            arrIndex = Dindex;// 先获取countMonth所在的arr的Dindex
          }
        });
        if (Tindex === 0) { // 当值在多个页面出现时重新记录rowSpan
          tableData.forEach((item, Dindex) => {
            if (Dindex > arrIndex) {
              if (item.countMonth === row.countMonth) {
                rowSpan += 1;
              }
            }
          });
        } else if (tableData[arrIndex].countMonth === tableData[arrIndex - 1].countMonth) {
          rowSpan = 0;
        } else {
          tableData.forEach((item, Dindex) => {
            if (Dindex > arrIndex) {
              if (item.countMonth === row.countMonth) {
                rowSpan += 1;
              }
            }
          });
        }
        return {
          children: row.countMonth,
          props: {
            rowSpan,
          },
        }
      }
    },
    {
      title: "地市",
      dataIndex: "city",
      width: 100,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉案量",
      dataIndex: "dataCount",
      width: 100,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉案量环比涨幅（件次）",
      dataIndex: "monthIncrease",
      width: 180,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "百万用户涉案率（件次）",
      dataIndex: "millionUserRate",
      width: 180,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉案量考核值",
      dataIndex: "examineCount",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "超考核值百分比",
      dataIndex: "exceedExamineRate",
      width: 120,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉案日均量",
      dataIndex: "dailyCount",
      width: 100,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉案日均量环比涨幅",
      dataIndex: "dailyCountIncrease",
      width: 140,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉诈量全省排名",
      dataIndex: "countOrderInProvince",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "百万用户涉诈率全省排名",
      dataIndex: "rateOrderInProvince",
      width: 180,
      align: "center",
      render: (text) => text || '--'
    }
  ];

  //表格切换
  const handlePaginationTable = (pagination) => {
    handleChangeTable(pagination)
  };
  return (
    <div style={{ marginTop: 24 }}>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: tableData,
          pagination:false
          // pagination: {
          //   current: params.currentPage,
          //   pageSize: params.pageSize,
          //   total: 0
          // }
        }}
        onChange={handlePaginationTable}
        rowKey="id"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ x: true }}
      />
    </div>
  );
}

export default CaseCityTable;
