import React, { useState } from 'react'
import { Button, Col, Form, Row, Select, DatePicker } from "antd";
import moment from 'moment';

const { Option } = Select;
const { RangePicker } = DatePicker;

const CommonForm = (props) => {
    const { form: { getFieldDecorator, setFieldsValue,getFieldValue }, handleSearch, handleReset, handleExport,cityList,params ,exportLoading} = props
    const place = getFieldValue('province')

    const changePanel = (v) => {
        setFieldsValue({ months: v })
    }

    const changeDate = (v) => {
        setFieldsValue({ months: v })
    }
    return (
        <>
            <Row>
                <Col span={8}>
                    <Form.Item label="归属地">
                        {getFieldDecorator('province', {
                            initialValue: params.province
                        })(
                            <Select placeholder="请选择" allowClear>
                                <Option value={'全省'}>全省</Option>
                                <Option value={'地市'}>地市</Option>
                            </Select>,
                        )}
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="地市">
                        {getFieldDecorator('city')(
                            <Select placeholder="请选择" allowClear disabled={place == '全省'} >
                                {
                                    cityList?.map((item)=>{
                                        return (
                                            <Option value={item.name} key={item.id}>{item.name}</Option>
                                        )
                                    })
                                }
                            </Select>,
                        )}
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="时间">
                        {getFieldDecorator("months", {
                            initialValue: [moment(params.startMonth,'YYYY-MM'), moment(params.endMonth,'YYYY-MM')]
                        })(
                            <RangePicker
                                format={'YYYY-MM'}
                                mode={['month', 'month']}
                                onPanelChange={changePanel}
                                onChange={changeDate}
                            />
                        )}
                    </Form.Item>
                </Col>
                <Col span={24} style={{ textAlign: 'right' }}>
                    <Button
                        type="primary"
                        onClick={handleSearch}
                    >
                        查询
                    </Button>
                    <Button onClick={handleReset} style={{ margin: '0px 10px' }}>重置</Button>
                    <Button
                        type="primary"
                        onClick={handleExport}
                        loading={exportLoading}
                    >
                        导出
                    </Button>
                </Col>
            </Row>
        </>
    )
}

export default CommonForm