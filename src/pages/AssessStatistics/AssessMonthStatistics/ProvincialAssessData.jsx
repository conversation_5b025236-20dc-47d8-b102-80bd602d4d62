import { Form, Card } from 'antd';
import { connect } from 'dryad';
import { useEffect, useState } from 'react';
import moment from 'moment';
import { exportFile } from '@/utils/utils';
import ProvincialProvinceTable from './components/ProvincialProvinceTable';
import ProvincialCityTable from './components/ProvincialCityTable';
import CommonForm from './components/CommonForm';

const defaultPagination = {
  // pageSize: 10,
  // currentPage: 1,
  dataType: 'province',
  startMonth: moment().subtract(2, 'month').format('YYYY-MM'),
  endMonth: moment().subtract(1, 'month').format('YYYY-MM'),
  province: '全省',
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const ProvincialAssessData = (props) => {
  const {
    dispatch,
    assessMonthStatistics: { provinceTableData, cityList },
    form,
    form: { validateFields, resetFields, getFieldValue },
    loading,
  } = props;
  const [params, setParams] = useState(defaultPagination);
  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
      setExportLoading(true)
    const newParams = {
      ...params,
      // currentPage: 1,
      // pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/monthlyExamine/exportCityMonthData',
      decode: true,
      params: newParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const getPageList = (params) => {
    dispatch({
      type: 'assessMonthStatistics/getProvinceCityMonthlyData',
      payload: params,
    });
    setParams(params);
  };

  //查询
  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const newParams = {
        ...params,
        ...defaultPagination,
        startMonth: months && months.length > 0 ? moment(months[0]).format('YYYY-MM') : '',
        endMonth: months && months.length > 0 ? moment(months[1]).format('YYYY-MM') : '',
        ...rest,
      };
      getPageList(newParams);
    });
  };

  //表格切换
  const handleChangeTable = (pagination) => {
    const newParms = { ...params, currentPage: pagination.current, pageSize: pagination.pageSize };
    getPageList(newParms);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultPagination });
    getPageList({ ...defaultPagination });
  };

  useEffect(() => {
    getPageList({ ...params });
  }, []);

  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <CommonForm
          form={form}
          handleSearch={handleSearch}
          handleReset={handleReset}
          handleExport={handleExport}
          cityList={cityList}
          params={params}
          exportLoading={exportLoading}
        />
      </Form>
      {params.province == '全省' ? (
        <ProvincialProvinceTable
          params={params}
          tableData={provinceTableData}
          loading={loading}
          handleChangeTable={handleChangeTable}
        />
      ) : (
        <ProvincialCityTable
          params={params}
          tableData={provinceTableData}
          loading={loading}
          handleChangeTable={handleChangeTable}
        />
      )}
    </Card>
  );
};

export default connect(({ assessMonthStatistics, loading }) => ({
  assessMonthStatistics,
  loading: loading.effects['assessMonthStatistics/getProvinceCityMonthlyData'],
}))(Form.create()(ProvincialAssessData));
