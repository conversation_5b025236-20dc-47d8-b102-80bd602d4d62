import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input, Modal } from "antd";
import { connect } from 'dryad';
import { useEffect, useState } from "react";
import moment from 'moment';
import StandardTable from "@/components/StandardTable";
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal'
import { Licensee } from 'ponshine';

const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
  pageSize: 10,
  currentPage: 1,
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const CaseAndReportAssess = (props) => {
  const { dispatch, citiesAndDailyAssess: { involvedReportRank }, form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
    loading } = props;
  const [params, setParams] = useState(defaultPagination);
  const [provinceList, setProvinceList] = useState([])
  const [selectedRows, setSelectedRows] = useState([]);
  const [importVisible, setImportVisible] = useState(false)

  const columns = [
    {
      title: "月份",
      dataIndex: "month",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "考核指标",
      dataIndex: "assessmentIndicators",
      width: 120,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "省份",
      dataIndex: "province",
      width: 100,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉案/举报数量",
      dataIndex: "involvedReportCount",
      width: 120,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "涉案/举报率",
      dataIndex: "involvedReportRate",
      width: 130,
      align: "center",
      render: (text) => text || '--'
    },
    {
      title: "当月排名情况",
      dataIndex: "ranking",
      width: 120,
      align: "center",
      render: (text) => text || '--'
    },
  ];

  const [exportLoading, setExportLoading] = useState(false)
  const handleExport = () => {
    setExportLoading(true);
    const newParams = {
      ...params,
      currentPage: 1,
      pageSize: 10,
    }
    exportFile({
      urlAPi: '/api/hn/statisticsInvolvedReportRank/exportStatisticsInvolvedReportRank',
      decode: true,
      params: newParams,
      method: 'POST',
      callback:()=>{
        setExportLoading(false);
    }
    });
  };

  const getPageList = (params) => {
    dispatch({
      type: "citiesAndDailyAssess/pageStatisticsInvolvedReportRank",
      payload: params,
    });
    setParams(params)
  }

  const getProvinceList = () => {
    dispatch({
      type: 'antiFraudAssess/getSystemConfigListByConfigType',
      payload: { configType: 'province' },
      callback: (res) => {
        if (res && res.code == '200' && res.data) {
          setProvinceList(res.data || []);
        } else {
          setProvinceList([]);
        }
      },
    });
  };

  //查询
  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const newParams = {
        ...params,
        ...defaultPagination,
        startMonth: months && months.length > 0 ? moment(months[0]).format('YYYY-MM') : '',
        endMonth: months && months.length > 0 ? moment(months[1]).format('YYYY-MM') : '',
        ...rest,
      }
      getPageList(newParams)
    });
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    const newParms = { ...params, currentPage: pagination.current, pageSize: pagination.pageSize }
    getPageList(newParms)
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultPagination })
    getPageList({ ...defaultPagination })
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '考核排名查询删除',
      content: '是否确认删除选中数据?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        dispatch({
          type: 'citiesAndDailyAssess/deleteByIdsStatisticsInvolvedReportRank',
          payload: { idList: selectedRows.map((item => item.id)) },
          callback: (res) => {
            if (res && res.code == 200) {
              message.success(res.message)
              getPageList({ ...params, ...defaultPagination })
              setSelectedRows([])
            } else {
              message.error(res.message)
            }
          },
        });
      },
    });
  }

  const handleImport = () => {
    setImportVisible(true)
  }

  const hideImport = () => {
    setImportVisible(false)
  }

  const [confirmLoading, setConfirmLoading] = useState(false);

  const onImport = (values, callback) => {
    setConfirmLoading(true);
    dispatch({
      type: 'citiesAndDailyAssess/importStatisticsInvolvedReportRank',
      payload: values,
      callback: (res) => {
        setConfirmLoading(false);
        if (res.code == 200) {
          message.success(res.message || '导入成功');
          getPageList({ ...params, ...defaultPagination })
          hideImport()
        } else {
          if (res.code == 401) {
            callback()
            getPageList({ ...defaultPagination })
          } else {
            message.error(res.message);
          }
        }
      },
    });
  }

  const changeDate = (v) => {
    setFieldsValue({ months: v })
  }

  const changePanel = (v) => {
    setFieldsValue({ months: v })
  }

  useEffect(() => {
    getPageList({ ...params })
    getProvinceList()
  }, [])


  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="工信部考核月份">
              {getFieldDecorator("months")(
                <RangePicker
                  format={'YYYY-MM'}
                  mode={['month', 'month']}
                  onPanelChange={changePanel}
                  onChange={changeDate}
                />
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="考核指标">
              {getFieldDecorator('assessmentIndicators')(
                <Select placeholder="请选择" allowClear>
                  <Option value='涉案诈骗'>涉案诈骗</Option>
                  <Option value='举报诈骗'>举报诈骗</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="省份">
              {getFieldDecorator('province')(
                <Select placeholder="请选择" allowClear>
                  {provinceList.map((item) => (
                    <Option value={item.name} key={item.id}>{item.name}</Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={24} style={{ textAlign: 'right', marginBottom: 24 }}>
            <Licensee license="StatisticsInvolvedReportRank_pageStatisticsInvolvedReportRank">
              <Button
                type="primary"
                onClick={handleSearch}
              >
                查询
              </Button>
              <Button onClick={handleReset} style={{ margin: '0px 10px' }}>重置</Button>
            </Licensee>
            <Licensee license="StatisticsInvolvedReportRank_exportStatisticsInvolvedReportRank">
              <Button
                type="primary"
                onClick={handleExport}
                loading={exportLoading}
                >
                导出
              </Button>
            </Licensee>
            <Licensee license="StatisticsInvolvedReportRank_importStatisticsInvolvedReportRank">
              <Button
                onClick={handleImport}
                style={{ margin: '0px 10px' }}
                type="primary"
              >
                导入
              </Button>
            </Licensee>
            <Licensee license="StatisticsInvolvedReportRank_deleteByIdsStatisticsInvolvedReportRank">
              <Button
                onClick={handleDelete}
                disabled={selectedRows.length === 0}
                type="danger"
                icon="delete"
              >
                删除
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: involvedReportRank?.items || [],
          pagination: {
            current: params.currentPage,
            pageSize: params.pageSize,
            total: involvedReportRank?.totalNum || 0
          }
        }}
        onChange={handlePaginationTable}
        rowKey="id"
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        scroll={{ x: true }}
      />
      {
        importVisible && <BatchImportModal
          title='批量导入'
          visible={importVisible}
          onImport={onImport}
          onClose={hideImport}
          loading={confirmLoading}
          errorExportUrl={'/api/hn/statisticsInvolvedReportRank/exportStatisticsInvolvedReportRankError'}
          downTemplateUrl={`template/getTemplate?templateCode=involvedReportRankData`}
        />
      }
    </Card>
  );
}

export default connect(({ citiesAndDailyAssess, antiFraudAssess, loading }) => ({
  citiesAndDailyAssess,
  antiFraudAssess,
  loading: loading.effects["citiesAndDailyAssess/pageStatisticsInvolvedReportRank"],
}))(Form.create()(CaseAndReportAssess));
