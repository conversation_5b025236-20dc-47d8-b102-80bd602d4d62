import { Row, Col } from 'antd';
import React, { useState, useEffect } from 'react';

import CommonCard from '@/components/CommonCard';
import BarTopChart from '@/components/Chart/BarTopChart';
import LineChart from '@/components/Chart/LineChart';

import request from 'ponshine-request';

const barTopChartPorps = {
  height: 220,
  width: '100%',
  isMutipleYAxisIndex: false,
  seriesConfig: { barWidth: 10, triggerEvent: false },
  gridOption: { left: 220, top: 20, bottom: 50 },
  axisLabelOption: {
    width: 200,
    rotate: 0,
    overflow: 'none',
    formatter: (value) => {
      if (value.length > 16) {
        return value.slice(0, 16) + '...';
      } else {
        return value;
      }
    },
  },
  yAxisOption: {
    triggerEvent: true,
  },
  // xAxisOption: {
  //   triggerEvent: false,
  // },
};

const lineChartPorps = {
  height: 220,
  width: '100%',
  isMutipleYAxisIndex: false,
  seriesConfig: {},
  gridOption: { left: 80, top: 20, bottom: 50 },
  axisLabelOption: { width: 100, rotate: 0 },
};

let arr = Array.from({ length: 12 }, (_, index) => ({
  xvalue: index + 1 + '月',
  yvalue: Math.floor(Math.random() * 100) + 1,
}));

export default function Center({ searchValue, typeList }) {
  const [barTopData, setBarTopData] = useState([]);
  const [lineData, setLineData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [accessPointName, setAccessPointName] = useState('');

  const [lineLoading, setLineLoading] = useState(false);

  const onEvents = {
    click: (params) => {
      const { value, componentType } = params;
      if (componentType === 'yAxis') {
        getLineData(value);
      }
    },
  };
  const getBarTopData = async () => {
    setLoading(true);
    const response = await request('/api/hn/accessPointStatistics/getTopFiveAccessPoint', {
      method: 'POST',
      data: {
        ...searchValue,
      },
      requestType: 'form',
    });

    setLoading(false);
    if (response.code === 200) {
      setBarTopData(response?.data?.reverse() || []);
    } else {
      message.error(response.message);
    }
  };
  const getLineData = async (accessPoint) => {
    setAccessPointName(accessPoint);
    if (!accessPoint) {
      setLineData([]);
      return;
    }
    setLineLoading(true);
    const response = await request(
      '/api/hn/accessPointStatistics/getSpecificAccessPointFraudTrend',
      {
        method: 'POST',
        data: {
          accessPoint,
          ...searchValue,
        },
        requestType: 'form',
      },
    );

    setLineLoading(false);
    if (response.code === 200) {
      setLineData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    searchValue && getBarTopData();
  }, [searchValue]);

  useEffect(() => {
    getLineData(barTopData?.[barTopData.length - 1]?.xvalue);
  }, [barTopData]);
  return (
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <CommonCard title={`${typeList?.[searchValue.type]}TOP网点`}>
          <BarTopChart
            {...barTopChartPorps}
            // legendData={['']}
            xData={barTopData?.map((ele) => ele?.xvalue)}
            yData={[{ data: barTopData?.map((ele) => ele?.yvalue) }]}
            loading={loading}
            onEvents={onEvents}
          />
        </CommonCard>
      </Col>
      <Col span={12}>
        <CommonCard title={`${accessPointName ? accessPointName + '-' : ''}${typeList?.[searchValue.type]}关停号码趋势分析`}>
          <LineChart
            {...lineChartPorps}
            xData={lineData?.map((ele) => ele?.xvalue)}
            yData={[{ data: lineData?.map((ele) => ele?.yvalue) }]}
            loading={lineLoading}
          />
        </CommonCard>
      </Col>
    </Row>
  );
}
