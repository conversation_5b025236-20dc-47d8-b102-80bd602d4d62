import React, { useEffect, useState } from 'react';
import StandardTable from '@/components/StandardTable';
import request from 'ponshine-request';
let externalColumns = [
  {
    title: '网点名称',
    width: 200,
    dataIndex: 'accessPoint',
    ellipsis: true,
  },
  {
    title: '涉案号码数',
    width: 100,
    dataIndex: 'firstColumn',
    ellipsis: true,
  },
  {
    title: '专班研判(SH-A)诈骗号码数',
    width: 140,
    dataIndex: 'secondColumn',
    ellipsis: true,
  },
  {
    title: '外呼研判诈骗号码数',
    width: 140,
    dataIndex: 'thirdColumn',
    ellipsis: true,
  },
  {
    title: '黑终端号码数',
    width: 100,
    dataIndex: 'fourthColumn',
    ellipsis: true,
  },
  {
    title: '短信诈骗号码数',
    width: 120,
    dataIndex: 'fifthColumn',
    ellipsis: true,
  },
  {
    title: '12321举报号码数',
    width: 140,
    dataIndex: 'sixthColumn',
    ellipsis: true,
  },
  {
    title: '省内客服号码数',
    width: 100,
    dataIndex: 'seventhColumn',
    ellipsis: true,
  },
];
let bigDataMonitoringColumns = [
  {
    title: '网点名称',
    width: 200,
    dataIndex: 'accessPoint',
    ellipsis: true,
  },
  {
    title: '高频主叫异常',
    width: 100,
    dataIndex: 'firstColumn',
    ellipsis: true,
  },
  {
    title: '手机终端变化',
    width: 120,
    dataIndex: 'secondColumn',
    ellipsis: true,
  },
  {
    title: '全网黑终端用户',
    width: 140,
    dataIndex: 'thirdColumn',
    ellipsis: true,
  },
  {
    title: '沉默7天终端异动',
    width: 100,
    dataIndex: 'fourthColumn',
    ellipsis: true,
  },
  {
    title: '本机呼转话务异常',
    width: 120,
    dataIndex: 'fifthColumn',
    ellipsis: true,
  },
  {
    title: '国漫异常通用',
    width: 140,
    dataIndex: 'sixthColumn',
    ellipsis: true,
  },
  {
    title: '固话话务异常',
    width: 100,
    dataIndex: 'seventhColumn',
    ellipsis: true,
  },
];
export default function Bottom({ searchValue }) {
  const columns = {
    replay: externalColumns, //外部通报
    bigData: bigDataMonitoringColumns, //大数据检测
  };

  const [listData, setlistData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async () => {
    setLoading(true);
    const response = await request(
      '/api/hn/accessPointStatistics/getAccessPointFraudTypeCountList',
      {
        method: 'POST',
        data: {
          ...searchValue,
        },
        requestType: 'form',
      },
    );

    setLoading(false);
    if (response.code === 200) {
      setlistData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getData();
  }, [searchValue]);

  return (
    <StandardTable
      showSelectCount={false}
      rowSelectionProps={false}
      columns={columns[searchValue.type]}
      data={{
        list: listData,
        pagination: false,
      }}
      loading={loading}
      rowKey="id"
      scroll={{ x: 1000 }}
    />
  );
}
