import React, { useState, useEffect } from 'react';
import { Card, Form, DatePicker, Select, Button } from 'antd';
import Center from './Center';
import Bottom from './Bottom';

const { RangePicker } = DatePicker;
import moment from 'moment';

const getDateFormatter = (date) => {
  return {
    startDate: date?.[0]?.format('YYYY-MM-DD'),
    endDate: date?.[1]?.format('YYYY-MM-DD'),
  };
};

const Index = ({ form: { getFieldDecorator, getFieldsValue, setFieldsValue } }) => {
  const typeList = {
    replay: '外部通报',
    bigData: '大数据监测',
  };
  const initialType = 'replay';
  const initialDate = [moment().subtract(1, 'day'), moment().subtract(1, 'day')];
  const [searchValue, setSearchValue] = useState({
    ...getDateFormatter(initialDate),
    type: initialType,
  });
  const disabledDate = (current) => {
    return current && current >= moment().endOf('day');
  };

  const handleSearch = () => {
    const formValues = getFieldsValue();
    setSearchValue({ ...getDateFormatter(formValues.time), type: formValues.type });
  };
  const handleReset = () => {
    setFieldsValue({
      time: initialDate,
      type: initialType,
    });
    setSearchValue({ ...getDateFormatter(initialDate), type: initialType });
  };

  return (
    <Card>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="时间范围">
          {getFieldDecorator('time', {
            initialValue: initialDate,
          })(<RangePicker disabledDate={disabledDate} allowClear={false} />)}
        </Form.Item>
        <Form.Item label="类型">
          {getFieldDecorator('type', {
            initialValue: initialType,
          })(
            <Select placeholder="请选择" style={{ width: 150 }}>
              {Object.keys(typeList).map((ele, index) => (
                <Select.Option key={index} value={ele}>
                  {typeList[ele]}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item>
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
            查询
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Form.Item>
      </Form>
      <Center searchValue={searchValue} typeList={typeList} />
      <Bottom searchValue={searchValue} />
    </Card>
  );
};
export default Form.create()(Index);
