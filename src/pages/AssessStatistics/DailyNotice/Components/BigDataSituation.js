import React, { useState, useEffect, useRef } from 'react';
import { Button, message } from 'antd';
import Echarts from '@/components/Echarts';
import request from '@/utils/request';
import html2canvas from 'html2canvas';

const Index = (props) => {
  const { searchValues } = props;
  const [data, setData] = useState([]);
  const [maxLength, setMaxLength] = useState([]);
  const echartsRef = useRef();
  useEffect(() => {
    getData();
  }, [searchValues.startDateTime, searchValues.endDateTime]);
  const getData = () => {
    request({
      url: '/api/hn/daily_report/bigData_fraud',
      method: 'POST',
      requestType: 'json',
      data: {
        ...searchValues,
        pageNum: 1,
        pageSize: 999,
      },
    }).then((res) => {
      if (res.code === 200) {
        if (res.data) {
          let list = Object.keys(res.data).map((i) => ({
            label: i,
            value: res.data[i] ? res.data[i] * 1 : 0,
          }));
          let sortData = list.sort((a, b) => a.value - b.value);
          let maxLength = 0;
          sortData.forEach((i) => {
            if (i.label.length > maxLength) maxLength = i.label.length;
          });
          setData(sortData);
          setMaxLength(maxLength);
        } else {
          setData([]);
          setMaxLength(0);
        }
      } else {
        setData([]);
        setMaxLength(0);
        message.error(res.message);
      }
    });
  };

  let option = {
    title: {
      text: '省内大数据关停风险号码',
      left: 'center',
      top: 20,
      textStyle: {
        fontWeight: 'normal',
        fontSize: 14,
      },
    },

    grid: {
      left: maxLength * 1,
      right: '4%',
      bottom: '4%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },

    yAxis: {
      type: 'category',
      data: data.map((i) => i.label),
      axisLabel: {
        textStyle: {
          color: '#000', //更改坐标轴文字颜色
          fontSize: 12, //更改坐标轴文字大小
        },
      },
      // tooltip: {
      //   show: true,
      //   formatter: function (params) {
      //     return '详细信息：' + params.name;
      //   },
      // },
    },
    series: [
      {
        data: data.map((i) => i.value || 0),
        type: 'bar',
        barWidth: data.length < 8 ? '40%' : '60%',
        label: {
          show: true,
          position: 'right',
        },
        itemStyle: {
          normal: {
            color: '#0CB155',
          },
        },
      },
    ],
  };
  const [loading, setLoading] = useState(false)
  return (
    <div>
      <div>
        <Button
          style={{ marginTop: '10px' }}
          size="small"
          type="primary"
          loading={loading}
          onClick={() => {
            if (!document.getElementById('bigData')) return;
            setLoading(true);
            html2canvas(document.getElementById('bigData')).then(function (canvas) {
              var img = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream');
              // 创建a标签，实现下载
              var creatIMg = document.createElement('a');
              creatIMg.download = '省内大数据关停风险号码.png'; // 设置下载的文件名，
              creatIMg.href = img; // 下载url
              document.body.appendChild(creatIMg);
              creatIMg.click();
              creatIMg.remove(); // 下载之后把创建的元素删除
            }).finally(()=>{
              setLoading(false)
            });
          }}
        >
          导出
        </Button>
      </div>
      <Echarts echartsId="bigData" ref={echartsRef} option={option} style={{ height: '420px' }} />
    </div>
  );
};
export default Index;
