import React, { useState, useEffect, useRef } from 'react';
import { Button, message } from 'antd';
import Echarts from '@/components/Echarts';
import request from '@/utils/request';
import html2canvas from 'html2canvas';
const Index = (props) => {
  const { searchValues } = props;
  const [data, setData] = useState([]);
  const echartsRef = useRef();
  useEffect(() => {
    getData();
  }, [searchValues.startDateTime, searchValues.endDateTime]);
  const getData = () => {
    request({
      url: '/api/hn/daily_report/department_fraud',
      method: 'POST',
      requestType: 'json',
      data: {
        ...searchValues,
        pageNum: 1,
        pageSize: 999,
      },
    }).then((res) => {
      if (res.code === 200) {
        if (res.data) {
          let list = Object.keys(res.data).map((i) => ({
            cityName: i,
            phoneNumberCount: res.data[i] ? res.data[i] * 1 : 0,
          }));
          let sortData = list.sort((a, b) => b.phoneNumberCount - a.phoneNumberCount);
          setData(sortData);
        } else {
          setData([]);
        }
      } else {
        setData([]);
        message.error(res.message);
      }
    });
  };

  const option = {
    title: {
      text: '省内大数据关停风险号码',
      left: 'center',
      top: 20,
      textStyle: {
        fontWeight: 'normal',
        fontSize: 14,
      },
    },
    grid: {
      left: '8%',
    },
    xAxis: {
      type: 'category',
      data: data.map((i) => i.cityName),
      axisLabel: {
        rotate: 70,
        textStyle: {
          color: '#000', //更改坐标轴文字颜色
          fontSize: 12, //更改坐标轴文字大小
          textShadowBlur: 0,
        },
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: data.map((i) => i.phoneNumberCount || 0),
        type: 'bar',
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
        },
        itemStyle: {
          normal: {
            color: '#5B9BD5',
          },
        },
      },
    ],
  };

  const [loading, setLoading] = useState(false)

  return (
    <div>
      <div>
        <Button
          style={{ marginTop: '10px' }}
          size="small"
          type="primary"
          loading={loading}
          onClick={() => {
            if (!document.getElementById('department')) return;
            setLoading(true);
            html2canvas(document.getElementById('department')).then(function (canvas) {
              var img = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream');
              // 创建a标签，实现下载
              var creatIMg = document.createElement('a');
              creatIMg.download = '省内大数据关停风险号码.png'; // 设置下载的文件名，
              creatIMg.href = img; // 下载url
              document.body.appendChild(creatIMg);
              creatIMg.click();
              creatIMg.remove(); // 下载之后把创建的元素删除
            }).finally(()=>{
              setLoading(false)
            });
          }}
        >
          导出
        </Button>
      </div>
      <Echarts
        echartsId="department"
        ref={echartsRef}
        option={option}
        style={{ height: '420px' }}
      />
    </div>
  );
};
export default Index;
