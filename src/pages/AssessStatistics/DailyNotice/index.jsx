import React, { useReducer, useEffect, useRef, useState } from 'react';
import { Row, Col, Form, Button, Select, message, Card, Input, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import DepartmentSituation from './Components/DepartmentSituation';
import BigDataSituation from './Components/BigDataSituation';
import moment from 'moment';
import { exportFile } from '@/utils/utils';
import request from '@/utils/request';
import styles from './index.less';
const defaultPagination = {
  pageSize: 10,
  current: 1,
};
const FormItem = Form.Item;
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const { RangePicker } = DatePicker;
const Index = (props) => {
  const { form, location } = props;
  const [state, setState] = useReducer(reducer, {});
  const {
    dataSource = [{}, {}],
    total = 0,
    // pagination = { ...defaultPagination },
    loading = false,
    searchValues = {},
  } = state;

  useEffect(() => {
    initTable();
  }, [0]);

  const columns = [
    {
      title: '序号',
      render: (data, _, index) => index + 1,
    },
    {
      title: '地市',
      key: 'cityName',
      dataIndex: 'cityName',
      render: (data) => data,
    },
    // {
    //   title: '日期',
    //   key: 'dateTime',
    //   dataIndex: 'dateTime',
    //   render: (data) => data,
    // },
    {
      title: '固话号码总计',
      key: 'landlineNumberCount',
      dataIndex: 'landlineNumberCount',
      width: 40,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '移动号码总计',
      key: 'phoneNumberCount',
      dataIndex: 'phoneNumberCount',
      width: 40,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '外部通报合计',
      key: 'externalNotifyCount',
      dataIndex: 'externalNotifyCount',
      width: 40,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '大数据合计',
      key: 'bigDataAllCount',
      dataIndex: 'bigDataAllCount',
      width: 40,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: `外部通报`,
      align: 'center',
      children: [
        {
          title: '云南MB高危地',
          dataIndex: 'yunnanMbCount',
          align: 'center',
          width: 40,
        },
        {
          title: '注册微信QQ诈骗',
          dataIndex: 'weiQqFraudCount',
          align: 'center',
          width: 40,
        },
        {
          title: '合成作战平台（公安侦办）',
          dataIndex: 'combatPlatformCount',
          align: 'center',
          width: 40,
        },
        {
          title: '公安通报涉案',
          dataIndex: 'relateCaseCount',
          align: 'center',
          width: 60,
        },
        {
          title: '12321举报待核查',
          dataIndex: 'reportPendingVerifyCount',
          align: 'center',
          width: 40,
        },
        {
          title: '省内客服',
          dataIndex: 'customerServiceCount',
          align: 'center',
          width: 40,
        },
        {
          title: '重点地区（公安通报）',
          dataIndex: 'keyAreaReportCount',
          align: 'center',
          width: 40,
        },
        {
          title: '终端换机高危号码',
          dataIndex: 'highRiskNumberCount',
          align: 'center',
          width: 40,
        },
        {
          title: '专办研判诈骗号码',
          dataIndex: 'fraudNumberCount',
          align: 'center',
          width: 40,
        },
        {
          title: '外呼研判诈骗号码',
          dataIndex: 'outboundCount',
          align: 'center',
          width: 40,
        },
        {
          title: '短信预留诈骗',
          dataIndex: 'smsFraudCount',
          align: 'center',
          width: 40,
        },
        {
          title: '专办研判诈骗(SH-A)',
          dataIndex: 'fraudRecordCountSha',
          align: 'center',
          width: 40,
        },
      ],
    },
    {
      title: `大数据`,
      align: 'center',
      children: [
        {
          title: '大数据关停(手机)',
          dataIndex: 'bigDataStopCount',
          align: 'center',
          width: 50,
        },
        {
          title: '大数据复开(手机)',
          dataIndex: 'bigDataRestartCount',
          align: 'center',
          width: 50,
        },
        {
          title: '大数据关停(固话)',
          dataIndex: 'bigDataStopCountFixNum',
          align: 'center',
          width: 60,
        },
        {
          title: '大数据(电渠发卡地)',
          dataIndex: 'bigDataCount',
          align: 'center',
          width: 40,
        },
      ],
    },
  ];

  const initTable = async (params = {}) => {
    const { pagination = {}, ...props } = params;

    setState({ loading: true, searchValues: props });
    request({
      url: '/api/hn/daily_report/daily_report_list',
      method: 'POST',
      requestType: 'json',
      data: {
        pageNum: 1,
        pageSize: 999,
        startDateTime: moment().format('YYYY-MM-DD') + ' ' + '00:00:00',
        endDateTime: moment().format('YYYY-MM-DD') + ' ' + '23:59:59',
        ...props,
      },
    }).then((res) => {
      if (res && res.code === 200 && res.data) {
        const list = res.data.PageListData?.items || [];
        if (res.data.TotalData) {
          list.push({
            ...res.data?.TotalData,
            cityName: '总计',
          });
        }

        setState({
          dataSource: list,
          total: res.data.totalNum || 0,
          loading: false,
        });
      } else {
        message.error(res.message);
        setState({
          dataSource: [],
          total: 0,
          loading: false,
        });
      }
    });
  };

  const handleSearch = () => {
    let values = form.getFieldsValue();
    const { time } = values;
    if (time) {
      values.startDateTime = moment(time).format('YYYY-MM-DD') + ' ' + '00:00:00';
      values.endDateTime = moment(time).format('YYYY-MM-DD') + ' ' + '23:59:59';
    }
    delete values.time;
    initTable({ ...values });
  };

  const handleReset = () => {
    form.resetFields();
    setState({ searchValues: {} });
    initTable();
  };

  const [exportLoading, setExportLoading] = useState(false);

  const handleExport = () => {
    setExportLoading(true);
    const newParams = {
      ...searchValues,
      pageNum: 1,
      pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/daily_report/export_daily_report',
      decode: true,
      params: newParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };
  return (
    <Card className={styles.dailyNotice}>
      <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
        <Row>
          <Col span={6}>
            <FormItem label="起止日期">
              {form.getFieldDecorator('time', {
                initialValue: moment(),
              })(
                <DatePicker
                  style={{ width: '100%' }}
                  disabledDate={(current) => {
                    return current > moment().endOf('day');
                  }}
                  allowClear={false}
                ></DatePicker>,
              )}
            </FormItem>
          </Col>

          <Col offset={12} span={6}>
            <div style={{ textAlign: 'right', marginBottom: 10, marginTop: 2 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={() => handleSearch()}>
                查询
              </Button>
              <Button type="default" onClick={() => handleReset()} style={{ marginRight: 10 }}>
                重置
              </Button>
              <Button type="primary" onClick={() => handleExport()} loading={exportLoading}>
                导出
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <div className={styles.table}>
        <StandardTable
          rowKey="id"
          loading={loading}
          // onChange={handlePaginationTable}
          data={{
            list: dataSource,
            pagination: false,
          }}
          columns={columns}
          isNeedAutoWidth={true}
          scroll={{ x: 'max-content' }}
          showSelectCount={false}
          rowSelectionProps={false}
          bordered
        />
      </div>
      <Row>
        <Col span={12}>
          <DepartmentSituation searchValues={searchValues} />
        </Col>
        <Col span={12}>
          <BigDataSituation searchValues={searchValues} />
        </Col>
      </Row>
    </Card>
  );
};

export default Form.create()(Index);
