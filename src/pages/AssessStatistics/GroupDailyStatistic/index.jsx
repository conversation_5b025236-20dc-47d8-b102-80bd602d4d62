import React, { useState, useEffect } from 'react';
import { Card, Form, DatePicker, Button, Col, Row, message } from 'antd';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { pageKinshipStatistics } from './service';
import { exportFile } from '@/utils/utils';

const { RangePicker } = DatePicker;
const FormItem = Form.Item;

function GroupDailyStatistic(props) {
  const { form } = props;
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 10 });

  const columns = [
    {
      dataIndex: 'countDate',
      title: '上报日期',
      align: 'center',
    },
    {
      dataIndex: 'lurkVictimCount',
      title: '12381号码数',
      align: 'center',
    },
    {
      dataIndex: 'kinshipCount',
      title: '12381推送短信数',
      align: 'center',
    },
    {
      dataIndex: 'groupShutdownCount',
      title: '集团大数据关停量',
      align: 'center',
    },
    {
      dataIndex: 'groupResumeCount',
      title: '集团大数据复机数',
      align: 'center',
    },
  ];

  useEffect(() => {
    onSearch({});
  }, []);

  const [loading, setLoading] = useState(false)
  const onSearch = ({ pageNum = 1, pageSize = 10 }) => {
    form.validateFields(async (errors, values) => {
      if (errors) return;
      setLoading(true)
      const response = await pageKinshipStatistics({
        startDate: values.time[0].format('yyyyMMDD'),
        endDate: values.time[1].format('yyyyMMDD'),
        pageNum,
        pageSize,
      });
      setLoading(false)
      if (response.code != 200) {
        message.warn(response.message);
        return;
      }
      setData(response.data.items);
      setPagination({
        current: pageNum,
        pageSize: pageSize,
        total: response.data.totalNum,
      });
    });
  };
  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    form.validateFields(async (errors, values) => {
      if (errors) return;
      setExportLoading(true);
      exportFile({
        urlAPi: '/api/hn/kinshipSms/exportKinshipStatistics',
        mime: 'xlsx',
        params: {
          startDate: values.time[0].format('yyyyMMDD'),
          endDate: values.time[1].format('yyyyMMDD'),
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        },
        decode: true,
        callback: () => {
          setExportLoading(false);
        },
      });
    });
  };

  const disabledDate = (current) => {
    return current && current > moment().startOf('day');
  };

  return (
    <Card>
      <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
        <Row>
          <Col span={8}>
            <FormItem label="起止日期" >
              {form.getFieldDecorator('time', {
                initialValue: [moment().subtract(10, 'day'), moment().subtract(1, 'day')],
              })(
                <RangePicker
                  // disabledDate={(current)=>(current.isSameOrAfter(moment().subtract(1,'day'),'day'))}
                  allowClear={false}
                  disabledDate={disabledDate}
                ></RangePicker>,
              )}
            </FormItem>
          </Col>
          <Col offset={8} span={8} style={{textAlign:'right'}}>
              <Button
                type="primary"
                style={{ marginLeft: '10px' }}
                onClick={() => {
                  onSearch({});
                }}
              >
                查询
              </Button>
              <Button type="primary" style={{ marginLeft: '10px' }} onClick={handleExport} loading={exportLoading}>
                导出
              </Button>
          </Col>
        </Row>
      </Form>
      <StandardTable
        loading = {loading}
        showSelectCount={false}
        rowSelectionProps={false}
        data={{
          list: data,
          pagination: pagination,
        }}
        columns={columns}
        onChange={(pagination) => {
          onSearch({ pageNum: pagination.current, pageSize: pagination.pageSize });
        }}
      ></StandardTable>
    </Card>
  );
}

export default Form.create()(GroupDailyStatistic);
