import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import { Card, Form, Row, Col, DatePicker, Select, Input, Button, message } from 'antd';

import moment from 'moment';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { exportFile } from '@/utils/utils';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState({});
  const initialDate = [moment().subtract(4, 'day'), moment().subtract(4, 'day')];
  const initialParams = {
    startTime: initialDate?.[0]?.format('YYYY-MM-DD'),
    endTime: initialDate?.[1]?.format('YYYY-MM-DD'),
  };

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas(initialParams);
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    const { date } = values;
    getListDatas({
      ...values,
      date: undefined,
      startTime: date?.[0]?.format('YYYY-MM-DD'),
      endTime: date?.[1]?.format('YYYY-MM-DD'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas(initialParams);
  };

  let columns = [
    {
      title: '入网时间',
      width: 170,
      dataIndex: 'networkAccessTime',
      ellipsis: true,
    },
    {
      title: '入网号卡量',
      dataIndex: 'networkAccessFlow',
      width: 140,
      ellipsis: true,
    },
    {
      title: '外呼日期',
      width: 100,
      dataIndex: 'outboundTime',
      ellipsis: true,
    },
    {
      title: '外呼量',
      width: 100,
      dataIndex: 'outboundNum',
      ellipsis: true,
    },
    {
      title: '关停量',
      width: 100,
      dataIndex: 'shutdownNum',
      ellipsis: true,
    },
    {
      title: '复机量',
      width: 100,
      dataIndex: 'restartNum',
      ellipsis: true,
    },
    {
      title: '累积月份',
      width: 100,
      dataIndex: 'totalMonth',
      ellipsis: true,
    },
    {
      title: '月累计外呼量',
      width: 140,
      dataIndex: 'monthOutboundNum',
      ellipsis: true,
    },
  ];

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/newOutboundCallStatistics/export',
      mime: 'xlsx',
      params: searchParams,
      decode: true,
    });
  };

  return (
    <Card>
      <Row style={{ marginBottom: 16 }}>
        <Form layout="inline">
          <Col span={8}>
            <Form.Item label="入网时间">
              {getFieldDecorator('date', {
                initialValue: initialDate,
              })(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>

          <Col align="right" span={16} style={{ textAlign: 'right' }}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button onClick={onReset} style={{ marginRight: 10 }}>
              重置
            </Button>
            <Button onClick={handleExport} type="primary" disabled={!listData?.list?.length}>
              导出
            </Button>
          </Col>
        </Form>
      </Row>

      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </Card>
  );
};
export default Form.create({})(Index);
