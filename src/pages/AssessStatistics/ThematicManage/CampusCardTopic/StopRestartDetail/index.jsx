import React, { useRef, useState, useEffect, Fragment } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, Button } from 'antd';
import { Licensee } from 'ponshine';

import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import request from 'ponshine-request';
import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';

import renderCascadeFormItem from '@/components/CommonFormItem/CascadeFormItem';

import { exportFile } from '@/utils/utils';
import moment from 'moment';

const { RangePicker } = DatePicker;

const Index = ({ form, form: { getFieldDecorator, validateFields } }) => {
  const localNetworkRef = useRef();
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [serachParams, setSearchParams] = useState({});
  const [startTime, setStartTime] = useState();
  const [fjStartTime, setFjStartTime] = useState();
  const [responsibleUnitList, setResponsibleUnitList] = useState([]);

  let columns = [
    {
      title: '号码',
      width: 120,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '责任单位',
      width: 100,
      dataIndex: 'responsibleUnit',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '营业区',
      width: 120,
      dataIndex: 'businessArea',
      ellipsis: true,
    },
    {
      title: '关停时间',
      width: 160,
      dataIndex: 'importTime',
      ellipsis: true,
    },
    {
      title: '关停人',
      width: 120,
      dataIndex: 'shutdownOperator',
      ellipsis: true,
    },
    {
      title: '关停备注',
      width: 140,
      dataIndex: 'shutdownRemark',
      ellipsis: true,
    },
    {
      title: '复机时间',
      width: 160,
      dataIndex: 'resumeTime',
      ellipsis: true,
    },
    {
      title: '复核受理日期',
      width: 160,
      dataIndex: 'reviewAcceptanceTime',
      ellipsis: true,
    },
    {
      title: '是否复机',
      width: 140,
      dataIndex: 'ifResumed',
      ellipsis: true,
    },
    {
      title: '复机方式',
      width: 120,
      dataIndex: 'resumeWay',
      ellipsis: true,
    },
    {
      title: '涉诈类型',
      width: 100,
      dataIndex: 'fraudType',
      ellipsis: true,
    },
    {
      title: '关停标签',
      width: 140,
      dataIndex: 'shutdownTag',
      ellipsis: true,
    },
    {
      title: '子标签',
      width: 120,
      dataIndex: 'subtag',
      ellipsis: true,
    },
    {
      title: '停机类型',
      width: 100,
      dataIndex: 'shutdownType',
      ellipsis: true,
    },
    {
      title: '涉诈性质',
      width: 100,
      dataIndex: 'fraudNature',
      ellipsis: true,
    },
    {
      title: '套餐名称',
      width: 140,
      dataIndex: 'phonePlanName',
      ellipsis: true,
    },
    {
      title: '套餐价格',
      width: 100,
      dataIndex: 'salePrice',
      ellipsis: true,
    },
    {
      title: '受理网点',
      width: 140,
      dataIndex: 'acceptancePoint',
      ellipsis: true,
    },
    {
      title: '受理人',
      width: 140,
      dataIndex: 'acceptStaff',
      ellipsis: true,
    },
    {
      title: '受理人工号',
      width: 100,
      dataIndex: 'acceptanceWorkId',
      ellipsis: true,
    },
    {
      title: '是否最新用户',
      width: 100,
      dataIndex: 'ifLatestUser',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/schoolCard/pageSchoolPhoneShutdownDetail', {
      requestType: 'json',
      method: 'POST',
      data: { pageNum, pageSize, ...props },
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      const { gtTime, fjTime } = values;
      getListDatas({
        ...values,
        importTimeStart: gtTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
        importTimeEnd: gtTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
        resumeTimeStart: fjTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
        resumeTimeEnd: fjTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
        gtTime: undefined,
        fjTime: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    setListData({
      list: [],
      pagination: false,
    });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/schoolCard/exportSchoolPhoneShutdownDetail',
      decode: true,
      params: { ...serachParams, pageNum: 1, pageSize: 1 },
      method: 'POST',
    });
  };

  const disabledDate = (current) => {
    if (!startTime) return current > moment().endOf('day');

    return (
      current > moment(startTime).add(90, 'day') ||
      current < moment(startTime).subtract(90, 'day') ||
      current > moment().endOf('day')
    );
  };
  const disabledFjDate = (current) => {
    if (!fjStartTime) return current > moment().endOf('day');

    return (
      current > moment(fjStartTime).add(90, 'day') ||
      current < moment(fjStartTime).subtract(90, 'day') ||
      current > moment().endOf('day')
    );
  };

  const getOrganizationByUser = () => {
    request(`/api/hn/systemConfig/getOrganizationByUser2`, {
      method: 'get',
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setResponsibleUnitList(res?.data || []);
      } else {
        setResponsibleUnitList([]);
      }
    });
  };

  useEffect(() => {
    getOrganizationByUser();
  }, []);

  return (
    <div>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          formList={[
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="关停时间">
              {getFieldDecorator('gtTime')(
                <RangePicker
                  format={'YYYY-MM-DD'}
                  disabledDate={disabledDate}
                  onCalendarChange={(dates) => {
                    if (dates && dates.length === 1) {
                      setStartTime(dates[0]);
                    } else if (dates && dates.length === 2) {
                      setStartTime();
                    }
                  }}
                  onOpenChange={(status) => {
                    setStartTime();
                  }}
                />,
              )}
            </Form.Item>,
            <LocalNetworkFormItem
              form={form}
              getListDatas={getListDatas}
              cref={localNetworkRef}
              defaultSearch={false}
            />,
            <Form.Item label="责任单位">
              {getFieldDecorator('responsibleUnit')(
                <Select placeholder="请选择" allowClear>
                  {responsibleUnitList?.map((item, index) => (
                    <Option value={item.name} key={index}>
                      {item.name}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>,
            <Form.Item label="受理网点">
              {getFieldDecorator('acceptancePoint')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            <Form.Item label="受理人">
              {getFieldDecorator('acceptStaff')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,
            ...renderCascadeFormItem({ form: form }),

            <Form.Item label="是否复机">
              {getFieldDecorator('ifResumed')(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  <Select.Option value={'是'}>是</Select.Option>
                  <Select.Option value={'否'}>否</Select.Option>
                </Select>,
              )}
            </Form.Item>,
            <Form.Item label="复机时间">
              {getFieldDecorator('fjTime')(
                <RangePicker
                  format={'YYYY-MM-DD'}
                  disabledDate={disabledFjDate}
                  onCalendarChange={(dates) => {
                    if (dates && dates.length === 1) {
                      setFjStartTime(dates[0]);
                    } else if (dates && dates.length === 2) {
                      setFjStartTime();
                    }
                  }}
                  onOpenChange={(status) => {
                    setFjStartTime();
                  }}
                />,
              )}
            </Form.Item>,
            <Form.Item label="是否最新用户">
              {getFieldDecorator('ifLatestUser', {
                initialValue: '是',
              })(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  <Select.Option value={'是'}>是</Select.Option>
                  <Select.Option value={'否'}>否</Select.Option>
                </Select>,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
                查询
              </Button>
              <Button onClick={onReset} style={{ marginRight: 8 }}>
                重置
              </Button>
              <Licensee license="schoolCard_shutdownDetail_export">
                <Button type="primary" onClick={handleExport} disabled={!listData?.list?.length}>
                  导出
                </Button>
              </Licensee>
            </Fragment>
          }
        />
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        detailColumns={columns}
        tools={true}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </div>
  );
};

export default Form.create()(Index);
