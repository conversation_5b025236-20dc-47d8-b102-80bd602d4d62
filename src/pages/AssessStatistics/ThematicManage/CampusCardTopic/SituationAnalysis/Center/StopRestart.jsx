import React, { useState, useEffect } from 'react';
import CommonTitle from '@/components/CommonTitle';
import DateRadio from '../DateRadio';
import LineChart from '@/components/Chart/LineChart';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '@/components/CommonCard';

const data = [
  {
    countDate: '2024/01/01',
    shutdownCount: 1,
    resumeCount: 2,
  },
  {
    countDate: '2024/01/02',
    shutdownCount: 4,
    resumeCount: 5,
  },
];

export default function AccessNetNum({ currentCity }) {
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async () => {
    setLoading(true);
    const response = await request(`/api/hn/schoolCardAnalysis/getShutDownDateView`, {
      method: 'POST',
      requestType: 'form',
      data: { localNetwork: currentCity },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    currentCity && getData();
  }, [currentCity]);

  return (
    <CommonCard title={'校园卡关停复机情况'}>
      <LineChart
        xData={listData?.map((ele) => ele?.countDate)}
        yData={[
          { name: '关停号码量', data: listData?.map((ele) => ele?.shutdownCount) },
          { name: '当日复机号码量', data: listData?.map((ele) => ele?.resumeCount) },
        ]}
        legendData={['关停号码量', '当日复机号码量']}
        height={250}
        width="100%"
        loading={loading}
        isMutipleYAxisIndex={false}
        axisLabelOption={{ width: 80 }}
        gridOption={{ left: 60 }}
      />
    </CommonCard>
  );
}
