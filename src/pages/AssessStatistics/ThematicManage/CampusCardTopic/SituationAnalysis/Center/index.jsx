import React from 'react';
import { Row, Col } from 'antd';

import AccessNetNum from './AccessNetNum';
import StopRestart from './StopRestart';
import StopTop from './StopTop';

export default function index({ currentCity }) {
  return (
    <Row gutter={[24, 24]}>
      <Col span={8}>
        <AccessNetNum currentCity={currentCity} />
      </Col>
      <Col span={8}>
        <StopRestart currentCity={currentCity} />
      </Col>
      <Col span={8}>
        <StopTop currentCity={currentCity} />
      </Col>
    </Row>
  );
}
