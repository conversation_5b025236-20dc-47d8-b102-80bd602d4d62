import React, { useState, useEffect } from 'react';
import CommonTitle from '@/components/CommonTitle';
import DateRadio from '../DateRadio';
import { message, Button, Tooltip, Icon } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from 'ponshine-request';
import CommonCard from '@/components/CommonCard';

import { exportFile } from '@/utils/utils';

export default function AccessNetNum({ currentCity }) {
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const dateRadio = [
    {
      text: '周',
      value: '1-week',
    },
    {
      text: '月',
      value: '1-month',
    },
    {
      text: '90天',
      value: '90-day',
    },
  ];

  const [dateRadioValue, setDateRadioValue] = useState(dateRadio?.[0]?.value);

  const getDateParams = () => {
    const arr = dateRadioValue.split('-');
    return {
      queryTimeInterval: Number(arr[0]),
      queryTimeDimension: arr[1],
    };
  };

  const columns = [
    {
      title: '网点名称',
      width: '50%',
      dataIndex: 'acceptancePoint',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: '30%',
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '关停次数',
      width: '20%',
      dataIndex: 'shutdownCount',
      ellipsis: true,
    },
  ];

  const getData = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`/api/hn/schoolCardAnalysis/getAcceptancePointTop`, {
      method: 'POST',
      requestType: 'form',
      data: { pageNum, pageSize, localNetwork: currentCity, ...props },
    });

    setLoading(false);
    if (response.code === 200) {
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleTableChange = (pagination) => {
    getData({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...getDateParams(),
    });
  };

  const changeRadio = (params) => {
    setDateRadioValue(params.queryTimeInterval + '-' + params.queryTimeDimension);
    getData(params);
  };

  useEffect(() => {
    currentCity && getData(getDateParams());
  }, [currentCity]);

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/schoolCardAnalysis/exportAcceptancePointTop',
      decode: true,
      params: { localNetwork: currentCity, ...getDateParams() },
      method: 'POST',
      requestType: 'form',
    });
  };

  return (
    <CommonCard
      title={
        <span>
          校园卡关停TOP网点
          <Tooltip title="省内大数据模型关停的校园卡开卡网点按照关停数量统计TOP网点，详情信息请点击【校园卡关停复机详单】进行下载">
            <Icon type="question-circle" style={{ color: '#1890ff', marginLeft: 8 }} />
          </Tooltip>
        </span>
      }
      extra={
        <div>
          <DateRadio data={dateRadio} onChange={changeRadio} value={dateRadioValue} />
          <Button type="primary" size="small" style={{ marginLeft: 16 }} onClick={handleExport}>
            导出
          </Button>
        </div>
      }
    >
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={{
          list: listData?.list,
          pagination: {
            ...listData?.pagination,
            showQuickJumper: false,
          },
        }}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{ y: 150 }}
      />
    </CommonCard>
  );
}
