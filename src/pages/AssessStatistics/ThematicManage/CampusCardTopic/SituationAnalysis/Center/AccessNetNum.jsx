import React, { useState, useEffect } from 'react';
import CommonTitle from '@/components/CommonTitle';
import DateRadio from '../DateRadio';
import LineChart from '@/components/Chart/LineChart';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '@/components/CommonCard';

export default function AccessNetNum({ currentCity }) {
  const dateRadio = [
    {
      text: '日',
      value: '30-day',
    },
    {
      text: '月',
      value: '12-month',
    },
  ];
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dateRadioValue, setDateRadioValue] = useState(dateRadio?.[0]?.value);

  const getDateParams = () => {
    const arr = dateRadioValue.split('-');
    return {
      queryTimeInterval: Number(arr[0]),
      queryTimeDimension: arr[1],
    };
  };

  const getData = async (timeObj) => {
    setLoading(true);

    const response = await request(`/api/hn/schoolCardAnalysis/getInternetAccessTimeGroup`, {
      method: 'POST',
      requestType: 'form',
      data: { localNetwork: currentCity, ...timeObj },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  const changeRadio = (params) => {
    setDateRadioValue(params.queryTimeInterval + '-' + params.queryTimeDimension);
    getData(params);
  };

  useEffect(() => {
    if (currentCity) {
      getData(getDateParams());
    }
  }, [currentCity]);

  return (
    <CommonCard
      title={'校园卡入网数量统计'}
      extra={<DateRadio data={dateRadio} onChange={changeRadio} value={dateRadioValue} />}
    >
      <LineChart
        xData={listData?.map((ele) => ele?.countDate)}
        yData={[{ data: listData?.map((ele) => ele.newlineCount) }]}
        legendData={[]}
        height={250}
        width="100%"
        loading={loading}
        isMutipleYAxisIndex={false}
        axisLabelOption={{ width: 80 }}
        gridOption={{ left: 60 }}
      />
    </CommonCard>
  );
}
