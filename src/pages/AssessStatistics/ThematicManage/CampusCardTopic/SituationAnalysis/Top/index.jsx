import React, { useEffect, useState } from 'react';
import moment from 'moment';
import TopItem from './TopItem';
import { getTopData } from '../services';
import { Row, Col, message } from 'antd';
import one from '../imgs/one.png';
import two from '../imgs/two.png';
import three from '../imgs/three.png';

export default function index({ currentCity }) {
  const [data, setData] = useState({});
  const list = [
    {
      title: `${moment().subtract(2, 'day').format('YYYY-MM-DD')}校园卡在网数量`,
      img: one,
      keyStr: 'localNetworkCount',
    },
    {
      title: '最近30天新入网数量',
      img: two,
      keyStr: 'oneMonthNewUserCount',
    },
    {
      title: '最近30天关停数量',
      img: three,
      keyStr: 'oneMonthShutdownCount',
    },
  ];

  const findData = async () => {
    const response = await getTopData({ localNetwork: currentCity });
    if (response.code === 200) {
      setData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    currentCity && findData();
  }, [currentCity]);
  return (
    <Row gutter={[16, 16]}>
      {list.map((ele, index) => (
        <Col span={8} key={index}>
          <TopItem {...ele} data={data} />
        </Col>
      ))}
    </Row>
  );
}
