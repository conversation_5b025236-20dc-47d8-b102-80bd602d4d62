import request from 'ponshine-request';

// 查询所属地市
export async function getLocalNetwork(params) {
  return request(`/api/hn/systemConfig/getOrganizationByUser`, {
    method: 'GET',
    params,
  });
}

// 查询顶部数量
export async function getTopData(params) {
  return request(`/api/hn/schoolCardAnalysis/getOverViewData`, {
    method: 'POST',
    data: params,
  });
}

// 获取15天外呼分类趋势
export async function getOutCallTypeTrend(params) {
  return request(`/api/hn/outboundResult/getOutboundResultTrend`, {
    method: 'GET',
    params,
  });
}

// 获取诈骗数据
export async function getFraudTableData(params) {
  return request(`/api/hn/outboundResult/pagePartData`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
