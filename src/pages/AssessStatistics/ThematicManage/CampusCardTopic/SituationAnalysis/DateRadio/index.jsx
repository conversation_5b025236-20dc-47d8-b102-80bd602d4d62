import React from 'react';
import { Radio } from 'antd';

export default function index({ data, onChange, value }) {
  const changeRadio = (e) => {
    const value = e.target.value.split('-');
    onChange({
      queryTimeDimension: value[1],
      queryTimeInterval: Number(value[0]),
    });
  };
  return (
    <Radio.Group defaultValue={data?.[0]?.value} onChange={changeRadio} size="small" value={value}>
      {data?.map((ele) => (
        <Radio.Button value={ele.value}>{ele.text}</Radio.Button>
      ))}
    </Radio.Group>
  );
}
