import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { Form, Select } from 'antd';
import { getLocalNetwork } from './services';
import Top from './Top';
import Center from './Center';
import Bottom from './Bottom';
import styles from './index.less';
const Index = ({ form }) => {
  const [cityLists, setCityLists] = useState([]);

  const [currentCity, setCurrentCity] = useState('');

  const getDefaultCity = (list) => {
    const data = list || cityLists;
    let cityValue = '';
    if (data?.length > 1) {
      cityValue = '省公司';
    } else {
      cityValue = data?.[0]?.name;
    }
    return cityValue;
  };

  const getCityLists = async () => {
    const response = await getLocalNetwork();
    if (response.code === 200) {
      const list = response?.data || [];
      if (response?.data?.length > 1) {
        list.unshift({ name: '省公司' });
      }
      setCityLists(list);
      setCurrentCity(getDefaultCity(list));
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getCityLists();
  }, []);

  const handleChangeCity = (value) => {
    setCurrentCity(value);
  };

  return (
    <div className={styles.situationAnalysis}>
      <div>
        <Form.Item label="所属地市" style={{ display: 'flex' }}>
          {form.getFieldDecorator('localNetwork', {
            initialValue: getDefaultCity(),
          })(
            <Select
              style={{ width: 200 }}
              placeholder="请选择"
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              onChange={handleChangeCity}
              size="small"
              allowClear={false}
            >
              {cityLists?.map((ele, index) => (
                <Select.Option value={ele.name} key={index}>
                  {ele.name.replace('本地网', '')}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </div>
      <Top currentCity={currentCity} />
      <Center currentCity={currentCity} />
      <Bottom currentCity={currentCity} />
    </div>
  );
};

export default Form.create()(Index);
