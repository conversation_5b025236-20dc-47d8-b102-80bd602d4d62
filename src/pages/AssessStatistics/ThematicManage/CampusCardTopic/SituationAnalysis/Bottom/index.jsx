import React from 'react';
import { Row, Col } from 'antd';

import AccessDistribution from './AccessDistribution';
import ShutdownModel from './ShutdownModel';
import Over30YearsAccessNetNum from './Over30YearsAccessNetNum';

export default function index({ currentCity }) {
  return (
    <Row gutter={[24, 24]}>
      <Col span={8}>
        <AccessDistribution currentCity={currentCity} />
      </Col>
      <Col span={8}>
        <ShutdownModel currentCity={currentCity} />
      </Col>
      <Col span={8}>
        <Over30YearsAccessNetNum currentCity={currentCity} />
      </Col>
    </Row>
  );
}
