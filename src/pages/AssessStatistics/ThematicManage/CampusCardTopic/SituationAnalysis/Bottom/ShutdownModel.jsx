import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from '@/components/Chart/PieChart';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '@/components/CommonCard';

export default function AccessNetNum({ currentCity }) {
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async (date) => {
    setLoading(true);
    const response = await request(`/api/hn/schoolCardAnalysis/getShutDownModelView`, {
      method: 'POST',
      requestType: 'form',
      data: { localNetwork: currentCity },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(
        response?.data?.map((ele) => {
          return {
            name: ele.subtag,
            value: ele.shutdownCount,
          };
        }) || [],
      );
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    currentCity && getData();
  }, [currentCity]);

  return (
    <CommonCard title="校园卡月度关停模型统计">
      <PieChart
        data={listData}
        height={250}
        loading={loading}
        legendOption={{ left: '55%' }}
        labelLength={10}
      />
    </CommonCard>
  );
}
