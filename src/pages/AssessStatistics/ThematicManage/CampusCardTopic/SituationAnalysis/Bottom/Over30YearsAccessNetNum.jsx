import React, { useState, useEffect, Fragment } from 'react';
import CommonTitle from '@/components/CommonTitle';
import DateRadio from '../DateRadio';
import { Button, message, Icon, Tooltip } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from 'ponshine-request';
import { exportFile, renderToolTip } from '@/utils/utils';
import CommonCard from '@/components/CommonCard';

export default function AccessNetNum({ currentCity }) {
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const dateRadio = [
    {
      text: '周',
      value: '1-week',
    },
    {
      text: '月',
      value: '1-month',
    },
    {
      text: '90天',
      value: '90-day',
    },
  ];

  const [dateRadioValue, setDateRadioValue] = useState(dateRadio?.[0]?.value);

  const getDateParams = () => {
    const arr = dateRadioValue.split('-');
    return {
      queryTimeInterval: Number(arr[0]),
      queryTimeDimension: arr[1],
    };
  };

  const columns = [
    {
      title: '网点名称',
      width: '50%',
      dataIndex: 'acceptancePoint',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: '30%',
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '入网数量',
      width: '20%',
      dataIndex: 'internetAccessCount',
      ellipsis: true,
    },
  ];

  const getData = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`/api/hn/overTwentyEight/getInternetAccessOverTwentyEight`, {
      method: 'POST',
      requestType: 'form',
      data: { pageNum, pageSize, localNetwork: currentCity, ...props },
    });

    setLoading(false);
    if (response.code === 200) {
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleTableChange = (pagination) => {
    getData({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...getDateParams(),
    });
  };

  const changeRadio = (params) => {
    setDateRadioValue(params.queryTimeInterval + '-' + params.queryTimeDimension);
    getData(params);
  };

  useEffect(() => {
    currentCity && getData(getDateParams());
  }, [currentCity]);

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/overTwentyEight/exportAcceptancePointTop',
      decode: true,
      params: { localNetwork: currentCity, ...getDateParams() },
      requestType: 'form',
      method: 'POST',
    });
  };

  return (
    <CommonCard
      title={
        <span>
          超28岁入网数量统计
          <Tooltip title="对周期内超28岁及以上用户办理校园卡信息进行统计，详情信息请点击【超28岁以上入网用户清单】进行下载">
            <Icon type="question-circle" style={{ color: '#1890ff', marginLeft: 8 }} />
          </Tooltip>
        </span>
      }
      extra={
        <div>
          <DateRadio data={dateRadio} onChange={changeRadio} value={dateRadioValue} />
          <Button type="primary" size="small" style={{ marginLeft: 16 }} onClick={handleExport}>
            导出
          </Button>
        </div>
      }
    >
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={{
          list: listData?.list,
          pagination: {
            ...listData?.pagination,
            showQuickJumper: false,
          },
        }}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{ y: 150 }}
      />
    </CommonCard>
  );
}
