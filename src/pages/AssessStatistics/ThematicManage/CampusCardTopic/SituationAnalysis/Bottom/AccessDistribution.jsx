import React, { useState, useEffect } from 'react';
import CommonTitle from '@/components/CommonTitle';
import DateRadio from '../DateRadio';
import Bar<PERSON>hart from '@/components/Chart/BarChart';
import { message } from 'antd';
import request from 'ponshine-request';
import CommonCard from '@/components/CommonCard';

const data = [
  {
    newlineCount: 2730,
    localNetwork: '张家界本地网哈哈哈嗝',
  },
  {
    newlineCount: 6770,
    localNetwork: '张家界本地网',
  },
  {
    newlineCount: 195,
    localNetwork: '株洲本地网',
  },
  {
    newlineCount: 48,
    localNetwork: '湘潭本地网',
  },
  {
    newlineCount: 2184,
    localNetwork: '益阳本地网',
  },
  {
    newlineCount: 72,
    localNetwork: '衡阳本地网',
  },
  {
    newlineCount: 1708,
    localNetwork: '长沙本地网',
  },
  {
    newlineCount: 48,
    localNetwork: '测试1',
  },
  {
    newlineCount: 2184,
    localNetwork: '测试2',
  },
  {
    newlineCount: 72,
    localNetwork: '测试3',
  },
  {
    newlineCount: 1708,
    localNetwork: '测试4',
  },
  {
    newlineCount: 48,
    localNetwork: '测试6',
  },
  {
    newlineCount: 2184,
    localNetwork: '测试7',
  },
];
export default function AccessNetNum({ currentCity }) {
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);
  const dateRadio = [
    {
      text: '30天',
      value: '30-day',
    },
    {
      text: '90天',
      value: '90-day',
    },
  ];

  const [dateRadioValue, setDateRadioValue] = useState(dateRadio?.[0]?.value);

  const getDateParams = () => {
    const arr = dateRadioValue.split('-');
    return {
      queryTimeInterval: Number(arr[0]),
      queryTimeDimension: arr[1],
    };
  };

  const getData = async (props) => {
    setLoading(true);

    const response = await request(`/api/hn/schoolCardAnalysis/getInternetAccessAreaGroup`, {
      method: 'POST',
      requestType: 'form',
      data: { localNetwork: currentCity, ...props },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data?.sort((a,b) => b.newlineCount - a.newlineCount)|| []);
    } else {
      message.error(response.message);
    }
  };

  const changeRadio = (params) => {
    setDateRadioValue(params.queryTimeInterval + '-' + params.queryTimeDimension);
    getData(params);
  };

  useEffect(() => {
    currentCity && getData(getDateParams());
  }, [currentCity]);

  return (
    <CommonCard
      title={'校园卡入网分布'}
      extra={<DateRadio data={dateRadio} onChange={changeRadio} value={dateRadioValue} />}
    >
      <BarChart
        xData={listData?.map((ele) => ele?.localNetwork?.replace('本地网', ''))}
        yData={[{ data: listData?.map((ele) => ele?.newlineCount) }]}
        legendData={[]}
        height={250}
        width="100%"
        loading={loading}
        isMutipleYAxisIndex={false}
        seriesConfig={{ barWidth: 20 }}
        gridOption={{ left: 50 }}
        axisLabelOption={{ width: 80 }}
      />
    </CommonCard>
  );
}
