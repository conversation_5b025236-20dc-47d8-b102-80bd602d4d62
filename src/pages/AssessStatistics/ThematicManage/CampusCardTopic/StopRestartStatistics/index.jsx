import React, { useRef, useState, useEffect } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, Button } from 'antd';
import { Licensee } from 'ponshine';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import CascadeFormItem from '@/components/CommonFormItem/CascadeFormItem';
import request from 'ponshine-request';
import StandardTable from '@/components/StandardTable';

import { exportFile } from '@/utils/utils';
import moment from 'moment';

const { RangePicker, MonthPicker } = DatePicker;

const Index = ({
  form,
  form: { getFieldDecorator, validateFields, getFieldValue, setFieldsValue },
}) => {
  const localNetworkRef = useRef();
  const [loading, setLoading] = useState(false);

  const defaultMonth = [moment(), moment()];
  const defaultDate = [moment().startOf('month'), moment()];

  const defaultParams = {
    timeType: 'month',
    areaType: 'city',
    startTime: defaultMonth?.[0]?.format('YYYY-MM'),
    endTime: defaultMonth?.[1]?.format('YYYY-MM'),
  };

  const townColunm = [
    {
      title: '营业区',
      width: 100,
      dataIndex: 'businessArea',
      ellipsis: true,
    },
  ];

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [serachParams, setSearchParams] = useState({});

  let columns = [
    {
      title: '统计时间',
      width: 100,
      dataIndex: 'countDate',
      ellipsis: true,
    },

    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    ...(serachParams?.areaType === 'county' ? townColunm : []),
    {
      title: '入网号码量',
      width: 120,
      dataIndex: 'newlinePhoneCount',
      ellipsis: true,
    },
    {
      title: '关停号码数',
      width: 120,
      dataIndex: 'shutdownPhoneCount',
      ellipsis: true,
    },
    {
      title: '关停量',
      width: 120,
      dataIndex: 'shutdownCount',
      ellipsis: true,
    },
    {
      title: '当日复机量',
      width: 100,
      dataIndex: 'resumeCount',
      ellipsis: true,
    },
    {
      title: '当日复机率',
      width: 140,
      dataIndex: 'resumeRate',
      ellipsis: true,
    },
    {
      title: '当日线下复机量',
      width: 120,
      dataIndex: 'resumeOfflineCount',
      ellipsis: true,
    },
    {
      title: '当日线下复机率',
      width: 120,
      dataIndex: 'resumeOfflineRate',
      ellipsis: true,
    },
    {
      title: '近7天复机量',
      width: 140,
      dataIndex: 'resumeCountSevenDaysAgo',
      ellipsis: true,
    },
    {
      title: '近7天复机率',
      width: 140,
      dataIndex: 'resumeSevenDaysRate',
      ellipsis: true,
    },
    {
      title: '近7天线下复机量',
      width: 140,
      dataIndex: 'resumeOfflineCountSevenDaysAgo',
      ellipsis: true,
    },
    {
      title: '近7天线下复机率',
      width: 140,
      dataIndex: 'resumeOfflineSevenDaysRate',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/importResumeStatistic/pageImportResumeStatisticList', {
      requestType: 'form',
      method: 'POST',
      data: { pageNum, pageSize, ...props },
    });

    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      const { qzDate, timeType } = values;
      const formatStr = timeType === 'month' ? 'YYYY-MM' : 'YYYY-MM-DD';
      getListDatas({
        ...values,
        startTime: qzDate?.[0]?.format(formatStr),
        endTime: qzDate?.[1]?.format(formatStr),
        qzDate: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas({ localNetwork: localNetworkRef.current.getInitialValue(), ...defaultParams });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/importResumeStatistic/exportImportResumeStatisticList',
      decode: true,
      params: serachParams,
      method: 'POST',
      requestType: 'form',
    });
  };

  const changePanel = (v) => {
    setFieldsValue({ qzDate: v });
  };

  const handleChangeTimeType = (v) => {
    if (v === 'day') {
      setFieldsValue({ qzDate: defaultDate });
    } else {
      setFieldsValue({ qzDate: defaultMonth });
    }
  };

  return (
    <div>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="颗粒度">
              {getFieldDecorator('timeType', {
                initialValue: defaultParams?.timeType,
              })(
                <Select placeholder="请选择" onChange={handleChangeTimeType}>
                  <Select.Option value="month">按月</Select.Option>
                  <Select.Option value="day">按天</Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="起止日期">
              {getFieldDecorator('qzDate', {
                initialValue: getFieldValue('timeType') === 'month' ? defaultMonth : defaultDate,
              })(
                getFieldValue('timeType') === 'month' ? (
                  <RangePicker
                    format={'YYYY-MM'}
                    mode={['month', 'month']}
                    onPanelChange={changePanel}
                    onChange={changePanel}
                    allowClear={false}
                  />
                ) : (
                  <RangePicker format={'YYYY-MM-DD'} allowClear={false} />
                ),
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem
              form={form}
              getListDatas={getListDatas}
              cref={localNetworkRef}
              otherInitParams={defaultParams}
            />
          </Col>
          <Col span={6}>
            <Form.Item label="统计维度">
              {getFieldDecorator('areaType', {
                initialValue: defaultParams?.areaType,
              })(
                <Select placeholder="请选择">
                  <Select.Option value="city">地市</Select.Option>
                  <Select.Option value="county">区县</Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={24} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>

            <Button onClick={onReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Licensee license="schoolCard_shutdown_count_export">
              <Button type="primary" onClick={handleExport}>
                导出
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </div>
  );
};

export default Form.create()(Index);
