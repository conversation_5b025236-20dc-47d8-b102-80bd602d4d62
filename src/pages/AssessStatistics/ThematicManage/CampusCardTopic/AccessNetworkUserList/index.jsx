import React, { useRef, useState, useEffect } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, Button } from 'antd';

import { Licensee } from 'ponshine';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import CascadeFormItem from '@/components/CommonFormItem/CascadeFormItem';
import request from 'ponshine-request';
import StandardTable from '@/components/StandardTable';

import { exportFile } from '@/utils/utils';
import moment from 'moment';

const { RangePicker } = DatePicker;

const Index = ({ form, form: { getFieldDecorator, validateFields } }) => {
  const localNetworkRef = useRef();
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [serachParams, setSearchParams] = useState({});
  const [startTime, setStartTime] = useState();

  let columns = [
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },

    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '营业区',
      width: 120,
      dataIndex: 'businessArea',
      ellipsis: true,
    },
    {
      title: '入网日期',
      width: 120,
      dataIndex: 'internetAccessTime',
      ellipsis: true,
    },
    {
      title: '年龄',
      width: 100,
      dataIndex: 'age',
      ellipsis: true,
    },
    {
      title: '渠道',
      width: 140,
      dataIndex: 'channelType',
      ellipsis: true,
    },
    {
      title: '入网网点',
      width: 160,
      dataIndex: 'acceptancePoint',
      ellipsis: true,
    },
    {
      title: '办理人',
      width: 100,
      dataIndex: 'acceptStaff',
      ellipsis: true,
    },
    {
      title: '办理人工号',
      width: 140,
      dataIndex: 'acceptanceWorkId',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/overTwentyEight/pageOverTwentyEightUserInformation', {
      requestType: 'json',
      method: 'POST',
      data: { pageNum, pageSize, ...props },
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      const { rwDate } = values;
      getListDatas({
        ...values,
        internetAccessTimeStart: rwDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
        internetAccessTimeEnd: rwDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
        rwDate: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    setListData({
      list: [],
      pagination: false,
    });
    // getListDatas({ companyId: localNetworkRef.current.getInitialValue() });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/overTwentyEight/exportOverTwentyEightUserInformation',
      decode: true,
      params: { ...serachParams, pageNum: 1, pageSize: 1 },
      method: 'POST',
    });
  };

  return (
    <div>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="入网日期">
              {getFieldDecorator('rwDate')(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem
              form={form}
              getListDatas={getListDatas}
              cref={localNetworkRef}
              defaultSearch={false}
            />
          </Col>
          <Col span={6}>
            <Form.Item label="入网网点">
              {getFieldDecorator('acceptancePoint')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="办理人">
              {getFieldDecorator('acceptStaff')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={18} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>

            <Button onClick={onReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Licensee license="overTwentyEight_userInfo_export">
              <Button type="primary" onClick={handleExport} disabled={!listData?.list?.length}>
                导出
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </div>
  );
};

export default Form.create()(Index);
