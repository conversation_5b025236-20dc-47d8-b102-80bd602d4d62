import React, { useRef, useState, useEffect } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, Button, Modal } from 'antd';
import request from 'ponshine-request';
import { Licensee } from 'ponshine';

import StandardTable from '@/components/StandardTable';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import BatchImportModal from './BatchImport';

import { exportFile } from '@/utils/utils';

import { batchImportRequest, getTableData } from './services';

const { RangePicker } = DatePicker;

const Index = ({ form, form: { getFieldDecorator, validateFields } }) => {
  const localNetworkRef = useRef();
  const [loading, setLoading] = useState(false);
  const [importVisible, setImportVisible] = useState(false);

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [serachParams, setSearchParams] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);

  let columns = [
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },

    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '备注',
      width: 120,
      dataIndex: 'remark',
      ellipsis: true,
    },
    {
      title: '入网网点',
      width: 160,
      dataIndex: 'acceptancePoint',
      ellipsis: true,
    },
    {
      title: '录入人',
      width: 100,
      dataIndex: 'operatorName',
      ellipsis: true,
    },
    {
      title: '录入时间',
      width: 140,
      dataIndex: 'gmtCreate',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await getTableData({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      const { lrDate } = values;
      getListDatas({
        ...values,
        gmtCreateStart: lrDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
        gmtCreateEnd: lrDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
        lrDate: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  const onReset = () => {
    form.resetFields();
    getListDatas({ localNetwork: localNetworkRef.current.getInitialValue() });
    setSelectedRows([]);
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/schoolCard/exportSchoolCardRiskLibrary',
      decode: true,
      params: { ...serachParams, pageNum: 1, pageSize: 1 }, //pageNum 和pageSize只为满足接口参数要求，没实际意义
      method: 'POST',
    });
  };

  const handleUpdate = () => {
    setImportVisible(true);
  };
  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除已选中数据吗?',
      onOk: async () => {
        const response = await request('/api/hn/schoolCard/deleteByIdList', {
          requestType: 'form',
          method: 'POST',
          data: { idList: selectedRows?.map((ele) => ele.id) },
        });
        if (response.code === 200) {
          message.success(response?.message);
          onReset();
        } else {
          message.error(response?.message);
        }
      },
    });
  };

  return (
    <div>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem form={form} getListDatas={getListDatas} cref={localNetworkRef} />
          </Col>
          <Col span={6}>
            <Form.Item label="录入人">
              {getFieldDecorator('operatorName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="录入时间">
              {getFieldDecorator('lrDate')(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="备注">
              {getFieldDecorator('remark')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={18} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>
            <Button onClick={onReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Licensee license="schoolCard_library_export">
              <Button type="primary" onClick={handleExport} style={{ marginRight: 8 }}>
                数据导出
              </Button>
            </Licensee>

            <Licensee license="schoolCard_library_import">
              <Button type="primary" onClick={handleUpdate} style={{ marginRight: 8 }}>
                批量变更
              </Button>
            </Licensee>
            <Licensee license="schoolCard_library_delete">
              <Button type="primary" onClick={handleDelete} disabled={!selectedRows?.length}>
                删除
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        onSelectRow={handleSelectRows}
        selectedRows={selectedRows}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
      {/* 批量导入 */}
      <BatchImportModal
        title="批量变更"
        tipsText="*每个文件不超过10000条"
        visible={importVisible}
        onClose={() => {
          setImportVisible(false);
        }}
        errorExportUrl={'/api/hn/schoolCard/downloadErrorFile'}
        downTemplateUrl={`/api/template/getTemplate?templateCode=schoolCardRiskLibraryImport`}
        importRequest={batchImportRequest}
        reload={() => {
          onReset();
        }}
        closeModal={() => {
          setImportVisible(false);
        }}
      />
    </div>
  );
};

export default Form.create()(Index);
