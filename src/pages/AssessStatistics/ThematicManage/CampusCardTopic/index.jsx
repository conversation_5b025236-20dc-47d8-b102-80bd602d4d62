import React from 'react';
import { Card, Tabs } from 'antd';

import { Licensee } from 'ponshine';

import AccessNetworkUserList from './AccessNetworkUserList';
import RiskPool from './RiskPool';
import SituationAnalysis from './SituationAnalysis';
import StopRestartDetail from './StopRestartDetail';
import StopRestartStatistics from './StopRestartStatistics';
import NoAuth from '@/pages/NoAuth';

const { TabPane } = Tabs;

export default function index() {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab="校园卡态势分析" key="1">
          <Licensee license="schoolCard_analysis" fallback={<NoAuth />}>
            <SituationAnalysis />
          </Licensee>
        </TabPane>
        {/* 默认不查 */}
        <TabPane tab="校园卡关停复机详单" key="2">
          <Licensee license="schoolCard_shutdownDetail" fallback={<NoAuth />}>
            <StopRestartDetail />
          </Licensee>
        </TabPane>
        {/* 默认不查 */}
        <TabPane tab="超28岁以上入网用户清单" key="3">
          <Licensee license="overTwentyEight_userInfo" fallback={<NoAuth />}>
            <AccessNetworkUserList />
          </Licensee>
        </TabPane>
        <TabPane tab="校园卡关停复机统计" key="4">
          <Licensee license="schoolCard_shutdown_count" fallback={<NoAuth />}>
            <StopRestartStatistics />
          </Licensee>
        </TabPane>

        <TabPane tab="校园卡风险库" key="5">
          <Licensee license="schoolCard_library" fallback={<NoAuth />}>
            <RiskPool />
          </Licensee>
        </TabPane>
      </Tabs>
    </Card>
  );
}
