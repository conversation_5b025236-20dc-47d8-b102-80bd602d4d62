import request from 'ponshine-request';

// 查询固话异常模型检出地市排名
export function getAbnormalModelRanking(data) {
  return request(`/api/hn/fixTelWarn/getFixTelWarningCityShutdown`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}

// 查询固话涉案装机类型占比
export function getCaseInstallType(data) {
  return request(`/api/hn/fixTelWarn/getFixWarningInstallType`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
// 查询固话异常模型关停情况
export function getAbnormalModelStop(data) {
  return request(`/api/hn/fixTelWarn/getFixWarningCount`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
// 查询表格
export function getTableData(data) {
  return request(`/api/hn/fixTelWarn/getFixTelFraudWarning`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}

// 批量查询
export function batchImportData(data) {
  return request(`/api/hn/fixTelWarn/batchQueryFixTelFraudWarning`, {
    method: 'POST',
    data: data,
    requestType: 'form',
  });
}

