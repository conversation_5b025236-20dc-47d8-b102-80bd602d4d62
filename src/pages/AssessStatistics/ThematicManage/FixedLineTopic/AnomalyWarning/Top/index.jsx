import React, { Fragment, useMemo, useState } from 'react';
import { Form, DatePicker, Button, Card, Row, Col } from 'antd';
const { RangePicker } = DatePicker;
import moment from 'moment';
import { getAbnormalModelRanking, getCaseInstallType, getAbnormalModelStop } from '../services';

import ChartCard from '../../SituationAnalysis/Components/ChartCard';
import Bar<PERSON>hart from '../../SituationAnalysis/Components/Chart/BarChart';
import MixLineBarChart from '../../SituationAnalysis/Components/Chart/MixLineBarChart';
import RingPieChart from '../../SituationAnalysis/Components/Chart/RingPieChart';

const getTimeFormatValue = (time) => {
  return {
    timeStart: time?.[0]?.format('YYYY-MM-DD'),
    timeEnd: time?.[1]?.format('YYYY-MM-DD'),
  };
};

const Index = ({ form: { getFieldDecorator, setFieldsValue, getFieldValue } }) => {
  const initialTime = [moment().startOf('month'), moment().subtract(1, 'day')];
  const [dateValue, setDateValue] = useState(getTimeFormatValue(initialTime));

  const handleSearch = () => {
    const timeValue = getFieldValue('time');
    setDateValue(getTimeFormatValue(timeValue));
  };
  const handleReset = () => {
    setFieldsValue({
      time: initialTime,
    });
    setDateValue(getTimeFormatValue(initialTime));
  };

  // const specialDateValue = useMemo(() => {
  //   return {
  //     timeStart: moment(dateValue?.timeStart)?.format('YYYY-MM-DD'),
  //     timeEnd: moment(dateValue?.timeEnd)?.format('YYYY-MM-DD'),
  //   };
  // }, [dateValue]);
  const getAbnormalModelStopChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.countDate),
      yData: [
        {
          name: '复机数',
          data: data?.map((ele) => ele.resumeCount),
          type: 'bar',
          stack: 'total',
          barMinHeight: 1,
        },
        {
          name: '自动复机数',
          data: data?.map((ele) => ele.autoResumeCount),
          type: 'bar',
          barMinHeight: 1,
          stack: 'total',
        },
        {
          name: '关停数',
          data: data?.map((ele) => ele.shutdownCount),
          type: 'line',
        },
      ],
    };
  };

  return (
    <Fragment>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="时间范围">
          {getFieldDecorator('time', {
            initialValue: initialTime,
          })(<RangePicker allowClear={false} />)}
        </Form.Item>
        <Form.Item>
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
            查询
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Form.Item>
      </Form>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <ChartCard
            title="固话异常模型检出地市排名"
            chartType={'bar'}
            getDataRequest={getAbnormalModelRanking}
            dateValue={dateValue}
          >
            <BarChart dateValue={dateValue} getDataRequest={getAbnormalModelRanking} />
          </ChartCard>
        </Col>
        <Col span={8}>
          <ChartCard
            title="固话涉案装机类型占比"
            chartType={'ringPie'}
            getDataRequest={getCaseInstallType}
            dateValue={dateValue}
          >
            <RingPieChart dateValue={dateValue} getDataRequest={getCaseInstallType} />
          </ChartCard>
        </Col>
        <Col span={8}>
          <ChartCard
            title="固话异常模型关停情况"
            chartType={'mixLineBar'}
            getDataRequest={getAbnormalModelStop}
            getChartProps={getAbnormalModelStopChartProps}
            dateValue={dateValue}
          >
            <MixLineBarChart
              dateValue={dateValue}
              getDataRequest={getAbnormalModelStop}
              getChartProps={getAbnormalModelStopChartProps}
            />
          </ChartCard>
        </Col>
      </Row>
    </Fragment>
  );
};

export default Form.create()(Index);
