import React, { useRef, useState, useEffect } from 'react';
import Top from './Top';
import { Form, Row, Col, Select, Input, Button, message, DatePicker, Modal } from 'antd';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import ExportApprove from '@/components/ExportApprove';
import StandardTable from '@/components/StandardTable';
import BatchImportModal from '@/components/BatchImport';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import EarlyWarningSettingModal from './EarlyWarningSettingModal';
import { Licensee } from 'ponshine';

const { RangePicker } = DatePicker;
import { getTableData, batchImportData } from './services';

import moment from 'moment';

const Index = ({
  form,
  form: { getFieldDecorator, validateFields, resetFields, getFieldValue },
}) => {
  const localNetworkRef = useRef();
  const [areaList, setAreaList] = useState([]);
  const serachParams = useRef();
  const [loading, setLoading] = useState(false);

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [importVisible, setImportVisible] = useState(false);
  const [settingVisible, setSettingVisible] = useState(false);
  // 区分是否是批量查询，批量查询时， 不可导出
  const [isBatchQuery, setIsBatchQuery] = useState(false);

  const columns = [
    {
      title: '固定号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },
    {
      title: '所属地市',
      width: 100,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '是否高危地区',
      width: 100,
      dataIndex: 'isHighRiskArea',
      ellipsis: true,
    },

    {
      title: '模型名称',
      width: 100,
      dataIndex: 'modelName',
      ellipsis: true,
    },
    {
      title: '处置策略',
      width: 100,
      dataIndex: 'shutdownType',
      ellipsis: true,
    },
    {
      title: '关停备注',
      width: 100,
      dataIndex: 'shutdownRemark',
      ellipsis: true,
    },
    {
      title: '处置时间',
      width: 160,
      dataIndex: 'importTime',
      ellipsis: true,
    },
    {
      title: '处置结果',
      width: 100,
      dataIndex: 'disposalResult',
      ellipsis: true,
    },

    {
      title: '开户渠道',
      width: 100,
      dataIndex: 'internetAccessPoint',
      ellipsis: true,
    },
    {
      title: '渠道类型',
      width: 100,
      dataIndex: 'channelType',
      ellipsis: true,
    },
    {
      title: '装机地址',
      width: 100,
      dataIndex: 'userInstallAddress',
      ellipsis: true,
    },
    {
      title: '装机类型',
      width: 100,
      dataIndex: 'userInstallType',
      ellipsis: true,
    },
    {
      title: '风险等级',
      width: 100,
      dataIndex: 'riskLevel',
      ellipsis: true,
    },
    {
      title: '上传人',
      width: 100,
      dataIndex: 'shutdownOperator',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await getTableData({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;
      setIsBatchQuery(false);
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    const startFormatStr = 'YYYY-MM-DD 00:00:00';
    const endFormatStr = 'YYYY-MM-DD 23:59:59';

    validateFields(async (err, values) => {
      if (err) return;
      const { time } = values;
      getListDatas({
        ...values,
        importTimeStart: time?.[0]?.format(startFormatStr),
        importTimeEnd: time?.[1]?.format(endFormatStr),
        time: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleReset = () => {
    resetFields();
    getListDatas({ localNetwork: localNetworkRef.current.getInitialValue() });
  };

  const handleSetting = () => {
    setSettingVisible(true);
  };

  const handlebathSearch = () => {
    setImportVisible(true);
  };

  const handleImport = async (fileData) => {
    const response = await batchImportData(fileData);
    if (response.code === 200) {
      message.success(response.message);
      setIsBatchQuery(true);
      setListData({
        list: response?.data || [],
        pagination: false,
      });
      setImportVisible(false);
    } else {
      message.error(response.message);
    }
  };
  return (
    <div>
      <Top />
      <Row style={{ marginTop: 24 }}>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
          <ShrinkSearchForm
            colSpan={8}
            formList={[
              <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <Form.Item
                  label="查询条件"
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  style={{ width: '45%' }}
                >
                  {getFieldDecorator('searchType', {
                    rules: [{ required: true, message: '请选择查询条件' }],
                    initialValue: 'phoneNum',
                  })(
                    <Select
                      getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      // style={{ width: 60, marginLeft: '-3px' }}
                    >
                      <Select.Option value="phoneNum">号码</Select.Option>
                      <Select.Option value="shutdownOperator">上传人</Select.Option>
                    </Select>,
                  )}
                </Form.Item>
                {getFieldValue('searchType') && (
                  <Form.Item
                    style={{ display: 'inline-block', marginLeft: 5, flex: 1, flexShrink: 1 }}
                    wrapperCol={{ span: 24 }}
                  >
                    {getFieldDecorator(getFieldValue('searchType'), {
                      rules: [{ required: true, message: '请选择查询条件内容' }],
                      initialValue: '',
                    })(
                      <Input
                        placeholder="请输入内容"
                        style={{ width: '100%' }}
                        allowClear
                        getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      />,
                    )}
                  </Form.Item>
                )}
              </div>,
              <LocalNetworkFormItem
                form={form}
                getListDatas={getListDatas}
                cref={localNetworkRef}
              />,

              <Form.Item label="处置时间">
                {getFieldDecorator('time')(
                  <RangePicker
                    allowClear={true}
                    placeholder={['开始时间', '结束时间']}
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD"
                    ranges={{
                      今天: [moment(), moment()],
                    }}
                    disabledDate={(current) => {
                      return current && current > moment().endOf('day');
                    }}
                  />,
                )}
              </Form.Item>,
              <Form.Item label="装机类型">
                {getFieldDecorator('userInstallType', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['个人', '公司', '酒店', '医院', '政企', '其他'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="是否高危地区" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('isHighRiskArea', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,

              <Form.Item label="风险等级">
                {getFieldDecorator('riskLevel', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['高', '中', '低'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
            ]}
            optButton={
              <Col>
                <Button type="primary" style={{ marginRight: 8 }} onClick={handleSearch}>
                  查询
                </Button>
                <Button type="primary" style={{ marginRight: 8 }} onClick={handlebathSearch}>
                  批量查询
                </Button>
                <Button type="" style={{ marginRight: 8 }} onClick={handleReset}>
                  重置
                </Button>
                <ExportApprove
                  buttonStyle={{ marginRight: 8 }}
                  exportParams={{
                    urlAPi: '/api/hn/fixTelWarn/exportFixTelFraudWarning',
                    decode: true,
                    params: {
                      ...serachParams.current,
                    },
                    method: 'POST',
                  }}
                  moduleTile="固话异常预警"
                  buttonText="导出"
                  disabledExport={isBatchQuery}
                />
              </Col>
            }
          ></ShrinkSearchForm>
        </Form>

        <StandardTable
          detailColumns={columns}
          columns={columns}
          loading={loading}
          data={listData}
          rowKey="id"
          onChange={handleTableChange}
          tools={true}
          rowSelectionProps={false}
          showSelectCount={false}
          scroll={{ x: 1000 }}
          extraButton={
            <Licensee license="fixTelWarningConfig">
              <Button type="primary" style={{ marginRight: 8 }} onClick={handleSetting}>
                预警规则设置
              </Button>
            </Licensee>
          }
        />
      </Row>
      {/* 批量导入 */}
      <BatchImportModal
        title="批量查询"
        tipsText="*每个文件不能超过20个号码"
        visible={importVisible}
        onClose={() => {
          setImportVisible(false);
        }}
        downTemplateUrl={`/api/template/getTemplate?templateCode=fixTelWarningBatchQuery`}
        closeModal={() => {
          setImportVisible(false);
        }}
        onImport={handleImport}
      />
      <EarlyWarningSettingModal
        visible={settingVisible}
        onCancel={() => {
          setSettingVisible(false);
        }}
        onReload={handleReset}
      />
    </div>
  );
};
export default Form.create()(Index);
