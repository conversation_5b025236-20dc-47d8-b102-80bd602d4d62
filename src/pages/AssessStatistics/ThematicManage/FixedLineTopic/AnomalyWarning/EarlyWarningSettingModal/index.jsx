import React, { Fragment, useEffect, useState } from 'react';
import {
  Form,
  Modal,
  Row,
  Col,
  Checkbox,
  Input,
  Divider,
  message,
  InputNumber,
  Button,
} from 'antd';
import CommonTitle from '@/components/CommonTitle';
import request from 'ponshine-request';

const Index = ({
  visible,
  form: { getFieldDecorator, getFieldsValue, getFieldValue, resetFields, validateFields },
  onCancel,
  onReload,
}) => {
  const highAreaConfigData = [
    {
      label: '近一个月涉案数大于等于',
      value: 'monthInvolved',
      defaultValue: 3,
    },
    {
      label: '新入网涉案数大于等于',
      value: 'networkInvolved',
      defaultValue: 2,
    },
    {
      label: '同一网点涉案大于等于',
      value: 'channelInvolved',
      defaultValue: 2,
    },

    {
      label: '复机后涉案数大于等于',
      value: 'resumeInvolved',
      defaultValue: 1,
    },
    {
      label: '日均关停量大于',
      value: 'shutdownRate',
      defaultValue: 5,
    },
  ];
  const highRiskOptions = [
    { label: '开户地址属于高危地区', value: 'highRisk' },
    { label: '开户网点曾涉案', value: 'highInvolved' },
    { label: '被12321举报', value: 'highReported' },
    { label: '黑名单二次开户', value: 'highBlack' },
  ];
  const middleRiskOptions = [
    { label: '开户网点曾涉案', value: 'middleInvolved' },
    { label: '被12321举报', value: 'middleReported' },
    { label: '黑名单二次开户', value: 'middleBlack' },
  ];
  const lowRiskOptions = [{ label: '白名单用户', value: 'lowWhite' }];

  const [riskConfigData, setRiskConfigData] = useState({});
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);

  const handleParamsValue = (options, checkedOpions) => {
    const obj = {};
    options.forEach((ele) => {
      // 为组装接口结构，选中的值为1， 未选中为0
      obj[ele.value] = checkedOpions?.includes(ele.value) ? 1 : 0;
    });
    return obj;
  };

  const updateRequest = async (params, fallback) => {
    const response = await request('/api/hn/fixTelWarn/updateRiskConfig', {
      method: 'POST',
      data: params,
      requestType: 'json',
    });
    fallback && fallback();
    if (response.code === 200) {
      message.success(response.message);
      onCancel();
      onReload();
    } else {
      message.error(response.message);
    }
  };
  const handleOk = async () => {
    const formValues = getFieldsValue();
    const { highArea, heightRisk, middletRisk, lowRisk } = formValues;

    if (highArea.some((ele) => !/^(?:[1-9]\d{0,2})$/.test(formValues[ele]))) {
      return message.info('高危地区选中项所填值只支持1-999的整数');
    }

    const params = {
      riskLevel: {},
      highRisk: {},
    };
    highAreaConfigData.forEach((ele) => {
      params.highRisk[ele.value] = highArea?.includes(ele.value) ? getFieldValue(ele.value) : 0;
    });
    params.riskLevel = {
      ...handleParamsValue(highRiskOptions, heightRisk),
      ...handleParamsValue(middleRiskOptions, middletRisk),
      ...handleParamsValue(lowRiskOptions, lowRisk),
    };
    Modal.confirm({
      title: '确认按照新规则执行数据统计吗？',
      onOk: async () => {
        setConfirmLoading(true);
        updateRequest(params, () => {
          setConfirmLoading(false);
        });
      },
    });
  };

  const getRiskConfigData = async () => {
    const response = await request('/api/hn/fixTelWarn/getRiskConfig', {
      method: 'POST',
    });
    if (response.code === 200) {
      setRiskConfigData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };
  useEffect(() => {
    visible && getRiskConfigData();
  }, [visible]);

  const getCheckedValues = (options, keyStr = 'riskLevel') => {
    return (
      options
        ?.filter((ele) => riskConfigData?.[keyStr]?.[ele.value] > 0)
        ?.map((ele) => ele.value) || []
    );
  };

  const handleReset = () => {
    const params = {
      riskLevel: {},
      highRisk: {},
    };

    highAreaConfigData.forEach((ele) => {
      params.highRisk[ele.value] = ele.defaultValue;
    });

    params.riskLevel = {
      ...handleParamsValue(
        highRiskOptions,
        highRiskOptions.map((ele) => ele.value),
      ),
      ...handleParamsValue(
        middleRiskOptions,
        middleRiskOptions.map((ele) => ele.value),
      ),
      ...handleParamsValue(
        lowRiskOptions,
        lowRiskOptions.map((ele) => ele.value),
      ),
    };
    setResetLoading(true);
    updateRequest(params, () => {
      setResetLoading(false);
    });
  };

  return (
    <Modal
      title="预警设置"
      visible={visible}
      okText="保存"
      onCancel={onCancel}
      width={1000}
      cancelText="恢复默认"
      afterClose={() => {
        setRiskConfigData([]);
        resetFields();
      }}
      footer={
        <Fragment>
          <Button onClick={handleReset} loading={resetLoading}>
            恢复默认
          </Button>
          <Button type="primary" onClick={handleOk} loading={confirmLoading}>
            保存修改
          </Button>
        </Fragment>
      }
    >
      <Row>
        <Col span={8}>
          <CommonTitle title="高危地区" />

          {getFieldDecorator('highArea', {
            initialValue: getCheckedValues(highAreaConfigData, 'highRisk'),
          })(
            <Checkbox.Group style={{ width: '100%' }}>
              {highAreaConfigData?.map((ele) => (
                <Checkbox
                  value={ele.value}
                  style={{ display: 'flex', alignItems: 'center', marginBottom: 16, marginLeft: 0 }}
                >
                  {ele.label}
                  {getFieldDecorator(`${ele.value}`, {
                    initialValue: riskConfigData?.highRisk?.[ele.value],
                  })(<Input style={{ width: 60, margin: '0 8px' }} size="small" />)}
                  件
                </Checkbox>
              ))}
            </Checkbox.Group>,
          )}
        </Col>
        <Col span={1}>
          <Divider type="vertical" style={{ height: 220, marginTop: 50 }} />
        </Col>
        <Col span={15}>
          <CommonTitle title="风险等级" />

          <Form.Item label={<strong>高风险</strong>} style={{ marginBottom: 0 }}>
            {getFieldDecorator('heightRisk', {
              initialValue: getCheckedValues(highRiskOptions),
            })(<Checkbox.Group options={highRiskOptions} />)}
          </Form.Item>
          <Form.Item label={<strong>中风险</strong>} style={{ marginBottom: 0 }}>
            {getFieldDecorator('middletRisk', {
              initialValue: getCheckedValues(middleRiskOptions),
            })(<Checkbox.Group options={middleRiskOptions} />)}
          </Form.Item>
          <Form.Item label={<strong>低风险</strong>} style={{ marginBottom: 0 }}>
            {getFieldDecorator('lowRisk', {
              initialValue: getCheckedValues(lowRiskOptions),
            })(<Checkbox.Group options={lowRiskOptions} />)}
          </Form.Item>
        </Col>
      </Row>
      <p style={{ color: 'red', marginTop: 16 }}>
        *注：保存规则并确认后，数据统计将按照新规则执行并展示计算结果，请谨慎修改
      </p>
    </Modal>
  );
};
export default Form.create()(Index);
