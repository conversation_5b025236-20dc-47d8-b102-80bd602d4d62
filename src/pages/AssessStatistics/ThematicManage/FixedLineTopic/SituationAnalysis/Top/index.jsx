import React, { useState, useEffect, memo } from 'react';
import styles from './index.less';
import { Row, Col, message } from 'antd';
import request from 'ponshine-request';

import one from '../imgs/one.png';
import two from '../imgs/two.png';
import three from '../imgs/three.png';
import four from '../imgs/four.png';
import five from '../imgs/five.png';
import six from '../imgs/six.png';
const Index = ({ dateValue }) => {
  const dataList = [
    {
      key: 'fixInternetAccessCount',
      title: '固话入网总量',
      img: one,
    },
    {
      key: 'fixInvolvedCount',
      title: '固话涉案总量',
      img: two,
    },
    {
      key: 'fixInvolvedPercent',
      title: '固话涉案占比',
      img: three,
    },
    {
      key: 'fixShutdownCount',
      title: '固话关停总量',
      img: four,
    },
    {
      key: 'fixResumeCount',
      title: '固话复机总量',
      img: five,
    },
    {
      key: 'fixResumePercent',
      title: '关停复机占比',
      img: six,
    },
  ];
  const [data, setData] = useState({});

  const findData = async () => {
    const response = await request('/api/hn/fixTel/getFixTelTotalCount', {
      method: 'POST',
      requestType: 'json',
      data: dateValue,
    });

    if (response.code === 200) {
      setData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && findData();
  }, [dateValue]);

  return (
    <Row gutter={[16, 16]}>
      {dataList?.map((ele) => (
        <Col span={4} key={ele.key}>
          <div className={styles.topItem}>
            <img src={ele.img} />
            <div>
              <div>{ele.title}</div>
              <div>
                <strong>{data?.[ele.key] ?? '--'}</strong>
              </div>
            </div>
          </div>
        </Col>
      ))}
    </Row>
  );
};

export default memo(Index);
