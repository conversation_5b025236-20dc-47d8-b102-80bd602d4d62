import React, { useState, useEffect } from 'react';
import BarChart from '@/components/Chart/BarChart';
import request from 'ponshine-request';

export default function index({ dateValue, getDataRequest, getChartProps }) {
  const commonBarChartPorps = {
    legendData: [],
    height: 220,
    width: '100%',
    isMutipleYAxisIndex: false,
    seriesConfig: { barWidth: 10 },
    gridOption: { left: 40, top: 50, bottom: 50 },
    axisLabelOption: { width: 80, rotate: 0 },
  };
  const [loading, setLoading] = useState(false);

  const [chartData, setChartData] = useState([]);
  const getData = async () => {
    setLoading(true);
    const response = await getDataRequest(dateValue);

    setLoading(false);
    if (response.code === 200) {
      setChartData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue]);
  return (
    <BarChart
      {...commonBarChartPorps}
      xData={chartData?.map((ele) => ele?.xvalue)}
      yData={[{ data: chartData?.map((ele) => ele?.yvalue) }]}
      // {...getChartProps(chartData)}
      loading={loading}
    />
  );
}
