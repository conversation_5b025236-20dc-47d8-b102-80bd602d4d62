import React, { useState, useEffect } from 'react';
import Ring<PERSON><PERSON><PERSON>hart from '@/components/Chart/RingPieChart';

export default function index({ dateValue, getDataRequest, getChartProps }) {
  const commonRangPieChartPorps = {
    height: 220,
    width: '100%',
  };

  const [loading, setLoading] = useState(false);

  const [chartData, setChartData] = useState([]);
  const getData = async () => {
    setLoading(true);
    const response = await getDataRequest(dateValue);

    setLoading(false);
    if (response.code === 200) {
      setChartData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue]);
  return (
    <RingPieChart
      {...commonRangPieChartPorps}
      data={chartData}
      // {...getChartProps(chartData)}
      loading={loading}
    />
  );
}
