import React, { useState, useEffect } from 'react';
import LineChart from '@/components/Chart/LineChart';

export default function index({ dateValue, getDataRequest, getChartProps }) {
  const commonLineChartPorps = {
    legendData: [],
    height: 220,
    width: '100%',
    isMutipleYAxisIndex: false,
    seriesConfig: { barWidth: 20 },
    gridOption: { left: 70 },
    axisLabelOption: { width: 80 },
  };
  const [loading, setLoading] = useState(false);

  const [chartData, setChartData] = useState([
    {
      shutNum: 10,
      restartNum: 15,
      autoRestartNum: 10,
      date: '2024-09-08',
    },
    {
      shutNum: 12,
      restartNum: 66,
      autoRestartNum: 80,
      date: '2024-09-09',
    },
    {
      shutNum: 34,
      restartNum: 56,
      autoRestartNum: 100,
      date: '2024-09-10',
    },
  ]);
  const getData = async () => {
    setLoading(true);
    const response = await getDataRequest(dateValue);

    setLoading(false);
    if (response.code === 200) {
      setChartData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue]);
  return (
    <LineChart
      {...commonLineChartPorps}
      legendData={['关停量', '复机量', '自动复机量']}
      {...(getChartProps
        ? getChartProps(chartData)
        : {
            xData: chartData?.map((ele) => ele?.xvalue),
            yData: [{ data: chartData?.map((ele) => ele?.yvalue) }],
          })}
      loading={loading}
    />
  );
}
