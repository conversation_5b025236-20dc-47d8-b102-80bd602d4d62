import React, { useState, useEffect } from 'react';
import MixLineBarChart from '@/components/Chart/MixLineBarChart';

export default function index({ dateValue, getDataRequest, getChartProps, legendData }) {
  const commonMixChartPorps = {
    legendData: legendData,
    height: 220,
    width: '100%',
    isMutipleYAxisIndex: false,
    seriesConfig: { barWidth: 15 },
    gridOption: { left: 60 },
    axisLabelOption: { width: 80, hideOverlap: true },
  };
  const [loading, setLoading] = useState(false);

  const [chartData, setChartData] = useState([]);
  const getData = async () => {
    setLoading(true);
    const response = await getDataRequest(dateValue);

    setLoading(false);
    if (response.code === 200) {
      setChartData(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    dateValue && getData();
  }, [dateValue]);
  return (
    <MixLineBarChart
      {...commonMixChartPorps}
      {...(getChartProps
        ? getChartProps(chartData)
        : {
            xData: chartData?.map((ele) => ele?.xValue),
            yData: [{ data: chartData?.map((ele) => ele?.yValue), type: 'bar' }],
          })}
      loading={loading}
    />
  );
}
