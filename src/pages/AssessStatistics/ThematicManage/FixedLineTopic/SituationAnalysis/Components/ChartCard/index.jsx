import React, { Fragment, useState, useContext } from 'react';
import { Icon } from 'antd';

import CommonCard from '@/components/CommonCard';
import ChartDetailModal from './ChartDetailModal';

export default function index({
  title,
  chartType,
  chartProps,
  getDataRequest,
  getChartProps = null,
  children,
  dateValue,
}) {
  const [visible, setVisible] = useState(false);
  const tiggerVisible = () => {
    setVisible(!visible);
  };

  return (
    <Fragment>
      <CommonCard
        title={title}
        extra={<Icon type="info-circle" theme="twoTone" onClick={tiggerVisible} />}
        style={{ marginBottom: 16 }}
      >
        {children}
      </CommonCard>
      {visible && (
        <ChartDetailModal
          title={title}
          chartProps={chartProps}
          chartType={chartType}
          onCancel={tiggerVisible}
          visible={visible}
          getDataRequest={getDataRequest}
          dateValue={dateValue}
          getChartProps={getChartProps}
        />
      )}
    </Fragment>
  );
}
