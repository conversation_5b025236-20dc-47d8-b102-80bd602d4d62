import Line<PERSON>hart from '@/components/Chart/LineChart';
import Bar<PERSON>hart from '@/components/Chart/BarChart';
import Pie<PERSON>hart from '@/components/Chart/PieChart';

export const renderChart = (type, chartProps) => {
  const commonBarChartPorps = {
    legendData: [],
    height: 250,
    width: '100%',
    isMutipleYAxisIndex: false,
    seriesConfig: { barWidth: 20 },
    gridOption: { left: 50 },
    axisLabelOption: { width: 80 },
  };
  const chartList = {
    bar: <BarChart {...chartProps} {...commonBarChartPorps} />,
    pie: <PieChart {...chartProps} />,
    line: <LineChart {...chartProps} />,
  };
  return chartList?.[type];
};
