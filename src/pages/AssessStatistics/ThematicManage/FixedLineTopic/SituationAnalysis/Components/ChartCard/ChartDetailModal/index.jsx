import React, { Fragment, useMemo, useState, useContext } from 'react';
import { Modal, Radio, Icon } from 'antd';
import CommonCard from '@/components/CommonCard';

import LineChart from '../../Chart/LineChart';
import BarChart from '../../Chart/BarChart';
import RingPieChart from '../../Chart/RingPieChart';
import BarTopChart from '../../Chart/BarTopChart';
import MixLineBarChart from '../../Chart/MixLineBarChart';
import DateRadio from '../../DateRadio';

export default function index({
  visible,
  title,
  chartType,
  onCancel,
  getDataRequest,
  getChartProps,
  dateValue,
}) {
  const [raidoValue, setRadioValue] = useState('');
  const [radioDateValue, setRadioDateValue] = useState(dateValue);
  const commonChartProps = {
    dateValue: radioDateValue,
    getDataRequest: getDataRequest,
    getChartProps: getChartProps,
  };

  const chartList = {
    bar: <BarChart {...commonChartProps} />,
    barTop: <BarTopChart {...commonChartProps} />,
    ringPie: <RingPieChart {...commonChartProps} />,
    line: <LineChart {...commonChartProps} />,
    mixLineBar: <MixLineBarChart {...commonChartProps} />,
  };

  const handleChange = (v) => {
    setRadioValue(v.radioValue);
    setRadioDateValue(v);
  };

  const handleRefresh = () => {
    setRadioValue('');
    setRadioDateValue({ ...dateValue });
  };

  return (
    <Modal visible={visible} footer={null} onCancel={onCancel} width={1000} maskClosable={false}>
      <CommonCard
        title={title}
        extra={<DateRadio onChange={handleChange} value={raidoValue} onRefresh={handleRefresh} />}
      >
        {chartList?.[chartType]}
      </CommonCard>
    </Modal>
  );
}
