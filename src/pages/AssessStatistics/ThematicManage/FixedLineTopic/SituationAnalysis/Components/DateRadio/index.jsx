import React from 'react';
import { Radio, Icon } from 'antd';
import moment from 'moment';
export default function index({ onChange, value, onRefresh }) {
  const changeRadio = (e) => {
    const value = e.target.value;
    onChange({
      radioValue: value,
      timeStart: moment().subtract(value, 'day').format('YYYY-MM-DD'),
      timeEnd: moment().subtract(1, 'day').format('YYYY-MM-DD'),
    });
  };

  return (
    <Radio.Group onChange={changeRadio} size="small" value={value}>
      <Icon type="reload" style={{ marginRight: 16 }} onClick={onRefresh} />
      <Radio.Button value={7}>近7天</Radio.Button>
      <Radio.Button value={30}>近30天</Radio.Button>
    </Radio.Group>
  );
}
