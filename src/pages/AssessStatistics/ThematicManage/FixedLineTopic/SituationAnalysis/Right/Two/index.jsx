import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getShutNumRanking } from '../services';
import BarChart from '../../Components/Chart/BarChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.xValue),
      yData: [{ data: data?.map((ele) => ele?.yValue) }],
    };
  };

  return (
    <ChartCard
      title="固话关停量地市排名"
      chartType={'bar'}
      getDataRequest={getShutNumRanking}
      getChartProps={getChartProps}
      dateValue={dateValue}
    >
      <BarChart
        dateValue={dateValue}
        getDataRequest={getShutNumRanking}
        getChartProps={getChartProps}
      />
    </ChartCard>
  );
}
