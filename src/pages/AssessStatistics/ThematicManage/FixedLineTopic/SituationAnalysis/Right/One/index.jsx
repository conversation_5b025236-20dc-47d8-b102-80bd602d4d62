import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getStopRestartNum } from '../services';
import LineChart from '../../Components/Chart/LineChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.countDate),
      yData: [
        { name: '关停量', data: data?.map((ele) => ele?.shutdownCount) },
        { name: '复机量', data: data?.map((ele) => ele?.resumeCount) },
        { name: '自动复机量', data: data?.map((ele) => ele?.autoResumeCount) },
      ],
    };
  };

  return (
    <ChartCard
      title="关停复机量趋势"
      chartType={'line'}
      getDataRequest={getStopRestartNum}
      getChartProps={getChartProps}
      dateValue={dateValue}
    >
      <LineChart
        dateValue={dateValue}
        getDataRequest={getStopRestartNum}
        getChartProps={getChartProps}
      />
    </ChartCard>
  );
}
