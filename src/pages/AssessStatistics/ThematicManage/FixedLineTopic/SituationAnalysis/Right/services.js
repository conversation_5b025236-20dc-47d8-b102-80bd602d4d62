import request from 'ponshine-request';

// 查询关停复机量趋势
export function getStopRestartNum(data) {
  return request(`/api/hn/fixTel/getShutdownResumeTrend`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}

// 查询固话关停量地市排名
export function getShutNumRanking(data) {
  return request(`/api/hn/fixTel/getFixShutdownCityRank`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
// 查询网点关停量TOP5
export function getNetPonintStopNum(data) {
  return request(`/api/hn/fixTel/getFixShutdownTopFive`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
// 查询复机后涉案占比
export function getRestartCaseProportion(data) {
  return request(`/api/hn/fixTel/getFixInvolvedAfterResumePercent`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
