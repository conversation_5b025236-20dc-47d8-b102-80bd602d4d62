import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getRestartCaseProportion } from '../services';
import RingPieChart from '../../Components/Chart/RingPieChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.xValue),
      yData: [{ data: data?.map((ele) => ele?.yValue) }],
    };
  };

  return (
    <div>
      <ChartCard
        title="复机后涉案占比"
        chartType={'ringPie'}
        getDataRequest={getRestartCaseProportion}
        getChartProps={getChartProps}
        dateValue={dateValue}
      >
        <RingPieChart
          dateValue={dateValue}
          getDataRequest={getRestartCaseProportion}
          getChartProps={getChartProps}
        />
      </ChartCard>
    </div>
  );
}
