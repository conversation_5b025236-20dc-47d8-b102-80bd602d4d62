import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getNetPonintStopNum } from '../services';
import BarTopChart from '../../Components/Chart/BarTopChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.xValue),
      yData: [{ data: data?.map((ele) => ele?.yValue) }],
    };
  };

  return (
    <ChartCard
      title="网点关停量TOP5"
      chartType={'barTop'}
      getDataRequest={getNetPonintStopNum}
      getChartProps={getChartProps}
      dateValue={dateValue}
    >
      <BarTopChart
        dateValue={dateValue}
        getDataRequest={getNetPonintStopNum}
        getChartProps={getChartProps}
      />
    </ChartCard>
  );
}
