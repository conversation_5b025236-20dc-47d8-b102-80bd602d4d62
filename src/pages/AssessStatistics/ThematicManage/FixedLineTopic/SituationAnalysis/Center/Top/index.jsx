import React, { useEffect, useMemo, useState, useContext } from 'react';
import { message, Spin } from 'antd';
import MapChart from './MapChart/index';
import request from 'ponshine-request';
import { getMapData } from '../services';

export default function index({ dateValue }) {
  const [loading, setLoading] = useState(false);

  var customerBatteryCityData = [
    {
      name: '长沙市',
      value: 20,
    },
    {
      name: '株洲市',
      value: 10,
    },
    {
      name: '湘潭市',
      value: 120,
    },
    {
      name: '衡阳市',
      value: 8,
    },
    {
      name: '邵阳市',
      value: 17,
    },
    {
      name: '岳阳市',
      value: 11,
    },
    {
      name: '常德市',
      value: 29,
    },
    {
      name: '张家界市',
      value: 20,
    },
    {
      name: '益阳市',
      value: 46,
    },
    {
      name: '郴州市',
      value: 40,
    },
    {
      name: '永州市',
      value: 30,
    },
    {
      name: '怀化市',
      value: 43,
    },
    {
      name: '娄底市',
      value: 98,
    },
    {
      name: '湘西土家族苗族自治州',
      value: 0,
    },
  ];
  const [data, setData] = useState([]);
  const getData = async () => {
    setLoading(true);
    const response = await getMapData(dateValue);
    setLoading(false);

    if (response.code === 200) {
      setData(
        response?.data?.map((ele) => {
          return {
            name: ele?.xvalue?.includes('湘西') ? '湘西土家族苗族自治州' : ele.xvalue + '市',
            value: ele.yvalue,
          };
        }) || [],
      );
    } else {
      message.error(response.message);
    }
  };

  const maxHeight = useMemo(() => {
    return Math.max(...data.map((ele) => ele.value));
  }, [data]);
  useEffect(() => {
    if (dateValue) {
      getData();
    }
  }, [dateValue]);
  return (
    <Spin spinning={loading}>
      <MapChart data={data} loading={loading} maxHeight={maxHeight} />
    </Spin>
  );
}
