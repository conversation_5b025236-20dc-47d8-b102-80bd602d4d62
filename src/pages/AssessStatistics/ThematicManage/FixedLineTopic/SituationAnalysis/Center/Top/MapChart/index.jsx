import React, { useEffect, useRef, useMemo } from 'react';
import * as echarts from 'echarts/lib/echarts';
import ReactEcharts from 'echarts-for-react';

import hunan from '@/components/Chart/hunan.json';

export default function index({ data: mapData, loading }) {
  const barWidth = 7;

  // 地图实际描绘的点
  var geoCoordMap = {
    长沙市: [112.982279, 28.19409],
    株洲市: [113.251737, 27.835806],
    湘潭市: [112.744052, 27.82973],
    衡阳市: [112.607693, 26.900358],
    邵阳市: [111.46923, 27.237842],
    岳阳市: [113.132855, 29.37029],
    常德市: [111.691347, 29.040225],
    张家界市: [110.479921, 29.127401],
    益阳市: [112.355042, 28.570066],
    郴州市: [113.032067, 25.793589],
    永州市: [111.608019, 26.434516],
    怀化市: [109.97824, 27.550082],
    娄底市: [112.008497, 27.728136],
    湘西土家族苗族自治州: [109.639735, 28.314296],
  };

  // 动态计算柱形图的高度（定一个max）
  const lineMaxHeight = () => {
    const maxValue = Math.max(...mapData.map((item) => item.value)) || 1;
    return 0.9 / maxValue;
  };

  const barMaxHeight = () => {
    return Math.max(...mapData.map((item) => item.value));
  };

  // 柱状体的主干
  const lineData = useMemo(() => {
    return mapData.map((item) => {
      return {
        coords: [
          geoCoordMap[item.name],
          [geoCoordMap[item.name][0], geoCoordMap[item.name][1] + item.value * lineMaxHeight()],
        ],
      };
    });
  }, [mapData]);

  // 柱状体的顶部
  const scatterData = useMemo(() => {
    return mapData.map((item) => {
      return {
        ...item,
        total: item.value,
        value: [
          geoCoordMap[item.name][0],
          geoCoordMap[item.name][1] + item.value * lineMaxHeight(),
        ],
      };
    });
  }, [mapData]);

  // 柱状体的底部
  const scatterData2 = useMemo(() => {
    return mapData.map((item) => {
      return {
        name: item.name,
        value: geoCoordMap[item.name],
      };
    });
  }, [mapData]);

  const option = useMemo(() => {
    echarts.registerMap('湖南', hunan);

    return {
      // backgroundColor: '#131C38',
      tooltip: {
        trigger: 'item',
        show: true,
        enterable: true,
        textStyle: {
          fontSize: 14,
          color: '#fff',
        },
        backgroundColor: 'rgba(0,2,89,0.8)',
        formatter: '{b}：{c}',
      },
      grid: {
        left: '88%',
        bottom: '15%',
        height: 120,
      },
      xAxis: {
        gridIndex: 0,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#fff',
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        data: ['电渠'],
      },
      yAxis: {
        gridIndex: 0,
        interval: 0,
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#00effc',
          },
        },
        max: barMaxHeight(),
      },
      geo: [
        {
          map: '湖南', // 注册的地图名称
          aspectScale: 0.9, //地图的长宽比
          roam: false, // 是否允许缩放
          zoom: 0.9, // 当前视角的缩放比例。
          layoutSize: '95%', //地图的大小
          layoutCenter: ['46%', '50%'], //定义地图中心在屏幕中的位置
          itemStyle: {
            normal: {
              //设置整个地图区域的颜色
              areaColor: '#3181dd',
              borderColor: 'rgba(255,255,255,0.5)',
              borderWidth: 1,
            },
          },
          emphasis: {
            // 鼠标移入高亮
            itemStyle: {
              areaColor: '#5694db',
            },
          },
          label: {
            show: false,
            emphasis: {
              show: false,
            },
            // color: '#fff',
          },

          zlevel: 3, //zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面。
          clickable: false,
        },
      ],
      series: [
        // map
        {
          map: '湖南', // 使用

          geoIndex: 0, // geoIndex 指定一个 geo 组件,map 和 其他 series（例如散点图）就可以共享一个 geo 组件
          // coordinateSystem: 'geo',
          showLegendSymbol: true, //在图例相应区域显示图例的颜色标识（系列标识的小圆点）
          type: 'map',
          selectedMode: false, // 禁用选中模式
          roam: true, //是否开启鼠标缩放和平移漫游

          data: mapData,

          // data: this.difficultData //热力图数据   不同区域 不同的底色
        },
        // 柱状体的主干
        {
          type: 'lines',
          coordinateSystem: 'geo',
          geoIndex: 0,
          zlevel: 6,
          effect: {
            //线特效的配置
            show: false,
            // period: 4, //箭头指向速度，值越小速度越快
            // trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
            // symbol: 'arrow', //箭头图标
            // symbol: imgDatUrl,
            symbolSize: 5, // 图标大小
          },
          lineStyle: {
            // color: '#F94638',
            normal: {
              width: barWidth, // 尾迹线条宽度

              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,248,0,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255,248,0,0)',
                  },
                ],
                false,
              ),
              opacity: 1, // 尾迹线条透明度
              curveness: 0, //边的曲度，支持从 0 到 1 的值，值越大曲度越大。
            },
          },

          silent: true, //图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件
          data: lineData,
        },
        // 柱状体的顶部
        {
          type: 'scatter', //散点（气泡）图
          coordinateSystem: 'geo', //该系列使用的坐标系
          geoIndex: 0,
          zlevel: 7,
          // width: 50,
          // height: 26,
          label: {
            show: true,
            // backgroundColor: {
            //   image: mapTip,
            // },
            backgroundColor: '#fff',

            padding: [4, 10],
            borderRadius: 4,
            borderWidth: 1,
            borderColor: 'rgba(30,75,255,0.34)',
            shadowColor: 'rgba(66,164,255,0.9)',
            shadowBlur: 5,
            color: '#777',
            formatter: function (params) {
              return `{a|${params.data.total}}{b|个}`;
            },
            position: 'top',
            rich: {
              a: {
                fontSize: 16,
                color: '#909FAB',
              },
              b: {
                fontSize: 12,
                align: 'center',
                width: 20,
                color: '#909FAB',
              },
            },
          },
          symbol: 'circle',
          symbolSize: [barWidth, 4],
          itemStyle: {
            color: 'rgba(255,248,0,0)',
            opacity: 1,
          },
          silent: true,
          data: scatterData,
        },
        // 柱状体的底部
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          geoIndex: 0,
          zlevel: 4,

          symbol: 'circle',
          symbolSize: [barWidth, 4],
          itemStyle: {
            color: 'rgba(255,248,0,0)',
            opacity: 1,
          },

          label: {
            // 这儿是处理的
            formatter: '{b}',
            position: 'bottom',
            color: '#F1F4F7',
            fontSize: 12,
            distance: 10,
            show: true,
          },

          silent: true,
          data: scatterData2,
        },
      ],
    };
  }, [mapData]);

  return <ReactEcharts option={option} style={{ width: '100%', height: 846 }} />;
}
