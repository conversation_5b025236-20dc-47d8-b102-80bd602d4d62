import React from 'react';
import styles from './index.less';
import { Badge } from 'antd';

export default function index({ data = {} }) {
  const overViewList = [
    {
      title: '个人',
      percentKey: 'personalPercent',
      key: 'personalCount',
      color: '#f50',
    },
    {
      title: '酒店',
      percentKey: 'hotelPercent',
      key: 'hotelCount',
      color: '#2db7f5',
    },
    {
      title: '医院',
      percentKey: 'hospitalPercent',
      key: 'hospitalCount',
      color: '#87d068',
    },
    {
      title: '政企',
      percentKey: 'companyPercent',
      key: 'companyCount',
      color: '#108ee9',
    },
    {
      title: '其他客户',
      percentKey: 'othersPercent',
      key: 'othersCount',
      color: '#722ed1',
    },
  ];
  return (
    <div className={styles.overView}>
      {overViewList?.map((ele) => (
        <div className={styles.overViewItem}>
          <strong>
            <Badge
              color={ele.color}
              text={
                <span>
                  {ele.title}: {data?.[ele.key] ?? '--'}
                </span>
              }
            />
          </strong>
          <div style={{ marginLeft: 13 }}>
            <span>占比：{data?.[ele.percentKey] ?? '--'}</span>
          </div>
        </div>
      ))}
    </div>
  );
}
