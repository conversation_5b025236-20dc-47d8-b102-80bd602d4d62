import React, { memo, useContext, useEffect, useRef, useState } from 'react';
import StandardTable from '@/components/StandardTable';
import { Table, Spin, Popover, message } from 'antd';
import { getRiskNumTable } from '../../services';

const AutoScrollPaginationTable = memo(({ radioDate }) => {
  const pageSize = 20;

  const [data, setData] = useState([]);

  const [loading, setLoading] = useState(false);

  const currentPage = useRef(1);
  const total = useRef(0);
  const tableRef = useRef(null); // 表格容器的引用
  const scrollTimer = useRef();

  const columns = [
    {
      title: '固话号码',
      dataIndex: 'fixTelNum',
      key: 'fixTelNum',
      ellipsis: true,
      width: 100,
    },
    {
      title: '所属地区',
      dataIndex: 'localNetwork',
      key: 'localNetwork',
      ellipsis: true,
      width: 100,
    },
    {
      title: '发展渠道',
      dataIndex: 'acceptancePoint',
      key: 'acceptancePoint',
      width: 100,
      ellipsis: true,
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      key: 'channelType',
      width: 100,
      ellipsis: true,
    },
    {
      title: '安装地址',
      dataIndex: 'userInstallAddress',
      key: 'userInstallAddress',
      width: 100,
      ellipsis: true,
    },
    {
      title: '所属类型',
      dataIndex: 'userType',
      key: 'userType',
      width: 100,
      ellipsis: true,
    },
    // {
    //   title: '风险等级',
    //   dataIndex: 'riskLevel',
    //   key: 'riskLevel',
    //   ellipsis: true,
    // },
    {
      title: '操作',
      width: 100,
      dataIndex: 'opt',
      key: 'opt',
      render: (v, r) => {
        return (
          <Popover
            content={<div dangerouslySetInnerHTML={{ __html: getDetail(r.shutdownDetail) }}></div>}
            trigger="hover"
          >
            <a>查看</a>
          </Popover>
        );
      },
    },
  ];

  const getDetail = (str) => {
    let regex = /被(.*?)命中/;
    let matchStr = str.match(regex)?.[1];
    return str.replace(matchStr, `<span style="color: blue">${matchStr}</span>`);
  };

  // 加载数据的方法
  const loadMoreData = async ({ pageNum = currentPage.current, isRefresh = false } = {}) => {
    if (loading) return;
    setLoading(true);
    const response = await getRiskNumTable({
      pageSize,
      pageNum,
      ...radioDate,
    });

    setLoading(false);
    if (response.code === 200) {
      if (isRefresh) {
        setData(response?.data?.items || []);
      } else {
        setData((prevData) => [...prevData, ...(response?.data?.items || [])]);
      }
      total.current = response?.data?.totalNum;
    } else {
      message.error(response?.message);
    }
  };

  const autoScroll = () => {
    let v = document.querySelector(`.autoScrollTable .ant-table-body`);
    scrollTimer.current && clearInterval(scrollTimer.current);
    scrollTimer.current = setInterval(() => {
      if (v) {
        v.scrollTop += 1; // 控制表格自动向下滚动
      }
    }, 50); // 自动滚动速度

    return () => clearInterval(scrollTimer.current);
  };

  useEffect(() => {
    currentPage.current = 1;
    let v = document.querySelector(`.autoScrollTable .ant-table-body`);
    v.scrollTop = 0;
    radioDate && loadMoreData({ pageNum: 1, isRefresh: true });
  }, [radioDate]);

  useEffect(() => {
    autoScroll();
  }, []);

  const handleScroll = () => {
    let v = document.querySelector(`.autoScrollTable .ant-table-body`);
    const { scrollTop, scrollHeight, clientHeight } = v;
    if (Math.ceil(scrollTop) >= parseFloat(scrollHeight - clientHeight)) {
      const totalPage = Math.ceil(total.current / pageSize);
      if (currentPage.current >= totalPage) {
        v.scrollTop = 0;
      } else {
        currentPage.current += 1;
        loadMoreData({
          pageNum: currentPage.current,
        });
      }
    }
  };

  return (
    <div
      ref={tableRef}
      className="autoScrollTable"
      onMouseEnter={() => {
        clearInterval(scrollTimer.current);
      }}
      onMouseLeave={() => {
        autoScroll();
      }}
      onScroll={handleScroll}
    >
      <StandardTable
        columns={columns}
        data={{
          list: data,
          pagination: false,
        }}
        rowKey="key"
        scroll={{
          y: 187,
          scrollToFirstRowOnChange: true,
        }}
        showSelectCount={false}
        rowSelectionProps={false}
        loading={loading}
      />
    </div>
  );
});

export default AutoScrollPaginationTable;
