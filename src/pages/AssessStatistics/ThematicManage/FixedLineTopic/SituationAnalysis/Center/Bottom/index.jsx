import React, { useContext, useEffect, useState } from 'react';
import { message } from 'antd';
import CommonCard from '@/components/CommonCard';
import DateRadio from '../../Components/DateRadio';
import OverView from './OverView';
import AutoScrollTable from './AutoScrollTable';

import { getRiskNumToal, getRiskNumTable } from '../services';

export default function index({ dateValue }) {
  const [loading, setLoading] = useState(false);
  const [radioValue, setRadioValue] = useState('');
  const [radioDate, setRadioDate] = useState(dateValue);

  const [totalData, setTotalData] = useState({});

  const getTotalData = async (params = {}) => {
    setLoading(true);
    const response = await getRiskNumToal({ ...dateValue, ...params });
    setLoading(false);
    if (response.code === 200) {
      setTotalData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    if (radioValue) {
      setRadioValue('');
    }
    setRadioDate(dateValue);
    getTotalData();
  }, [dateValue]);
  const handleChangeRadio = (v) => {
    setRadioValue(v.radioValue);
    setRadioDate(v);
    getTotalData(v);
  };

  const handleRefresh = () => {
    setRadioValue('');
    setRadioDate({ ...dateValue });
    getTotalData();
  };

  return (
    <CommonCard
      title={
        <div>
          模型检出风险号码数:{' '}
          <span style={{ fontSize: 20 }}>{totalData?.modelShutdownCount ?? '--'}</span>
        </div>
      }
      extra={
        <DateRadio onChange={handleChangeRadio} value={radioValue} onRefresh={handleRefresh} />
      }
    >
      <OverView data={totalData} />
      <AutoScrollTable radioDate={radioDate} />
    </CommonCard>
  );
}
