import React, { useState } from 'react';
import { Form, DatePicker, Button, Row, Col } from 'antd';
import moment from 'moment';

import Top from './Top';
import Left from './Left';
import Center from './Center';
import Right from './Right';

const { RangePicker } = DatePicker;

const getTimeFormatValue = (time) => {
  return {
    timeStart: time?.[0]?.format('YYYY-MM-DD'),
    timeEnd: time?.[1]?.format('YYYY-MM-DD'),
  };
};
const Index = ({ form: { getFieldDecorator, setFieldsValue, getFieldValue } }) => {
  // 当天是1号的话，展示上个月第一天到最后一天的数据
  const isFirstDayOfMonth = moment().date() === 1;
  const lastMonth = [
    moment().subtract(1, 'month').startOf('month'),
    moment().subtract(1, 'month').endOf('month'),
  ];
  const initialTime = isFirstDayOfMonth
    ? lastMonth
    : [moment().startOf('month'), moment().subtract(1, 'day')];

  const [dateValue, setDateValue] = useState(getTimeFormatValue(initialTime));

  const handleSearch = () => {
    const timeValue = getFieldValue('time');
    setDateValue(getTimeFormatValue(timeValue));
  };
  const handleReset = () => {
    setFieldsValue({
      time: initialTime,
    });
    setDateValue(getTimeFormatValue(initialTime));
  };

  const disabledDate = (current) => {
    return current && current >= moment().startOf('day');
  };

  return (
    <div>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="时间范围">
          {getFieldDecorator('time', {
            initialValue: initialTime,
          })(<RangePicker disabledDate={disabledDate} />)}
        </Form.Item>
        <Form.Item>
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
            查询
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Form.Item>
      </Form>
      <Top dateValue={dateValue} />
      <div style={{ display: 'flex', width: '100%' }}>
        <div style={{ width: '24%' }}>
          <Left dateValue={dateValue} />
        </div>
        <div style={{ width: 'calc(52% - 32px)', margin: '0 16px' }}>
          <Center dateValue={dateValue} />
        </div>
        <div style={{ width: '24%' }}>
          <Right dateValue={dateValue} />
        </div>
      </div>
    </div>
  );
};

export default Form.create()(Index);
