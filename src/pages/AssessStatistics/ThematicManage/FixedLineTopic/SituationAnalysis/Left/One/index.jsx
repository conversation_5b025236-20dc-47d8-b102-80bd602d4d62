import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getCaseNumberRanking } from '../services';
import BarChart from '../../Components/Chart/BarChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.xValue),
      yData: [{ data: data?.map((ele) => ele?.yValue) }],
    };
  };

  return (
    <ChartCard
      title="固话涉案量地市排名"
      chartType={'bar'}
      getDataRequest={getCaseNumberRanking}
      getChartProps={getChartProps}
      dateValue={dateValue}
    >
      <BarChart
        dateValue={dateValue}
        getDataRequest={getCaseNumberRanking}
        getChartProps={getChartProps}
      />
    </ChartCard>
  );
}
