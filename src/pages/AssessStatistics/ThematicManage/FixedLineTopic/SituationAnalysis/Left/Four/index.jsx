import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getNewInNetProportion } from '../services';
import RingPieChart from '../../Components/Chart/RingPieChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.xValue),
      yData: [{ data: data?.map((ele) => ele?.yValue) }],
    };
  };

  return (
    <div>
      <ChartCard
        title="固话新入网涉案占比"
        chartType={'ringPie'}
        getDataRequest={getNewInNetProportion}
        getChartProps={getChartProps}
        dateValue={dateValue}
      >
        <RingPieChart
          dateValue={dateValue}
          getDataRequest={getNewInNetProportion}
          getChartProps={getChartProps}
        />
      </ChartCard>
    </div>
  );
}
