import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getNewInNetRanking } from '../services';
import BarChart from '../../Components/Chart/BarChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.xValue),
      yData: [{ data: data?.map((ele) => ele?.yValue) }],
    };
  };

  return (
    <ChartCard
      title="固话新入网涉案量地市排名"
      chartType={'bar'}
      getDataRequest={getNewInNetRanking}
      getChartProps={getChartProps}
      dateValue={dateValue}
    >
      <BarChart
        dateValue={dateValue}
        getDataRequest={getNewInNetRanking}
        getChartProps={getChartProps}
      />
    </ChartCard>
  );
}
