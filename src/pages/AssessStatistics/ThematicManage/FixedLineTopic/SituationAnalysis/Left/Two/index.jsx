import React, { useEffect, useState, useContext } from 'react';
import ChartCard from '../../Components/ChartCard';

import { getCaseBussinessType } from '../services';
import RingPieChart from '../../Components/Chart/RingPieChart';

export default function index({ dateValue }) {
  const getChartProps = (data) => {
    return {
      xData: data?.map((ele) => ele?.xValue),
      yData: [{ data: data?.map((ele) => ele?.yValue) }],
    };
  };

  return (
    <div>
      <ChartCard
        title="固化涉案业务类型占比"
        chartType={'ringPie'}
        getDataRequest={getCaseBussinessType}
        getChartProps={getChartProps}
        dateValue={dateValue}
      >
        <RingPieChart
          dateValue={dateValue}
          getDataRequest={getCaseBussinessType}
          getChartProps={getChartProps}
        />
      </ChartCard>
    </div>
  );
}
