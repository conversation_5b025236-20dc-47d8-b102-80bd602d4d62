import request from 'ponshine-request';

// 查询固话涉案量地市排名
export function getCaseNumberRanking(data) {
  return request(`/api/hn/fixTel/getFixInvolvedCityRank`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}

// 查询固话涉案业务类型占比
export function getCaseBussinessType(data) {
  return request(`/api/hn/fixTel/getFixInvolvedTypePercent`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
// 查询固话新入网涉案量地市排名
export function getNewInNetRanking(data) {
  return request(`/api/hn/fixTel/getFixInternetNewAccessInvolvedCityRank`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
// 查询固话新入网涉案占比
export function getNewInNetProportion(data) {
  return request(`/api/hn/fixTel/getFixInternetNewAccessInvolvedPercent`, {
    method: 'POST',
    data: data,
    requestType: 'json',
  });
}
