import React from 'react';

import { Tabs, Card } from 'antd';
import SituationAnalysis from './SituationAnalysis';
import { Licensee } from 'ponshine';
import NoAuth from '@/pages/NoAuth';
import FraudVerification from './FraudVerification';
import AnomalyWarning from './AnomalyWarning';

const { TabPane } = Tabs;

export default function index() {
  return (
    <Card bordered={false}>
      <Tabs defaultActiveKey="1">
        <TabPane tab="固话态势分析" key="1">
          <Licensee license="fixTelSituation" fallback={<NoAuth />}>
            <SituationAnalysis />
          </Licensee>
        </TabPane>
        <TabPane tab="固话涉诈核查" key="2">
          <Licensee license="fixTelCheck" fallback={<NoAuth />}>
            <FraudVerification />
          </Licensee>
        </TabPane>
        <TabPane tab="固话异常预警" key="3">
          <Licensee license="fixTelWarning" fallback={<NoAuth />}>
            <AnomalyWarning />
          </Licensee>
        </TabPane>
      </Tabs>
    </Card>
  );
}
