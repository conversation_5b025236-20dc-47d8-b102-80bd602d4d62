/*
 * @Author: zxw
 * @Date: 2023-08-10 10:33:56
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-11-20 15:42:16
 * @FilePath: \newHunanfanzha\src\pages\ExportPermission\Approve\index.jsx
 * @Description:
 */
import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  DatePicker,
  Modal,
  Upload,
  Icon,
} from 'antd';
import request from '@/utils/request';
import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { exportFile } from '@/utils/utils';
import moment from 'moment';
import ExportApprove from '@/components/ExportApprove';
import BatchImportModal from './BatchImportModal';
import Details from './Details';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import { getNumberAttributionList } from './server.js';
import { batchImportData } from './services.js';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import { set } from 'lodash';
const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;
  const localNetworkRef = useRef();
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);
  const [batchFile, setBatchFile] = useState([]); //批量上传
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [currentType, setCurrentType] = useState(''); //判断类型
  const [detailsInfo, setDetails] = useState({});
  const [importVisible, setImportVisible] = useState(false);
  const [batchState, setBatchState] = useState(false); //是否是批量上传状态标记
  const [batchQueryData, setBatchQueryData] = useState([]); //是否是批量上传状态标记
  const [confirmLoading, setConfirmLoading] = useState(false);
  // 区分是否是批量查询，批量查询时， 不可导出
  const [isBatchQuery, setIsBatchQuery] = useState(false);
  const [numberAttributionList, setNumberAttributionList] = useState([]); //开户地区
  const areaList = [
    '全部',
    '长沙',
    '岳阳',
    '株洲',
    '湘潭',
    '娄底',
    '郴州',
    '永州',
    '益阳',
    '怀化',
    '邵阳',
    '吉首',
    '衡阳',
    '张家界',
    '常德',
  ];
  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ currentPage: pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setIsBatchQuery(false);
      setBatchQueryData([]);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas();
    getNumberAttributionList((data) => {
      setNumberAttributionList(data);
    });
  }, []);

  const handleSearch = () => {
    const startFormatStr = 'YYYY-MM-DD';
    const endFormatStr = 'YYYY-MM-DD';

    form.validateFields(async (err, values) => {
      if (err) return;
      const { applyTime, internetAccessTime } = values;
      let params = {
        ...values,
        internetAccessTime: internetAccessTime?.format('YYYY-MM-DD'),
        notificationDateStart: applyTime?.[0]?.format(startFormatStr),
        notificationDateEnd: applyTime?.[1]?.format(endFormatStr),
      };
      delete params.applyTime;
      delete params.numType;
      getListDatas({
        ...params,
      });
    });
  };

  const handleTableChange = (pagination) => {
    if (isBatchQuery) {
      setListData({
        list: batchQueryData,
        pagination: pagination,
      });
    } else {
      getListDatas({
        ...serachParams,
        pageNum: pagination.current || pagination.pageNum,
        pageSize: pagination.pageSize,
      });
    }
  };

  const onReset = () => {
    form.resetFields();
    setIsBatchQuery(false);
    setBatchQueryData([]);
    getListDatas({ localNetwork: localNetworkRef.current.getInitialValue() });
    // getListDatas();
    setSelectedRows([]);
  };

  const [exportLoading, setExportLoading] = useState(false);

  let columns = [
    {
      title: '固话号码',
      width: 100,
      dataIndex: 'reportedTelNum',
      align: 'center',
      ellipsis: true,
    },
    // {
    //   title: '状态',
    //   width: 100,
    //   dataIndex: 'applicant',
    //   align: 'center',
    //   ellipsis: true,
    // },
    {
      title: '开户地区',
      width: 140,
      dataIndex: 'localNetwork',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '产权客户',
      width: 140,
      dataIndex: 'propertyCustomer',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '产权客户证件号码',
      width: 140,
      dataIndex: 'propertyCertificatesNumber',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '使用客户',
      width: 140,
      dataIndex: 'usingCustomer',
      align: 'center',
      ellipsis: true,
    },

    {
      title: '使用客户证件号码',
      width: 170,
      dataIndex: 'certificatesNumber',
      align: 'center',
      ellipsis: true,
    },
    // {
    //   title: '开户人姓名/公司名称',
    //   width: 150,
    //   dataIndex: '开户人姓名',
    //   align: 'center',
    //   ellipsis: true,
    // },
    // {
    //   title: '证件号码',
    //   width: 100,
    //   dataIndex: '证件号码',
    //   align: 'center',
    //   ellipsis: true,
    // },
    {
      title: '开户时间',
      width: 150,
      dataIndex: 'internetAccessTime',
      align: 'center',
      ellipsis: true,
      // render: (data) => {
      //   return moment(data, 'YYYYMMDDHHmmss').format('YYYY-MM-DD');
      // }
    },
    {
      title: '开户工号',
      width: 100,
      dataIndex: 'acceptanceWorkId',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '开户渠道',
      width: 100,
      dataIndex: 'channelType',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '业务类型',
      width: 100,
      dataIndex: 'businessType',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '装机地址',
      width: 150,
      dataIndex: 'userInstallAddress',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '装机类型',
      width: 100,
      dataIndex: 'userInstallType',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '高危地区',
      width: 100,
      dataIndex: 'highRiskArea',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '涉案时间',
      width: 150,
      dataIndex: 'notificationDate',
      align: 'center',
      ellipsis: true,
    },
    // {
    //   title: '是否调证',
    //   width: 100,
    //   dataIndex: '是否调证',
    //   align: 'center',
    //   ellipsis: true,
    // },
    {
      title: '是否12321举报',
      width: 120,
      dataIndex: 'ifReported',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '是否新入网',
      width: 100,
      dataIndex: 'ifNewNetwork',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '是否复机',
      width: 120,
      dataIndex: 'ifResume',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '是否复机后涉案',
      width: 150,
      dataIndex: 'ifResumeInvolved',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '是否白名单',
      width: 120,
      dataIndex: 'ifWhite',
      align: 'center',
      ellipsis: true,
      render: (text, record) => {
        return text == '是' ? (
          <a
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              handleDetail(record, '白名单详情');
            }}
            style={{ cursor: text == '是' ? 'pointer' : 'default' }}
          >
            是
          </a>
        ) : (
          <span>{text}</span>
        );
      },
    },
    {
      title: '是否黑名单',
      width: 120,
      dataIndex: 'ifBlack',
      align: 'center',
      ellipsis: true,
      render: (text, record) => {
        return text == '是' ? (
          <a
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              handleDetail(record, '黑名单详情');
            }}
            style={{ cursor: text == '是' ? 'pointer' : 'default' }}
          >
            是
          </a>
        ) : (
          <span>{text}</span>
        );
      },
    },
    {
      title: '是否复盘',
      width: 100,
      dataIndex: 'ifReplay',
      align: 'center',
      ellipsis: true,
      render: (text, record) => {
        return (
          <a
            style={{ color: text == '是' ? '#1890ff' : 'red', cursor: 'pointer' }}
            onClick={(e) => {
              e.stopPropagation();
              handleDetail(record, '复盘详情');
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '入网详情',
      width: 120,
      dataIndex: 'opt',
      ellipsis: true,
      // fixed: 'right',
      render: (text, record) => {
        return (
          <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              handleDetail(record, '入网详情');
            }}
          >
            查看
          </Button>
        );
      },
    },
  ];

  const handleDetail = (r, type) => {
    setDetails(r);
    setDetailsVisible(true);
    setCurrentType(type);
  };
  const hideDetails = () => {
    setDetailsVisible(false);
    setDetails({});
    setCurrentType('');
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };
  // 批量查询
  const handlebathSearch = () => {
    setImportVisible(true);
  };
  const handleImport = async (fileData) => {
    const response = await batchImportData(fileData);
    if (response.code === 200) {
      message.success(response.message);
      setIsBatchQuery(true);
      setBatchQueryData(response?.data || []);
      setListData({
        list: response?.data || [],
        pagination: {
          total: response?.data?.length,
          current: 1,
          pageSize: 10,
        },
      });
      setImportVisible(false);
    } else {
      message.error(response.message);
    }
  };

  const hideImport = () => {
    setImportVisible(false);
  };
  const handleTempDown = () => {
    let url = '/api/template/getTemplate',
      filename = '固话核查查询模板.xlsx',
      data = { templateCode: 'fixTelCheckBatchQuery' };

    return new Promise((resolve, reject) => {
      request
        .get(url, {
          responseType: 'blob',
          useCSRFToken: false,
          getResponse: true,
          params: data,
        })
        .then(({ data, response = {} }) => {
          const name = filename;
          if (data) {
            if ('download' in document.createElement('a')) {
              // 非IE下载
              const elink = document.createElement('a');
              elink.download = name;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(data);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(data, name);
            }
          } else {
            reject();
            return;
          }
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  };
  // 批量查询提交
  const handleBatchSearchSubmit = () => {
    if (!batchFile?.length) {
      return message.error('请选择上传文件');
    }
    const params = new FormData();
    params.append('file', batchFile[0]);
    request('/api/hn/fixTelCheck/batchQueryFixTelFraudCheck', {
      method: 'POST',
      data: params,
    }).then((res) => {
      if (res.code == 200) {
        res.message !== '操作成功' && message.info(res.message);
        if (res?.data?.hasFail) {
          setBatchFile([]);
          setBatchState(true);
          setImportVisible(false);
          setListData({
            list: res.data?.list || [],
            pagination: {
              total: res.data?.list?.length || 0,
              current: 1,
              pageSize: 10,
            },
          });
          // setState({
          //   batchFile: [],
          //   batchState: true,
          //   batchVisible: false,
          //   dataSource: res.data?.list || [],
          //   total: res.data?.list?.length || 0,

          // });
          Modal.confirm({
            title: '导入文件存在错误，是否下载错误文件？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              let url = '/api/userInfo/queryBatchUserInformationError',
                filename = '固话涉诈核查错误数据导出.xlsx';

              return new Promise((resolve, reject) => {
                request
                  .post(url, {
                    responseType: 'blob',
                    useCSRFToken: false,
                    getResponse: true,
                    // params: data,
                  })
                  .then(({ data, response = {} }) => {
                    let fileName =
                      response?.headers?.get('content-disposition')?.split('filename=')[1] || '';
                    const name = fileName || filename;
                    if (data) {
                      if ('download' in document.createElement('a')) {
                        // 非IE下载
                        const elink = document.createElement('a');
                        elink.download = name;
                        elink.style.display = 'none';
                        elink.href = URL.createObjectURL(data);
                        document.body.appendChild(elink);
                        elink.click();
                        URL.revokeObjectURL(elink.href); // 释放URL 对象
                        document.body.removeChild(elink);
                      } else {
                        // IE10+下载
                        navigator.msSaveBlob(data, name);
                      }
                    } else {
                      reject();
                      return;
                    }
                    resolve();
                  })
                  .catch((err) => {
                    reject(err);
                  });
              });
            },
          });
        } else {
          setBatchFile([]);
          setImportVisible(false), setBatchState(true);
          setListData({
            list: res.data?.list || [],
            pagination: {
              total: res.data?.list?.length || 0,
              current: 1,
              pageSize: 10,
            },
          });
          // setState({
          //   batchFile: [],
          //   batchState: true,
          //   batchVisible: false,
          //   dataSource: res.data?.list || [],
          //   total: res.data?.list?.length || 0,
          // });
        }
      } else {
        message.error(res.message);
        setListData({
          list: [],
          pagination: {
            total: 0,
            current: 1,
            pageSize: 10,
          },
        });
      }
    });
  };
  let listDataMock = {
    list: [
      {
        key: 1,
        id: 1,
        phoneNum: 22,
        reportedTelNum: '733',
        localNetwork: '733',
        ifBlack: '否',
        ifWhite: '否',
        ifReplay: '否',
      },
      {
        key: 11,
        id: 11,
        phoneNum: 22,
        reportedTelNum: '733',
        localNetwork: '733',
        ifBlack: '是',
        ifWhite: '是',
        ifReplay: '是',
        certificatesNumberEncrypt: '123456789012345678',
        notificationDateStart: '2024-11-01',
        notificationDateEnd: '2024-11-30',
      },
    ],
  };
  let typesExportURL = {
    入网详情: '/api/userInfo/exportUserInformation',
    白名单详情: '/api/white/exportWhiteWorkOrderManage',
    黑名单详情: '/api/hn/blackAndGrayList/exportOperateRecord ',
    复盘详情: '/api/hn/fixTelReplay/exportFixTelReplay',
  };
  let formValues = form.getFieldsValue();
  const { applyTime, internetAccessTime } = formValues;
  const startFormatStr = 'YYYY-MM-DD';
  const endFormatStr = 'YYYY-MM-DD';
  let currentNumberAttribution =
    numberAttributionList.find((item) => item.name == detailsInfo.localNetwork) || [];
  let paramsEmu = {
    入网详情: {
      phoneNum: detailsInfo?.reportedTelNum,
      localNetwork: detailsInfo?.localNetwork,
      // pageNum:pageNum
    },
    白名单详情: {
      phoneNum: detailsInfo?.reportedTelNum,
      numberAttribution: currentNumberAttribution ? currentNumberAttribution.value : '',
    },
    黑名单详情: {
      idCardNum: detailsInfo?.certificatesNumberEncrypt,
    },
    复盘详情: {
      // pageNum:pageNum,
      notificationDateStart: detailsInfo?.notificationDateStart
        ? moment(detailsInfo?.notificationDateStart).format('YYYYMMDD')
        : '',
      notificationDateEnd: detailsInfo?.notificationDateEnd
        ? moment(detailsInfo?.notificationDateEnd).format('YYYYMMDD')
        : '',
      reportedPhoneNum: detailsInfo?.reportedTelNum,
    },
  };

  let exportInfo = {
    // ...formValues,
    ...paramsEmu[currentType],
    // internetAccessTime: internetAccessTime?.format('YYYY-MM-DD'),
    // notificationDateStart: applyTime?.[0]?.format(startFormatStr),
    // notificationDateEnd: applyTime?.[1]?.format(endFormatStr),
    // // phoneNum:
  };
  if (currentType == '黑名单详情') {
    exportInfo.pageSize = '200';
    exportInfo.currentPage = 1;
  } else if (currentType == '复盘详情') {
    exportInfo.pageNum = 1;
    exportInfo.pageSize = 10;
    // exportInfo.notificationDateStart = applyTime?.[0]?.format('YYYYMMDD');
    // exportInfo.notificationDateEnd = applyTime?.[1]?.format('YYYYMMDD');
    // delete exportInfo.notificationDateStart
    // delete exportInfo.notificationDateEnd
  } else if (currentType == '入网详情') {
    exportInfo.pageNum = 1;
    exportInfo.pageSize = 9999;
  }
  delete exportInfo.internetAccessTime;
  delete exportInfo.applyTime;
  delete exportInfo.numType;

  return (
    <Fragment>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <ShrinkSearchForm
            colSpan={8}
            formList={[
              <Col style={{ display: 'flex', justifyContent: 'space-around' }}>
                <Form.Item
                  label="查询条件"
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  style={{ width: '55%' }}
                >
                  {form.getFieldDecorator('numType', {
                    rules: [
                      // { required: true, message: '请选择查询条件' },
                    ],
                    initialValue: '号码',
                  })(
                    <Select
                      getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      // style={{ width: 60, marginLeft: '-3px' }}
                    >
                      <Select.Option value="号码">号码</Select.Option>
                      {/* <Select.Option value="证件">
                    证件
                  </Select.Option>
                  <Select.Option
                    value="开户工号"
                  >
                    开户工号
                  </Select.Option> */}
                    </Select>,
                  )}
                </Form.Item>

                <Form.Item
                  style={{ display: 'inline-block', marginLeft: 5, flex: 1, flexShrink: 1 }}
                  wrapperCol={{ span: 24 }}
                >
                  {form.getFieldDecorator('reportedTelNum', {
                    // rules: [{ required: true, message: '请选择查询条件内容' },],
                    initialValue: '',
                  })(
                    <Input
                      placeholder="请输入内容"
                      style={{ width: '100%' }}
                      allowClear
                      getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                    />,
                  )}
                </Form.Item>
              </Col>,
              <LocalNetworkFormItem
                form={form}
                getListDatas={getListDatas}
                cref={localNetworkRef}
                formLable="开户地区"
              />,
              // <Form.Item label="开户地区">
              //   {getFieldDecorator('localNetwork', {
              //      initialValue: undefined
              //   })(
              //     // <Select allowClear
              //     //   placeholder="请选择"
              //     // >
              //     //   {
              //     //     areaList.map((v, i) => {
              //     //       return <Select.Option key={v} value={v}>{v}</Select.Option>
              //     //     })
              //     //   }
              //     // </Select>
              //     <Select placeholder="请选择" allowClear>
              //       {numberAttributionList?.map((ele, index) => (
              //         <Select.Option value={ele.value} key={index}>
              //           {ele.name}
              //         </Select.Option>
              //       ))}
              //     </Select>,
              //   )}
              // </Form.Item>,
              <Form.Item label="涉案时间">
                {getFieldDecorator('applyTime')(
                  <RangePicker
                    allowClear={true}
                    placeholder={['开始时间', '结束时间']}
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD"
                    ranges={{
                      今天: [moment(), moment()],
                    }}
                    disabledDate={(current) => {
                      return current && current > moment().endOf('day');
                    }}
                  />,
                )}
              </Form.Item>,
              <Form.Item label="开户时间">
                {getFieldDecorator('internetAccessTime')(
                  <DatePicker
                    allowClear={true}
                    placeholder={'请选择'}
                    format="YYYY-MM-DD"
                    showToday
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>,
              <Form.Item label="是否白名单" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('ifWhite', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="是否黑名单">
                {getFieldDecorator('ifBlack', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,

              <Form.Item label="是否复机">
                {getFieldDecorator('ifResume', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="是否复机后涉案">
                {getFieldDecorator('ifResumeInvolved', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="12321举报" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('ifReported', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="是否复盘" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('ifReplay', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="是否新入网">
                {getFieldDecorator('ifNewNetwork', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="业务类型">
                {getFieldDecorator('businessType', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['个人固话', '政企固话', '语音中继（DID固话）'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="装机类型">
                {getFieldDecorator('userInstallType', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['个人',  '酒店', '医院','政企', '其他'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
              <Form.Item label="是否高危地区">
                {getFieldDecorator('highRiskArea', {
                  initialValue: undefined,
                })(
                  <Select allowClear placeholder="请选择">
                    {['是', '否'].map((v, i) => {
                      return (
                        <Select.Option key={v} value={v}>
                          {v}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>,
            ]}
            optButton={
              <Col>
                <Button type="primary" style={{ marginRight: 5 }} onClick={handleSearch}>
                  查询
                </Button>
                <Button type="primary" style={{ marginRight: 5 }} onClick={handlebathSearch}>
                  批量查询
                </Button>
                <Button type="" style={{ marginRight: 5 }} onClick={onReset}>
                  重置
                </Button>
                <ExportApprove
                  buttonStyle={{ marginRight: 5 }}
                  exportParams={{
                    urlAPi: '/api/hn/fixTelCheck/exportFixTelFraudCheck',
                    decode: true,
                    params: {
                      ...exportInfo,
                      pageNum: listData?.pagination?.pageNum || 1,
                      pageSize: listData?.pagination?.pageSize || 10,
                    },
                    method: 'POST',
                  }}
                  moduleTile="固话涉诈核查"
                  buttonText="导出"
                  disabledExport={isBatchQuery}
                />
                {/* <Button type="" loading={exportLoading} onClick={handleExport} disabled={!listData?.list?.length}>
                导出
              </Button> */}
              </Col>
            }
          ></ShrinkSearchForm>
        </Form>
      </Row>
      <StandardTable
        detailColumns={columns}
        columns={columns}
        loading={loading}
        data={listData}
        rowKey="id"
        onChange={handleTableChange}
        // multiple={true}
        // selectedRows={selectedRows}
        // onSelectRow={handleSelectRows}
        // scroll={{ x: true }}
        tools={true}
        isNoneRefresh={true}
        isNoMenu={true}
        rowSelectionProps={false}
        showSelectCount={false}
      />
      {/* 批量导入 */}
      {importVisible && (
        <BatchImportModal
          title="批量查询"
          tipsText="*每个文件不能超过20个号码"
          visible={importVisible}
          onClose={() => {
            setImportVisible(false);
          }}
          downTemplateUrl={`/api/template/getTemplate?templateCode=fixTelCheckBatchQuery`}
          closeModal={() => {
            setImportVisible(false);
          }}
          onImport={handleImport}
        />
      )}

      {/* <Modal
          title="批量查询导入"
          okText="查询"
          cancelText="取消"
          maskClosable={false}
          destroyOnClose
          visible={importVisible}
          onCancel={() => {
            setBatchFile([])
            setImportVisible(false);
          }}
          onOk={() => {
            handleBatchSearchSubmit();
          }}
        >
          <Form>
            <Form.Item label="文件导入" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Upload
                accept=".xls,.xlsx"
                beforeUpload={(file) => {
                  setBatchFile([file]);
                  return false;
                }}
                onRemove={(file) => {
                  setBatchFile([])
                  return false;
                }}
                fileList={batchFile}
              >
                <Button>
                  <Icon type="vertical-align-top" />
                  选择文件
                </Button>
              </Upload>
              {!batchFile?.length && <span>未选择文件</span>}
              <div>注：每个文件不能超过20个号码。</div>
              <div>
                <Button
                  type="link"
                  onClick={() => {
                    handleTempDown();
                  }}
                  style={{ paddingLeft: 0 }}
                >
                  模板下载
                </Button>
              </div>
            </Form.Item>
          </Form>
        </Modal> */}
      {detailsVisible && (
        <Modal
          footer={null}
          title={
            <>
              <Row>
                <Col span={4}>{currentType}</Col>
                <Col span={19} align="right">
                  <ExportApprove
                    buttonStyle={{ marginRight: 10 }}
                    exportParams={{
                      urlAPi: typesExportURL[currentType],
                      decode: true,
                      method: 'POST',
                      mime: 'xlsx',
                      isDate: true,
                      currentDateFormate: 'YYYYMMDD',
                      // title: currentType,
                      isShowDate: currentType !== '黑名单详情',
                      title:
                        currentType == '黑名单详情'
                          ? `${detailsInfo.reportedTelNum}_${detailsInfo.localNetwork}_${detailsInfo.notificationDate}`
                          : currentType,
                      params: exportInfo,
                    }}
                    moduleTile={currentType}
                    buttonText="导出"
                  />
                </Col>
              </Row>
            </>
          }
          destroyOnClose
          visible={detailsVisible}
          onCancel={hideDetails}
          width={'90%'}
          bodyStyle={{ minHeight: 600 }}
          maskClosable={false}
        >
          <Details
            detailsInfo={detailsInfo}
            currentType={currentType}
            numberAttributionList={numberAttributionList}
          />
        </Modal>
      )}
    </Fragment>
  );
};
export default Form.create({})(Index);
