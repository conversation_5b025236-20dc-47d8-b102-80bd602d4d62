/*
 * @Author: ss <EMAIL>
 * @Date: 2024-09-18 14:00:29
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-10-25 14:08:46
 * @FilePath: /hunanfanzha/src/pages/AssessStatistics/ThematicManage/FixedLineTopic/FraudVerification/services.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/fixTelCheck/getFixTelFraudCheck`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 审批
export async function approve(params) {
  return request(`/api/hn/hnFraudExportAuth/approveHnFraudExportAuthApprove`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
// 获取号码归属
export function getNumberAttribution(params) {
  return request('/api/hn/systemConfig/getOrganizationByUser', {
    method: 'GET',
    params,
    requestType: 'json',
  });
}
export function batchImportData(data) {
  return request(`/api/hn/fixTelCheck/batchQueryFixTelFraudCheck`, {
    method: 'POST',
    data: data,
    requestType: 'form',
  });
}