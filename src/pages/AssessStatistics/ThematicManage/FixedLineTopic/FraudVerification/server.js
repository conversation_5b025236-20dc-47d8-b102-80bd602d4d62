/*
 * @Author: ss <EMAIL>
 * @Date: 2024-10-25 14:17:38
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-10-25 14:23:52
 * @FilePath: /hunanfanzha/src/pages/AssessStatistics/ThematicManage/FixedLineTopic/FraudVerification/server.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {getNumberAttribution} from './services'
import { message } from 'antd';
export const getNumberAttributionList = (callback) => {
    getNumberAttribution()
      .then((res) => {
        if (res.code === 200) {
          callback(res.data);
        } else {
          callback([]);
          message.error(res.message);
        }
      })
      .catch((err) => {});
  };