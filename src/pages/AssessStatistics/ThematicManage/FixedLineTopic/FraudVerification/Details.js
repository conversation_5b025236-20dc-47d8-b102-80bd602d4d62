/*
 * @Author: ss <EMAIL>
 * @Date: 2024-09-18 15:53:21
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-11-18 10:34:30
 * @FilePath: /hunanfanzha/src/pages/AssessStatistics/ThematicManage/FixedLineTopic/FraudVerification/Details.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Button, Card, Col, Row, message } from 'antd';
import React, { Component, useState, useEffect } from 'react';
import request from '@/utils/request';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import ExportApprove from '@/components/ExportApprove';
import { renderToolTip, getReloadTableWithFilterPageNum } from '@/utils/utils';
import FileContent from '@/components/FileContent';
import moment from 'moment';

const listType = [
    { label: '黑名单', value: 2 },
    { label: '灰名单', value: 3 },
  ];
const Details = (props) => {
    const { detailsInfo, currentType,numberAttributionList } = props;
    const [exportLoading, setExportLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [listData, setListData] = useState({
        list: [],
        pagination: {
            total: 0,
            pageNum: 1,
            pageSize: 10,
        },
    });
    let typesGetURL = {
        '入网详情': '/api/userInfo/getUserInformation',
        '白名单详情': '/api/white/getWhiteWorkOrderManage',
        '黑名单详情': '/api/hn/blackAndGrayList/pageBlackRecord',
        '复盘详情': '/api/hn/fixTelReplay/pageFixTelReplay',
    }
    let typesExportURL = {
        '入网详情': '/api/userInfo/exportUserInformation',
        '白名单详情': '/api/white/exportWhiteWorkOrderManage',
        '黑名单详情': '/api/hn/blackAndGrayList/exportOperateRecord ',
        '复盘详情': '/api/hn/fixTelReplay/exportFixTelReplay',
    }
    useEffect(() => {
        getListDatas()

    }, []);
  
    const getListDatas = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
        console.log(detailsInfo, "detailsInfo--", numberAttributionList)
        let currentNumberAttribution=numberAttributionList.find(item=>item.name==detailsInfo.localNetwork) || []
        let paramsEmu = {
            '入网详情': {
                phoneNum: detailsInfo?.reportedTelNum,
                localNetwork: detailsInfo?.localNetwork,
                pageNum:pageNum
            },
            '白名单详情': {
                phoneNum: detailsInfo?.reportedTelNum,
                numberAttribution: currentNumberAttribution ?currentNumberAttribution.value:''
            },
            '黑名单详情': {
                idCardNum: detailsInfo?.certificatesNumberEncrypt,
            },
            "复盘详情": {
                pageNum:pageNum,
                notificationDateStart: detailsInfo?.notificationDateStart?moment(detailsInfo?.notificationDateStart).format('YYYYMMDD'):'',
                notificationDateEnd: detailsInfo?.notificationDateEnd?moment(detailsInfo?.notificationDateEnd).format('YYYYMMDD'):'',
                reportedPhoneNum: detailsInfo?.reportedTelNum,
            }
        }
        let params = {
            currentPage: pageNum,
            pageSize,
            ...paramsEmu[currentType],
            ...props,
        }
        setLoading(true);
        request(typesGetURL[currentType], {
            method: 'POST',
            data: {
                ...params
            },
            requestType: 'json',
        }).then((response) => {
            if (response) {
                setLoading(false)
            }
            if (response.code === 200) {
                setListData({
                    list: response.data.items,
                    pagination: {
                        total: response.data.totalNum,
                        current: pageNum,
                        pageSize,
                    },
                });
            } else {
                message.error(response.message);
            }
        });

    };

    const handleTableChange = (pagination) => {
        getListDatas({
            pageNum: pagination.current || pagination.pageNum,
            pageSize: pagination.pageSize,
        });
    };

    const handleExport = () => {
        setExportLoading(true)
        exportFile({
            urlAPi: '/api/hn/userAccess/exportUserAccess',
            params: { ...searchParams },
            method: 'POST',
            decode: true,
            callback: () => {
                setExportLoading(false);
            },
        });
    }
    // 白名单详情
    let columnsWhite1 = [
        {
            title: '固话号码',
            width: 100,
            dataIndex: 'phoneNum',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '所属地区',
            width: 100,
            dataIndex: 'numberAttribution',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '入网时间',
            width: 150,
            dataIndex: 'accessTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '客户名称',
            width: 100,
            dataIndex: 'customerName',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '证件号',
            width: 150,
            dataIndex: 'documentsNumber',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '类型',
            width: 100,
            dataIndex: 'changeType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '子类型',
            width: 100,
            dataIndex: '子类型',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '创建人',
            width: 100,
            dataIndex: '创建人',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '创建时间',
            width: 100,
            dataIndex: '创建时间',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '审批人',
            width: 100,
            dataIndex: '审批人',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '审批意见',
            width: 100,
            dataIndex: '审批意见',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '变更时间',
            width: 100,
            dataIndex: '变更时间',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '到期时间',
            width: 100,
            dataIndex: '到期时间',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '添加原因',
            width: 150,
            dataIndex: '添加原因',
            align: 'center',
            ellipsis: true,
        },

    ]
    const columnsWhite = [
        {
            title: '号码',
            dataIndex: 'phoneNum',
            align: 'center',
            width: 200,
            ellipsis: true,
        },
        {
            title: '号码归属',
            dataIndex: 'numberAttribution',
            align: 'center',

            width: 150,
            ellipsis: true,
        },

        {
            title: '状态',
            dataIndex: 'state',
            align: 'center',

            // render: (text) => statusList?.find((ele) => ele.value === text)?.label || '--',
            width: 150,
            ellipsis: true,
        },
        {
            title: '变更类型',
            dataIndex: 'changeType',
            align: 'center',

            // render: (text) => changeTypeList?.find((ele) => ele.value === text)?.label || '--',
            width: 150,
            ellipsis: true,
        },
        {
            title: '到期时间',
            dataIndex: 'expireDate',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '客户标签',
            dataIndex: 'customerTag',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '客户子标签',
            dataIndex: 'customerSubTab',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '关联模型',
            dataIndex: 'associationModel',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '原因',
            dataIndex: 'reason',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '客户名称',
            dataIndex: 'customerName',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '证件号',
            dataIndex: 'documentsNumber',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '入网时间',
            dataIndex: 'accessTime',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '产权客户',
            dataIndex: 'propertyCustomer',
            align: 'center',
            width: 120,
            ellipsis: true,
        },
        {
            title: '产权证件号码',
            dataIndex: 'propertyCertificatesNumber',
            align: 'center',
            width: 150,
            ellipsis: true,
        },
        {
            title: '申请人',
            dataIndex: 'applicant',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '审批人',
            dataIndex: 'approver',
            align: 'center',

            width: 150,
            ellipsis: true,
        },

        {
            title: '申请时间',
            dataIndex: 'gmtCreate',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '审批时间',
            dataIndex: 'approvalTime',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '审批意见',
            dataIndex: 'approvalOpinions',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '省公司审批人',
            dataIndex: 'provincialApprover',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '省公司审批时间',
            dataIndex: 'provincialApprovalTime',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '省公司审批意见',
            dataIndex: 'provincialApprovalOpinions',
            align: 'center',

            width: 150,
            ellipsis: true,
        },
        {
            title: '工单号',
            dataIndex: 'workOrderId',
            align: 'center',

            width: 200,
            ellipsis: true,
        },
    ];
    // 黑名单详情
    let columnsBlack = [
        {
            title: '固话号码',
            width: 100,
            dataIndex: 'reportedTelNum',
            align: 'center',
            ellipsis: true,
            render: () => {
                return detailsInfo.reportedTelNum
            }
        },
        {
            title: '所属地区',
            width: 140,
            dataIndex: 'localNetwork',
            align: 'center',
            ellipsis: true,
            render: () => {
                return detailsInfo?.localNetwork
            }
        },
        {
            title: '入网时间',
            width: 150,
            dataIndex: 'internetAccessTime',
            align: 'center',
            ellipsis: true,
            render: () => {
                return detailsInfo?.internetAccessTime
            }
            // render: (data) => {
            //   return moment(data, 'YYYYMMDDHHmmss').format('YYYY-MM-DD');
            // }
        },
        {
            title: '证件号',
            dataIndex: 'idCardNum',
            width: 170,
            ellipsis: true,
        },
        {
            title: '状态',
            dataIndex: 'approvalStatus',

            width: 80,
            ellipsis: true,
        },
        {
            title: '客户名称',
            dataIndex: 'userName',

            width: 100,
            ellipsis: true,
        },
        {
            title: '变更类型',
            dataIndex: 'operateType',

            width: 100,
            ellipsis: true,
        },
        {
            title: '到期时间',
            dataIndex: 'expireTime',

            width: 160,
            ellipsis: true,
        },
        {
            title: '名单类型',
            dataIndex: 'listType',

            width: 100,
            ellipsis: true,
            render: (v) => {
                const str = listType?.find((ele) => ele.value == v)?.label;
                return renderToolTip(str);
            },
        },

        {
            title: '客户标签',
            dataIndex: 'userTag',

            width: 100,
            ellipsis: true,
        },
        {
            title: '客户子标签',
            dataIndex: 'userSubTag',

            width: 130,
            ellipsis: true,
        },

        {
            title: '原因',
            dataIndex: 'blackReason',

            width: 100,
            ellipsis: true,
        },
        {
            title: '附件',
            dataIndex: 'attachments',

            width: 70,
            render: (v, r) => (
                <FileContent data={r.fileList} downLoadApi="/api/hn/blackAndGrayList/downDeleteBlackFile" />
            ),
        },

        {
            title: '录入人',
            dataIndex: 'creator',

            width: 100,
            ellipsis: true,
        },

        {
            title: '操作时间',
            dataIndex: 'gmtCreate',

            width: 160,
            ellipsis: true,
        },

        {
            title: '工单号',
            dataIndex: 'recordNum',

            width: 150,
            ellipsis: true,
        },
    ];
    // 复盘详情
    let columnsReplay = [
        {
            title: '被举报号码',
            width: 120,
            dataIndex: 'reportedPhoneNum',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '被举报号码归属地',
            width: 150,
            dataIndex: 'reportedLocation',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '创建时间',
            width: 100,
            dataIndex: 'gmtCreate',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '修改时间',
            width: 100,
            dataIndex: 'gmtUpdate',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '通报时间',
            width: 100,
            dataIndex: 'notificationDate',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '分类',
            width: 100,
            dataIndex: 'classification',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '提供单位',
            width: 100,
            dataIndex: 'provider',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '发案开始时间',
            width: 120,
            dataIndex: 'incidentStartTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '发案结束时间',
            width: 120,
            dataIndex: 'incidentEndTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '受害人电话',
            width: 120,
            dataIndex: 'victimPhoneNum',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '举报号码运营商',
            width: 140,
            dataIndex: 'reportIsp',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '举报号码归属省',
            width: 140,
            dataIndex: 'reportProvince',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '举报号码归属市',
            width: 140,
            dataIndex: 'reportCity',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '举报时间',
            width: 100,
            dataIndex: 'reportTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '来电时间',
            width: 100,
            dataIndex: 'callTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '举报内容',
            width: 140,
            dataIndex: 'reportContent',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '关停时间',
            width: 100,
            dataIndex: 'shutdownTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '备注',
            width: 100,
            dataIndex: 'remark',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否处置成功',
            width: 120,
            dataIndex: 'ifDisposeSuccess',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否外呼确认',
            width: 120,
            dataIndex: 'ifOutboundCheck',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '绑定关系',
            width: 120,
            dataIndex: 'bindingRela',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '业务类型',
            width: 100,
            dataIndex: 'businessType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '省内处置前状态',
            width: 140,
            dataIndex: 'statusBeforeProDispose',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否黑名单二次入网',
            width: 160,
            dataIndex: 'ifBlackSecondNetwork',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '名下固话数量',
            width: 120,
            dataIndex: 'telCount',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '名下固话安装地址数量',
            width: 160,
            dataIndex: 'telAddressCount',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '涉案固话安装地是否存在其它户名固话',
            width: 280,
            dataIndex: 'ifOtherTelWithSameAddress',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '安装地址是否为宾馆或酒店',
            width: 230,
            dataIndex: 'ifHotel',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '涉案场景描述',
            width: 120,
            dataIndex: 'involvedSceneDesc',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否为首次入网新用户',
            width: 180,
            dataIndex: '是否为首次入网新用户',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否新入网',
            width: 100,
            dataIndex: 'ifFirstNetwork',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '省内模型是否关停',
            width: 140,
            dataIndex: 'ifShutdownProvinceModel',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '省内关停时间差',
            width: 140,
            dataIndex: 'provinceShutdownInterval',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '省内模型分类',
            width: 120,
            dataIndex: 'provinceModelType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '省内模型关停次数',
            width: 140,
            dataIndex: 'provinceModelShutdownTimes',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '集团模型是否关停',
            width: 140,
            dataIndex: 'ifShutdownGroupModel',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '集团关停时间差',
            width: 120,
            dataIndex: 'groupShutdownInterval',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '集团模型分类',
            width: 120,
            dataIndex: 'groupModelType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '集团模型关停次数',
            width: 140,
            dataIndex: 'groupModelShutdownTimes',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否多次复机',
            width: 140,
            dataIndex: 'ifResumeFrequently',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '复机次数',
            width: 100,
            dataIndex: 'resumeTimes',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '涉案前是否被模型关停',
            width: 160,
            dataIndex: 'ifShutdownByModelBefore',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '复机方式',
            width: 100,
            dataIndex: 'resumeWay',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '异常备注',
            width: 100,
            dataIndex: 'exceptionRemark',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否加黑',
            width: 100,
            dataIndex: 'ifAddBlack',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '加黑到期时间',
            width: 140,
            dataIndex: 'blackExpireTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '加黑原因',
            width: 100,
            dataIndex: 'blackReason',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否已经复盘',
            width: 100,
            dataIndex: 'ifReplay',
            align: 'center',
            ellipsis: true,
        },
    ]
    // 入网详情
    let columnsNetwork = [
        {
            title: '固话号码',
            width: 100,
            dataIndex: 'phoneNum',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '本地网',
            width: 100,
            dataIndex: 'localNetwork',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '营业区',
            width: 100,
            dataIndex: 'businessArea',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '入网时间',
            width: 150,
            dataIndex: 'internetAccessTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '渠道类型',
            width: 100,
            dataIndex: 'channelType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '受理网点',
            width: 100,
            dataIndex: 'acceptancePoint',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '号码状态',
            width: 100,
            dataIndex: 'phoneNumState',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '销售品',
            width: 100,
            dataIndex: 'saleCategory',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '销售价格',
            width: 100,
            dataIndex: 'salePrice',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否校园',
            width: 100,
            dataIndex: 'whetherCampus',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否开通国漫',
            width: 100,
            dataIndex: 'whetherOpenChineseAnime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否一号双终端',
            width: 150,
            dataIndex: 'whetherOneDualTerminal',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '是否主副卡',
            width: 100,
            dataIndex: 'masterCard',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '使用客户',
            width: 100,
            dataIndex: 'usingCustomer',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '使用客户证件号码',
            width: 140,
            dataIndex: 'certificatesNumber',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '使用人证件号码',
            width: 180,
            dataIndex: '开户市2',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '使用人证件类型',
            width: 180,
            dataIndex: 'certificatesType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '使用客户地址',
            width: 180,
            dataIndex: 'usingCustomerAddress',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '产权客户',
            width: 100,
            dataIndex: 'propertyCustomer',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '产权证件号码',
            width: 150,
            dataIndex: 'propertyCertificatesNumber',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '产权证件类型',
            width: 150,
            dataIndex: 'propertyCertificatesType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '产权客户地址',
            width: 150,
            dataIndex: 'propertyCustomerAddress',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '客户类型',
            width: 100,
            dataIndex: 'customerType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '产品类型',
            width: 100,
            dataIndex: 'productType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '号码标识',
            width: 100,
            dataIndex: 'phoneNumTag',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '用户装机地址',
            width: 150,
            dataIndex: 'userInstallAddress',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '复机单号',
            width: 100,
            dataIndex: 'resumeNumbers',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '复机时间',
            width: 100,
            dataIndex: 'resumeTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '复机工号',
            width: 150,
            dataIndex: 'resumeWorkId',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '复机人',
            width: 150,
            dataIndex: 'resumeOperator',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '受理员工',
            width: 100,
            dataIndex: 'acceptStaff',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '受理工号',
            width: 150,
            dataIndex: 'acceptanceWorkId',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '揽机人工号',
            width: 150,
            dataIndex: 'grabMachineWorkId',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '揽机人名称',
            width: 150,
            dataIndex: 'grabMachineName',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '揽机人组织',
            width: 150,
            dataIndex: 'grabMachineOrganize',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '揽机人组织渠道类型',
            width: 150,
            dataIndex: 'grabMachineOrganizeChannelType',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '甩单时间',
            width: 150,
            dataIndex: 'throwOrderTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '甩单工号',
            width: 150,
            dataIndex: 'throwOrderWorkId',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '甩单人',
            width: 100,
            dataIndex: 'throwOrderOperator',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '输单时间',
            width: 100,
            dataIndex: 'inputOrderTime',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '输单工号',
            width: 100,
            dataIndex: 'inputOrderWorkId',
            align: 'center',
            ellipsis: true,
        },
        {
            title: '输单人',
            width: 100,
            dataIndex: 'inputOrderOperator',
            align: 'center',
            ellipsis: true,
        },
    ]
    let listDataMock = {
        list: [
            {
                key: 1,
                id: 1,
                phone: 22
            }
        ]
    }
    const types = {
        '入网详情': columnsNetwork,
        '白名单详情': columnsWhite,
        '黑名单详情': columnsBlack,
        '复盘详情': columnsReplay,
    }
    return (
        <>
            <StandardTable
                detailColumns={types[currentType]}
                columns={types[currentType]}
                loading={loading}
                data={listData}
                rowKey="id"
                onChange={handleTableChange}
                rowSelectionProps={false}
                showSelectCount={false}
                tools={true}
                isNoneRefresh={true}
                isNoMenu={true}

            />
        </>

    );
}

export default Details;