import React, { useEffect, useState } from 'react';
import { Button, Col, Form, Table, Row, DatePicker, Card } from 'antd';
import { connect } from 'dryad';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import styles from './index.less';

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const defaultParams = {
  date: moment().subtract(1, 'day').format('YYYY-MM-DD'),
};

const AssessDaily = (props) => {
  const {
    dispatch,
    citiesAndDailyAssess: { assessDaily },
    form: { getFieldDecorator, getFieldValue, validateFields, resetFields },
    loading,
  } = props;
  const [currentMonth, setCurrentMonth] = useState(moment().format('YYYY年MM月'));
  const [nextMonth, setNextMonth] = useState(moment().add(1, 'month').format('YYYY年MM月'));
  const [lastMonth, setLastMonth] = useState(moment().subtract(1, 'month').format('YYYY年MM月'));
  const [currentDate, setCurrentDate] = useState(
    moment().format('DD') <= 25 ? currentMonth : nextMonth,
  );
  const [params, setParams] = useState(defaultParams);

  const [columns, setColumns] = useState([]);

  const changeDate = (date) => {
    let curMonth = moment(date).format('YYYY年MM月');
    let nextMonth = moment(date).add(1, 'month').format('YYYY年MM月');
    setCurrentMonth(curMonth);
    setNextMonth(nextMonth);
    setLastMonth(moment(date).subtract(1, 'month').format('YYYY年MM月'));
    setCurrentDate(moment(date).format('DD') <= 25 ? curMonth : nextMonth);
  };

  // useEffect(() => {
  //   const date = getFieldValue('months');
  //   setCurrentDate(moment(date).format('DD') < 25 ? currentMonth : nextMonth);
  // }, [getFieldValue('months')]);

  useEffect(() => {
    let newColumns = [
      {
        title: '日期',
        dataIndex: 'date',
        align: 'center',
        width: 60,
      },
      {
        title: '单位',
        dataIndex: 'unit',
        align: 'center',
        width: 60,
      },
      {
        title: <div>12321平台举报号码情况 （{currentDate}周期）</div>,
        align: 'center',
        children: [
          {
            title: '日数据',
            align: 'center',
            dataIndex: 'reportDailyData',
            width: 38,
          },
          {
            title: '累计数据',
            dataIndex: 'reportCumulativeData',
            align: 'center',
            width: 38,
            align: 'center',
          },
          {
            title: '工信部考核口径（有通联）',
            dataIndex: 'reportAssessmentCaliber',
            width: 42,
            align: 'center',
          },
        ],
      },
      {
        title: <div>涉案调证号码情况 （{currentMonth}周期）</div>,
        align: 'center',
        children: [
          {
            title: '日数据',
            dataIndex: 'involvedDailyData',
            width: 38,
            align: 'center',
          },
          {
            title: '累计数据',
            dataIndex: 'involvedCumulativeData',
            width: 38,
            align: 'center',
          },
          {
            title: '工信部考核口径（有通联）',
            dataIndex: 'involvedAssessmentCaliber',
            width: 42,
            align: 'center',
          },
        ],
      },
      {
        title: `日均环比上月变化情况（通报口径）`,
        align: 'center',
        children: [
          {
            title: '被举报量环比上月变化情况（件次）',
            dataIndex: 'reportedChain',
            width: 42,
            align: 'center',
            render: (data) => {
              if (data && data * 1 > 0) {
                return <div style={{ backgroundColor: '#FFF000' }}>{data}</div>;
              }
              return <div>{data}</div>;
            },
          },
          {
            title: '涉案量环比上月变化情况（件次）',
            dataIndex: 'involvedChain',
            width: 42,
            align: 'center',
            render: (data) => {
              if (data && data * 1 > 0) {
                return <div style={{ backgroundColor: '#FFF000' }}>{data}</div>;
              }
              return <div>{data}</div>;
            },
          },
        ],
      },
      {
        title: `工信部被举报核定数据（${lastMonth}周期已完结）`,
        dataIndex: 'reportedRatifying',
        width: 42,
        align: 'center',
      },
      {
        title: `工信部口径涉案号码核定数据（${lastMonth}周期已完结）`,
        dataIndex: 'involvedRatifying',
        width: 42,
        align: 'center',
      },
      {
        title: `联席办下发的涉案线索号码（${lastMonth}周期已完结）`,
        dataIndex: 'involvedClues',
        width: 42,
        align: 'center',
      },
      {
        title: `被举报下发数百万用户涉诈率（件次）`,
        dataIndex: 'reportedMillionFraudRate',
        width: 42,
        align: 'center',
        render: (data) => {
          if (data && data * 1 > 0) {
            return <div style={{ backgroundColor: '#FFF000' }}>{data}</div>;
          }
          return <div>{data}</div>;
        },
      },
      {
        title: `涉案下发数百万用户涉诈率（件次）`,
        dataIndex: 'involvedMillionFraudRate',
        width: 42,
        align: 'center',
        render: (data) => {
          if (data && data * 1 > 0) {
            return <div style={{ backgroundColor: '#FFF000' }}>{data}</div>;
          }
          return <div>{data}</div>;
        },
      },
      {
        title: `工信部核定（被举报+涉案）累计到达数（${currentMonth}）`,
        dataIndex: 'reportedInvolvedChain',
        width: 42,
        align: 'center',
      },
      {
        title: `超目标管控值`,
        dataIndex: 'exceedingControlValue',
        width: 38,
        align: 'center',
      },
      {
        title: `月度超量考核扣分`,
        dataIndex: 'monthlyAssessPointsDeducted',
        width: 50,
        align: 'center',
        render: (data) => {
          if (data && data * 1 < 0) {
            return <div style={{ backgroundColor: '#FFF000' }}>{data}</div>;
          }
          return <div>{data}</div>;
        },
      },
      {
        title: `大数据关停`,
        dataIndex: 'bigDataDown',
        width: 50,
        align: 'center',
      },
      {
        title: `大数据复机`,
        dataIndex: 'bigDataResume',
        width: 50,
        align: 'center',
      },
      {
        title: `(SH)实时诈骗数据`,
        dataIndex: 'shrealTimeFraudData',
        width: 50,
        align: 'center',
      },
      {
        title: `涉诈风险TOP网点情况`,
        align: 'center',
        children: [
          {
            title: '单位',
            dataIndex: 'unitTwo',
            align: 'center',
            width: 38,
          },
          {
            title: '类型',
            dataIndex: 'type',
            align: 'center',
            width: 42,
          },
          {
            title: '网点名称',
            dataIndex: 'networkName',
            align: 'center',
            width: 80,
          },
          {
            title: '问题情况',
            dataIndex: 'problemSituation',
            align: 'center',
            width: 80,
          },
        ],
      },
    ];
    setColumns(newColumns);
  }, [currentDate, lastMonth, nextMonth, currentMonth]);
  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    setExportLoading(true);
    const newParams = {
      ...params,
    };
    exportFile({
      urlAPi: '/api/hn/statisticsAssessDaily/exportAssessDaily',
      decode: true,
      params: newParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const getPageList = (params) => {
    dispatch({
      type: 'citiesAndDailyAssess/getAssessDaily',
      payload: params,
    });
    setParams(params);
  };

  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const newParams = {
        ...params,
        date: months ? moment(months).format('YYYY-MM-DD') : '',
        ...rest,
      };
      changeDate(months);
      getPageList(newParams);
    });
  };

  const handleReset = () => {
    resetFields();
    setParams(defaultParams);
    getPageList(defaultParams);
  };

  useEffect(() => {
    getPageList(defaultParams);
  }, []);

  return (
    <Card bordered={false} className={styles.assessDaily}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="时间">
              {getFieldDecorator('months', {
                initialValue: moment(params.date, 'YYYY-MM-DD'),
              })(<DatePicker format={'YYYY-MM-DD'} allowClear={false} />)}
            </Form.Item>
          </Col>
          <Col offset={8} span={8} style={{ marginTop: 3, textAlign:'right' }}>
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
            <Button onClick={handleReset} style={{ margin: '0px 10px' }}>
              重置
            </Button>
            <Button type="primary" onClick={handleExport} loading={exportLoading} >
              导出
            </Button>
          </Col>
        </Row>
      </Form>
      <div className={styles.table}>
        <Table
          columns={columns}
          bordered
          loading={loading}
          dataSource={assessDaily}
          scroll={{ x: 'max-content' }}
          size="small"
          pagination={false}
        />
      </div>

      {/* <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: assessDaily,
          pagination: false,
        }}
        rowKey="id"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ x: true }}
        bordered
      /> */}
    </Card>
  );
};

export default connect(({ citiesAndDailyAssess, loading }) => ({
  citiesAndDailyAssess,
  loading: loading.effects['citiesAndDailyAssess/getAssessDaily'],
}))(Form.create()(AssessDaily));
