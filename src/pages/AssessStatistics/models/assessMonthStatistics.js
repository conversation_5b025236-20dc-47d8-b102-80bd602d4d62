import {
  getCityMonthlyData,
  getConfigTypeByPidAndConfigType,
  getLineChartData,
  getSystemConfigListByConfigType
} from '@/services/AssessStatistics/assessMonthStatistics';

const defaultState = {
  reportTableData:[],
  involvedTableData:[],
  provinceTableData:[],
  cityList:[]
};

export default {
  namespace: 'assessMonthStatistics',
  state: defaultState,
  effects: {
    *getInvolvedCityMonthlyData({ payload, callback }, { call, put }) {
      const response = yield call(getCityMonthlyData, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { involvedTableData:response.data.map((item,index)=>{
          return {...item,id:index+1}
        }) },
      });
    },
    *getReportCityMonthlyData({ payload, callback }, { call, put }) {
      const response = yield call(getCityMonthlyData, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { reportTableData:response.data.map((item,index)=>{
          return {...item,id:index+1}
        }) },
      });
    },
    *getProvinceCityMonthlyData({ payload, callback }, { call, put }) {
      const response = yield call(getCityMonthlyData, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { provinceTableData:response.data.map((item,index)=>{
          return {...item,id:index+1}
        }) },
      });
    },
    *getConfigTypeByPidAndConfigType({ payload, callback }, { call, put }) {
      const response = yield call(getConfigTypeByPidAndConfigType, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getLineChartData({ payload, callback }, { call, put }) {
      const response = yield call(getLineChartData, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getSystemConfigListByConfigType({ payload, callback }, { call, put }) {
      const response = yield call(getSystemConfigListByConfigType, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { cityList:response.data },
      });
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
