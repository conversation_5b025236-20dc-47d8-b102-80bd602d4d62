import {
  getCityExamineIndicators,
  updateCityExamineIndicators,
  pageStatisticsInvolvedReportRank,
  importStatisticsInvolvedReportRank,
  deleteByIdsStatisticsInvolvedReportRank,
  getAssessDaily,
} from '@/services/AssessStatistics/citiesAndDailyAssess';
import { message } from 'antd';

const defaultState = {
  tableData: [],
  involvedReportRank: {},
  assessDaily: [],
};

export default {
  namespace: 'citiesAndDailyAssess',
  state: defaultState,
  effects: {
    *getCityExamineIndicators({ payload, callback }, { call, put }) {
      const response = yield call(getCityExamineIndicators, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: {
          tableData: response?.data?.map((item, index) => {
            if (response?.data?.length === index + 1) {
              return { ...item, isTotal: true };
            } else {
              return item;
            }
          }),
        },
      });
    },
    *updateCityExamineIndicators({ payload, callback }, { call, put }) {
      const response = yield call(updateCityExamineIndicators, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *pageStatisticsInvolvedReportRank({ payload, callback }, { call, put }) {
      const response = yield call(pageStatisticsInvolvedReportRank, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { involvedReportRank: response.data },
      });
    },
    *importStatisticsInvolvedReportRank({ payload, callback }, { call, put }) {
      const response = yield call(importStatisticsInvolvedReportRank, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *deleteByIdsStatisticsInvolvedReportRank({ payload, callback }, { call, put }) {
      const response = yield call(deleteByIdsStatisticsInvolvedReportRank, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getAssessDaily({ payload, callback }, { call, put }) {
      const response = yield call(getAssessDaily, payload);
      if (!response) return;
      if (callback) callback(response);
      if (response.code !== 200) {
        message.error(response.message);
      }
      yield put({
        type: 'save',
        payload: { assessDaily: response.data },
      });
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
