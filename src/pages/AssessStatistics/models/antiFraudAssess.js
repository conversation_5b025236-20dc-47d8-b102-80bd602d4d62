import {
  pageStatisticsInvolved,
  importStatisticsInvolved,
  deleteByIdsStatisticsInvolved,
  pageStatisticsReportPhoneData,
  importStatisticsReportPhoneData,
  deleteByIdsStatisticsReportPhoneData,
  pageStatisticsProvincial,
  importStatisticsProvincial,
  deleteByIdsStatisticsProvincial,
  getSystemConfigListByConfigType
} from '@/services/AssessStatistics/antiFraudAssess';

const defaultState = {
  involvedData:{},
  reportPhoneData:{},
  provincialData:{}
};

export default {
  namespace: 'antiFraudAssess',
  state: defaultState,
  effects: {
    *pageStatisticsInvolved({ payload, callback }, { call, put }) {
      const response = yield call(pageStatisticsInvolved, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { involvedData:response.data },
      });
    },
    *importStatisticsInvolved({ payload, callback }, { call, put }) {
      const response = yield call(importStatisticsInvolved, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *deleteByIdsStatisticsInvolved({ payload, callback }, { call, put }) {
      const response = yield call(deleteByIdsStatisticsInvolved, payload);
      if (callback) callback(response);
    },
    *pageStatisticsReportPhoneData({ payload, callback }, { call, put }) {
      const response = yield call(pageStatisticsReportPhoneData, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { reportPhoneData:response.data },
      });
    },
    *importStatisticsReportPhoneData({ payload, callback }, { call, put }) {
      const response = yield call(importStatisticsReportPhoneData, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *deleteByIdsStatisticsReportPhoneData({ payload, callback }, { call, put }) {
      const response = yield call(deleteByIdsStatisticsReportPhoneData, payload);
      if (callback) callback(response);
    },
    *pageStatisticsProvincial({ payload, callback }, { call, put }) {
      const response = yield call(pageStatisticsProvincial, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { provincialData:response.data },
      });
    },
    *importStatisticsProvincial({ payload, callback }, { call, put }) {
      const response = yield call(importStatisticsProvincial, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *deleteByIdsStatisticsProvincial({ payload, callback }, { call, put }) {
      const response = yield call(deleteByIdsStatisticsProvincial, payload);
      if (callback) callback(response);
    },
    *getSystemConfigListByConfigType({ payload, callback }, { call, put }) {
      const response = yield call(getSystemConfigListByConfigType, payload);
      if (callback) callback(response);
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
