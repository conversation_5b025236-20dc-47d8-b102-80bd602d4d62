import React from 'react';
import { Modal } from 'antd';

export default function ContentDetailModal({ visible, onClose, content }) {
  // 将 \n 转换为 <br/> 标签
  const formatContent = (text) => {
    if (!text) return '';
    return text.replace(/\n/g, '<br/>');
  };

  return (
    <Modal visible={visible} onCancel={onClose} title="报告内容" width={800} footer={null}>
      <div dangerouslySetInnerHTML={{ __html: formatContent(content) }} />
    </Modal>
  );
}
