import React, { useRef, useState, useEffect, Fragment } from 'react';
import { Form, Row, Col, DatePicker, Input, message, Button, Modal } from 'antd';
import request from 'ponshine-request';
import StandardTable from '@/components/StandardTable';
import ContentDetailModal from './ContentDetailModal';
import { goPage, onEnterPage } from '@/utils/openTab';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator, getFieldsValue },
    tabProps,
  } = props;
  const [loading, setLoading] = useState(false);

  const [contentDetailVisible, setContentDetailVisible] = useState(false);
  const [contentDetailContent, setContentDetailContent] = useState('');

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const serachParams = useRef();

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/report/page', {
      method: 'POST',
      requestType: 'form',
      data: {
        pageNum,
        pageSize,
        ...props,
      },
    });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    const values = getFieldsValue();
    const { paymentPeriod } = values;
    getListDatas({
      ...values,
      startDate: paymentPeriod?.[0]?.format('YYYYMMDD'),
      endDate: paymentPeriod?.[1]?.format('YYYYMMDD'),
      paymentPeriod: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleReset = () => {
    form.resetFields();
    getListDatas();
  };

  const handleFun = (r) => {
    goPage(`/assessStatistics/reportMakeReport_recordManage_handle/${r.id || 1}`, {});
  };

  const handleSend = (r) => {
    Modal.confirm({
      title: '提示',
      content: '确认发送该报告至指定邮箱？',
      onOk: async () => {
        const response = await request('/api/hn/report/send', {
          method: 'POST',
          requestType: 'form',
          data: { id: r.id },
        });
        if (response.code === 200) {
          message.success(response.message);
          handleReset();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleContentDetail = (content) => {
    setContentDetailContent(content);
    setContentDetailVisible(true);
  };

  let columns = [
    {
      title: '账期',
      width: 100,
      dataIndex: 'accountPeriod',
      ellipsis: true,
    },
    {
      title: '报告名称',
      width: 100,
      dataIndex: 'reportName',
      ellipsis: true,
    },

    {
      title: '报告生成时间',
      width: 120,
      dataIndex: 'reportCreateTime',
      ellipsis: true,
    },
    {
      title: '报告发送时间',
      width: 120,
      dataIndex: 'reportSendTime',
      ellipsis: true,
    },
    {
      title: '原始报告附件',
      width: 120,
      dataIndex: 'originalReportAttachmentName',
      ellipsis: true,
      render: (v, r) => {
        return <a onClick={() => handleContentDetail(r.originalReportAttachmentContent)}>{v}</a>;
      },
    },
    {
      title: '报告更新时间',
      width: 120,
      dataIndex: 'reportUpdateTime',
      ellipsis: true,
    },
    {
      title: '更新报告附件',
      width: 120,
      dataIndex: 'updateReportAttachmentName',
      ellipsis: true,
      render: (v, r) => (
        <a onClick={() => handleContentDetail(r.updateReportAttachmentContent)}>{v}</a>
      ),
    },
    {
      title: '操作',
      width: 120,
      dataIndex: 'opt',
      fixed: 'right',
      render: (v, r) => {
        return (
          <Fragment>
            <a style={{ marginRight: 16 }} onClick={() => handleFun(r)}>
              处理
            </a>
            <a onClick={() => handleSend(r)}>发送</a>
          </Fragment>
        );
      },
    },
  ];

  useEffect(() => {
    getListDatas();
    const unlistenHistory = onEnterPage(tabProps, () => {
      getListDatas();
    });
    return () => {
      if (unlistenHistory) unlistenHistory();
    };
  }, []);

  return (
    <Fragment>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <Row>
          <Col span={8}>
            <Form.Item label="报告名称">
              {getFieldDecorator('reportName')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="账期">
              {getFieldDecorator('paymentPeriod', {})(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>

          <Col span={8} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>
            <Button onClick={handleReset} style={{ marginRight: 8 }}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        tools={true}
        detailColumns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
      <ContentDetailModal
        visible={contentDetailVisible}
        onClose={() => {
          setContentDetailVisible(false);
          setContentDetailContent('');
        }}
        content={contentDetailContent}
      />
    </Fragment>
  );
};

export default Form.create()(Index);
