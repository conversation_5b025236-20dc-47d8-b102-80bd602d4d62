import { Modal, Form, Input, message, Select, <PERSON><PERSON>, Card } from 'antd';
import React, { Fragment, useEffect } from 'react';
import request from 'ponshine-request';
import DailyBriefingForm from './DailyBriefingForm';
// import DailyNewspaperAgainstFraud from './DailyNewspaperAgainstFraud';
import { goBack, goPage } from '@/utils/openTab';
import { withContext } from 'demasia-pro-layout';
import styles from './index.less';

const HandleDetail = Form.create()(
  ({
    closeSingleTab,
    tabKey,
    match: {
      params: { id },
    },
    form,
    ...props
  }) => {
    const handleSubmit = () => {
      form.validateFields((err, values) => {
        if (err) return;
        Modal.confirm({
          title: '提示',
          content: '确认修改并更新报告的附件内容',
          onOk: async () => {
            const response = await request('/api/hn/report/update', {
              requestType: 'form',
              method: 'POST',
              data: {
                id,
                ...values,
              },
            });
            if (response.code === 200) {
              message.success(response.message);
            } else {
              message.error(response.message);
            }
          },
        });
      });
    };

    const handleCancel = () => {
      closeSingleTab(tabKey);
      goPage('/assessStatistics/reportMakeReport');
    };

    const handleGetLatestData = async () => {
      Modal.confirm({
        title: '提示',
        content: '当前已编辑的数据会被清空，确认重新获取当天最新指标数据生成新的报告？',
        onOk: async () => {
          const response = await request('/api/hn/report/regenerate', {
            requestType: 'form',
            method: 'POST',
            data: { id },
          });
          if (response.code === 200) {
            message.success(response.message);
            form.setFieldsValue({
              updateReportAttachmentContent: response.data,
            });
          } else {
            message.error(response.message);
          }
        },
      });
    };

    const getInitContentData = async () => {
      const response = await request('/api/hn/report/content', {
        requestType: 'form',
        method: 'POST',
        data: { id },
      });
      if (response.code === 200) {
        form.setFieldsValue({
          updateReportAttachmentContent: response.data,
        });
      } else {
        message.error(response.message);
      }
    };

    useEffect(() => {
      id && getInitContentData();
    }, [id]);

    return (
      <div className={styles.handleDetail}>
        <div className={styles.content}>
          <Card>
            <Form>
              <DailyBriefingForm form={form} />
              {/* <DailyNewspaperAgainstFraud /> */}
            </Form>
          </Card>
        </div>

        <div className={styles.fixedFooter}>
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleGetLatestData}>
            获取最新数据
          </Button>
          <Button style={{ marginRight: 16 }} onClick={handleCancel}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            提交
          </Button>
        </div>
      </div>
    );
  },
);

let config = [['tabKey', 'closeSingleTab']];

export default withContext(...config)((props) => {
  return <HandleDetail {...props} />;
});
