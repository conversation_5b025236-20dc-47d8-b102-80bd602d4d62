.handleDetail {
  position: relative;
  height: 100%;
  padding-bottom: 60px; // 为底部按钮预留空间

  .content {
    height: 100%;
    overflow-y: auto;
    background: #f0f2f5;


    :global {
      .ant-card {
        margin-bottom: 24px;
      }
    }
  }

  .fixedFooter {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    padding: 12px 24px;
    text-align: right;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
  }
}

