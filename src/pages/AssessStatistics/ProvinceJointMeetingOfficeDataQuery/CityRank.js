import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input, Modal } from 'antd';
import { connect } from 'dryad';
import { useEffect, useState, useRef } from 'react';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import BatchImportModal from '@/components/BatchImport';
import { Licensee } from 'ponshine';
import request from '@/utils/request';
import moment from 'moment';
const { RangePicker } = DatePicker;

const importRequest = (values) => {
  return request(`/api/hn/clueCities/importHuFraudClueCities`, {
    method: 'POST',
    data: values,
    requestType: 'form',
  });
};

const defaultPagination = {
  pageSize: 10,
  currentPage: 1,
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const initTime = [moment(), moment()];

const CityRank = (props) => {
  const {
    dispatch,
    form,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
  } = props;
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [importVisible, setImportVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const serachParams = useRef();

  const columns = [
    {
      title: '省内排名',
      dataIndex: 'provincialRank',
      width: 130,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '市州分公司',
      dataIndex: 'company',
      width: 140,
      align: 'center',
      ellipsis: true,
    },

    {
      title: '线索数',
      dataIndex: 'cluesNumber',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '全国排名',
      dataIndex: 'nationalRank',
      width: 140,
      align: 'center',
      ellipsis: true,
    },

    {
      title: '导入时间',
      dataIndex: 'importTime',
      width: 160,
      align: 'center',
      ellipsis: true,
    },
  ];

  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    setExportLoading(true);

    exportFile({
      urlAPi: '/api/hn/clueCities/exportHuFraudClueCities',
      decode: true,
      params: serachParams.current,
      method: 'POST',
      requestType: 'form',
      callback: () => {
        setExportLoading(false);
      },
    });
  };
  useEffect(() => {
    getListDatas({
      startDate: initTime[0].format('YYYY-MM-DD'),
      endDate: initTime[1].format('YYYY-MM-DD'),
    });
  }, []);
  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request(`/api/hn/clueCities/pageHuFraudClueCities`, {
      method: 'POST',
      data: { pageNum, pageSize, ...props },
      requestType: 'form',
    });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;

      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum || 0,
          pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  //查询
  const handleSearch = () => {
    const formValues = form.getFieldsValue();
    let params = {
      ...formValues,
      startDate: formValues.months?.[0]?.format('YYYY-MM-DD'),
      endDate: formValues.months?.[1]?.format('YYYY-MM-DD'),
    };
    delete params.months;
    getListDatas(params);
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    const newParms = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...serachParams.current,
    };
    getListDatas(newParms);
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  const handleReset = () => {
    resetFields();
    setSelectedRows([]);
    getListDatas({
      startDate: initTime[0].format('YYYY-MM-DD'),
      endDate: initTime[1].format('YYYY-MM-DD'),
    });
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '提示',
      content: '是否确认删除选中数据?',
      onOk: async () => {
        const response = await request(`/api/hn/clueCities/deleteByIds`, {
          method: 'POST',
          data: { idList: selectedRows?.map((item) => item.id)?.join(',') },
          requestType: 'form',
        });
        if (response.code === 200) {
          message.success(response.message);
          handleReset();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleImport = () => {
    setImportVisible(true);
  };

  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="起止日期">
              {getFieldDecorator('months', {
                initialValue: initTime,
                rules: [{ required: true, message: '请选择起止日期' }],
              })(
                <RangePicker
                  format={'YYYY-MM-DD'}
                  placeholder={['开始时间', '结束时间']}
                  allowClear={false}
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="市州">
              {getFieldDecorator('company')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item style={{ textAlign: 'right' }} wrapperCol={{ span: 24 }}>
              {/* <Licensee license="statisticsProvincial_pageStatisticsProvincial"> */}
              <Button
                type="primary"
                onClick={handleSearch}
                style={{ marginRight: 5, marginLeft: 5 }}
              >
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginRight: 5 }}>
                重置
              </Button>
              {/* </Licensee> */}
              {/* <Licensee license="statisticsProvincial_exportStatisticsProvincial"> */}
              <Button
                type="primary"
                onClick={handleExport}
                loading={exportLoading}
                style={{ marginRight: 5 }}
              >
                导出
              </Button>
              {/* </Licensee>
                        <Licensee license="statisticsProvincial_importStatisticsProvincial"> */}
              <Button type="primary" onClick={handleImport} style={{ marginRight: 5 }}>
                导入
              </Button>
              {/* </Licensee> */}
              {/* <Licensee license="statisticsProvincial_deleteByIdsStatisticsProvincial"> */}
              <Button
                type="danger"
                onClick={handleDelete}
                disabled={selectedRows.length === 0}
                icon="delete"
              >
                删除
              </Button>
              {/* </Licensee> */}
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        loading={loading}
        data={listData}
        onChange={handlePaginationTable}
        rowKey="id"
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        scroll={{ x: '100%' }}
      />
      <BatchImportModal
        acceptType={['.xlsx']}
        title="批量导入"
        tipsText={'*每个文件不超过1万条数据，文件格式为xlsx'}
        visible={importVisible}
        onClose={() => {
          setImportVisible(false);
        }}
        errorExportUrl={''}
        downTemplateUrl={`/api/template/getTemplate?templateCode=clueCitiesImport`}
        importRequest={importRequest}
        reload={() => {
          handleReset();
        }}
        closeModal={() => {
          setImportVisible(false);
        }}
      />
    </Card>
  );
};

export default connect(({}) => ({}))(Form.create()(CityRank));
