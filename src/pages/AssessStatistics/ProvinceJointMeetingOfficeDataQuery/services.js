/*
 * @Author: ss <EMAIL>
 * @Date: 2024-09-18 14:00:29
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-10-25 14:08:46
 * @FilePath: /hunanfanzha/src/pages/AssessStatistics/ThematicManage/FixedLineTopic/FraudVerification/services.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from 'ponshine-request';

// 线索统计数据
export async function selectLineStatisticsData(params) {
  return request(`/api/hn/fixTelCheck/getFixTelFraudCheck`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
// 涉案线索地市排名
export async function selectCityRank(params) {
  return request(`/api/hn/fixTelCheck/getFixTelFraudCheck`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
// 涉案线索集中网点
export async function selectConcentratedNetwork(params) {
  return request(`/api/hn/fixTelCheck/getFixTelFraudCheck`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
