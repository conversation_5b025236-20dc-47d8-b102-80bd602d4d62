/*
 * @Author: ss
 * @Date: 2025-03-12 16:03:44
 * @LastEditors: ss
 * @LastEditTime: 2025-03-12 16:12:55
 * @Description:
 */
import { Card, Tabs } from 'antd';
import React, { useState } from 'react';
import LineStatisticsData from './LineStatisticsData';
import CityRank from './CityRank';
import ConcentratedNetwork from './ConcentratedNetwork';
import { Licensee } from 'ponshine';
import NoAuth from '@/pages/NoAuth';

const ProvinceJointMeetingOfficeDataQuery = () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <Card bordered={false}>
      <Tabs activeKey={activeKey} onChange={(key) => setActiveKey(key)}>
        <Tabs.TabPane tab="线索统计数据" key="1">
          {activeKey === '1' && (
            <Licensee license="clue_statistics_data" fallback={<NoAuth />}>
              <LineStatisticsData />
            </Licensee>
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab="涉案线索地市排名" key="2">
          {activeKey === '2' && (
            <Licensee license="clue_cities" fallback={<NoAuth />}>
              <CityRank />
            </Licensee>
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab="涉案线索集中网点" key="3">
          {activeKey === '3' && (
            <Licensee license="clue_outlets" fallback={<NoAuth />}>
              <ConcentratedNetwork />
            </Licensee>
          )}
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};

export default ProvinceJointMeetingOfficeDataQuery;
