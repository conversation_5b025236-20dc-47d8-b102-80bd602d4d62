import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input, Modal } from 'antd';
import { useEffect, useState, useRef } from 'react';
// import { VTComponents } from 'virtualizedtableforantd';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import BatchImportModal from '@/components/BatchImport';
import { Licensee } from 'ponshine';
import request from '@/utils/request';
import moment from 'moment';

import { v4 as uuidv4 } from 'uuid';

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const importRequest = (values) => {
  return request(`/api/hn/clueStatistics/importHuFraudClueStatisticsData`, {
    method: 'POST',
    data: values,
    requestType: 'form',
  });
};

const LineStatisticsData = (props) => {
  const {
    form,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
  } = props;
  const initTime = moment();
  const [listData, setListData] = useState({
    list: [],
    pagination: false,
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [importVisible, setImportVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const serachParams = useRef();

  const columns = [
    {
      title: '分组',
      dataIndex: 'subset',
      width: 130,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '市州',
      dataIndex: 'city',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '移动',
      dataIndex: 'yd',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '联通',
      dataIndex: 'unicom',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '电信',
      dataIndex: 'dx',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '广电',
      dataIndex: 'gd',
      width: 160,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '合计',
      dataIndex: 'total',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '组内全国排名',
      dataIndex: 'nationalRank',
      width: 160,
      align: 'center',
      ellipsis: true,
      render: (text) => {
        return text ?? '';
      },
    },
    {
      title: '导入时间',
      dataIndex: 'importTime',
      width: 200,
      align: 'center',
      ellipsis: true,
    },
  ];

  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/hn/clueStatistics/exportHuFraudClueStatisticsData',
      decode: true,
      params: serachParams.current,
      method: 'POST',
      requestType: 'form',
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const getListDatas = async (props) => {
    setLoading(true);
    const response = await request(`/api/hn/clueStatistics/pageHuFraudClueStatisticsData`, {
      method: 'POST',
      data: { ...props, pageNum: 1, pageSize: 99 },
      requestType: 'form',
    });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;

      setListData({
        list: response?.data?.items || [],
        pagination: false,
      });
    } else {
      message.error(response.message);
    }
  };

  //查询
  const handleSearch = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      let params = {
        ...values,
        startDate: values.months?.format('YYYY-MM-DD'),
      };
      delete params.months;
      getListDatas({
        ...params,
      });
      setSelectedRows([]);
    });
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    const newParms = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...serachParams.current,
    };
    getListDatas(newParms);
  };

  const handleSelectRows = (newSelectedRows) => {
    setSelectedRows(newSelectedRows);
  };

  const handleReset = () => {
    setSelectedRows([]);
    resetFields();
    getListDatas({ startDate: initTime.format('YYYY-MM-DD') });
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '提示',
      content: '是否确认删除选中数据?',
      onOk: async () => {
        const response = await request(`/api/hn/clueStatistics/deleteByIds`, {
          method: 'POST',
          data: { idList: selectedRows?.map((item) => item.id)?.join(',') },
          requestType: 'form',
        });
        if (response.code === 200) {
          message.success(response.message);
          handleReset();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleImport = () => {
    setImportVisible(true);
  };

  useEffect(() => {
    getListDatas({ startDate: initTime.format('YYYY-MM-DD') });
  }, []);

  const processTableData = (data) => {
    if (!Array.isArray(data) || data.length === 0) return data;

    // 先计算合计值
    const sums = data.reduce((acc, cur) => {
      return {
        yd: (acc.yd || 0) + (Number(cur.yd) || 0),
        unicom: (acc.unicom || 0) + (Number(cur.unicom) || 0),
        dx: (acc.dx || 0) + (Number(cur.dx) || 0),
        gd: (acc.gd || 0) + (Number(cur.gd) || 0),
        total: (acc.total || 0) + (Number(cur.total) || 0),
      };
    }, {});

    // 创建合计行
    const summaryRow = {
      subset: <span style={{ fontWeight: 'bold' }}>合计</span>,
      city: '-',
      yd: <span style={{ fontWeight: 'bold' }}>{sums.yd}</span>,
      unicom: <span style={{ fontWeight: 'bold' }}>{sums.unicom}</span>,
      dx: <span style={{ fontWeight: 'bold' }}>{sums.dx}</span>,
      gd: <span style={{ fontWeight: 'bold' }}>{sums.gd}</span>,
      total: <span style={{ fontWeight: 'bold' }}>{sums.total}</span>,
      nationalRank: '-',
      importTime: '-',
      id: uuidv4(),
      disabled: true,
    };

    return [...data, summaryRow];
  };

  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="查询日期">
              {getFieldDecorator('months', {
                initialValue: initTime,
                rules: [{ required: true, message: '请选择统计时间' }],
              })(<DatePicker format={'YYYY-MM-DD'} style={{ width: '100%' }} allowClear={false} />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="市州">
              {getFieldDecorator('city')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'right' }}>
              {/* <Licensee license="statisticsProvincial_pageStatisticsProvincial"> */}
              <Button
                type="primary"
                onClick={handleSearch}
                style={{ marginRight: 5, marginLeft: 5 }}
              >
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginRight: 5 }}>
                重置
              </Button>
              {/* </Licensee> */}
              {/* <Licensee license="statisticsProvincial_exportStatisticsProvincial"> */}
              <Button
                type="primary"
                onClick={handleExport}
                loading={exportLoading}
                style={{ marginRight: 5 }}
              >
                导出
              </Button>
              {/* </Licensee>
                        <Licensee license="statisticsProvincial_importStatisticsProvincial"> */}
              <Button type="primary" onClick={handleImport} style={{ marginRight: 5 }}>
                导入
              </Button>
              {/* </Licensee> */}
              {/* <Licensee license="statisticsProvincial_deleteByIdsStatisticsProvincial"> */}
              <Button
                type="danger"
                onClick={handleDelete}
                disabled={selectedRows.length === 0}
                icon="delete"
              >
                删除
              </Button>
              {/* </Licensee> */}
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: processTableData(listData?.list),
          pagination: false,
        }}
        onChange={handlePaginationTable}
        rowKey="id"
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        scroll={{ x: '100%' }}
      
      />

      <BatchImportModal
        acceptType={['.xlsx']}
        title="批量导入"
        visible={importVisible}
        onClose={() => {
          setImportVisible(false);
        }}
        errorExportUrl={''}
        downTemplateUrl={`/api/template/getTemplate?templateCode=clueStatisticsDataImport`}
        importRequest={importRequest}
        reload={() => {
          handleReset();
        }}
        closeModal={() => {
          setImportVisible(false);
        }}
        tipsText={'*每个文件不超过1万条数据，文件格式为xlsx'}
      />
    </Card>
  );
};

export default Form.create()(LineStatisticsData);
