import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input, Modal } from "antd";
import { connect } from 'dryad';
import { useEffect, useState } from "react";
import moment from 'moment';
import StandardTable from "@/components/StandardTable";
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal'
import { Licensee } from 'ponshine';

const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
    pageSize: 10,
    currentPage: 1,
};
const formItemLayout = {
    labelCol: {
        span: 8,
    },
    wrapperCol: {
        span: 16,
    },
};

const ProvincialAssessData = (props) => {
    const { dispatch, antiFraudAssess: { provincialData }, form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
        loading } = props;
    const [params, setParams] = useState(defaultPagination);
    const [operatorList, setOperatorList] = useState([])
    const [selectedRows, setSelectedRows] = useState([]);
    const [importVisible, setImportVisible] = useState(false)

    const columns = [
        {
            title: "考核月份",
            dataIndex: "month",
            width: 130,
            align: "center",
            
        },
        {
            title: "线索编号",
            dataIndex: "leadNumber",
            width: 140,
            align: "center",
            
        },
        {
            title: "案件编号",
            dataIndex: "caseNumber",
            width: 200,
            align: "center",

            
        },
        {
            title: "涉案号码",
            dataIndex: "involvedPhone",
            width: 120,
            align: "center",
            
        },
        {
            title: "责任单位",
            dataIndex: "responsibilityUnit",
            width: 120,
            align: "center",
            
        },
        {
            title: "本地网",
            dataIndex: "localNetWork",
            width: 160,
            align: "center",
            
        },
        {
            title: "开卡人",
            dataIndex: "cardholder",
            width: 100,
            align: "center",
            
        },
        {
            title: "证件号码",
            dataIndex: "idNumber",
            width: 160,
            align: "center",
            ellipsis: true,
            
        },
        {
            title: "运营商",
            dataIndex: "operators",
            width: 110,
            align: "center",
            
        },
        {
            title: "开户网点",
            dataIndex: "accountOutlets",
            width: 140,
            align: "center",
            
        },
        {
            title: "开户日期",
            dataIndex: "accountDate",
            width: 150,
            align: "center",
            
        },
        {
            title: "开户省",
            dataIndex: "accountProvince",
            width: 130,
            align: "center",
            
        },
        {
            title: "开户市",
            dataIndex: "accountCity",
            width: 130,
            align: "center",
            
        },
        {
            title: "导入时间",
            dataIndex: "importTime",
            width: 160,
            align: "center",
            
        },
        {
            title: "导入人",
            dataIndex: "importUser",
            width: 120,
            align: "center",
            
        },
    ];

    const [exportLoading, setExportLoading] = useState(false)
    const handleExport = () => {
        setExportLoading(true);
        const newParams = {
            ...params,
            currentPage: 1,
            pageSize: 10,
        }
        exportFile({
            urlAPi: '/api/hn/statisticsProvincial/exportStatisticsProvincial',
            decode: true,
            params: newParams,
            method: 'POST',
            callback:()=>{
                setExportLoading(false);
            }
        });
    };

    const getPageList = (params) => {
        dispatch({
            type: "antiFraudAssess/pageStatisticsProvincial",
            payload: params,
        });
        setParams(params)
    }

    //查询
    const handleSearch = () => {
        validateFields((err, { months, ...rest }) => {
            if (err) {
                return;
            }
            const newParams = {
                ...params,
                ...defaultPagination,
                startMonth: months && months.length > 0 ? moment(months[0]).format('YYYY-MM') : '',
                endMonth: months && months.length > 0 ? moment(months[1]).format('YYYY-MM') : '',
                ...rest,
            }
            getPageList(newParams)
        });
    };

    //表格切换
    const handlePaginationTable = (pagination) => {
        const newParms = { ...params, currentPage: pagination.current, pageSize: pagination.pageSize }
        getPageList(newParms)
    };

    const handleSelectRows = (newSelectedRows) => {
        setSelectedRows(newSelectedRows);
    };

    const handleReset = () => {
        resetFields();
        setParams({ ...defaultPagination })
        getPageList({ ...defaultPagination })
    };

    const handleDelete = () => {
        Modal.confirm({
            title: '省联席办考核数据删除',
            content: '是否确认删除选中数据?',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                dispatch({
                    type: 'antiFraudAssess/deleteByIdsStatisticsProvincial',
                    payload: { idList: selectedRows?.map((item => item.id)) },
                    callback: (res) => {
                        if (res && res.code == 200) {
                            message.success(res.message)
                            getPageList({ ...params, ...defaultPagination })
                            setSelectedRows([])
                        } else {
                            message.error(res.message)
                        }
                    },
                });
            },
        });
    }

    const handleImport = () => {
        setImportVisible(true)
    }

    const hideImport = () => {
        setImportVisible(false)
    }

    const [confirmLoading, setConfirmLoading] = useState(false);

    const onImport = (values, callback) => {
        setConfirmLoading(true);
        dispatch({
            type: 'antiFraudAssess/importStatisticsProvincial',
            payload: values,
            callback: (res) => {
                setConfirmLoading(false);
                if (res.code == 200) {
                    message.success(res.message || '导入成功');
                    getPageList({ ...params, ...defaultPagination })
                    hideImport()
                } else {
                    if (res.code == 401) {
                        callback()
                        getPageList({ ...defaultPagination })
                    } else {
                        message.error(res.message);
                    }
                }
            },
        });
    }

    const changeDate = (v) => {
        setFieldsValue({ months: v })
    }

    const changePanel = (v) => {
        setFieldsValue({ months: v })
    }

    const getOperatorList = () => {
        dispatch({
            type: 'antiFraudAssess/getSystemConfigListByConfigType',
            payload: { configType: 'operators' },
            callback: (res) => {
                if (res && res.code == '200' && res.data) {
                    setOperatorList(res.data || []);
                } else {
                    setOperatorList([]);
                }
            },
        });
    };

    useEffect(() => {
        getPageList({ ...params })
        getOperatorList()
    }, [])


    return (
        <Card bordered={false}>
            <Form {...formItemLayout}>
                <Row>
                    <Col span={8}>
                        <Form.Item label="考核月份">
                            {getFieldDecorator("months")(
                                <RangePicker
                                    format={'YYYY-MM'}
                                    mode={['month', 'month']}
                                    onPanelChange={changePanel}
                                    onChange={changeDate}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="涉案号码">
                            {getFieldDecorator('involvedPhone')(
                                <Input placeholder="请输入" allowClear />,
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="运营商">
                            {getFieldDecorator('operators')(
                                <Select placeholder="请选择" allowClear>
                                    {operatorList.map((item) => (
                                        <Option value={item.name} key={item.id}>{item.name}</Option>
                                    ))}
                                </Select>,
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="开户市">
                            {getFieldDecorator('city')(
                                <Input placeholder="请输入" allowClear />,
                            )}
                        </Form.Item>
                    </Col>
                    <Col offset={8} span={8} style={{ marginTop: 3, textAlign: 'right' }}>
                        <Licensee license="statisticsProvincial_pageStatisticsProvincial">
                            <Button
                                type="primary"
                                onClick={handleSearch}
                            >
                                查询
                            </Button>
                            <Button onClick={handleReset} style={{ margin: '0px 10px' }}>重置</Button>
                        </Licensee>
                        <Licensee license="statisticsProvincial_exportStatisticsProvincial">
                            <Button
                                type="primary"
                                onClick={handleExport}
                                loading={exportLoading}
                            >
                                导出
                            </Button>
                        </Licensee>
                        <Licensee license="statisticsProvincial_importStatisticsProvincial">
                            <Button
                                type="primary"
                                onClick={handleImport}
                                style={{ margin: '0px 10px' }}
                            >
                                导入
                            </Button>
                        </Licensee>
                        <Licensee license="statisticsProvincial_deleteByIdsStatisticsProvincial">
                            <Button
                                type='danger'
                                onClick={handleDelete}
                                disabled={selectedRows.length === 0}
                                icon="delete"
                            >
                                删除
                            </Button>
                        </Licensee>
                    </Col>
                </Row>
            </Form>
            <StandardTable
                columns={columns}
                loading={loading}
                data={{
                    list: provincialData?.items || [],
                    pagination: {
                        current: params.currentPage,
                        pageSize: params.pageSize,
                        total: provincialData?.totalNum || 0
                    }
                }}
                onChange={handlePaginationTable}
                rowKey="id"
                selectedRows={selectedRows}
                onSelectRow={handleSelectRows}
                scroll={{ x: true }}
            />
            {
                importVisible && <BatchImportModal
                    title='批量导入'
                    visible={importVisible}
                    onImport={onImport}
                    onClose={hideImport}
                    loading={confirmLoading}
                    errorExportUrl={'/api/hn/statisticsProvincial/exportStatisticsProvincialError'}
                    downTemplateUrl={`template/getTemplate?templateCode=provinicalAssessData`}
                />
            }
        </Card>
    );
}

export default connect(({ antiFraudAssess, loading }) => ({
    antiFraudAssess,
    loading: loading.effects["antiFraudAssess/pageStatisticsProvincial"],
}))(Form.create()(ProvincialAssessData));
