import React, { useState } from 'react'
import { Card, Tabs } from 'antd'
import CaseData from './CaseData'
import ReportData from './ReportData'
import ProvincialAssessData from './ProvincialAssessData'

const { TabPane } = Tabs;

const AntiFraudAssess = (props) => {
  const [tabKey, setTabKey] = useState('1')
  const changeTabKey = (key) => {
    setTabKey(key)
  }
  return (
    <Card bordered={false}>
      <Tabs defaultActiveKey="1" onChange={changeTabKey}>
        <TabPane tab="涉案数据" key="1">
          {tabKey == '1' && <CaseData {...props} />}
        </TabPane>
        <TabPane tab="12321举报数据" key="2">
          {tabKey == '2' && <ReportData {...props} />}
        </TabPane>
        {/* <TabPane tab="省联席办考核数据" key="3">
          {tabKey == '3' && <ProvincialAssessData {...props} />}
        </TabPane> */}
      </Tabs>
    </Card>
  )
}

export default AntiFraudAssess