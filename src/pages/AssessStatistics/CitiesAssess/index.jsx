import { Button, Form, message, Card, Input, Table, Popconfirm } from 'antd';
import { connect } from 'dryad';
import { useEffect, useState, createContext, Fragment } from 'react';
import { Licensee } from 'ponshine';
const EditableContext = createContext();

function EditableCell(props) {
  const { dataIndex, title, editing, record, children, rowIndex, isTotal, ...restProps } = props;

  return (
    <EditableContext.Consumer>
      {(form) => (
        <td {...restProps}>
          {editing ? (
            <Form.Item style={{ margin: 0 }}>
              {form.getFieldDecorator(`${dataIndex}.${record.id}`, {
                initialValue: record[dataIndex],
                rules: [
                  {
                    pattern: /(^[0-9]\d*$)/,
                    message: '请输入非负整数',
                  },
                ],
              })(
                <Input
                  size="small"
                  placeholder="请输入"
                  style={{ width: 80 }}
                  disabled={isTotal ? true : false}
                />,
              )}
            </Form.Item>
          ) : (
            children
          )}
        </td>
      )}
    </EditableContext.Consumer>
  );
}

const CitiesAssess = (props) => {
  const {
    dispatch,
    loading,
    form,
    citiesAndDailyAssess: { tableData },
  } = props;
  const [editing, setEditing] = useState(false);

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 130,
      align: 'center',
      render: (text, record, index) => index + 1 || '--',
    },
    {
      title: '归属',
      dataIndex: 'cityName',
      width: 120,
      align: 'center',
      render: (text) => text || '--',
    },
    {
      title: '用户基数',
      dataIndex: 'userBaseNum',
      width: 130,
      align: 'center',
      render: (text) => text || '--',
    },
    {
      title: '涉案量考核值',
      dataIndex: 'involvedExamineNum',
      width: 100,
      align: 'center',
      editable: true,
      render: (text) => text || '--',
    },
    {
      title: '12321举报考核值',
      dataIndex: 'reportExamineNum',
      width: 100,
      align: 'center',
      editable: true,
      render: (text) => text || '--',
    },
    {
      title: '省联席办考核值',
      dataIndex: 'provincialExamineNum',
      width: 120,
      align: 'center',
      editable: true,
      render: (text) => text || '--',
    },
    {
      title: '涉案号码月度(日均)管控值',
      dataIndex: 'involvedMonthlyControlValue',
      width: 120,
      align: 'center',
      editable: true,
      render: (text) => text || '--',
    },
  ];

  const save = (form) => {
    form.validateFields((errors, values) => {
      if (errors) return;
      let data = [];
      values['involvedExamineNum'].forEach((it, id) => {
        if (id == 0 || id == tableData.length) {
          return;
        }
        data.push({
          id: id,
          involvedExamineNum: it,
          provincialExamineNum: values['provincialExamineNum'][id],
          reportExamineNum: values['reportExamineNum'][id],
          involvedMonthlyControlValue: values['involvedMonthlyControlValue'][id],
        });
      });
      dispatch({
        type: 'citiesAndDailyAssess/updateCityExamineIndicators',
        payload: { updateDataList: data },
        callback: (res) => {
          if (res.code == 200) {
            message.success(res.message);
            getTableData();
            setEditing(false);
          } else {
            message.warn(res.message);
          }
        },
      });
    });
  };

  const components = {
    body: {
      cell: EditableCell,
    },
  };

  useEffect(() => {
    getTableData();
  }, []);

  const getTableData = () => {
    dispatch({
      type: 'citiesAndDailyAssess/getCityExamineIndicators',
    });
  };

  return (
    <Card bordered={false}>
      <EditableContext.Provider value={form}>
        <Table
          columns={columns.map((col) => {
            if (!col.editable) {
              return col;
            }
            return {
              ...col,
              onCell: (record, rowIndex) => ({
                record,
                dataIndex: col.dataIndex,
                title: col.title,
                editing: editing,
                rowIndex,
                isTotal: record.isTotal,
              }),
            };
          })}
          components={components}
          dataSource={tableData}
          pagination={false}
          rowKey="id"
          loading={loading}
          size="small"
        ></Table>
      </EditableContext.Provider>
      {tableData?.length > 0 && (
        <div style={{ marginTop: 20, display: 'flex', justifyContent: 'flex-end' }}>
          {editing ? (
            <span>
              <Button
                onClick={() => {
                  save(form);
                }}
                style={{ marginRight: 10 }}
              >
                保存
              </Button>
              <Popconfirm
                title="确定要取消吗？"
                onConfirm={() => {
                  setEditing(false);
                }}
              >
                <Button>取消</Button>
              </Popconfirm>
            </span>
          ) : (
            <Licensee license="assessStatistics_citiesAssess_update">
              <Button
                onClick={() => {
                  setEditing(true);
                }}
              >
                编辑
              </Button>
            </Licensee>
          )}
        </div>
      )}
    </Card>
  );
};

export default connect(({ citiesAndDailyAssess, loading }) => ({
  citiesAndDailyAssess,
  loading: loading.effects['citiesAndDailyAssess/getCityExamineIndicators'],
}))(Form.create()(CitiesAssess));
