import { Al<PERSON>, Card, Typography } from 'antd'; // @ts-ignore
import { ContentPageHeader } from 'demasia-pro-layout'; // eslint-disable-next-line import/no-extraneous-dependencies
import { Licensee } from 'ponshine';
import { FormattedMessage } from 'ponshine-plugin-react/locale';
import styles from './Welcome.less';

const CodePreview = ({ children }) => (
  <pre className={styles.pre}>
    <code>
      <Typography.Text copyable>{children}</Typography.Text>
    </code>
  </pre>
);

export default () => (
  <ContentPageHeader>
    <Card>
      <Typography.Text
        strong
        style={{
          display: 'none',
        }}
      >
        <a target="_blank" rel="noopener noreferrer" href="http://pro.demasia.org/docs/block">
          <FormattedMessage
            id="app.welcome.link.block-list"
            defaultMessage="基于 block 开发，快速构建标准页面"
          />
        </a>
      </Typography.Text>
      <Typography.Text
        strong
        style={{
          marginBottom: 12,
        }}
      >
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="http://pro.demasia.org/docs/available-script#npm-run-fetchblocks"
        >
          <FormattedMessage id="app.welcome.link.fetch-blocks" defaultMessage="获取全部区块" />
        </a>
      </Typography.Text>
      <CodePreview> npm run fetch:blocks</CodePreview>
      <FormattedMessage id="app.welcome.link.or" defaultMessage="或" />
      <CodePreview>
        {' '}
        npm run fetch:blocks:demasia-pro-blocks && npm run fetch:blocks:system-manage
      </CodePreview>
      <Typography.Text
        strong
        style={{
          marginBottom: 12,
        }}
      >
        Licensee授权
      </Typography.Text>
      <Alert
        message={
          <Licensee license="admin" fallback="我是user（用户）" iAm="我是">
            {(licenseeProps) => {
              const { iAm, text } = licenseeProps;
              return `${iAm}admin（${text}）`;
            }}
          </Licensee>
        }
      />
    </Card>
    <p
      style={{
        textAlign: 'center',
        marginTop: 24,
      }}
    >
      Want to add more pages? Please refer to{' '}
      <a href="http://pro.demasia.org/docs/block-cn" target="_blank" rel="noopener noreferrer">
        use block
      </a>
      。
    </p>
  </ContentPageHeader>
);
