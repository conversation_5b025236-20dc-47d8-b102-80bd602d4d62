@import "./common.less";

.searchBtn {
  display: inline-block;
  line-height: 18px;
  height: 18px;
  background: #0265CC;
  border-radius: 9px;
  padding: 0 6px;
  color: rgba(255,255,255, 0.7);
  font-size: 12px;
  margin-right: 4px;
  cursor: pointer;
}

.ghostBtn {
  display: inline-block;
  line-height: 18px;
  height: 18px;
  padding: 0 6px;
  border-radius: 9px;
  border: 1px solid #298FFB;
  font-size: 12px;
  cursor: pointer;
  color: #298FFB;

}
.container::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.levelBox {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
  &.yzLevel {
    background: linear-gradient(270deg, rgba(254,8,80,0.05) 0%, #FF0850 100%);
  }
  &.ptLevel {
    background: linear-gradient(90deg, rgba(22,89,164,0.8) 0%, rgba(36,45,65,0) 100%);
  }
}
.tableCol {
  display: flex;
  align-items: center;
  justify-content: center;
  // height: 42px;
  width: 100%;
  line-height: 16px;
  text-align: center;
  overflow: hidden; //超出文本隐藏
  text-overflow: ellipsis; ///超出部分省略号显示
  display: -webkit-box; //弹性盒模型
  -webkit-line-clamp: 2; //自定义行数
  /* autoprefixer: off */ 
  -webkit-box-orient: vertical;
  /* autoprefixer: on */
  font-size: 12px;
  vertical-align: 'middle';

  .optBox {
    display: inline-block;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background: url(./imgs/setting.png);
    background-size: 100%;
    background-repeat: no-repeat;
  
    &:hover {
      background-image: url(./imgs/settingActive.png);
      
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  // overflow: scroll;
  color: #FFF;
  background: #050F22 !important;
  padding: 12px 12px 0 12px!important;
  box-sizing: border-box;
  font-family: PingFang;

  :global {
    .ant-table-small {
      border: none;
    }
    .ant-table-small > .ant-table-content > .ant-table-body {
      background: none;
    }
    .ant-table-small > .ant-table-content {
      border: none;
      .ant-table-body {
        .ant-table-thead {
          &>tr {
            border: none;
            border-bottom: 1px solid rgba(190, 190, 190, 0.2);
            &>th {
              border: none;
              padding: 0px;
              padding-bottom: 4px;
            }
          } 
        }
        .ant-table-tbody {
          border: none;
        }
      }
      
    }
    .ant-modal {
      height: initial !important;
      padding-bottom: 0px;
    }

    .ant-modal-content {
      background-color: #152543;
    }


    .ant-modal-header {
      background-color: #152543;
    }

    .ant-modal-title {
      color: #FFF;
    }

    .ant-modal-close-x {
      color: #FFF;
    }
  }

  * {
    box-sizing: border-box;
  }

  h1,
  h2,
  h3,
  h4,
  p {
    margin: 0;
    padding: 0;
  }

  background: rgb(35, 40, 51);
  background-size: 100% 100%;
  padding:10px;
  position: relative;
  // height: 1700px;
}

.handleColumnVal {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100px;
  text-align: center;
  color: rgba(232, 232, 232, 0.65);
  font-size: 12px;
}

.main {
  // display: flex;
  height: calc(100% - 40px);

  .left {
    // flex: 3;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .center {
    // flex: 4;
    height: 100%;
    padding: 0 16px;
    display: flex;
    flex-direction:column;


    .centerTopTitle {
      position: relative;
      font-size: 40px;
      font-family: PangMenZhengDao;
      font-weight: 550;
      color: #fff;
      text-align: center;

      .centerTopTitleContent {
        position: absolute;
        font-size: 25px;
        width: 100%;
        text-align: center;
      }
    }

    .centerGJNumber {
      // display: flex;
      // margin-top: 30px;
      // margin-bottom: 16px;
       
      .centerGJNumberItem {
        display: flex;
        align-items: center;
        background: #071632;
        box-shadow: inset 0px 0px 8px 0px rgba(63,120,255,0.7);
        border-radius: 4px; 
        padding: 16px 12px; 

          img {
            width: 36px;
            height: 36px;
            margin-right: 10px;
          }
          .centerGJNumberItemTotal {

            .centerGJNumberTotal {
              display: block;
              font-size: 20px;
              font-family: DiGit;
              color: #FFFFFF;
            }
            .centerGJNumberTitle {
              color: #8da7d4;
            }
          }
   
       
        
      }

      .centerGJNumberContent {
        display: flex;
        height: 43px;
        line-height: 43px;

        .centerGJNumberTitle {
          margin-right: 22px;
        }

        .centerGJNumberData {
          font-size: 29px;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 62.5px;
          text-align: center;
          overflow: hidden;
        }
      }
    }
  }
  .right {
    // flex: 3;
    height: 100%;
    display: flex;
    flex-direction: column;
  
    .rightTop {
    
      // margin-bottom: 10px;
      flex: 1;
      display: flex;
      flex-direction: column;
  
      :global {
        .ant-form-item label {
          color: rgba(255, 255, 255, 0.5);
          font-size: 12px;
        }
  
        .ant-form-item {
          margin-bottom: 0px;
        }
       
  
        .ant-table-tbody>tr.ant-table-row:hover>td {
          background: none !important;
        }
  
        .ant-empty-normal {
          display: none;
        }
  
        .ant-select-selection {
          font-size: 12px;
          height: 18px;
          margin-top: 11px;
          border-radius: 2px;
          border: 1px solid #3C4760;
          background: #24314B;
          color: rgba(255, 255, 255, 0.8);
        }
        .ant-input {
          line-height: 12px;
          font-size: 12px;
          height: 20px;
          margin-top: 11px;
          border-radius: 2px;
          border: 1px solid #3C4760;
          background: #24314B;
          color: rgba(255, 255, 255, 0.7);
        }
        .ant-select-selection__rendered {
          line-height: 20px;
        }

        .ant-empty-description {
          color: rgba(255, 255, 255, 0.8);
        }

        
  
    
  
        .anticon {
          color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item-active,
        .ant-pagination-item-active:hover {
          border-color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item {
          a {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-container {
          .anticon {
            color: rgba(255, 255, 255, 0.8);
          }
  
          .ant-pagination-item-ellipsis {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-active {
          background: rgb(35, 40, 51);
        }
      }
    }
  
    .rightTopIsFullScreen {
      width: 290px;
      height: 238px;
      float: right;
      margin-bottom: 10px;
  
      :global {
        .ant-form-item label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
        }
  
        .ant-form-item {
          margin-bottom: 0px;
        }
  
        .ant-table-tbody>tr.ant-table-row:hover>td {
          background: none !important;
        }
  
        .ant-empty-normal {
          display: none;
        }
  
        .ant-select-selection {
          font-size: 12px;
          height: 20px;
          margin-top: 11px;
        }
  
        .ant-select-selection__rendered {
          line-height: 20px;
        }
  
        .ant-input {
          margin-top: 11px;
          height: 20px;
          font-size: 12px;
          line-height: 12px;
        }
  
        .ant-table-column-title {
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          display: inline-block;
        }
  
        .ant-table-placeholder {
          background: rgb(35, 40, 51);
          
          display: none;
        }
  
        .ant-table-body {
          background: rgb(53, 61, 77);
          margin: 0px !important;
        }
  
        .ant-empty-description {
          color: rgba(255, 255, 255, 0.8);
        }
  
        
  
        .ant-table-small {
          border-radius: 0;
          
        }
  
        .ant-table-bordered .ant-table-thead>tr>th {
          border-right: 1px solid rgb(121, 121, 121);
        }
  
  
        .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {
  
          color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-table-tbody {
          display: block;
          color: #fff;
          text-align: center;
          height: 247px;
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
        }
  
        .ant-table-thead>tr,
        .ant-table-tbody>tr {
          display: table;
          width: 100%;
          table-layout: fixed;
        }
  
        .ant-table-thead>tr {
          border: none;
        }
  
        table tbody::-webkit-scrollbar {
          width: 0px;
        }
  
        
  
        .ant-table-thead>tr:first-child>th:first-child {
          border-radius: 0px;
        }
  
        
  
        .anticon {
          color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item-active,
        .ant-pagination-item-active:hover {
          border-color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item {
          a {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-container {
          .anticon {
            color: rgba(255, 255, 255, 0.8);
          }
  
          .ant-pagination-item-ellipsis {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-active {
          background: rgb(35, 40, 51);
        }
      }
    }
  
    .rightBottom {
      // width: 290px;
      // height: 264px;
      // float: right;
      
    }
    :global {
      .ant-table-small > .ant-table-content {
        .ant-table-scroll {
          background: none;
          .ant-table-hide-scrollbar {
              margin-bottom: -20px !important;
          }
        }
      }
    }
  }
}



#ScrollBoard {
  
  :global {
    .ant-col :nth-child(5) div {
      border-right: 0px;
    }
  }
}

.commonBox {
  background: linear-gradient(41deg, rgba(0,0,0,0) 69%, #18243D 100%);
  border-radius: 4px;
  box-shadow: #0d2a4c 0px 0px 1px 2px ;
  padding: 8px;
  margin-bottom: 16px;
  box-sizing: border-box;
}
.commonFormBox {
 :global {
  .ant-form-item label {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
  }
  .ant-select-selection {
    font-size: 12px;
    height: 18px;
    margin-top: 11px;
    border-radius: 2px;
    border: 1px solid #3C4760;
    background: #24314B;
    color: rgba(255, 255, 255, 0.8);
  }
  .ant-input {
    line-height: 12px;
    font-size: 12px;
    height: 20px;
    // margin-top: 11px;
    border-radius: 2px;
    border: 1px solid #3C4760;
    background: #24314B;
    color: rgba(255, 255, 255, 0.7);
  }
 } 
}

.leftTop, .leftCenter, .leftBottom {
  width: 100%;
  height: calc((100% - 48px) / 3);  
}

.leftBottom {
  display: flex;
  flex-direction: column;
  .leftBottomContent {
    flex: 1;
  }
}

.GJ {
  // border-bottom: 1px solid rgba(255, 255, 255, 0.3);

  .tabTitleBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .tabTitleLeft {
      display: flex;
      align-items: center;
      .activeTab {
        display: flex;
        align-items: center;
        color: #B4B7C3;
        font-size: 14px;
        margin-right: 6px;
        &::before {
          content: '';
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #54A6FF;
          box-shadow: 0px 0px 6px 0px rgba(80,177,255,0.71);
          margin-right: 6px;
        }
      }
      .tool {

        width: 16px;
        height: 16px;
        background-image: url(./imgs/menu.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: relative;
        cursor: pointer;
        &:hover {
          background-image: url(./imgs/menuHover.png);
          .tabBox {
            display: block;
          };
        }
        .tabBox {
          width: 134px;
          z-index: 999;
          background: rgba(40,53,79,0.9);
          position: absolute;
          top: 0px;
          left: 16px;
          display: none;

          .tabItem {
            text-align: center;
            padding: 0 4px;
            height: 22px;
            line-height: 22px;
            font-size: 12px;
            color: #B4B7C3;
            border-bottom: 1px solid rgba(190,190,190,0.2);
            &:hover {
              color: #fff;
            }
            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}

.centerCenter {
  height: 41%;
  width: 100%;
  margin-bottom: 16px;


  :global {
    

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection__rendered {
      line-height: 16px;
    }

  
    .ant-table-tbody {
      &>.ant-table-row {
        &>td {
          padding: 4px !important;
        }
      }
      tr {
        background: transparent !important;
      }
      td {
        
        &:first-child {
          position: relative;
          
          // background: red;
        }
      }
    }
  }
}


.centerBottom {
  flex: 1;
}


.modalTableBox {
  width: 100%;
  :global {
    .ant-form-item label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-table-tbody>tr.ant-table-row:hover>td {
      background: none !important;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection {
      font-size: 12px;
      height: 20px;
      margin-top: 11px;
    }

    .ant-select-selection__rendered {
      line-height: 20px;
    }

    .ant-input {
      margin-top: 11px;
      height: 20px;
      font-size: 12px;
      line-height: 12px;
    }

    .ant-table-column-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-table-placeholder {
      background: rgb(35, 40, 51);
      border: 1px solid rgb(121, 121, 121);
      display: none;
    }

    .ant-table-body {
      background: rgb(53, 61, 77);
      margin: 0px !important;
    }

    .ant-empty-description {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-bordered .ant-table-tbody>tr>td {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-small {
      border-radius: 0;
      border: 1px solid rgb(121, 121, 121);
    }

    .ant-table-bordered .ant-table-thead>tr>th {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-align-center {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-tbody {
      display: block;
      color: #fff;
      text-align: center;
      height: 90px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .ant-table-thead>tr,
    .ant-table-tbody>tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .ant-table-thead>tr {
      border: none;
    }

    table tbody::-webkit-scrollbar {
      width: 0px;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-thead>tr:first-child>th:first-child {
      border-radius: 0px;
    }

    .ant-table-small.ant-table-bordered .ant-table-content {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .anticon {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item-active,
    .ant-pagination-item-active:hover {
      border-color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item {
      a {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-container {
      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-ellipsis {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-active {
      background: rgb(35, 40, 51);
    }
  }
}

.MoadlTable {
  :global {
    .ant-table-body {

      colgroup {
        display: flex;
        width: 949px;
      }
    }

    .ant-table-tbody {
      min-height: 400px;
      overflow-y: auto;
      

    }
  }
 

}

.flowPath {
  width: 30%;
  height: 40px;
  background: rgb(4, 25, 54);
  line-height: 40px;
  text-align: center;
  border: 1px solid rgb(255, 255, 255, 0.7);
  font-size: 12px;
}

.TooltipClass {
  :global {
    .ant-tooltip-arrow::before {
      background: rgb(204, 204, 204);
    }

    .ant-tooltip-inner {
      color: #000;
      font-size: 12px;
      background: rgb(204, 204, 204);
    }
  }
}

.Form {
  width: 100%;

  :global {
    .ant-form-item-label {
      label {
        color: #FFF
      }
    }
  }
}

