
@font-face{
    font-family:PingFang;
    src: url('./font/PingFangMedium.woff2');
}
@font-face{
    font-family:DiGit;
    src: url('./font/DS-DIGIT.TTF');
}

.searchBtn {
display: inline-block;
line-height: 18px;
height: 18px;
background: #0265CC;
border-radius: 9px;
padding: 0 6px;
color: rgba(255,255,255, 0.7);
font-size: 12px;
margin-right: 4px;
cursor: pointer;
}

.ghostBtn {
    display: inline-block;
    line-height: 18px;
    height: 18px;
    padding: 0 6px;
    border-radius: 9px;
    border: 1px solid #298FFB;
    font-size: 12px;
    color: #298FFB;
    cursor: pointer;


}

.commonBox {
    background: linear-gradient(41deg, rgba(0,0,0,0) 69%, #18243D 100%);
    border-radius: 4px;
    box-shadow: #0d2a4c 0px 0px 1px 2px ;
    padding: 8px;
    margin-bottom: 16px;
    box-sizing: border-box;
}

.commonFormBox {
    :global {
        .anticon {
            color: rgba(255, 255, 255, 0.5);
          }
        .ant-form-item label {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }
        .ant-select-selection {
            font-size: 12px;
            height: 18px;
            margin-top: 11px;
            border-radius: 2px;
            border: 1px solid #3C4760;
            background: #24314B;
            color: rgba(255, 255, 255, 0.8);
        }
        .ant-input {
            line-height: 12px;
            font-size: 12px;
            height: 20px;
            // margin-top: 11px;
            border-radius: 2px;
            border: 1px solid #3C4760;
            background: #24314B;
            color: rgba(255, 255, 255, 0.7);
        }
    } 
}




.primaryStateTag {
    display: inline-block;
    padding: 0 8px;
    line-height: 18px;
    height: 18px;
    background: #223767;
    border-radius: 2px;
    color: #629DFF;
    font-size: 12px;
}

.errorStateTag {
    display: inline-block;

    padding: 0 8px;
    line-height: 18px;
    height: 18px;
    background: rgba(165,14,67,0.65);
    border-radius: 2px;
    color: #FF0850;    
    font-size: 12px;
}

.commonBox {
    background: linear-gradient(41deg, rgba(0,0,0,0) 69%, #18243D 100%);
    border-radius: 4px;
    box-shadow: #0d2a4c 0px 0px 1px 2px ;
    padding: 8px;
    margin-bottom: 16px;
    box-sizing: border-box;
  }

  .tableCol {
    display: flex;
    align-items: center;
    justify-content: center;
    // height: 42px;
    width: 100%;
    line-height: 16px;
    text-align: center;
    overflow: hidden; //超出文本隐藏
    text-overflow: ellipsis; ///超出部分省略号显示
    display: -webkit-box; //弹性盒模型
    -webkit-line-clamp: 2; //自定义行数
    /* autoprefixer: off */ 
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    font-size: 12px;
    vertical-align: 'middle';
  
    .optBox {
      display: inline-block;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background: url(./imgs/setting.png);
      background-size: 100%;
      background-repeat: no-repeat;
    
      &:hover {
        background-image: url(./imgs/settingActive.png);
        
      }
    }
  }

  