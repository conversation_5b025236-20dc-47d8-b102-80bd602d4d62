import React, { useEffect, useState, useCallback, useRef } from 'react';

import MainTitle from './components/MainTitle';
import ConfigModal from './components/configModal';
import { Form, Row, Col, Select, DatePicker } from 'antd';

import Left from './components/Left';
import Center from './components/Center';
import Right from './components/Right';
import styles from './index.less';

import Context from './context';

function index(props) {
  const [isFullScreen, setisFullScreen] = useState(false);

  const containerRef = useRef();

  const toFullScreen = () => {
    setisFullScreen(true);

    const elem = document.getElementById('indexWrapper');
    // const elem = document.documentElement;
    if (elem.webkitRequestFullScreen) {
      elem.webkitRequestFullScreen();
    } else if (elem.mozRequestFullScreen) {
      elem.mozRequestFullScreen();
    } else if (elem.requestFullScreen) {
      elem.requestFullscreen();
    } else {
      // 浏览器不支持全屏API或已被禁用
    }
  };
  useEffect(() => {
    function onKeyup(e) {
      if (e.key == 'Escape') {
        exitFullscreen();
      }
    }
    window.addEventListener('keyup', onKeyup);
    return () => window.removeEventListener('keyup', onKeyup);
  }, [isFullScreen]);

  const exitFullscreen = () => {
    setisFullScreen(false);
    const elem = document;
    if (elem.webkitCancelFullScreen) {
      elem.webkitCancelFullScreen();
    } else if (elem.mozCancelFullScreen) {
      elem.mozCancelFullScreen();
    } else if (elem.cancelFullScreen) {
      elem.cancelFullScreen();
    } else if (elem.exitFullscreen) {
      elem.exitFullscreen();
    } else {
      // 浏览器不支持全屏API或已被禁用
    }
  };

  const handleScreen = () => {
    if (isFullScreen) {
      exitFullscreen();
    } else {
      toFullScreen();
    }
  };
  return (
    <div id="indexWrapper" className={styles.container} ref={containerRef}>
      <Context.Provider
        value={{
          isFullScreen,
          containerRef,
        }}
      >
        <MainTitle fullScreenClick={handleScreen} />

        <div className={styles.main}>
          <Row style={{ display: 'flex', height: '100%' }}>
            <Col span={7}>
              <Left isFullScreen={isFullScreen} />
            </Col>
            <Col span={10} style={{ height: '100%' }}>
              <Center isFullScreen={isFullScreen} />
            </Col>

            <Col span={7} style={{ height: '100%' }}>
              <Right isFullScreen={isFullScreen} />
            </Col>
          </Row>
        </div>
      </Context.Provider>
    </div>
  );
}
export default Form.create()(index);
