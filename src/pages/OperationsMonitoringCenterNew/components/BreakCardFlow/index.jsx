import React, { useEffect, useState } from 'react';
import styles from './index.less';
import flow1 from './imgs/flow1.png';
import errorFlow1 from './imgs/errorFlow1.png';
import flow2 from './imgs/flow2.png';
import errorFlow2 from './imgs/errorFlow2.png';
import flow3 from './imgs/flow3.png';
import errorFlow3 from './imgs/errorFlow3.png';
import flow4 from './imgs/flow4.png';
import errorFlow4 from './imgs/errorFlow4.png';
import flow5 from './imgs/flow5.png';
import errorFlow5 from './imgs/errorFlow5.png';
import { message } from 'antd';

import request from '@/utils/request';
import moment from 'moment';

const Index = ({ rightTime }) => {
  const flowList = [
    {
      title: '断卡数据入库',
      info: [
        {
          title: 'CRM量',
          key: 'crmCount',
        },
        {
          title: '大数据量',
          key: 'bigDataCount',
        },
        {
          title: '是否一致',
          key: 'ifSame',
        },
        {
          title: '检测时间',
          key: 'dataLoadingTime',
        },
      ],
      isErrorKey: 'ifCrmException',
      img: flow1,
      errorImg: errorFlow1,
    },
    {
      title: 'CRM复核',
      info: [
        {
          title: '复核数据量',
          key: 'checkCount',
        },
        {
          title: '检测时间',
          key: 'checkTime',
        },
      ],
      isErrorKey: 'ifCheckException',
      img: flow2,
      errorImg: errorFlow2,
    },
    {
      title: '断卡数据整合',
      info: [
        {
          title: '认证通过量',
          key: 'authenticationPassCount',
        },
        {
          title: '检测时间',
          key: 'authenticationTime',
        },
      ],
      isErrorKey: 'ifAuthenticationException',
      img: flow3,
      errorImg: errorFlow3,
    },

    {
      title: '断卡数据上传',
      info: [
        {
          title: '是否上传',
          key: 'ifUpload',
        },
        {
          title: '检测时间',
          key: 'uploadTime',
        },
      ],
      isErrorKey: 'ifUploadException',
      img: flow4,
      errorImg: errorFlow4,
    },
    {
      title: '集团断卡关停量',
      info: [
        {
          title: '关停量',
          key: 'shutdownCount',
        },
        {
          title: '检测时间',
          key: 'shutdownTime',
        },
      ],
      isErrorKey: 'ifShutdownException',
      img: flow5,
      errorImg: errorFlow5,
    },
  ];
  const [flowInfo, setFlowInfo] = useState({});

  useEffect(() => {
    getBreakCardFlowData();
  }, [rightTime]);

  const getBreakCardFlowData = () => {
    request('/api/hn/monitor/getMonitorBreakCardData', {
      method: 'GET',
      params: {
        date: rightTime,
      },
    }).then((res) => {
      if (res?.code == 200) {
        setFlowInfo(res.data);
      } else {
        message.error(res.message);
      }
    });
  };
  return (
    <div className={styles.breakCardFlowWrapper}>
      <div className={styles.flowWrapper}>
        {flowList.map((ele, index) => (
          <div className={styles.flowBox}>
            <img src={flowInfo?.[ele.isErrorKey] ? ele.errorImg : ele.img} />
            <div className={styles.flowTitleBox}>
              {ele.title}
              <div className={styles.flowInfoBox}>
                {ele.info.map((ele) => (
                  <div className={styles.flowInfoItem}>
                    <span className={styles.itemTitle}>{ele.title}：</span>
                    <span className={styles.itemValue}>{flowInfo?.[ele.key]}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Index;
