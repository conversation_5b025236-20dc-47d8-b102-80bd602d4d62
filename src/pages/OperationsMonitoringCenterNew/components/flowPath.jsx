import React, { useEffect, useState } from 'react';
import { Icon, Row, Tooltip } from 'antd';
import styles from '../index.less';
import right from './icon/right.png';
import rightLang from './icon/rightLang.png';
import bottomLang from './icon/bottomLang.png';
import request from '@/utils/request';
import moment from 'moment';
function NewTooltip(props) {
  const { children, RightTime } = props;
  return (
    <Tooltip
      placement={props?.placement || 'bottomLeft'}
      arrowPointAtCenter={true}
      overlayClassName={styles.TooltipClass}
      getPopupContainer={() => document.getElementById('indexWrapper')}
      title={props?.title}
    >
      {children}
    </Tooltip>
  );
}
export default function flowPath(props) {
  const { isFullScreen, letfTime, RightTime } = props;
  const [MonitorCloseGroupData, setMonitorCloseGroupData] = useState({});
  useEffect(() => {
    getMonitorCloseGroupData();
  }, []);
  useEffect(() => {
    getMonitorCloseGroupData();
  }, [letfTime, props?.type]);
  const getMonitorCloseGroupData = () => {
    request(
      props?.type == '集团渠道关停号码同步'
        ? 'api/hn/monitor/getMonitorCloseGroupData'
        : `api/hn/monitor/getMonitorWhiteGroupData`,
      {
        method: 'POST',
        data: {
          time: moment(letfTime).format('YYYY-MM-DD 00:00:00'),
        },
        requestType: 'form',
      },
    ).then((res) => {
      if (res?.code == 200) {
        let obj = {};
        for (let key in res?.data) {
          obj[key] = `${res?.data[key]}`;
        }
        setMonitorCloseGroupData(obj);
      } else {
        setMonitorCloseGroupData({});
      }
    });
  };
  if (props?.type == 'duanka') {
    return (
      <div style={{ padding: '37px 10px 0px' }}>
        <div style={{ width: '100%', display: 'flex' }}>
          <NewTooltip
            title={
              <div>
                <div>CRM量：333</div>
                <div>大数据量：333</div>
                <div>是否一只：是</div>
                <div>检测时间：{RightTime}</div>
              </div>
            }
          >
            <div style={{ background: 'red' }} className={styles.flowPath}>
              断卡数据入库
            </div>
          </NewTooltip>

          <img src={right} style={{ width: '5%', height: '35px' }} />
          <NewTooltip
            title={
              <div>
                <div>复核数据量：222</div>
                <div>检测时间：{RightTime}</div>
              </div>
            }
          >
            <div className={styles.flowPath}>CRM复核</div>
          </NewTooltip>

          <img src={right} style={{ width: '5%', height: '35px' }} />
          <NewTooltip
            title={
              <div>
                <div>认证通过量：333</div>
                <div>检测时间：{RightTime}</div>
              </div>
            }
          >
            <div className={styles.flowPath}>断卡数据整合</div>
          </NewTooltip>
        </div>

        <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
          <img
            src={bottomLang}
            style={{ width: '80px', height: isFullScreen ? '165px' : '93px' }}
          />
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between', position: 'relative' }}>
          <NewTooltip
            title={
              <div>
                <div>关停量：333</div>
                <div>检测时间：{RightTime}</div>
              </div>
            }
          >
            <div style={{ width: '33%' }} className={styles.flowPath}>
              集团断卡关停量
            </div>
          </NewTooltip>

          <img
            src={rightLang}
            style={{
              width: isFullScreen ? '156px' : '98px',
              height: '94px',
              position: 'absolute',
              left: isFullScreen ? '133px' : '90px',
              top: '-30px',
            }}
          />
          <NewTooltip
            title={
              <div>
                <div>是否上传：是</div>
                <div>检测时间：{RightTime}</div>
              </div>
            }
          >
            <div className={styles.flowPath}>断卡数据上传</div>
          </NewTooltip>
        </div>
      </div>
    );
  }
  if (props?.type == '集团渠道关停号码同步') {
    return (
      <div style={{ padding: '10px 10px 0px' }}>
        <div style={{ width: '100%', display: 'flex' }}>
          <NewTooltip
            title={
              <div>
                <div>采集文件数量：{MonitorCloseGroupData?.fileCount || '--'}</div>
                <div>监测时间：{MonitorCloseGroupData?.monitorDate || '--'}</div>
              </div>
            }
          >
            <div
              style={MonitorCloseGroupData?.fileState == 0 ? { background: 'red' } : {}}
              className={styles.flowPath}
            >
              文件采集
            </div>
          </NewTooltip>
          <img src={right} style={{ width: '5%', height: '35px' }} />
          <NewTooltip
            title={
              <div>
                <div>集团渠道关停采集号码数：{MonitorCloseGroupData?.phoneCount || '--'}</div>
                <div>监测时间：{MonitorCloseGroupData?.monitorDate || '--'}</div>
              </div>
            }
          >
            <div
              style={MonitorCloseGroupData?.phoneState == 0 ? { background: 'red' } : {}}
              className={styles.flowPath}
            >
              数据入库
            </div>
          </NewTooltip>
          <img src={right} style={{ width: '5%', height: '35px' }} />
          <NewTooltip
            title={
              <div>
                <div>调用成功次数：{MonitorCloseGroupData?.crmSuccessCount || '--'}</div>
                <div>调用失败次数：{MonitorCloseGroupData?.crmFailCount || '--'}</div>
                <div>监测时间：{MonitorCloseGroupData?.monitorDate || '--'}</div>
              </div>
            }
          >
            <div
              style={MonitorCloseGroupData?.crmState == 0 ? { background: 'red' } : {}}
              className={styles.flowPath}
            >
              CRM接口调用
            </div>
          </NewTooltip>
        </div>
      </div>
    );
  }
  if (props?.type == '集团白名单流程') {
    return (
      <div style={{ padding: '10px 10px 0px' }}>
        <div style={{ width: '100%', display: 'flex' }}>
          <NewTooltip
            title={
              <div>
                <div>文件数量：{MonitorCloseGroupData?.fileCount || '--'}</div>
                <div>采集号码数：{MonitorCloseGroupData?.phoneCount || '--'}</div>
                <div>监测时间：{MonitorCloseGroupData?.monitorDate || '--'}</div>
              </div>
            }
          >
            <div
              style={MonitorCloseGroupData?.fileState == 0 ? { background: 'red' } : {}}
              className={styles.flowPath}
            >
              集团白名单
            </div>
          </NewTooltip>
          <img src={right} style={{ width: '5%', height: '35px' }} />
          <NewTooltip
            title={
              <div>
                <div>成功数量：{MonitorCloseGroupData?.crmSuccessCount || '--'}</div>
                <div>失败数量：{MonitorCloseGroupData?.crmFailCount || '--'}</div>
                <div>监测时间：{MonitorCloseGroupData?.monitorDate || '--'}</div>
              </div>
            }
          >
            <div
              style={MonitorCloseGroupData?.crmState == 0 ? { background: 'red' } : {}}
              className={styles.flowPath}
            >
              白名单传CRM
            </div>
          </NewTooltip>
          <img src={right} style={{ width: '5%', height: '35px' }} />
          <NewTooltip
            title={
              <div>
                <div>入库数量：{MonitorCloseGroupData?.phoneDbCount || '--'}</div>
                <div>监测时间：{MonitorCloseGroupData?.monitorDate || '--'}</div>
              </div>
            }
          >
            <div
              style={MonitorCloseGroupData?.phoneState == 0 ? { background: 'red' } : {}}
              className={styles.flowPath}
            >
              白名单入库
            </div>
          </NewTooltip>
        </div>
        <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start' }}>
          <img src={bottomLang} style={{ width: '80px', height: isFullScreen ? '86px' : '28px' }} />
        </div>
        <div style={{}}>
          <NewTooltip
            title={
              <div>
                <div>
                  是否报错：
                  {MonitorCloseGroupData?.backState
                    ? MonitorCloseGroupData?.backState == 1
                      ? '是'
                      : '否'
                    : '--'}
                </div>
                <div>监测时间：{MonitorCloseGroupData?.monitorDate || '--'}</div>
              </div>
            }
          >
            <div
              style={MonitorCloseGroupData?.backState == 0 ? { background: 'red' } : {}}
              className={styles.flowPath}
            >
              采集结果回传
            </div>
          </NewTooltip>
        </div>
      </div>
    );
  }
}
