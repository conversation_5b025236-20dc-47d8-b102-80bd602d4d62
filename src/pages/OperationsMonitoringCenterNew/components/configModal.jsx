import React, { Fragment, useContext, useEffect, useState } from 'react';
import {
  Input,
  TextArea,
  Select,
  Modal,
  Row,
  Form,
  Col,
  Upload,
  Button,
  Icon,
  message,
  Table,
  Tag,
} from 'antd';
import Context from '../context';

import styles from '../index.less';
import {
  getMonitorModelConfig,
  getMonitorModelDataDropDown,
  saveAndUpdateModelConfig,
  deleteModelConfig,
} from '../components/ModelMonitor/services';
import { handleCommonResponse, exportFile } from '@/utils/utils';
import { startMessage } from '../utils';
import request from 'ponshine-request';

const detectionTimeList = [];
for (let i = 1; i < 25; i++) {
  detectionTimeList.push(String(i));
}
const Index = (props) => {
  const { visible, setVisible, form } = props;

  const { isFullScreen } = useContext(Context);
  const { getFieldDecorator, validateFields, resetFields, setFieldsValue, getFieldValue } = form;
  const [tableData, setTableData] = useState([]);
  const [modelList, setModelList] = useState([]);
  const [specialModelList, setSpecialModelList] = useState([]);
  const [currentRow, setCurrentRow] = useState({});

  const findTableDataPager = async ({ pageSize = 10, current = 1, ...props } = {}) => {
    const response = await getMonitorModelConfig();
    handleCommonResponse(
      response,
      () => {
        setTableData(response?.data || []);
      },
      false,
    );
  };

  const handleEdit = (row) => {
    setCurrentRow(row);
    setconfigvisible(true);
  };

  const handleDelete = (row) => {
    Modal.confirm({
      title: '模型监控任务删除',
      content: '是否确定删除该条模型监控?',
      getContainer: () => document.getElementById('indexWrapper'),
      onOk: async () => {
        const response = await deleteModelConfig({ id: row.id });
        handleCommonResponse(response, () => {
          findTableDataPager();
        });
      },
    });
  };
  const columns = [
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      width: 100,
      render: (value, row) => {
        return (
          <>
            <Button type="link" icon="edit" onClick={() => handleEdit(row)}></Button>
            <Button type="link" icon="delete" onClick={() => handleDelete(row)}></Button>
          </>
        );
      },
    },
    {
      title: '监控模型',
      dataIndex: 'monitorModel',
      key: 'monitorModel',
      align: 'center',
      width: 150,
    },
    {
      title: '告警级别',
      dataIndex: 'alarmLevel',
      key: 'alarmLevel',
      align: 'center',
      width: 80,
    },
    {
      title: '检测时间',
      dataIndex: 'detectionTime',
      key: 'detectionTime',
      align: 'center',
      width: 120,
    },
    {
      title: '阈值下限',
      dataIndex: 'lowerThreshold',
      key: 'lowerThreshold',
      align: 'center',
      width: 100,
    },
    {
      title: '阈值上限',
      dataIndex: 'upperThreshold',
      key: 'upperThreshold',
      align: 'center',
      width: 100,
    },

    {
      title: '是否连续三次告警升级提醒',
      dataIndex: 'threeWarnings',
      key: 'threeWarnings',
      align: 'center',
      width: 100,
      render: (text) => {
        return text ? '是' : '否';
      },
    },
    {
      title: '责任人',
      dataIndex: 'personResponsible',
      key: 'personResponsible',
      align: 'center',
      width: 100,
    },
    {
      title: '联系方式',
      dataIndex: 'contactInformation',
      key: 'contactInformation',
      align: 'center',
      width: 120,
    },
    {
      title: '配置人',
      dataIndex: 'configuredBy',
      key: 'configuredBy',
      align: 'center',
      width: 120,
    },
    {
      title: '配置时间',
      dataIndex: 'configurationTime',
      key: 'configurationTime',
      align: 'center',
      width: 120,
    },
  ];
  const [configvisible, setconfigvisible] = useState(false);

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/monitor/exportMonitorModelConfig',
      decode: true,
      method: 'POST',
    });
  };

  const findModelList = async () => {
    const response = await getMonitorModelDataDropDown();
    handleCommonResponse(
      response,
      () => {
        setModelList(response?.data || []);
      },
      false,
    );
  };

  const bigScreenMessage = (tip) => {
    message.destroy();
    message.config({
      getContainer: () => document.getElementById('indexWrapper'), //父组件元素
    });
    message.warning(tip);
  };

  const handleConfirm = () => {
    validateFields(async (err, values) => {
      if (err) return;
      const { upperThreshold, lowerThreshold } = values;

      // 都没有填写
      if (!lowerThreshold && !upperThreshold) {
        if (isFullScreen) {
          return bigScreenMessage('阈值上限、下限必须至少填写一个');
        }
        return message.warning('阈值上限、下限必须至少填写一个');
      }

      if (upperThreshold || lowerThreshold) {
        const maxNum = 100000000;
        if (Number(lowerThreshold) > maxNum || Number(upperThreshold) > maxNum) {
          if (isFullScreen) {
            return bigScreenMessage('阈值上限、下限不能超过100000000');
          }
          return message.warning('阈值上限、下限不能超过100000000');
        }
      }

      // 俩个都填
      if (lowerThreshold && upperThreshold) {
        if (Number(upperThreshold) <= Number(lowerThreshold)) {
          if (isFullScreen) {
            return bigScreenMessage('阈值上限要求大于下限');
          }
          return message.warning('阈值上限要求大于下限');
        }
      }
      // values.upperThreshold = (values.upperThreshold && Number(values.upperThreshold)) || undefined;
      // values.lowerThreshold = values.lowerThreshold ? Number(values.lowerThreshold) : undefined;
      values.saveOrUpdate = currentRow?.id ? 1 : 0;
      values.id = currentRow?.id;
      const response = await saveAndUpdateModelConfig(values);
      if (response.code === 200) {
        startMessage(response.message);
        setconfigvisible(false);
        currentRow?.id && setCurrentRow({});
        findTableDataPager();
      } else {
        startMessage(response.message, 'error');
      }
    });
  };

  const getmonitorModelData = async () => {
    const response = await request.get('/api/hn/monitor/getAllModel');
    handleCommonResponse(
      response,
      () => {
        setSpecialModelList(response?.data || []);
      },
      false,
    );
  };

  const handleChangeModalType = (v) => {
    setFieldsValue({
      monitorModel: undefined,
      modelCode: undefined,
      upperThreshold: '',
      lowerThreshold: '',
    });
    if (v) {
      if (v === '模型推送量') {
        setFieldsValue({
          lowerThreshold: 0,
        });
      }
      getmonitorModelData();
    }
  };

  const handleChangeModelCode = async (v) => {
    // 确保 specialModelList 中有数据
    const model = specialModelList?.find((ele) => ele.modelCode === v);
    if (model) {
      setFieldsValue({
        monitorModel: model.modelName,
      });
    }

    if (getFieldValue('modelType') === '模型关停量' && v) {
      const response = await request.get('/api/hn/monitor/getModelByCode', {
        params: {
          modelCode: v,
        },
      });
      handleCommonResponse(
        response,
        () => {
          setFieldsValue({
            upperThreshold: response?.data?.monitorThreshold,
            lowerThreshold: 0,
          });
        },
        false,
      );
    }
  };

  useEffect(() => {
    if (visible) {
      findTableDataPager();
    }
  }, [visible]);

  useEffect(() => {
    if (configvisible && currentRow?.id) {
      if (currentRow?.modelType) {
        // 先获取模型数据，再设置表单值
        getmonitorModelData().then(() => {
          form.setFieldsValue({
            modelType: currentRow?.modelType,
            modelCode: currentRow?.modelCode,
            monitorModel: currentRow?.monitorModel,
          });
        });
      }
    }
  }, [configvisible, currentRow]);

  return (
    <>
      <Modal
        wrapClassName="speicalModal"
        title={''}
        visible={visible}
        width={1000}
        style={{ background: 'rgb(46,54,68)' }}
        footer={null}
        height={500}
        onCancel={() => {
          setVisible();
        }}
        centered={true}
        getContainer={document.getElementById('indexWrapper')}
      >
        <div style={{ display: 'flex', color: '#FFF', alignItems: 'center', marginBottom: '10px' }}>
          <div> 模型监控配置</div>
          <div style={{ fontSize: '10px', marginLeft: '10px' }}>ps：当日配置后次小时生效</div>
          <Tag
            style={{ marginLeft: '10px' }}
            color="rgb(0,153,255)"
            onClick={() => {
              setconfigvisible(true);
              findModelList();
            }}
          >
            新增
          </Tag>
          <Tag style={{ marginLeft: '10px' }} color="rgb(0,153,255)" onClick={handleExport}>
            导出
          </Tag>
        </div>
        <div className={styles.modalTableBox}>
          <Table
            className={styles.MoadlTable}
            bordered={true}
            pagination={false}
            columns={columns}
            size="small"
            dataSource={tableData}
            // scroll={{ y: 300 }}
          ></Table>
        </div>
      </Modal>
      <Modal
        wrapClassName="speicalModal"
        title={'模拟监控配置'}
        visible={configvisible}
        width={1000}
        style={{ background: 'rgb(46,54,68)' }}
        bodyStyle={{ height: '300px', overflowY: 'auto' }}
        afterClose={() => {
          resetFields();
        }}
        onOk={handleConfirm}
        height={300}
        onCancel={() => {
          setconfigvisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        centered={true}
        getContainer={document.getElementById('indexWrapper')}
        maskClosable={false}
      >
        <div className={styles.Form}>
          <Form labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} layout={'horizontal'}>
            <Row>
              <Col span={12}>
                <Row>
                  <Col span={24}>
                    <Form.Item label={'监控类型'}>
                      {getFieldDecorator('modelType', {
                        initialValue: currentRow?.modelType,
                      })(
                        <Select
                          placeholder="请选择"
                          allowClear
                          disabled={currentRow?.id}
                          getPopupContainer={() => document.getElementById('indexWrapper')}
                          onChange={handleChangeModalType}
                        >
                          {['模型关停量', '模型推送量'].map((ele, index) => (
                            <Select.Option key={index} value={ele}>
                              {ele}
                            </Select.Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'监控模型'}>
                      {!getFieldValue('modelType') ? (
                        getFieldDecorator('monitorModel', {
                          initialValue: currentRow?.monitorModel,
                          rules: [
                            {
                              required: true,
                              message: '请选择监控模型',
                            },
                          ],
                        })(
                          <Select
                            placeholder="请选择"
                            disabled={currentRow?.id}
                            getPopupContainer={() => document.getElementById('indexWrapper')}
                          >
                            {modelList?.map((ele, index) => (
                              <Select.Option key={index} value={ele}>
                                {ele}
                              </Select.Option>
                            ))}
                          </Select>,
                        )
                      ) : (
                        <Fragment>
                          {getFieldDecorator('modelCode', {
                            initialValue: currentRow?.modelCode,
                            rules: [
                              {
                                required: true,
                                message: '请选择监控模型',
                              },
                            ],
                          })(
                            <Select
                              placeholder="请选择"
                              disabled={currentRow?.id}
                              getPopupContainer={() => document.getElementById('indexWrapper')}
                              onChange={handleChangeModelCode}
                            >
                              {specialModelList?.map((ele) => (
                                <Select.Option key={ele.modelCode} value={ele.modelCode}>
                                  {ele.modelName}
                                </Select.Option>
                              ))}
                            </Select>,
                          )}
                          {getFieldDecorator('monitorModel')}
                        </Fragment>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'检测时间'}>
                      {getFieldDecorator('detectionTime', {
                        initialValue: currentRow?.detectionTime,
                        rules: [
                          {
                            required: true,
                            message: '检测时间为必填',
                          },
                        ],
                      })(
                        <Select
                          allowClear
                          placeholder="请选择"
                          // mode="multiple"
                          getPopupContainer={() => document.getElementById('indexWrapper')}
                        >
                          {['每分钟', '每五分钟', '每小时', '每天'].map((ele, index) => (
                            <Select.Option value={ele} key={index}>
                              {ele}
                            </Select.Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'责任人'}>
                      {getFieldDecorator('personResponsible', {
                        initialValue: currentRow?.personResponsible,
                        rules: [
                          {
                            required: true,
                            message: '责任人为必填',
                          },
                          {
                            max: 50,
                            message: '最多输入50个字符',
                          },
                        ],
                      })(<Input placeholder="请填写"></Input>)}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'联系方式'}>
                      {getFieldDecorator('contactInformation', {
                        initialValue: currentRow?.contactInformation,
                        rules: [
                          {
                            required: true,
                            message: '联系方式为必填',
                          },
                          {
                            max: 120,
                            message: '最多输入120个字符',
                          },
                          {
                            validator: (rule, value, callback) => {
                              var retNum = /^\d*$/; //判断是否为数字
                              if (value) {
                                const valueArr = value.split(',');
                                if (
                                  valueArr.some((item) => {
                                    return !retNum.test(item) || item?.length != 11;
                                  })
                                ) {
                                  callback('多个时用英文逗号隔开，每个值要求为11位数字符号');
                                } else {
                                  callback();
                                }
                              } else {
                                callback('');
                              }
                            },
                          },
                        ],
                      })(<Input placeholder="请填写"></Input>)}
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col span={12}>
                <Row>
                  <Col span={24}>
                    <Form.Item label={'告警级别'} placeholder="请选择">
                      {getFieldDecorator('alarmLevel', {
                        initialValue: currentRow?.alarmLevel,
                        rules: [
                          {
                            required: true,
                            message: '告警级别为必选',
                          },
                        ],
                      })(
                        <Select
                          allowClear
                          placeholder="请选择"
                          getPopupContainer={() => document.getElementById('indexWrapper')}
                        >
                          {['严重', '普通'].map((item) => {
                            return (
                              <Select.Option value={item} key={item}>
                                {item}
                              </Select.Option>
                            );
                          })}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'阈值上限'}>
                      {getFieldDecorator('upperThreshold', {
                        initialValue: currentRow?.upperThreshold,
                        rules: [
                          {
                            validator: (rule, value, callback) => {
                              const regRule = /^[1-9]+[0-9]*$/;
                              if (value && !regRule.test(value)) {
                                callback('请输入正整数');
                              } else {
                                callback();
                              }
                            },
                          },
                        ],
                      })(
                        <Input
                          placeholder="请填写"
                          disabled={getFieldValue('modelType') === '模型关停量'}
                        ></Input>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'阈值下限'}>
                      {getFieldDecorator('lowerThreshold', {
                        initialValue: currentRow?.id
                          ? currentRow?.lowerThreshold === 0 || currentRow?.lowerThreshold
                            ? String(currentRow?.lowerThreshold)
                            : currentRow?.lowerThreshold
                          : '0',
                        rules: [
                          {
                            validator: (rule, value, callback) => {
                              const regRule = /^[0-9]+$/;

                              if (value && !regRule.test(value)) {
                                callback('请输入非负整数');
                              } else {
                                callback();
                              }
                            },
                          },
                        ],
                      })(
                        <Input
                          placeholder="请填写"
                          disabled={getFieldValue('modelType') === '模型关停量'}
                        ></Input>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'是否连续三次告警升级提醒'}>
                      {getFieldDecorator('threeWarnings', {
                        initialValue: currentRow?.threeWarnings,
                        rules: [
                          {
                            required: true,
                            message: '是否连续三次告警升级提醒为必选',
                          },
                        ],
                      })(
                        <Select
                          allowClear
                          placeholder="请选择"
                          getPopupContainer={() => document.getElementById('indexWrapper')}
                        >
                          {['是', '否'].map((item) => {
                            return (
                              <Select.Option value={item === '是' ? 1 : 0} key={item}>
                                {item}
                              </Select.Option>
                            );
                          })}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </>
  );
};
export default Form.create()(Index);
