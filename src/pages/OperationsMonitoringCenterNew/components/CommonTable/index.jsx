/*
 * @Author: zxw
 * @Date: 2023-06-20 11:44:30
 * @LastEditors: zxw
 * @LastEditTime: 2023-07-03 16:55:42
 * @FilePath: \newHunanfanzha\src\pages\OperationsMonitoringCenterNew\components\CommonTable\index.jsx
 * @Description:
 */
import React, { Fragment, useEffect, useRef, useState, useContext } from 'react';
import { Table, Tooltip } from 'antd';
import styles from './index.less';
import Context from '../../context';

const Index = ({ data, onChange, columns, idStr, loading = false }) => {
  const { isFullScreen, containerRef } = useContext(Context);
  const tableRef = useRef();
  const [maxHeight, setMaxHeight] = useState(50);
  const [minHeight, setMinHeight] = useState(50);
  const newColunms = columns.map((ele) => {
    return {
      ...ele,
      render: (text, row) => {
        if (ele.render) {
          return ele.render(text, row);
        } else {
          return (
            <Tooltip title={text}>
              <span>{text}</span>
            </Tooltip>
          );
        }
      },
    };
  });

  const getHeaderHeight = () => {
    const tableHearder = document
      ?.getElementById(idStr)
      ?.getElementsByClassName('ant-table-thead')[0];
    const headerHeight = tableHearder?.getBoundingClientRect()?.height;
    return headerHeight;
  };

  const getPaginationHeight = () => {
    if (data.pagination === false) {
      return 0;
    }
    return 24;
  };

  // 解决暂未知道原因的问题
  const getSpecialTableHeight = () => {
    if (idStr === 'modalMonitorTable') {
      return 14;
    } else {
      return 0;
    }
  };

  useEffect(() => {
    setTimeout(() => {
      setMinHeight(
        tableRef?.current?.clientHeight -
          getHeaderHeight() -
          getPaginationHeight() -
          getSpecialTableHeight(),
      );
    });
  }, []);

  useEffect(() => {
    if (isFullScreen) {
      setTimeout(() => {
        setMaxHeight(tableRef?.current?.clientHeight - getHeaderHeight() - getPaginationHeight());
      }, 100);
    }
  }, [isFullScreen]);

  return (
    <div className={styles.tableWrapper} ref={tableRef} id={idStr}>
      <Table
        pagination={
          data?.pagination
            ? {
                ...data?.pagination,
                showTotal: (total, range) => {
                  return (
                    <Fragment>
                      <span style={{ color: 'rgba(146, 167, 181, 0.65)' }}>共计：</span>
                      <span style={{ color: '#298FFB' }}>{total}</span>
                    </Fragment>
                  );
                },
              }
            : false
        }
        onChange={onChange}
        columns={newColunms}
        size="small"
        dataSource={data?.list || []}
        scroll={{ y: isFullScreen ? maxHeight : minHeight }}
        loading={loading}
      ></Table>
    </div>
  );
};

export default Index;
