.tableWrapper {
    height: 100%;
    
    :global {
      
        .ant-table-small > .ant-table-content {
            // border: none;
            .ant-table-scroll {
                background: none;
                .ant-table-hide-scrollbar {
                    margin-bottom: -26px !important;
                }
                .ant-table-header {
    
                    overflow: hidden !important;
                    height: 46px;
                    .ant-table-thead {
                        &>tr {
                            border: none;
                            border-bottom: 1px solid rgba(190, 190, 190, 0.2);
                            &>th {
                                border: none;
                                padding: 0px;
                                // padding-bottom: 4px;
                                color: rgba(146,167,181,0.65);  
                                font-size: 12px; 
                                border-bottom: 1px solid rgba(190, 190, 190, 0.2);           
                            }
                        } 
                        .ant-table-column-title {
                            font-size: 12px;
                            line-height: 12px;
                            // white-space: nowrap;
                            // overflow: hidden;
                            // text-overflow: ellipsis;
                            // width: 100%;
                            // display: inline-block;
                          }
                    }
                    
                    // .ant-table-fixed {
                    //     width: 100% !important;
                    // } 
                }
                .ant-table-tbody {
                    &>tr.ant-table-row:hover>td {
                        background: none !important;
                      }
                    // height: 54px;
                    // display: block;
                    background: #050F22;
                    border: none;
                    -webkit-overflow-scrolling: touch;
                    overflow-y: auto;

                    tr {
                        &:nth-child(2n) {
                            background: rgba(80,93,123,0.18);
                        }
                    }
                    td {
                        border: none;
                        color: rgba(232,232,232,0.65);
                        font-size: 12px;
                        padding: 4px !important;
                        // padding: 0px !important;
                        line-height: 16px;
                    }
                    
                 
                
                      
                
                      
                }
               
            }
            // .ant-table-body {
            //     background: none;
            //     .ant-table-thead {
            //         &>tr {
            //             border: none;
            //             border-bottom: 1px solid rgba(190, 190, 190, 0.2);
            //             &>th {
            //                 border: none;
            //                 padding: 0px;
            //                 padding-bottom: 4px;
            //                 color: rgba(146,167,181,0.65);  
            //                 font-size: 12px; 
            //                 border-bottom: 1px solid rgba(190, 190, 190, 0.2);           
            //             }
            //         } 
            //         .ant-table-column-title {
            //             font-size: 12px;
            //             white-space: nowrap;
            //             overflow: hidden;
            //             text-overflow: ellipsis;
            //             width: 100%;
            //             // display: inline-block;
            //           }
            //     }
            //     .ant-table-tbody {
            //         // height: 54px;
            //         // // display: block;
            //         // border: none;
            //         // -webkit-overflow-scrolling: touch;
            //         // overflow-y: auto;


            //         tr {
            //             &:nth-child(2n) {
            //                 background: rgba(80,93,123,0.18);
            //             }
            //         }
            //         td {
            //             border: none;
            //             color: rgba(232,232,232,0.65);
            //             font-size: 12px;
            //             padding: 4px;
            //             line-height: 16px;
            //         }
                 
                
                      
                
                      
            //     }
                
            // }
            .ant-table-placeholder {
                background: #050F22;
                display: none;
                border: none;
            }
        
        }
        table tbody::-webkit-scrollbar {
            width: 0px;
        }
        .ant-table-scroll,.ant-table-body {
            &::-webkit-scrollbar {
                width: 0px !important;
            }
        }
        .ant-table-header {
            &::-webkit-scrollbar {
                width: 0px !important;
            }
        }
        .ant-table-wrapper {
            height: 100%;

        }
        .ant-spin-nested-loading {
            height: 100%;
            position: relative;
            .ant-spin-container {
                height: 100%;

            }
        }
        .ant-pagination {
            margin-bottom: 0px;
            position: absolute;
            bottom: 0px;
            right: 0px;
            .ant-pagination-total-text {
                font-size: 12px;
            }
            .ant-pagination-item-ellipsis {
                color: #A4A8AF;
            }
            .ant-pagination-item-link {
               color: rgba(146, 167, 181, 0.65);
            }
            .ant-pagination-item {
                height: 16px;
                width: 16px;
                line-height: 16px;
                background: #24314B !important;
                border-radius: 2px;
                border: 1px solid #3C4760;
                min-width: 16px;
                margin-left: 8px;
                &.ant-pagination-item-1 {
                    margin-left: 0px;

                }
                a { 
                    color: #A4A8AF;
                    padding: 0px;
                    font-size: 12px;
                }
                &.ant-pagination-item-active {
                    background: #0265CC !important;
                    border: none;
                    a {
                        color: #fff;
    
                    }
                }
               
            }
           
        }
    }
    
}