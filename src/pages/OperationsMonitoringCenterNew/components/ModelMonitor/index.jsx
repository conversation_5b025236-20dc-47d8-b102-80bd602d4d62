import React, { Fragment, useEffect, useState } from 'react';
import { Form, Row, Col, Input, DatePicker, Tooltip } from 'antd';
import CommonTable from '../CommonTable';
import { getMonitorModelData, getMonitorModelConfig, saveAndUpdateModelConfig } from './services';
import styles from './index.less';
import { handleCommonResponse, exportFile } from '@/utils/utils';
import moment from 'moment';

import ConfigModal from '../configModal';
import OptionModal from '../optionModal';

const { RangePicker } = DatePicker;

const Index = ({ form, isFullScreen }) => {
  const { getFieldDecorator, getFieldsValue } = form;
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
  });
  const [visible, setVisible] = useState(false);
  const [configvisible, setconfigvisible] = useState(false);
  const [row, setRow] = useState({});

  const [searchParams, setSearchParams] = useState({});
  const initialTime = [moment(), moment()];
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      title: '监控模型',
      dataIndex: 'monitorModel',
      key: 'level',
      align: 'center',
      width: 30,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text} getPopupContainer={() => document.getElementById('indexWrapper')}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      key: 'threshold',
      align: 'center',
      width: 10,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text} getPopupContainer={() => document.getElementById('indexWrapper')}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '号码量',
      dataIndex: 'phoneCount',
      key: 'phoneCount',
      align: 'center',
      width: 10,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text} getPopupContainer={() => document.getElementById('indexWrapper')}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '检测时间',
      dataIndex: 'detectionTime',
      key: 'detectionTime',
      align: 'center',
      width: 34,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text} getPopupContainer={() => document.getElementById('indexWrapper')}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '是否正常',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      width: 20,
      render: (text) => {
        return (
          <div className={text ? styles.primaryStateTag : styles.errorStateTag}>
            {text ? '正常' : '异常'}
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    findTableDataPager();
  }, []);
  const findTableDataPager = async ({
    pageSize = 10,
    current = 1,
    detectionStartTime,
    detectionEndTime,
    ...props
  } = {}) => {
    const params = {
      ...props,
      detectionStartTime: detectionStartTime || initialTime[0]?.format('YYYY-MM-DD 00:00:00'),
      detectionEndTime: detectionEndTime || initialTime[1]?.format('YYYY-MM-DD 23:59:59'),
      pageSize,
      currentPage: current,
    };
    setLoading(true);
    const response = await getMonitorModelData(params);
    setLoading(false);
    handleCommonResponse(
      response,
      () => {
        setSearchParams(params);
        setTableData({
          list: response?.data?.items || [],
          pagination: {
            total: response?.data?.totalNum || 0,
            current: params.currentPage,
            pageSize: params.pageSize,
          },
        });
      },
      false,
    );
  };

  const handleSearch = () => {
    const formValues = getFieldsValue();
    findTableDataPager({
      ...formValues,
      time: undefined,
      detectionStartTime: formValues?.time?.[0]?.format('YYYY-MM-DD 00:00:00'),
      detectionEndTime: formValues?.time?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    const { pageSize, current } = pagination;
    findTableDataPager({
      ...searchParams,
      pageSize,
      current,
    });
  };

  const handleExport = () => {
    let params = {
      ...searchParams,
      pageSize: 1,
      currentPage: 1,
    };
    exportFile({
      urlAPi: '/api/hn/monitor/exportMonitorModelData',
      decode: true,
      params: params,
      method: 'POST',
    });
  };

  const handleSearchTop = () => {};
  return (
    <div className={styles.centerBottomBox}>
      <div className={styles.commonFormBox}>
        <Form>
          <Row>
            <Col span={5}>
              <Form.Item
                labelCol={{ span: 14 }}
                wrapperCol={{ span: 10 }}
                label="监控模型"
                style={{ marginBottom: 0 }}
              >
                {getFieldDecorator('monitorModel')(<Input placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={isFullScreen ? 12 : 10}>
              <Form.Item
                labelCol={{ span: isFullScreen ? 6 : 6 }}
                wrapperCol={{ span: isFullScreen ? 18 : 18 }}
                label="检测时间"
                style={{ marginBottom: 0 }}
              >
                {getFieldDecorator('time', {
                  initialValue: initialTime,
                })(
                  <RangePicker
                    format={'YYYY-MM-DD'}
                    getCalendarContainer={() => document.getElementById('indexWrapper')}
                    allowClear={false}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={isFullScreen ? 7 : 9} style={{ textAlign: 'right' }}>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.searchBtn} onClick={handleSearch} style={{ marginRight: 4 }}>
                  查询
                </div>
                <div className={styles.ghostBtn} style={{ marginRight: 4 }} onClick={handleExport}>
                  导出
                </div>
                <div
                  className={styles.ghostBtn}
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  模型监控配置
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div className={`${styles.commonBox}`} style={{ flex: 1, paddingBottom: 0 }}>
        <CommonTable
          data={tableData}
          columns={columns}
          onChange={handleTableChange}
          scrollHeight={isFullScreen ? 132 : 100}
          keyStr="模型监控"
          idStr={'modalMonitorTable'}
          loading={loading}
        />
      </div>
      <ConfigModal
        getContainer={() => {
          document.getElementById('indexWrapper');
        }}
        visible={visible}
        setVisible={setVisible}
      ></ConfigModal>
      <OptionModal
        configvisible={configvisible}
        row={row}
        setconfigvisible={setconfigvisible}
        handleSearchTop={handleSearchTop}
      ></OptionModal>
    </div>
  );
};

export default Form.create()(Index);
