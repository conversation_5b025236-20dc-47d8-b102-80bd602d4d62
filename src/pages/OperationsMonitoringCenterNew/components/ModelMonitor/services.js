/*
 * @Author: zxw
 * @Date: 2023-06-21 16:06:07
 * @LastEditors: zxw
 * @LastEditTime: 2023-06-21 16:08:09
 * @FilePath: \newHunanfanzha\src\pages\OperationsMonitoringCenterNew\components\ModelMonitor\services.js
 * @Description:
 */
import request from '@/utils/request';

// 查询模型数据
export function getMonitorModelData(params) {
  return request('/api/hn/monitor/getMonitorModelData', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
// 获取配置信息
export function getMonitorModelConfig(params) {
  return request('/api/hn/monitor/getMonitorModelConfig', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 新增配置信息
export function saveAndUpdateModelConfig(params) {
  return request('/api/hn/monitor/saveAndUpdateModelConfig', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 删除配置信息
export function deleteModelConfig(params) {
  return request('/api/hn/monitor/deleteModelConfig', {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
// 获取模型监控下拉
export function getMonitorModelDataDropDown(params) {
  return request('/api/hn/monitor/getMonitorModelDataDropDown', {
    method: 'POST',
    data: params,
  });
}
