import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Form, Tooltip, DatePicker } from 'antd';
import SecondTitle from '../SecondTitle';
import CommonTable from '../CommonTable';
import LineEcharts from '../lineEcharts';
import BarEcharts from '../barEcharts';
import AlarmFlow from '../AlarmFlow';
import DifferentialDataDetailsModal from '../DifferentialDataDetailsModal';
import TableCol from '../TableCol';

import request from '@/utils/request';
import moment from 'moment';

const Index = ({ form, isFullScreen }) => {
  const { getFieldDecorator, getFieldValue } = form;
  const [tabsName, setTabsName] = useState('黑名单同步');
  const [monitorBlack, setMonitorBlack] = useState([
   
  ]);
  const [differentialDataDetailsModalVisible, setDifferentialDataDetailsModalVisible] =
    useState(false);
  const [loading, setLoading] = useState(false);
  const [currentBlackMonitorInfo, setCurrentBlackMonitorInfo] = useState({});

  const handleOpenDetail = (r) => {
    setCurrentBlackMonitorInfo(r);
    setDifferentialDataDetailsModalVisible(true);
  };

  const getMonitorBlack = () => {
    setLoading(true);
    request(`/api/hn/monitor/getBlackMonitor`, {
      method: 'POST',
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setLoading(false);

        setMonitorBlack(res?.data || []);
      } else {
        setMonitorBlack([]);
      }
    });
  };

  const abnormalColumn = [
    {
      title: '监控指标',
      dataIndex: 'monitoringIndicator',
      key: 'monitoringIndicator',
      align: 'center',
      width: 15,
      render: (val) => <TableCol val={val} />,
    },
    {
      title: '差异量',
      dataIndex: 'diffCount',
      key: 'diffCount',
      align: 'center',
      width: 8,
      render: (val, r) => (
        <TableCol val={val}>
          <div className={styles.tableCol}>
            {Number(val) ? (
              <a style={{ textDecoration: 'underline' }} onClick={() => handleOpenDetail(r)}>
                {val}
              </a>
            ) : (
              val
            )}
          </div>
        </TableCol>
      ),
    },
    {
      title: '检测时间',
      dataIndex: 'countDate',
      key: 'countDate',
      align: 'center',
      width: 20,
      render: (val) => <TableCol val={val} />,
    },
    {
      title: '指标情况',
      dataIndex: 'indicatorStatus',
      key: 'indicatorStatus',
      align: 'center',
      width: 14,
      render: (val) => {
        return (
          <div className={val === '正常' ? styles.primaryStateTag : styles.errorStateTag}>
            {val}
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    getMonitorBlack();
  }, []);

  return (
    <div className={styles.left}>
      <div
        className={`${styles.leftTop} ${styles.commonBox}`}
        style={{
          height: !isFullScreen && tabsName === '集团白名单流程' && 'calc((56% - 48px) / 2)',
        }}
      >
        <SecondTitle title="关停数据同步监控"></SecondTitle>
        <LineEcharts
          isFullScreen={isFullScreen}
          time={2000}
          height={isFullScreen ? '75%' : tabsName === '集团白名单流程' ? 60 : 100}
        ></LineEcharts>
      </div>
      <div
        className={`${styles.leftCenter} ${styles.commonBox}`}
        style={{
          height: !isFullScreen && tabsName === '集团白名单流程' && 'calc((56% - 48px) / 2)',
        }}
      >
        <SecondTitle title="用户信息同步监控"></SecondTitle>
        <BarEcharts
          isFullScreen={isFullScreen}
          time={1000}
          height={isFullScreen ? '75%' : tabsName === '集团白名单流程' ? 60 : 100}
        ></BarEcharts>
      </div>
      <div
        className={`${styles.leftBottom} ${styles.commonBox}`}
        style={{
          marginBottom: 0,
          height: !isFullScreen && tabsName === '集团白名单流程' && '44%',
        }}
      >
        <div className={styles.GJ}>
          <div
            className={styles.tabTitleBox}
            style={{ marginBottom: tabsName !== '黑名单同步' ? 0 : 10 }}
          >
            <SecondTitle title="平台综合告警" style={{ marginBottom: 0 }}></SecondTitle>
            <div className={styles.tabTitleLeft}>
              <span className={styles.activeTab}>{tabsName}</span>
              <div className={styles.tool}>
                <div className={styles.tabBox}>
                  {['黑名单同步', '集团渠道关停号码同步', '集团白名单流程'].map((ele, index) => (
                    <div
                      className={styles.tabItem}
                      key={index}
                      onClick={() => {
                        setTabsName(ele);
                      }}
                    >
                      {ele}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.leftBottomContent}>
          {tabsName == '黑名单同步' ? (
            <CommonTable
              data={{
                list: monitorBlack || [],
                pagination: false,
              }}
              columns={abnormalColumn}
              // onChange={TopTableChange}
              scrollHeight={isFullScreen ? 300 : 80}
              idStr="blackListTable"
              loading={loading}
            />
          ) : (
            <>
              <div className={styles.commonFormBox}>
                <Form>
                  <Form.Item wrapperCol={{ span: isFullScreen ? 7 : 10 }}>
                    {getFieldDecorator('letfTime', {
                      initialValue: moment().subtract(1, 'days'),
                    })(
                      <DatePicker
                        getCalendarContainer={() => document.getElementById('indexWrapper')}
                        placeholder=""
                        allowClear={false}
                      />,
                    )}
                  </Form.Item>
                </Form>
              </div>
              <AlarmFlow
                letfTime={getFieldValue('letfTime')}
                isFullScreen={isFullScreen}
                type={tabsName}
              />
            </>
          )}
        </div>
      </div>
      <DifferentialDataDetailsModal
        visible={differentialDataDetailsModalVisible}
        isFullScreen={isFullScreen}
        onCancel={() => {
          setDifferentialDataDetailsModalVisible(false);
          setCurrentBlackMonitorInfo({});
        }}
        currentBlackMonitorInfo={currentBlackMonitorInfo}
      />
    </div>
  );
};

export default Form.create()(Index);
