
@import "../../common.less";
.left {
    height: 100%;
    display: flex;
    flex-direction: column;
    .leftTop, .leftCenter, .leftBottom {
        width: 100%;
        height: calc((100% - 48px) / 3);  
    }
    .leftBottom {
        display: flex;
        flex-direction: column;
        .leftBottomContent {
          flex: 1;
        }
    }
      
    .GJ {
        // border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    
        .tabTitleBox {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        .tabTitleLeft {
            display: flex;
            align-items: center;
            .activeTab {
            display: flex;
            align-items: center;
            color: #B4B7C3;
            font-size: 14px;
            margin-right: 6px;
            &::before {
                content: '';
                display: inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #54A6FF;
                box-shadow: 0px 0px 6px 0px rgba(80,177,255,0.71);
                margin-right: 6px;
            }
            }
            .tool {
    
            width: 16px;
            height: 16px;
            background-image: url(../../imgs/menu.png);
            background-size: 100%;
            background-repeat: no-repeat;
            position: relative;
            cursor: pointer;
            &:hover {
                background-image: url(../../imgs/menuHover.png);
                .tabBox {
                display: block;
                };
            }
            .tabBox {
                width: 134px;
                z-index: 999;
                background: rgba(40,53,79,0.9);
                position: absolute;
                top: 0px;
                left: 16px;
                display: none;
    
                .tabItem {
                text-align: center;
                padding: 0 4px;
                height: 22px;
                line-height: 22px;
                font-size: 12px;
                color: #B4B7C3;
                border-bottom: 1px solid rgba(190,190,190,0.2);
                &:hover {
                    color: #fff;
                }
                &:last-child {
                    border-bottom: none;
                }
                }
            }
            }
        }
        }
    }
  }