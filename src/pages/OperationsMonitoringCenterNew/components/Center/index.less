@import "../../common.less";
.center {
    // flex: 4;
    height: 100%;
    padding: 0 16px;
    display: flex;
    flex-direction:column;


    .centerTopTitle {
      position: relative;
      font-size: 40px;
      font-family: PangMenZhengDao;
      font-weight: 550;
      color: #fff;
      text-align: center;

      .centerTopTitleContent {
        position: absolute;
        font-size: 25px;
        width: 100%;
        text-align: center;
      }
    }

    .centerGJNumber {
      // display: flex;
      // margin-top: 30px;
      // margin-bottom: 16px;
       
      .centerGJNumberItem {
        display: flex;
        align-items: center;
        background: #071632;
        box-shadow: inset 0px 0px 8px 0px rgba(63,120,255,0.7);
        border-radius: 4px; 
        padding: 16px 12px; 

          img {
            width: 36px;
            height: 36px;
            margin-right: 10px;
          }
          .centerGJNumberItemTotal {

            .centerGJNumberTotal {
              display: block;
              font-size: 20px;
              font-family: DiGit;
              color: #FFFFFF;
            }
            .centerGJNumberTitle {
              color: #8da7d4;
            }
          }
   
       
        
      }

      .centerGJNumberContent {
        display: flex;
        height: 43px;
        line-height: 43px;

        .centerGJNumberTitle {
          margin-right: 22px;
        }

        .centerGJNumberData {
          font-size: 29px;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 62.5px;
          text-align: center;
          overflow: hidden;
        }
      }
    }
    .centerBottom {
      flex: 1;
    }
    .centerCenter {
      height: 41%;
      width: 100%;
      margin-bottom: 16px;
    
    
      :global {
        
    
        .ant-form-item {
          margin-bottom: 0px;
        }
    
        .ant-empty-normal {
          display: none;
        }
    
        .ant-select-selection__rendered {
          line-height: 16px;
        }
    
      
        .ant-table-tbody {
          &>.ant-table-row {
            &>td {
              padding: 4px !important;
            }
          }
          tr {
            background: transparent !important;
          }
          td {
            
            &:first-child {
              position: relative;
              
              // background: red;
            }
          }
        }
      }
    }
    
  }
  .levelBox {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2px;
    &.yzLevel {
      background: linear-gradient(270deg, rgba(254,8,80,0.05) 0%, #FF0850 100%);
    }
    &.ptLevel {
      background: linear-gradient(90deg, rgba(22,89,164,0.8) 0%, rgba(36,45,65,0) 100%);
    }
  }

 