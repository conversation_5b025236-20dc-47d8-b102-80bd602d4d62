import React, { useEffect, useState } from 'react';
import { Row, Col, Form, Select, DatePicker, Tooltip } from 'antd';
import CommonTable from '../CommonTable';
import ModelMonitor from '../ModelMonitor';
import OptionModal from '../optionModal';
import TableCol from '../TableCol';

import styles from './index.less';

import moment from 'moment';
import request from '@/utils/request';
import { exportFile } from '@/utils/utils';

import alarmTip from '../../imgs/alarmTip.png';
import yzAlarm from '../../imgs/yzAlarm.png';
import ptAlarm from '../../imgs/ptAlarm.png';

const { RangePicker } = DatePicker;

const Index = ({ isFullScreen, form }) => {
  const { getFieldDecorator, getFieldsValue, validateFields } = form;
  const [TopShow, setTopShow] = useState({});
  const [configvisible, setconfigvisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [TopData, setTopData] = useState({});
  const [searchParams, setSearchParams] = useState({});

  const [row, setrow] = useState({});

  const columns = [
    {
      title: '级别',
      dataIndex: 'monitorLevel',
      key: 'monitorLevel',
      align: 'center',
      width: 8,
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={`${styles.levelBox} ${
                row?.monitorLevel == '严重' ? styles.yzLevel : styles.ptLevel
              }`}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '监控分类',
      dataIndex: 'monitorType',
      key: 'monitorType',
      align: 'center',
      width: 10,
      render: (val) => <TableCol val={val} />,
    },
    {
      title: '告警时间',
      dataIndex: 'alarmDate',
      key: 'alarmDate',
      width: 16,
      align: 'center',
      render: (val) => <TableCol val={val} />,
    },
    {
      title: '告警描述',
      dataIndex: 'alarmDescribe',
      key: 'alarmDescribe',
      width: 16,
      align: 'center',
      render: (val) => <TableCol val={val} />,
    },
    {
      title: '负责人',
      dataIndex: 'headPeople',
      key: 'headPeople',
      width: 8,
      align: 'center',
      render: (val) => <TableCol val={val} />,
    },
    {
      title: '状态',
      dataIndex: 'alarmState',
      key: 'alarmState',
      width: 12,
      align: 'center',

      render: (val) => {
        // 处理状态 0待处理 1已处理
        const type = {
          0: '待处理',
          1: '已处理',
        };
        return <TableCol val={type?.[val]} />;
      },
    },
    {
      title: '处置结果',
      dataIndex: 'alarmResult',
      key: 'alarmResult',
      width: 10,
      align: 'center',
      render: (val) => <TableCol val={val} />,
    },
    {
      title: '操作',
      dataIndex: 'address',
      key: 'address',
      width: 10,
      align: 'center',
      render: (v, row) => (
        <div className={styles.tableCol}>
          <Tooltip>
            <div
              className={styles.optBox}
              onClick={() => {
                setconfigvisible(true);
                setrow(row);
              }}
            ></div>
          </Tooltip>
        </div>
      ),
    },
  ];

  const alarmInfoList = [
    {
      img: alarmTip,
      key: 'alarmTotal',
      title: '告警总数',
    },
    {
      img: yzAlarm,
      key: 'seriousAlarm',
      title: '严重告警',
    },
    {
      img: ptAlarm,
      key: 'ordinaryAlarm',
      title: '普通告警',
    },
  ];

  const getMonitorAlarmPageData = (params) => {
    setLoading(true);
    request(`api/hn/monitor/getMonitorAlarmPage`, {
      //top 表格
      method: 'POST',
      data: params,
      requestType: 'json',
    }).then((res) => {
      if (res?.code == 200) {
        setLoading(false);
        setSearchParams(params);
        setTopData({
          list: res?.data?.items || [],
          pagination: {
            pageSize: params?.pageSize || 10,
            current: params?.currentPage || 1,
            total: res?.data?.totalNum || 0,
          },
        });
      } else {
        setTopData({
          list: [],
          pagination: {
            pageSize: 10,
            current: 1,
            total: 0,
          },
        });
      }
    });
  };

  const getMonitorAlarmTotalData = (params) => {
    request(`api/hn/monitor/getMonitorAlarmTotal`, {
      //top 表格
      method: 'POST',
      data: params,
      requestType: 'json',
    }).then((res) => {
      if (res?.code == 200) {
        setTopShow(res?.data || {});
      } else {
        setTopShow({});
      }
    });
  };
  const handleSearchTop = (pagination) => {
    const values = getFieldsValue();
    let params = {
      ...values,
      alarmDateStart: values?.alarmDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
      alarmDateEnd: values?.alarmDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
      alarmDate: undefined,
      pageSize: pagination?.pageSize || 10,
      currentPage: pagination?.current || 1,
    };

    getMonitorAlarmPageData(params);
    getMonitorAlarmTotalData(params);
  };

  const TopTableChange = (pagination) => {
    handleSearchTop(pagination);
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/monitor/exportMonitorAlarm',
      decode: true,
      params: searchParams,
      method: 'POST',
    });
  };

  useEffect(() => {
    handleSearchTop();
  }, []);

  useEffect(() => {
    if (!configvisible) {
      setrow({});
    }
  }, [configvisible]);

  return (
    <div className={styles.center}>
      <div className={styles.centerGJNumber} style={{ marginBottom: isFullScreen && 16 }}>
        <Row gutter={[16, 16]}>
          {alarmInfoList.map((ele, index) => (
            <Col span={8} key={index}>
              <div className={styles.centerGJNumberItem} style={{ padding: !isFullScreen && 8 }}>
                <img src={ele.img} />
                <div className={styles.centerGJNumberItemTotal}>
                  <span className={styles.centerGJNumberTotal}>{TopShow?.[`${ele.key}`]}</span>
                  <span className={styles.centerGJNumberTitle}>{ele.title}</span>
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </div>

      <div className={styles.centerCenter} style={{ height: isFullScreen && '35%' }}>
        <div className={styles.commonFormBox}>
          <Form>
            <Row>
              <Col span={isFullScreen ? 5 : 6}>
                <Form.Item labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="监控分类">
                  {getFieldDecorator('monitorType')(
                    <Select
                      placeholder="请选择"
                      allowClear
                      getPopupContainer={() => document.getElementById('indexWrapper')}
                    >
                      <Select.Option value="模型监控">模型监控</Select.Option>
                      <Select.Option value="平台监控">平台监控</Select.Option>
                      <Select.Option value="接口监控">接口监控</Select.Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={isFullScreen ? 11 : 9}>
                <Form.Item labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} label="告警时间">
                  {getFieldDecorator('alarmDate', {
                    initialValue: [moment(), moment()],
                  })(
                    <RangePicker
                      format={'YYYY-MM-DD'}
                      getCalendarContainer={() => document.getElementById('indexWrapper')}
                      allowClear={false}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={isFullScreen ? 4 : 5}>
                <Form.Item labelCol={{ span: 7 }} wrapperCol={{ span: 17 }} label={'状态'}>
                  {getFieldDecorator('alarmState', {
                    initialValue: '0',
                  })(
                    <Select
                      placeholder="请选择"
                      allowClear
                      getPopupContainer={() => document.getElementById('indexWrapper')}
                    >
                      <Select.Option value="0">待处理</Select.Option>
                      <Select.Option value="1">已处理</Select.Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={4} style={{ textAlign: 'right' }}>
                <Form.Item>
                  <div
                    className={styles.searchBtn}
                    onClick={() => {
                      handleSearchTop();
                    }}
                    style={{ marginRight: 2 }}
                  >
                    查询
                  </div>
                  <div className={styles.ghostBtn} onClick={handleExport}>
                    导出
                  </div>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>

        <div
          className={`${styles.commonBox} `}
          style={{ padding: 0, paddingTop: 4, height: 'calc(100% - 28px)' }}
        >
          <CommonTable
            data={TopData}
            columns={columns}
            onChange={TopTableChange}
            scrollHeight={isFullScreen ? 300 : 130}
            idStr="monitorLevelTable"
            loading={loading}
          />
        </div>
        <OptionModal
          configvisible={configvisible}
          row={row}
          setconfigvisible={setconfigvisible}
          handleSearchTop={handleSearchTop}
        ></OptionModal>
      </div>

      <div className={styles.centerBottom}>
        <ModelMonitor isFullScreen={isFullScreen} />
      </div>
    </div>
  );
};
export default Form.create()(Index);
