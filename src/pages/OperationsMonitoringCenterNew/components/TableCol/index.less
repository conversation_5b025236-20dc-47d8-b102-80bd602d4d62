.tableCol {
    display: flex;
    align-items: center;
    justify-content: center;
    // height: 42px;
    width: 100%;
    line-height: 16px;
    text-align: center;
    overflow: hidden; //超出文本隐藏
    text-overflow: ellipsis; ///超出部分省略号显示
    display: -webkit-box; //弹性盒模型
    -webkit-line-clamp: 2; //自定义行数
    /* autoprefixer: off */ 
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    font-size: 12px;
    vertical-align: 'middle';
  
    .optBox {
      display: inline-block;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background: url(../../imgs/setting.png);
      background-size: 100%;
      background-repeat: no-repeat;
    
      &:hover {
        background-image: url(../../imgs/settingActive.png);
        
      }
    }
  }