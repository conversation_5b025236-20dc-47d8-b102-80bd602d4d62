import React, { useEffect, useState } from 'react';
import styles from './index.less';
import flow from './imgs/flow.png';
import errorFlow from './imgs/errorFlow.png';
import downFlow from './imgs/downFlow.png';
import errorDownFlow from './imgs/errorDownFlow.png';

import { message } from 'antd';

import request from '@/utils/request';
import moment from 'moment';

const Index = ({ letfTime, type }) => {
  const closeNumberFlowList = [
    {
      title: '文件采集',
      info: [
        {
          title: '采集文件数量',
          key: 'fileCount',
        },
        {
          title: '监测时间',
          key: 'monitorDate',
        },
      ],
      isErrorKey: 'fileState',
      img: flow,
      errorImg: errorFlow,
    },
    {
      title: '数据入库',
      info: [
        {
          title: '集团渠道关停采集号码数',
          key: 'phoneCount',
        },
        {
          title: '监测时间',
          key: 'monitorDate',
        },
      ],
      isErrorKey: 'phoneState',
      img: flow,
      errorImg: errorFlow,
    },
    {
      title: 'CRM接口调用',
      info: [
        {
          title: '调用成功次数',
          key: 'crmSuccessCount',
        },
        {
          title: '调用失败次数',
          key: 'crmFailCount',
        },
        {
          title: '监测时间',
          key: 'monitorDate',
        },
      ],
      isErrorKey: 'crmState',
      img: flow,
      errorImg: errorFlow,
    },
  ];
  const whiteListFlowList = [
    {
      title: '集团白名单',
      info: [
        {
          title: '文件数量',
          key: 'fileCount',
        },
        {
          title: '采集号码数',
          key: 'phoneCount',
        },
        {
          title: '监测时间',
          key: 'monitorDate',
        },
      ],
      isErrorKey: 'fileState',
      img: flow,
      errorImg: errorFlow,
    },
    {
      title: '白名单传CRM',
      info: [
        {
          title: '成功数量',
          key: 'crmSuccessCount',
        },
        {
          title: '失败数量',
          key: 'crmFailCount',
        },
        {
          title: '监测时间',
          key: 'monitorDate',
        },
      ],
      isErrorKey: 'crmState',
      img: flow,
      errorImg: errorFlow,
    },
    {
      title: '白名单入库',
      info: [
        {
          title: '入库数量',
          key: 'phoneDbCount',
        },

        {
          title: '监测时间',
          key: 'monitorDate',
        },
      ],
      isErrorKey: 'phoneState',
      img: flow,
      errorImg: errorFlow,
    },
    {
      title: '采集结果回传',
      info: [
        {
          title: '是否报错',
          key: 'backState',
          render: () => {
            return !flowInfo?.backState && flowInfo?.backState !== 0
              ? '--'
              : flowInfo?.backState == 1
              ? '否'
              : '是';
          },
        },
        {
          title: '监测时间',
          key: 'monitorDate',
        },
      ],
      isErrorKey: 'backState',
      img: downFlow,
      errorImg: errorDownFlow,
    },
  ];

  const listObj = {
    集团渠道关停号码同步: closeNumberFlowList,
    集团白名单流程: whiteListFlowList,
  };
  const [flowInfo, setFlowInfo] = useState({});

  useEffect(() => {
    getFlowData();
  }, []);
  useEffect(() => {
    getFlowData();
  }, [letfTime, type]);

  const getFlowData = () => {
    request(
      type == '集团渠道关停号码同步'
        ? 'api/hn/monitor/getMonitorCloseGroupData'
        : `api/hn/monitor/getMonitorWhiteGroupData`,
      {
        method: 'POST',
        data: {
          time: moment(letfTime).format('YYYY-MM-DD 00:00:00'),
        },
        requestType: 'form',
      },
    ).then((res) => {
      if (res?.code == 200) {
        setFlowInfo(res?.data || {});
      } else {
        message.error(res.message);
      }
    });
  };
  const renderValue = (ele) => {
    if (ele.render) {
      return ele.render();
    } else {
      return flowInfo?.[ele.key] !== 0 && !flowInfo?.[ele.key] ? '--' : flowInfo?.[ele.key];
    }
  };
  return (
    <div
      className={styles.breakCardFlowWrapper}
      // style={{ height: type === '集团白名单流程' && 300 }}
    >
      <div className={styles.flowWrapper}>
        {listObj?.[type]?.map((ele, index) => (
          <div className={styles.flowBox}>
            <img
              src={
                flowInfo?.[ele.isErrorKey] ||
                (!flowInfo?.[ele.isErrorKey] && flowInfo?.[ele.isErrorKey] !== 0)
                  ? ele.img
                  : ele.errorImg
              }
            />
            <div className={styles.flowTitleBox}>
              {ele.title}
              <div className={styles.flowInfoBox}>
                {ele.info.map((ele) => (
                  <div className={styles.flowInfoItem}>
                    <span className={styles.itemTitle}>{ele.title}：</span>
                    <span className={styles.itemValue}>{renderValue(ele)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Index;
