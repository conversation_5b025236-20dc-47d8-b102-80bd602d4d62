.breakCardFlowWrapper {
    margin-top: 48px;
    // height: 200px;
    display: flex;
    // align-items: center;
    justify-content: center;
    .flowWrapper {
        position: relative;
        width: 290px;
        // height: 100px;
        .flowBox {
            position: absolute;
            top: 0;

            .flowTitleBox {
                position: absolute;
                top: -40px;
                right: 0px;
                height: 24px;
                font-size: 12px;
                color: #FFFFFF;
                line-height: 24px;
                border-radius: 12px;
                border: 1px solid rgba(251,251,251,0.52);
                padding: 0px 8px;
                cursor: pointer;
                
                &:hover { 
                    background: #15294F;
                    border: 1px solid #064FF0;
                    color: #629DFF;
                    .flowInfoBox {
                        display: block;
                    }
                }
                &::after {
                    content:' ';
                    width: 1px;
                    height: 17px;
                    background: rgba(251,251,251,0.52);
                    position: absolute;
                    bottom: -18px;
                    right: 10px;
                }
                .flowInfoBox {
                    display: none;
                    min-width: 146px;
                    background: rgba(40,53,79,0.82);
                    border-radius: 4px;
                    padding: 6px;
                    position: absolute;
                    bottom: 26px;
                    left: -18px;
                    .flowInfoItem {
                        display: flex;
                        line-height: 18px;
                        .itemTitle {
                            white-space: nowrap;
                            color: rgba(232,232,232,0.65);
                        }
                        .itemValue {
                            display: inline-block;
                            color: rgba(255,255,255,0.8);

                        }
                    }
                }
            }
            img {
                height: 22px;
                
            }
            &:nth-child(1) {
               left: 1px;
               z-index: 5;
    
            }
            &:nth-child(2) {
                left: 97px;
                z-index: 4;
    
     
             }
            &:nth-child(3) {
                left: 188px;
                z-index: 3;
            }
            &:nth-child(4) {
                top: 8px;
                left: 87px;
                z-index: 2;

    
                img {
                    height: 55px;
                }
                .flowTitleBox {
                    top: 69px;
                    right: -74px;
                    &::after {
                        height: 18px;
                        bottom: 23px;
                        left: 5px;
                    }
                }
            }
          
    
        }
    }
}

