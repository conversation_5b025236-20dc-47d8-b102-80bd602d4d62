import React, { useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';
import styles from '../index.less';
import request from '@/utils/request';
import { Row, Col, Checkbox, Empty, Icon, Tooltip } from 'antd';
import { connect } from 'dryad';
import moment from 'moment';
import { render } from 'react-dom';

const CheckboxGroup = Checkbox.Group;

const FraudShutDownTrend = (props) => {
  const { dispatch, isFullScreen, time, height } = props;
  const [checkedList, setCheckedList] = useState([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [lineChartData, setLineChartData] = useState({});
  const [phoneCloseSubtag, setPhoneCloseSubtag] = useState([]);
  const [MonitorUserData, setMonitorUserData] = useState([]);
  const domRef = useRef(null);
  const myChartRef = useRef(null);
  const dataInterval = [0, 100, 1000, 10000, 100000, 1000000];

  const getRenderColor = (item) => {
    if (item.state === 1) {
      return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: '#0816FF' },
        { offset: 1, color: '#128BDF' },
      ]);
    } else {
      return (
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#FF0850' },
          { offset: 1, color: '#E0850A' },
        ]) || '#FF0850'
      );
    }
  };

  const getMonitorUserData = () => {
    request(`api/hn/monitor/getMonitorUserData`, {
      method: 'POST',
      params: {},
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setMonitorUserData(res?.data || []);
      } else {
        setMonitorUserData([]);
      }
    });
  };
  const initCharts = (dom, data) => {
    myChartRef.current = dom && echarts.init(dom);
    myChartRef.current.clear();
    window.addEventListener('resize', () => {
      myChartRef.current.resize();
    });
    const options = {
      grid: {
        top: '6%',
        left: '3%',
        right: '3%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: MonitorUserData?.map((item) =>
          // moment(item?.monitorDate).format('YYYY-MM-DD'),
          moment(item?.monitorDate).format('MM-DD'),
        ),

        axisLabel: {
          textStyle: {
            color: 'rgb(133,141,146)', //坐标值得具体的颜色
          },
          interval: 0,
          rotate: 30, //角度顺时针计算的
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            color: ['rgb(62,70,81)'],
            width: 1,
            type: 'solid',
          },
        },
        axisLabel: {
          textStyle: {
            color: 'rgb(133,141,146)', //坐标值得具体的颜色
          },
        },

        // interval: 0,

        min: (value) => {
          return value.min;
        },
        max: (value) => {
          return value.max;
        },
      },

      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            //设置纵向指示线
            type: 'dashed',
            color: 'rgba(133, 141, 146,0.5)',
          },
        },
      },
      series: [
        {
          data: MonitorUserData?.map((item) => {
            return {
              value: item?.userCount,
              itemStyle: {
                // color: getRenderColor(item),
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: item.state === 1 ? '#0816FF' : '#FF0850', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: item.state === 1 ? '#128BDF' : '#E0850A', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            };
          }),
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(153,175,233,0.19)',
            // color: item.state == 1 ? 'rgba(153,175,233,0.19)' : 'rgba(186,196,221,0.5)',
          },
          type: 'bar',
          itemStyle: {
            color: 'rgb(27,172,244)',
          },
          barWidth: '30%',
        },
      ],
    };
    myChartRef.current.setOption({
      ...options,
    });
  };

  useEffect(() => {
    if (domRef.current) {
      myChartRef.current = echarts.init(domRef.current);
      echarts.dispose(myChartRef.current);
    }
    getMonitorUserData();
  }, []);
  useEffect(() => {
    myChartRef.current.resize();
  }, [height]);

  useEffect(() => {
    if (domRef.current) {
      initCharts(domRef.current, lineChartData);
    }
  }, [domRef.current, isFullScreen]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div className="commonCharts" ref={domRef} style={{ width: '100%', height }}></div>
    </div>
  );
};

export default connect(({ situationAnalysis }) => ({
  situationAnalysis,
}))(FraudShutDownTrend);
