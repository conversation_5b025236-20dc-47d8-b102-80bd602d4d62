import React, { useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';
import styles from '../index.less';
import { Row, Col, Checkbox, Empty, Icon, Tooltip } from 'antd';
import { connect } from 'dryad';
import request from '@/utils/request';

const CheckboxGroup = Checkbox.Group;

const FraudShutDownTrend = (props) => {
  const { dispatch, isFullScreen, time, height } = props;
  const [checkedList, setCheckedList] = useState([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [lineChartData, setLineChartData] = useState({});
  const [phoneCloseSubtag, setPhoneCloseSubtag] = useState([]);
  const [MonitorCloseData, setMonitorCloseData] = useState([]);
  const domRef = useRef(null);
  const myChartRef = useRef(null);
  const getMonitorCloseData = () => {
    request(`/api/hn/monitor/getMonitorCloseData`, {
      method: 'POST',
      params: {},
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setMonitorCloseData(res?.data || []);
      } else {
        setMonitorCloseData([]);
      }
    });
  };
  const initCharts = (dom, data) => {
    myChartRef.current = dom && echarts.init(dom);
    window.addEventListener('resize', () => {
      myChartRef.current.resize();
    });

    const options = {
      grid: {
        top: '6%',
        left: '3%',
        right: '3%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: (MonitorCloseData || [])?.map((item) => {
          return item?.monitorDate;
        }),
        splitLine: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            type: 'solid',
            color: 'rgb(62,70,81)', //左边线的颜色
            width: '1', //坐标线的宽度
          },
        },
        axisLabel: {
          textStyle: {
            color: 'rgb(133,141,146)', //坐标值得具体的颜色
          },
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            color: ['rgba(190,190,190, 0.2)'],
            width: 1,
            type: 'dashed',
          },
        },
        axisLabel: {
          textStyle: {
            color: 'rgb(133,141,146)', //坐标值得具体的颜色
          },
        },
        min: (value) => {
          return value.min;
        },
        max: (value) => {
          return value.max;
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            //设置纵向指示线
            type: 'dashed',
            color: 'rgba(133, 141, 146,0.5)',
          },
          crossStyle: {
            //设置横向指示线
            show: false,
          },
        },
      },
      series: [
        {
          data: (MonitorCloseData || []).map((itme) => {
            return {
              value: itme?.closeCount,
              itemStyle: {
                borderColor: itme?.state == 1 ? '#0265CC' : '#E94C00', //设置空心圆点边框颜色
                borderWidth: itme?.state == 1 ? 1 : 2, //设置空心圆点边框宽度
                // opacity: 0.5, //设置空心圆点透明度
                color: itme?.state == 1 ? '#0816FA' : '#FF0850', //设置空心圆点填充颜色
                // shadowColor: itme?.state == 1 ? 'none' : '#E94C00',
                // shadowBlur: itme?.state == 1 ? 0 : 100,
              },
            };
          }),
          symbol: 'circle', // 设置拐点为实心圆
          type: 'line',
          lineStyle: {
            // color:'rgb(62,70,81)'
            width: 2,
            color: '#0265CC',
          },
          areaStyle: {
            //区域填充渐变颜色
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#0265CC', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#23304C', // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
        },
      ],
    };
    myChartRef.current.setOption({
      ...options,
    });
  };

  useEffect(() => {
    if (domRef.current) {
      myChartRef.current = echarts.init(domRef.current);
      echarts.dispose(myChartRef.current);
      getMonitorCloseData();
    }
  }, []);

  useEffect(() => {
    myChartRef.current.resize();
  }, [height]);

  useEffect(() => {
    if (domRef.current) {
      initCharts(domRef.current, lineChartData);
    }
  }, [domRef.current, isFullScreen, MonitorCloseData]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div className="commonCharts" ref={domRef} style={{ width: '100%', height }}></div>
    </div>
  );
};

export default connect(({ situationAnalysis }) => ({
  situationAnalysis,
}))(FraudShutDownTrend);
