.titleWrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .titleBox {
        width: 496px;
        // height: 33px;
        background-image: url(../../imgs/titleBg.png);
        background-size: 100%;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
        align-items: start;
        img {
            width: 265px;
        }
    }
    .fullScreen {
        width: 69px;
        height: 24px;
        line-height: 24px;
        border-radius: 12px;
        border: 1px solid #064FF0;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        img {
            width: 14px;
            height: 14px;
            margin-right: 4px;
        }
        span {
            font-size: 12px;
            color: #629DFF;
        }
    }
}