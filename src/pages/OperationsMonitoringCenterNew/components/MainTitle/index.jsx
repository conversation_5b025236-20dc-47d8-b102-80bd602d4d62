/*
 * @Author: zxw
 * @Date: 2023-06-16 10:49:16
 * @LastEditors: zxw
 * @LastEditTime: 2023-06-25 21:28:55
 * @FilePath: \newHunanfanzha\src\pages\OperationsMonitoringCenterNew\components\MainTitle\index.jsx
 * @Description: 
 */
import React from 'react';
import styles from './index.less';

import title from '../../imgs/title.png';
import titleBg from '../../imgs/titleBg.png';
import screenFull from '../../imgs/screenFull.png';

const Index = ({ fullScreenClick }) => {
  return (
    <div className={styles.titleWrapper}>
      <div></div>
      <div className={styles.titleBox}>
        <img src={title} />
      </div>
      <div className={styles.fullScreen} onClick={() => {fullScreenClick()}}>
        <img src={screenFull} />
        <span>全屏</span>
      </div>
    </div>
  );
};

export default Index;
