@import "../../common.less";
.searchBtn {
    display: inline-block;
    line-height: 18px;
    height: 18px;
    background: #0265CC;
    border-radius: 9px;
    padding: 0 6px;
    color: rgba(255,255,255, 0.7);
    font-size: 12px;
    margin-right: 4px;
    cursor: pointer;
  }
  
  .ghostBtn {
    display: inline-block;
    line-height: 18px;
    height: 18px;
    padding: 0 6px;
    border-radius: 9px;
    border: 1px solid #298FFB;
    font-size: 12px;
    cursor: pointer;
    color: #298FFB;
  
  }
.right {
    // flex: 3;
    height: 100%;
    display: flex;
    flex-direction: column;
  
    .rightTop {
    
      // margin-bottom: 10px;
      flex: 1;
      display: flex;
      flex-direction: column;
  
      :global {
        .ant-form-item label {
          color: rgba(255, 255, 255, 0.5);
          font-size: 12px;
        }
  
        .ant-form-item {
          margin-bottom: 0px;
        }
       
  
        .ant-table-tbody>tr.ant-table-row:hover>td {
          background: none !important;
        }
  
        .ant-empty-normal {
          display: none;
        }
  
        .ant-select-selection {
          font-size: 12px;
          height: 18px;
          margin-top: 11px;
          border-radius: 2px;
          border: 1px solid #3C4760;
          background: #24314B;
          color: rgba(255, 255, 255, 0.8);
        }
        .ant-input {
          line-height: 12px;
          font-size: 12px;
          height: 20px;
          margin-top: 11px;
          border-radius: 2px;
          border: 1px solid #3C4760;
          background: #24314B;
          color: rgba(255, 255, 255, 0.7);
        }
        .ant-select-selection__rendered {
          line-height: 20px;
        }

        .ant-empty-description {
          color: rgba(255, 255, 255, 0.8);
        }

        
  
    
  
        .anticon {
          color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item-active,
        .ant-pagination-item-active:hover {
          border-color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item {
          a {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-container {
          .anticon {
            color: rgba(255, 255, 255, 0.8);
          }
  
          .ant-pagination-item-ellipsis {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-active {
          background: rgb(35, 40, 51);
        }
      }
    }
  
    .rightTopIsFullScreen {
      width: 290px;
      height: 238px;
      float: right;
      margin-bottom: 10px;
  
      :global {
        .ant-form-item label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
        }
  
        .ant-form-item {
          margin-bottom: 0px;
        }
  
        .ant-table-tbody>tr.ant-table-row:hover>td {
          background: none !important;
        }
  
        .ant-empty-normal {
          display: none;
        }
  
        .ant-select-selection {
          font-size: 12px;
          height: 20px;
          margin-top: 11px;
        }
  
        .ant-select-selection__rendered {
          line-height: 20px;
        }
  
        .ant-input {
          margin-top: 11px;
          height: 20px;
          font-size: 12px;
          line-height: 12px;
        }
  
        .ant-table-column-title {
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          display: inline-block;
        }
  
        .ant-table-placeholder {
          background: rgb(35, 40, 51);
          
          display: none;
        }
  
        .ant-table-body {
          background: rgb(53, 61, 77);
          margin: 0px !important;
        }
  
        .ant-empty-description {
          color: rgba(255, 255, 255, 0.8);
        }
  
        
  
        .ant-table-small {
          border-radius: 0;
          
        }
  
        .ant-table-bordered .ant-table-thead>tr>th {
          border-right: 1px solid rgb(121, 121, 121);
        }
  
  
        .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {
  
          color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-table-tbody {
          display: block;
          color: #fff;
          text-align: center;
          height: 247px;
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
        }
  
        .ant-table-thead>tr,
        .ant-table-tbody>tr {
          display: table;
          width: 100%;
          table-layout: fixed;
        }
  
        .ant-table-thead>tr {
          border: none;
        }
  
        table tbody::-webkit-scrollbar {
          width: 0px;
        }
  
        
  
        .ant-table-thead>tr:first-child>th:first-child {
          border-radius: 0px;
        }
  
        
  
        .anticon {
          color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item-active,
        .ant-pagination-item-active:hover {
          border-color: rgba(255, 255, 255, 0.8);
        }
  
        .ant-pagination-item {
          a {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-container {
          .anticon {
            color: rgba(255, 255, 255, 0.8);
          }
  
          .ant-pagination-item-ellipsis {
            color: rgba(255, 255, 255, 0.8);
          }
        }
  
        .ant-pagination-item-active {
          background: rgb(35, 40, 51);
        }
      }
    }
  
    :global {
      .ant-table-small > .ant-table-content {
        .ant-table-scroll {
          background: none;
          .ant-table-hide-scrollbar {
              margin-bottom: -20px !important;
          }
        }
      }
    }
  }