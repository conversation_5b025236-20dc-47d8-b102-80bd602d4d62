import React, { useEffect, useState } from 'react';
import { Form, Row, Col, DatePicker, Input, Tooltip } from 'antd';
import styles from './index.less';
import SecondTitle from '../SecondTitle';
import CommonTable from '../CommonTable';
import RightBottom from './RightBottom';
import TableCol from '../TableCol';

import moment from 'moment';
import request from '@/utils/request';

import { exportFile } from '@/utils/utils';

const { RangePicker } = DatePicker;

const TableColTitle = ({ title }) => {
  return (
    <Tooltip title={title} getPopupContainer={() => document.getElementById('indexWrapper')}>
      <div>{title}</div>
    </Tooltip>
  );
};

const Index = ({ form, isFullScreen }) => {
  const { getFieldDecorator, validateFields, getFieldValue, getFieldsValue } = form;
  const [rightData, setRightData] = useState({});
  const [searchParams, setSearchParams] = useState({});
  const [loading, setLoading] = useState(false);

  const rightColumns = [
    {
      title: <TableColTitle title="接口名称" />,
      dataIndex: 'interfaceName',
      key: 'interfaceName',
      align: 'center',
      width: 15,
      render: (val) => <TableCol val={val} />,
    },
    {
      title: <TableColTitle title="接口频率(天)" />,
      dataIndex: 'interfaceRate',
      key: 'interfaceRate',
      align: 'center',
      width: 18,
      render: (val) => <TableCol val={Number(val)} />,
    },
    {
      title: <TableColTitle title="数据量" />,
      dataIndex: 'dataCount',
      key: 'dataCount',
      align: 'center',
      width: 8,
      render: (val) => <TableCol val={Number(val)} />,
    },
    {
      title: <TableColTitle title="是否上传文件" />,
      dataIndex: 'upSuccess',
      key: 'upSuccess',
      align: 'center',
      width: 10,

      render: (value, row) => {
        const type = {
          0: '否',
          1: '是',
        };
        return <TableCol val={type[value] || '--'} />;
      },
    },
    {
      title: <TableColTitle title="检测时间" />,
      dataIndex: 'monitorDate',
      key: 'monitorDate',
      align: 'center',
      width: 24,
      render: (val) => <TableCol val={val} />,
    },
    {
      title: <TableColTitle title="是否正常" />,
      dataIndex: 'upSuccess',
      key: 'upSuccess',
      width: 16,
      align: 'center',
      render: (val, row) => {
        return (
          <div className={val ? styles.primaryStateTag : styles.errorStateTag}>
            {val ? '正常' : '异常'}
          </div>
        );
      },
    },
  ];

  const RightTableChange = (pagination) => {
    handleSearchRight(pagination);
  };

  const handleSearchRight = (pagination) => {
    const values = getFieldsValue();

    let params = {
      ...values,
      monitorDateStart: values?.monitorDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
      monitorDateEnd: values?.monitorDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
      monitorDate: undefined,
      pageSize: 20,
      currentPage: pagination?.current || 1,
    };
    setLoading(true);
    request(`/api/hn/monitor/getMonitorInterfaceData`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    }).then((res) => {
      setLoading(false);
      if (res?.code == 200) {
        setSearchParams(params);
        setRightData({
          list: res?.data?.items || [],
          pagination: {
            pageSize: params.pageSize,
            current: params.currentPage,
            total: res?.data?.totalNum || 0,
          },
        });
      } else {
        setRightData({
          list: [],
          pagination: {
            pageSize: 10,
            current: 1,
            total: 0,
          },
        });
      }
    });
  };

  useEffect(() => {
    handleSearchRight();
  }, []);

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/monitor/exportMonitorInterfaceData',
      decode: true,
      params: { ...searchParams, pageSize: undefined, currentPage: undefined },
      method: 'POST',
    });
  };

  return (
    <div className={styles.right}>
      <div
        className={styles.commonBox}
        style={{ flex: 1, display: 'flex', flexDirection: 'column', paddingBottom: 0 }}
      >
        <SecondTitle title="接口监控" style={{ marginBottom: 0 }}></SecondTitle>
        <div className={styles.rightTop}>
          <Form>
            <Row>
              <Col span={24}>
                <Form.Item
                  style={{ marginBottom: 2 }}
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 8 }}
                  label="接口名称"
                >
                  {getFieldDecorator('interfaceName')(<Input placeholder="请输入" size="small" />)}
                </Form.Item>
              </Col>
              <Col span={18}>
                <Form.Item labelCol={{ span: 5 }} wrapperCol={{ span: 19 }} label="检测时间">
                  {getFieldDecorator('monitorDate', {
                    initialValue: [moment(), moment()],
                  })(
                    <RangePicker
                      format={'YYYY-MM-DD'}
                      getCalendarContainer={() => document.getElementById('indexWrapper')}
                      allowClear={false}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col align="right" span={6}>
                <Form.Item>
                  <div
                    className={styles.searchBtn}
                    onClick={() => {
                      handleSearchRight();
                    }}
                  >
                    查询
                  </div>
                  <div className={styles.ghostBtn} onClick={handleExport}>
                    导出
                  </div>
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div style={{ flex: 1 }}>
            <CommonTable
              data={rightData}
              columns={rightColumns}
              onChange={RightTableChange}
              scrollHeight={isFullScreen ? 320 : 50}
              idStr="interfaceTable"
              loading={loading}
            />
          </div>
        </div>
      </div>
      {/* <RightBottom isFullScreen={isFullScreen} /> */}
    </div>
  );
};
export default Form.create()(Index);
