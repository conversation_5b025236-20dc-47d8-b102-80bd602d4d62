import React from 'react';
import styles from '../index.less';
import SecondTitle from '../../SecondTitle';
import BreakCardFlow from '../../BreakCardFlow';
import { Form, DatePicker } from 'antd';
import moment from 'moment';

const Index = ({ form: { getFieldDecorator, getFieldValue }, isFullScreen }) => {
  const rightTimeDisabledDate = (current) => {
    return (
      (current && current > moment().startOf('day')) || current < moment().subtract(2, 'months')
    );
  };

  return (
    <div className={`${styles.commonBox} ${styles.rightBottom}`}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <SecondTitle title="断卡流程" style={{ marginBottom: 0 }}></SecondTitle>
        <div className={styles.commonFormBox}>
          <Form>
            <Form.Item
              labelCol={{ span: 10 }}
              wrapperCol={{ span: 14 }}
              style={{ marginBottom: 0 }}
              label="检测时间"
            >
              {getFieldDecorator('rightTime', {
                initialValue: moment().subtract(4, 'days'),
              })(
                <DatePicker
                  placeholder=""
                  getCalendarContainer={() => document.getElementById('indexWrapper')}
                  allowClear={false}
                  disabledDate={rightTimeDisabledDate}
                />,
              )}
            </Form.Item>
          </Form>
        </div>
      </div>

      <BreakCardFlow
        rightTime={getFieldValue('rightTime')?.format('YYYY-MM-DD')}
        isFullScreen={isFullScreen}
      ></BreakCardFlow>
    </div>
  );
};
export default Form.create()(Index);
