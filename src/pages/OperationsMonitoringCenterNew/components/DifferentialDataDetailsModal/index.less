.differentialDataDetailsModalTable {
    :global{
      
       .ant-table-thead {
            th {
                color: rgba(255,255,255,0.7);
                border-bottom: 1px solid rgba(190, 190, 190, 0.2) !important;
            }
        
       }
       .ant-table-tbody {
            td {
                color: rgba(255,255,255,0.7);
                border-bottom: 1px solid rgba(190, 190, 190, 0.2) !important;
            }
       }
       .ant-table-scroll {
            .ant-table-body {
                overflow-x: auto !important;
            }
       }
       .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
            background: #152543;
        }
        .ant-pagination-item-link, .ant-pagination-total-text, .ant-pagination-item:not(.ant-pagination-item-active) > a, .ant-pagination-options-quick-jumper {
            color: rgba(255,255,255,0.5);
        }
        .ant-pagination-item-active{
            background: transparent;
        }
    
    }
}