import React, { useState, useEffect } from 'react';
import { Modal, message } from 'antd';
import StandardTable from '@/components/StandardTable';
import styles from './index.less';
import request from '@/utils/request';

export default function index({ visible, onCancel, currentBlackMonitorInfo }) {
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      current: 1,
      total: 0,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  let columns = [
    {
      title: '证件号码',
      width: 110,
      dataIndex: 'idCardNum',
      ellipsis: true,
    },
    {
      title: '检测时间',
      width: 120,
      dataIndex: 'checkTime',
      ellipsis: true,
    },
  ];

  useEffect(() => {
    if (visible) {
      findTableDataPager();
    }
  }, [visible]);

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    const { countDate, monitoringIndicator } = currentBlackMonitorInfo;
    setLoading(true);
    const response = await request('/api/hn/monitor/pageBlackDiffDetail', {
      method: 'POST',
      requestType: 'json',
      data: {
        pageNum,
        pageSize,
        checkTime: countDate,
        indicatorType: monitoringIndicator?.includes('删除') ? '2' : '1', //指标类型 crm增加量 1 crm删除量 2
      },
    });
    setLoading(false);
    if (response && response.code === 200) {
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleTableChange = (pagination) => {
    findTableDataPager({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  return (
    <Modal
      title="差异数据明细信息"
      visible={visible}
      footer={null}
      getContainer={document.getElementById('indexWrapper')}
      onCancel={onCancel}
      maskClosable={false}
      wrapClassName="speicalModal"
    >
      <div className={styles.differentialDataDetailsModalTable}>
        <StandardTable
          columns={columns}
          data={{
            list: listData?.list,
            pagination: {
              ...listData?.pagination,
              showSizeChanger: false,
              showQuickJumper: false,
              showTotal: (total, range) => {
                return `共计：${total}`;
              },
            },
          }}
          loading={loading}
          showSelectCount={false}
          rowSelectionProps={false}
          rowKey="id"
          onChange={handleTableChange}
        />
      </div>
    </Modal>
  );
}
