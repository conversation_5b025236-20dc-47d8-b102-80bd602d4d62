.spanTitle {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-family: PingFang;
  font-weight: 500;
  color: rgba(146, 167, 181, 0.65) !important;
  background-color: transparent;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-bottom: 1px solid rgba(190, 190, 190, 0.20);
}

.nobackground {
  background: none;
  width: 100%;
  height: 30px;
  line-height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  // color: #fff;
  color: #FFF;
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  // padding: 0 3px;
}

.noneData {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #00D8FF;
  height: 100%;
}

.spanItem {
  background: rgba(0, 106, 254, 0.2);
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  color: #fff;
  // padding: 0 3px;
  border-bottom: 1px solid #006AFE;
  line-height: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.carouselBox {

  // height: calc(100% - 14%) !important;

  :global {
    .ant-carousel .slick-vertical .slick-slide {
      border: none;
    }

    .ant-carousel {
      height: 100%;
    }

    .slick-slider {
      height: 100%;
    }

    .slick-list {
      height: 100%;
    }

    .slick-slide:nth-of-type(odd) {
      // background-color: rgb(53, 61, 77);

    }

    .slick-slide:nth-of-type(even) {

      background: rgba(80,93,123,0.18);    
    }
  }
}

.noneBackground {
  background: none;
}
