import React, { useEffect, useState, useCallback, useRef } from 'react';

import MainTitle from './components/MainTitle';
import RollView from './components/RollView/index';
import LineEcharts from './components/lineEcharts';
import BarEcharts from './components/barEcharts';
import ConfigModal from './components/configModal';
import OptionModal from './components/optionModal';
import { exportFile } from '@/utils/utils';
import { Tooltip, Form, Row, Col, Select, Input, DatePicker } from 'antd';

import alarmTip from './imgs/alarmTip.png';
import yzAlarm from './imgs/yzAlarm.png';
import ptAlarm from './imgs/ptAlarm.png';

import request from '@/utils/request';
const { RangePicker } = DatePicker;
import styles from './index.less';
import SecondTitle from './components/SecondTitle';
import CommonTable from './components/CommonTable';
import BreakCardFlow from './components/BreakCardFlow';
import AlarmFlow from './components/AlarmFlow';
import ModelMonitor from './components/ModelMonitor';
import DifferentialDataDetailsModal from './components/DifferentialDataDetailsModal';
import moment from 'moment';
import Context from './context';

const { Option } = Select;
function index(props) {
  const {
    form: { getFieldDecorator, getFieldValue, setFieldsValue, validateFields },
  } = props;
  const [isFullScreen, setisFullScreen] = useState(false);
  const [MonitorBlack, setMonitorBlack] = useState([]);
  const [configvisible, setconfigvisible] = useState(false);
  const [differentialDataDetailsModalVisible, setDifferentialDataDetailsModalVisible] =
    useState(false);

  const [row, setrow] = useState({});
  const containerRef = useRef();

  const alarmInfoList = [
    {
      img: alarmTip,
      key: 'alarmTotal',
      title: '告警总数',
    },
    {
      img: yzAlarm,
      key: 'seriousAlarm',
      title: '严重告警',
    },
    {
      img: ptAlarm,
      key: 'ordinaryAlarm',
      title: '普通告警',
    },
  ];
  useEffect(() => {
    if (!configvisible) {
      setrow({});
    }
  }, [configvisible]);
  const toFullScreen = () => {
    setisFullScreen(true);

    const elem = document.getElementById('indexWrapper');
    // const elem = document.documentElement;
    if (elem.webkitRequestFullScreen) {
      elem.webkitRequestFullScreen();
    } else if (elem.mozRequestFullScreen) {
      elem.mozRequestFullScreen();
    } else if (elem.requestFullScreen) {
      elem.requestFullscreen();
    } else {
      // 浏览器不支持全屏API或已被禁用
    }
  };
  useEffect(() => {
    getMonitorBlack();
    handleSearchTop();
    handleSearchRight();
  }, []);
  useEffect(() => {
    function onKeyup(e) {
      if (e.key == 'Escape') {
        exitFullscreen();
      }
    }
    window.addEventListener('keyup', onKeyup);
    return () => window.removeEventListener('keyup', onKeyup);
  }, [isFullScreen]);
  const exitFullscreen = () => {
    setisFullScreen(false);
    const elem = document;
    if (elem.webkitCancelFullScreen) {
      elem.webkitCancelFullScreen();
    } else if (elem.mozCancelFullScreen) {
      elem.mozCancelFullScreen();
    } else if (elem.cancelFullScreen) {
      elem.cancelFullScreen();
    } else if (elem.exitFullscreen) {
      elem.exitFullscreen();
    } else {
      // 浏览器不支持全屏API或已被禁用
    }
  };
  const [TabsNum, setTabsNum] = useState('黑名单同步');
  const [visible, setVisible] = useState(false);
  const callback = (e) => {
    setTabsNum(e.target.value);
    setFieldsValue({
      letfTime: moment().subtract(1, 'days'),
    });
  };

  const getMonitorBlack = () => {
    request(`/api/hn/monitor/getMonitorBlack`, {
      method: 'POST',
      params: {},
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setMonitorBlack(res?.data || []);
      } else {
        setMonitorBlack([]);
      }
    });
  };

  const handleOpenDetail = () => {
    setDifferentialDataDetailsModalVisible(true);
  };

  const abnormalColumn = [
    {
      title: '监控指标',
      dataIndex: 'blackType',
      key: 'blackType',
      align: 'center',
      width: 15,
      render: (val) =>
        (
          <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
            <div className={styles.tableCol}>{val}</div>
          </Tooltip>
        ) || '-',
    },
    {
      title: '差异量',
      dataIndex: 'contrastChange',
      key: 'contrastChange',
      align: 'center',
      width: 8,
      render: (val) => (
        <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
          <div className={styles.tableCol}>
            {val ? (
              <a style={{ textDecoration: 'underline' }} onClick={handleOpenDetail}>
                {val}
              </a>
            ) : (
              val
            )}
          </div>
        </Tooltip>
      ),
    },
    // {
    //   title: '平台变化量',
    //   dataIndex: 'platformChange',
    //   key: 'platformChange',
    //   align: 'center',
    //   width: 14,
    //   render: (value, row) => {
    //     return (
    //       <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
    //         <div className={styles.tableCol}>{value || '--'}</div>
    //       </Tooltip>
    //     );
    //   },
    // },
    // {
    //   title: '比对量',
    //   dataIndex: 'contrastChange',
    //   key: 'contrastChange',
    //   align: 'center',
    //   width: 8,

    //   render: (val) =>
    //     (
    //       <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
    //         <div className={styles.tableCol}>{val}</div>
    //       </Tooltip>
    //     ) || '-',
    // },
    {
      title: '检测时间',
      dataIndex: 'monitorDate',
      key: 'monitorDate',
      align: 'center',
      width: 20,

      render: (val) =>
        (
          <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
            <div className={styles.tableCol}>{val}</div>
          </Tooltip>
        ) || '-',
    },
    {
      title: '指标情况',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      width: 14,

      render: (val) =>
        // 状态 0异常 1正常
        {
          return (
            <div className={val ? styles.primaryStateTag : styles.errorStateTag}>
              {val ? '正常' : '异常'}
            </div>
          );
        },
    },
  ];
  const columns = [
    {
      title: '级别',
      dataIndex: 'monitorLevel',
      key: 'monitorLevel',
      align: 'center',
      width: 8,
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={`${styles.levelBox} ${
                row?.monitorLevel == '严重' ? styles.yzLevel : styles.ptLevel
              }`}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '监控分类',
      dataIndex: 'monitorType',
      key: 'monitorType',
      align: 'center',
      width: 10,
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '告警时间',
      dataIndex: 'alarmDate',
      key: 'alarmDate',
      width: 16,
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '告警描述',
      dataIndex: 'alarmDescribe',
      key: 'alarmDescribe',
      width: 16,
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div>
              <div className={styles.tableCol}>{value || '--'}</div>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '负责人',
      dataIndex: 'headPeople',
      key: 'headPeople',
      width: 8,
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'alarmState',
      key: 'alarmState',
      width: 12,
      align: 'center',
      render: (value, row) => {
        // 处理状态 0待处理 1已处理
        const type = {
          0: '待处理',
          1: '已处理',
        };
        return (
          <Tooltip
            title={type[value]}
            getPopupContainer={() => document.getElementById('indexWrapper')}
          >
            <div className={styles.tableCol}>{type[value] || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '处置结果',
      dataIndex: 'alarmResult',
      key: 'alarmResult',
      width: 10,
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'address',
      key: 'address',
      width: 10,
      align: 'center',
      render: (value, row) => {
        return (
          <div className={styles.tableCol}>
            <Tooltip>
              <div
                className={styles.optBox}
                onClick={() => {
                  setconfigvisible(true);
                  setrow(row);
                }}
              ></div>
            </Tooltip>
          </div>
        );
      },
    },
  ];
  const bottmColumns = [
    {
      title: '监控模型',
      dataIndex: 'monitorModel',
      key: 'level',
      align: 'center',
    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      key: 'threshold',
      align: 'center',
    },
    {
      title: '号码量',
      dataIndex: 'NumberQuantity',
      key: 'NumberQuantity',
      align: 'center',
    },
    {
      title: '检测时间',
      dataIndex: 'Time',
      key: 'Time',
      align: 'center',
    },
    {
      title: '是否正常',
      dataIndex: 'isnormal',
      key: 'normal',
      align: 'center',
    },
  ];
  const rightColumns = [
    {
      title: (
        <Tooltip
          title={'接口名称'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div>接口名称</div>
        </Tooltip>
      ),
      dataIndex: 'interfaceName',
      key: 'interfaceName',
      align: 'center',
      width: 15,
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'接口频率(天)'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div>{'接口频率(天)'}</div>
        </Tooltip>
      ),
      dataIndex: 'interfaceRate',
      key: 'interfaceRate',
      align: 'center',
      width: 18,

      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{Number(value) || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip title={'数据量'} getPopupContainer={() => document.getElementById('indexWrapper')}>
          <div>{'数据量'}</div>
        </Tooltip>
      ),
      dataIndex: 'dataCount',
      key: 'dataCount',
      align: 'center',
      width: 8,

      render: (value, row) => {
        return (
          <Tooltip
            title={(value && value == 0 && String(value)) || '--'}
            getPopupContainer={() => document.getElementById('indexWrapper')}
          >
            <div className={styles.tableCol}>{(value && value == 0 && String(value)) || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'是否上传文件'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div>{'是否上传文件'}</div>
        </Tooltip>
      ),
      dataIndex: 'upSuccess',
      key: 'upSuccess',
      align: 'center',
      width: 10,

      render: (value, row) => {
        const type = {
          0: '否',
          1: '是',
        };
        return (
          <Tooltip
            title={type[value]}
            getPopupContainer={() => document.getElementById('indexWrapper')}
          >
            <div className={styles.tableCol}>{type[value] || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'检测时间'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div>{'检测时间'}</div>
        </Tooltip>
      ),
      dataIndex: 'monitorDate',
      key: 'monitorDate',
      align: 'center',
      width: 24,

      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'是否正常'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div>{'是否正常'}</div>
        </Tooltip>
      ),
      dataIndex: 'upSuccess',
      key: 'upSuccess',
      width: 16,
      align: 'center',
      render: (val, row) => {
        return (
          <div className={val ? styles.primaryStateTag : styles.errorStateTag}>
            {val ? '正常' : '异常'}
          </div>
        );
      },
    },
  ];
  const [TopData, setTopData] = useState({});
  const [RightData, setRightData] = useState({});
  const [TopShow, setTopShow] = useState({});
  const handleSearchTop = (pagination) => {
    validateFields((error, value) => {
      if (error) return;
      let params = {
        monitorType: value?.monitorType,
        alarmState: value?.alarmState,
        alarmDateStart: value?.alarmDate
          ? moment(value?.alarmDate[0]).format('YYYY-MM-DD 00:00:00')
          : '',
        alarmDateEnd: value?.alarmDate
          ? moment(value?.alarmDate[1]).format('YYYY-MM-DD 23:59:59')
          : '',
        pageSize: pagination?.pageSize || 10,
        currentPage: pagination?.current || 1,
      };
      request(`api/hn/monitor/getMonitorAlarmPage`, {
        //top 表格
        method: 'POST',
        data: params,
        requestType: 'json',
      }).then((res) => {
        if (res?.code == 200) {
          setTopData({
            list: res?.data?.items || [],
            pagination: {
              pageSize: pagination?.pageSize || 10,
              current: pagination?.current || 1,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setTopData({
            list: [],
            pagination: {
              pageSize: 10,
              current: 1,
              total: 0,
            },
          });
        }
      });
      request(`api/hn/monitor/getMonitorAlarmTotal`, {
        //top 表格
        method: 'POST',
        data: {
          monitorType: params?.monitorType,
          alarmDateStart: params?.alarmDateStart,
          alarmDateEnd: params?.alarmDateEnd,
        },
        requestType: 'json',
      }).then((res) => {
        if (res?.code == 200) {
          setTopShow(res?.data || {});
        } else {
          setTopShow({});
        }
      });
    });
  };
  const RightTableChange = (pagination) => {
    handleSearchRight(pagination);
  };
  const handleSearchRight = (pagination) => {
    validateFields((error, value) => {
      if (error) return;
      let params = {
        interfaceName: value?.interfaceName,
        monitorDateStart: value?.monitorDate
          ? moment(value?.monitorDate[0]).format('YYYY-MM-DD 00:00:00')
          : '',
        monitorDateEnd: value?.monitorDate
          ? moment(value?.monitorDate[1]).format('YYYY-MM-DD 23:59:59')
          : '',
        pageSize: pagination?.pageSize || 10,
        currentPage: pagination?.current || 1,
      };
      request(`/api/hn/monitor/getMonitorInterfaceData`, {
        //top 表格
        method: 'POST',
        data: params,
        requestType: 'json',
      }).then((res) => {
        if (res?.code == 200) {
          setRightData({
            list: res?.data?.items || [],
            pagination: {
              pageSize: pagination?.pageSize || 10,
              current: pagination?.current || 1,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setRightData({
            list: [],
            pagination: {
              pageSize: 10,
              current: 1,
              total: 0,
            },
          });
        }
      });
    });
  };
  const TopTableChange = (pagination) => {
    handleSearchTop(pagination);
  };

  const handleScreen = () => {
    if (isFullScreen) {
      exitFullscreen();
    } else {
      toFullScreen();
    }
  };

  const rightTimeDisabledDate = (current) => {
    return (
      (current && current > moment().startOf('day')) || current < moment().subtract(2, 'months')
    );
  };

  return (
    <div id="indexWrapper" className={styles.container} ref={containerRef}>
      <Context.Provider
        value={{
          isFullScreen,
          containerRef,
        }}
      >
        <MainTitle fullScreenClick={handleScreen} />

        <div className={styles.main}>
          <Row style={{ display: 'flex', height: '100%' }}>
            <Col span={7}>
              <div className={styles.left}>
                <div
                  className={`${styles.leftTop} ${styles.commonBox}`}
                  style={{
                    height:
                      !isFullScreen && TabsNum === '集团白名单流程' && 'calc((56% - 48px) / 2)',
                  }}
                >
                  <SecondTitle title="关停数据同步监控"></SecondTitle>
                  <LineEcharts
                    isFullScreen={isFullScreen}
                    time={2000}
                    height={isFullScreen ? '75%' : TabsNum === '集团白名单流程' ? 60 : 100}
                  ></LineEcharts>
                </div>
                <div
                  className={`${styles.leftCenter} ${styles.commonBox}`}
                  style={{
                    height:
                      !isFullScreen && TabsNum === '集团白名单流程' && 'calc((56% - 48px) / 2)',
                  }}
                >
                  <SecondTitle title="用户信息同步监控"></SecondTitle>
                  <BarEcharts
                    isFullScreen={isFullScreen}
                    time={1000}
                    height={isFullScreen ? '75%' : TabsNum === '集团白名单流程' ? 60 : 100}
                  ></BarEcharts>
                </div>
                <div
                  className={`${styles.leftBottom} ${styles.commonBox}`}
                  style={{
                    marginBottom: 0,
                    height: !isFullScreen && TabsNum === '集团白名单流程' && '44%',
                  }}
                >
                  <div className={styles.GJ}>
                    <div
                      className={styles.tabTitleBox}
                      style={{ marginBottom: TabsNum !== '黑名单同步' ? 0 : 10 }}
                    >
                      <SecondTitle title="平台综合告警" style={{ marginBottom: 0 }}></SecondTitle>
                      <div className={styles.tabTitleLeft}>
                        <span className={styles.activeTab}>{TabsNum}</span>
                        <div className={styles.tool}>
                          <div className={styles.tabBox}>
                            {['黑名单同步', '集团渠道关停号码同步', '集团白名单流程'].map(
                              (ele, index) => (
                                <div
                                  className={styles.tabItem}
                                  key={index}
                                  onClick={() => {
                                    setTabsNum(ele);
                                  }}
                                >
                                  {ele}
                                </div>
                              ),
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.leftBottomContent}>
                    {TabsNum == '黑名单同步' && (
                      <CommonTable
                        data={{
                          list: MonitorBlack || [],
                          pagination: false,
                        }}
                        columns={abnormalColumn}
                        // onChange={TopTableChange}
                        scrollHeight={isFullScreen ? 300 : 80}
                        idStr="blackListTable"
                      />
                    )}
                    {TabsNum != '黑名单同步' && (
                      <>
                        <div className={styles.commonFormBox}>
                          <Form>
                            <Form.Item wrapperCol={{ span: isFullScreen ? 7 : 10 }}>
                              {getFieldDecorator('letfTime', {
                                initialValue: moment().subtract(1, 'days'),
                              })(
                                <DatePicker
                                  getCalendarContainer={() =>
                                    document.getElementById('indexWrapper')
                                  }
                                  placeholder=""
                                  allowClear={false}
                                />,
                              )}
                            </Form.Item>
                          </Form>
                        </div>
                        <AlarmFlow
                          letfTime={getFieldValue('letfTime')}
                          isFullScreen={isFullScreen}
                          type={TabsNum}
                        />
                      </>
                    )}
                  </div>
                </div>
              </div>
            </Col>
            <Col span={10} style={{ height: '100%' }}>
              <div className={styles.center}>
                <div className={styles.centerGJNumber} style={{ marginBottom: isFullScreen && 16 }}>
                  <Row gutter={[16, 16]}>
                    {alarmInfoList.map((ele, index) => (
                      <Col span={8}>
                        <div
                          className={styles.centerGJNumberItem}
                          style={{ padding: !isFullScreen && 8 }}
                        >
                          <img src={ele.img} />
                          <div className={styles.centerGJNumberItemTotal}>
                            <span className={styles.centerGJNumberTotal}>
                              {TopShow?.[`${ele.key}`]}
                            </span>
                            <span className={styles.centerGJNumberTitle}>{ele.title}</span>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </div>
                <div className={styles.centerCenter} style={{ height: isFullScreen && '35%' }}>
                  {/* <div ={isFullScreen ? styles.centerCenterIsFullScreen : styles.centerCenter}> */}
                  <div className={styles.commonFormBox}>
                    <Form>
                      <Row>
                        <Col span={isFullScreen ? 5 : 6}>
                          <Form.Item
                            labelCol={{ span: 10 }}
                            wrapperCol={{ span: 14 }}
                            label="监控分类"
                          >
                            {getFieldDecorator('monitorType')(
                              <Select
                                placeholder="请选择"
                                allowClear
                                getPopupContainer={() => document.getElementById('indexWrapper')}
                              >
                                <Option value="模型监控">模型监控</Option>
                                <Option value="平台监控">平台监控</Option>
                                <Option value="接口监控">接口监控</Option>
                              </Select>,
                            )}
                          </Form.Item>
                        </Col>
                        <Col span={isFullScreen ? 11 : 9}>
                          <Form.Item
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                            label="告警时间"
                          >
                            {getFieldDecorator('alarmDate', {
                              initialValue: [moment(), moment()],
                            })(
                              <RangePicker
                                format={'YYYY-MM-DD'}
                                getCalendarContainer={() => document.getElementById('indexWrapper')}
                              />,
                            )}
                          </Form.Item>
                        </Col>
                        <Col span={isFullScreen ? 4 : 5}>
                          <Form.Item
                            labelCol={{ span: 7 }}
                            wrapperCol={{ span: 17 }}
                            label={'状态'}
                          >
                            {getFieldDecorator('alarmState', {
                              initialValue: '0',
                            })(
                              <Select
                                placeholder="请选择"
                                allowClear
                                getPopupContainer={() => document.getElementById('indexWrapper')}
                              >
                                <Option value="0">待处理</Option>
                                <Option value="1">已处理</Option>
                              </Select>,
                            )}
                          </Form.Item>
                        </Col>
                        <Col span={4} style={{ textAlign: 'right' }}>
                          <Form.Item>
                            <div
                              className={styles.searchBtn}
                              onClick={() => {
                                handleSearchTop();
                              }}
                              style={{ marginRight: 2 }}
                            >
                              查询
                            </div>
                            <div
                              className={styles.ghostBtn}
                              onClick={() => {
                                validateFields((error, value) => {
                                  let params = {
                                    monitorType: value?.monitorType,
                                    alarmState: value?.alarmState,
                                    alarmDateStart: value?.alarmDate
                                      ? moment(value?.alarmDate[0]).format('YYYY-MM-DD 00:00:00')
                                      : '',
                                    alarmDateEnd: value?.alarmDate
                                      ? moment(value?.alarmDate[1]).format('YYYY-MM-DD 23:59:59')
                                      : '',
                                  };
                                  exportFile({
                                    urlAPi: '/api/hn/monitor/exportMonitorAlarm',
                                    decode: true,
                                    params: params,
                                    method: 'POST',
                                  });
                                });
                              }}
                            >
                              导出
                            </div>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                  </div>

                  <div
                    className={`${styles.commonBox} `}
                    style={{ padding: 0, paddingTop: 4, height: 'calc(100% - 28px)' }}
                  >
                    <CommonTable
                      data={TopData}
                      columns={columns}
                      onChange={TopTableChange}
                      scrollHeight={isFullScreen ? 300 : 130}
                      idStr="monitorLevelTable"
                    />
                  </div>
                </div>

                <div className={styles.centerBottom}>
                  <ModelMonitor isFullScreen={isFullScreen} />
                </div>
              </div>
            </Col>

            <Col span={7} style={{ height: '100%' }}>
              <div className={styles.right}>
                <div
                  className={styles.commonBox}
                  style={{ flex: 1, display: 'flex', flexDirection: 'column', paddingBottom: 0 }}
                >
                  <SecondTitle title="接口监控" style={{ marginBottom: 0 }}></SecondTitle>
                  <div
                    className={styles.rightTop}
                    // className={isFullScreen ? styles.rightTopIsFullScreen : styles.rightTop}
                    // style={isFullScreen ? { width: '95%', height: '49%' } : {}}
                  >
                    <Form>
                      <Row>
                        <Col span={24}>
                          <Form.Item
                            style={{ marginBottom: 2 }}
                            labelCol={{ span: 4 }}
                            wrapperCol={{ span: 8 }}
                            label="接口名称"
                          >
                            {getFieldDecorator('interfaceName')(
                              <Input placeholder="请输入" size="small" />,
                            )}
                          </Form.Item>
                        </Col>
                        <Col span={16}>
                          <Form.Item
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                            label="检测时间"
                          >
                            {getFieldDecorator('monitorDate', {
                              initialValue: [moment(), moment()],
                            })(
                              <RangePicker
                                format={'YYYY-MM-DD'}
                                getCalendarContainer={() => document.getElementById('indexWrapper')}
                              />,
                            )}
                          </Form.Item>
                        </Col>
                        <Col align="right" span={8}>
                          <Form.Item>
                            <div
                              className={styles.searchBtn}
                              onClick={() => {
                                handleSearchRight();
                              }}
                            >
                              查询
                            </div>
                            <div
                              className={styles.ghostBtn}
                              onClick={() => {
                                validateFields((error, value) => {
                                  let params = {
                                    interfaceName: value?.interfaceName,
                                    monitorDateStart: value?.monitorDate
                                      ? moment(value?.monitorDate[0]).format('YYYY-MM-DD 00:00:00')
                                      : '',
                                    monitorDateEnd: value?.monitorDate
                                      ? moment(value?.monitorDate[1]).format('YYYY-MM-DD 23:59:59')
                                      : '',
                                  };
                                  exportFile({
                                    urlAPi: '/api/hn/monitor/exportMonitorInterfaceData',
                                    decode: true,
                                    params: params,
                                    method: 'POST',
                                  });
                                });
                              }}
                            >
                              导出
                            </div>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                    <div style={{ flex: 1 }}>
                      <CommonTable
                        data={RightData}
                        columns={rightColumns}
                        onChange={RightTableChange}
                        scrollHeight={isFullScreen ? 320 : 50}
                        idStr="interfaceTable"
                      />
                    </div>
                  </div>
                </div>

                <div
                  className={`${styles.commonBox} ${styles.rightBottom}`}
                  // style={isFullScreen ? { width: '95%', height: '48%' } : {}}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    }}
                  >
                    <SecondTitle title="断卡流程" style={{ marginBottom: 0 }}></SecondTitle>
                    <div className={styles.commonFormBox}>
                      <Form>
                        <Form.Item
                          labelCol={{ span: 10 }}
                          wrapperCol={{ span: 14 }}
                          style={{ marginBottom: 0 }}
                          label="检测时间"
                        >
                          {getFieldDecorator('rightTime', {
                            initialValue: moment().subtract(4, 'days'),
                          })(
                            <DatePicker
                              placeholder=""
                              getCalendarContainer={() => document.getElementById('indexWrapper')}
                              allowClear={false}
                              disabledDate={rightTimeDisabledDate}
                            />,
                          )}
                        </Form.Item>
                      </Form>
                    </div>
                  </div>

                  <BreakCardFlow
                    rightTime={moment(getFieldValue('rightTime')).format('YYYY-MM-DD')}
                    isFullScreen={isFullScreen}
                  ></BreakCardFlow>
                </div>
              </div>
            </Col>

            <ConfigModal
              getContainer={() => {
                document.getElementById('indexWrapper');
              }}
              visible={visible}
              setVisible={setVisible}
              isFullScreen={isFullScreen}
            ></ConfigModal>
            <OptionModal
              configvisible={configvisible}
              row={row}
              setconfigvisible={setconfigvisible}
              handleSearchTop={handleSearchTop}
            ></OptionModal>
            <DifferentialDataDetailsModal
              visible={differentialDataDetailsModalVisible}
              isFullScreen={isFullScreen}
              onCancel={() => {
                setDifferentialDataDetailsModalVisible(false);
              }}
            />
          </Row>
        </div>
      </Context.Provider>
    </div>
  );
}
export default Form.create()(index);
