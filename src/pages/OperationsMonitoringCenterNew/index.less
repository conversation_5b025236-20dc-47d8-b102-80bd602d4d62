@import "./common.less";


.container::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.container {
  width: 100%;
  height: 100%;
  // overflow: scroll;
  color: #FFF;
  background: #050F22 !important;
  padding: 12px 12px 0 12px!important;
  box-sizing: border-box;
  font-family: PingFang;

  :global {
    .ant-table-small {
      border: none;
    }
    .ant-table-small > .ant-table-content > .ant-table-body {
      background: none;
    }
    .ant-table-small > .ant-table-content {
      border: none;
      .ant-table-body {
        .ant-table-thead {
          &>tr {
            border: none;
            border-bottom: 1px solid rgba(190, 190, 190, 0.2);
            &>th {
              border: none;
              padding: 0px;
              padding-bottom: 4px;
            }
          } 
        }
        .ant-table-tbody {
          border: none;
        }
      }
      
    }
    .speicalModal {
      .ant-modal {
        height: initial !important;
        padding-bottom: 0px;
      }
  
      .ant-modal-content {
        background-color: #152543;
      }
  
  
      .ant-modal-header {
        background-color: #152543;
      }
  
      .ant-modal-title {
        color: #FFF;
      }
  
      .ant-modal-close-x {
        color: #FFF;
      }
    }
   
  }

  * {
    box-sizing: border-box;
  }

  h1,
  h2,
  h3,
  h4,
  p {
    margin: 0;
    padding: 0;
  }

  background: rgb(35, 40, 51);
  background-size: 100% 100%;
  padding:10px;
  position: relative;
  // height: 1700px;
}

.handleColumnVal {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100px;
  text-align: center;
  color: rgba(232, 232, 232, 0.65);
  font-size: 12px;
}

.main {
  // display: flex;
  height: calc(100% - 40px);
}



#ScrollBoard {
  
  :global {
    .ant-col :nth-child(5) div {
      border-right: 0px;
    }
  }
}



.modalTableBox {
  width: 100%;
  :global {
    .ant-form-item label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-table-tbody>tr.ant-table-row:hover>td {
      background: none !important;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection {
      font-size: 12px;
      height: 20px;
      margin-top: 11px;
    }

    .ant-select-selection__rendered {
      line-height: 20px;
    }

    .ant-input {
      margin-top: 11px;
      height: 20px;
      font-size: 12px;
      line-height: 12px;
    }

    .ant-table-column-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-table-placeholder {
      background: rgb(35, 40, 51);
      border: 1px solid rgb(121, 121, 121);
      display: none;
    }

    .ant-table-body {
      background: rgb(53, 61, 77);
      margin: 0px !important;
    }

    .ant-empty-description {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-bordered .ant-table-tbody>tr>td {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-small {
      border-radius: 0;
      border: 1px solid rgb(121, 121, 121);
    }

    .ant-table-bordered .ant-table-thead>tr>th {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-align-center {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-tbody {
      display: block;
      color: #fff;
      text-align: center;
      height: 90px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .ant-table-thead>tr,
    .ant-table-tbody>tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .ant-table-thead>tr {
      border: none;
    }

    table tbody::-webkit-scrollbar {
      width: 0px;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-thead>tr:first-child>th:first-child {
      border-radius: 0px;
    }

    .ant-table-small.ant-table-bordered .ant-table-content {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .anticon {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item-active,
    .ant-pagination-item-active:hover {
      border-color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item {
      a {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-container {
      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-ellipsis {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-active {
      background: rgb(35, 40, 51);
    }
  }
}

.MoadlTable {
  :global {
    .ant-table-body {

      colgroup {
        display: flex;
        width: 949px;
      }
    }

    .ant-table-tbody {
      min-height: 400px;
      overflow-y: auto;
      

    }
  }


}

.TooltipClass {
  :global {
    .ant-tooltip-arrow::before {
      background: rgb(204, 204, 204);
    }

    .ant-tooltip-inner {
      color: #000;
      font-size: 12px;
      background: rgb(204, 204, 204);
    }
  }
}

.Form {
  width: 100%;

  :global {
    .ant-form-item-label {
      label {
        color: #FFF
      }
    }
  }
}

