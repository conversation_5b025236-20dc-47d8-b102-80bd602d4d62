import {
  Button,
  Col,
  Form,
  message,
  Row,
  Select,
  DatePicker,
  Card,
  Input,
  Divider,
  Modal,
  Icon,
  InputNumber,
} from 'antd';
import { connect } from 'dryad';
import { Fragment, useEffect, useState } from 'react';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal';
import EditModal from './EditModal';
import { useAuth } from 'ponshine';
import { onEnterPage } from '@/utils/openTab';
import { Licensee, useLicensee } from 'ponshine';
import styles from './index.less';

const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
  pageSize: 10,
  pageNum: 1,
};
const formItemLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};

const MobileFraudVerificationResume = (props) => {
  const {
    dispatch,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue, getFieldsValue },
    mobileFraudVerificationResume: {
      tableData,
      details,
      localNetworkList,
      classificationList,
      gopageParams,
    },
    loading,
    location: { query },
  } = props;
  const [params, setParams] = useState(defaultPagination);
  const [importVisible, setImportVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [isDetail, setIsDetail] = useState(false);
  const { authState } = useAuth() || {};
  const {
    user: { role = {} },
  } = authState || {};
  const isHightAuth = role.id == 1 || role.id == 5;
  const isEditAuth = role.id == 1 || role.id == 2 || role.id == 5;
  const [caseTypeList, setCaseTypeList] = useState([]);
  const renderNum = (type) => {
    return configData[type] || 60;
  };
  const getConfig = () => {
    request('/api/hn/mobileReplay/getTimeConfig', {
      method: 'get',
    }).then((res) => {
      if (res.code == 200) {
        setConfigData(res.data || {});
      }
    });
  };

  const [configData, setConfigData] = useState({});
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'taskId',
      align: 'center',
      ellipsis: true,
      width: 80,
    },
    {
      title: '通报时间',
      dataIndex: 'notificationDate',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '分类',
      dataIndex: 'classification',
      align: 'center',
      ellipsis: true,
      width: 110,
      render: (text) => text || '--',
    },
    {
      title: '本地网',
      dataIndex: 'localNetwork',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },

    {
      title: '号码',
      dataIndex: 'reportedPhoneNum',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '案件类别',
      dataIndex: 'caseType',
      width: 110,
      align: 'left',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '受害人电话',
      dataIndex: 'victimPhoneNum',
      align: 'center',
      ellipsis: true,
      width: 110,
      render: (text) => text || '--',
    },
    {
      title: '工信部考核',
      dataIndex: 'miitAssessment',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '省联席办考核',
      dataIndex: 'provincialOfficeAssessment',
      width: 120,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '是否已复盘',
      dataIndex: 'ifReplay',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '导入时间',
      dataIndex: 'gmtCreate',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '提供单位',
      dataIndex: 'provider',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '归属地',
      dataIndex: 'reportedLocation',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '发案开始时间',
      dataIndex: 'incidentStartTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '发案结束时间',
      dataIndex: 'incidentEndTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '受害人电话',
      dataIndex: 'victimPhoneNum',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '举报号码运营商',
      dataIndex: 'reportIsp',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '举报号码归属省',
      dataIndex: 'reportProvince',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '举报号码归属市',
      dataIndex: 'reportCity',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '举报号码归属市',
      dataIndex: 'callTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '举报内容',
      dataIndex: 'reportContent',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '是否话务异常',
      dataIndex: 'ifTelephoneException',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '是否存在通联',
      dataIndex: 'ifCommunications',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '通话时间',
      dataIndex: 'talkTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '发话地市',
      dataIndex: 'cityOfOrigin',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '区',
      dataIndex: 'areaOfOrigin',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '省',
      dataIndex: 'provinceOfOrigin',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '高危基站',
      dataIndex: 'highRiskBaseStationNum',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '是否诈骗',
      dataIndex: 'ifFraud',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '下发时是否关停',
      dataIndex: 'ifShutdownWhenIssued',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '省内模型是否关停',
      dataIndex: 'ifShutdownProvinceModel',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '省模型关停时间差（小时）',
      dataIndex: 'provinceShutdownInterval',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '省内模型分类',
      dataIndex: 'provinceModelType',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },

    {
      title: `省内模型关停次数（${renderNum('timeConfigOfProvinceModelShutdownTimes')}）天内`,
      dataIndex: 'timeConfigOfProvinceModelShutdownTimes',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '集团模型是否关停',
      dataIndex: 'ifShutdownGroupModel',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '团关停时间差（小时）',
      dataIndex: 'groupShutdownInterval',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `集团模型关停次数（${renderNum('timeConfigOfGroupModelShutdownTimes')}）天内`,
      dataIndex: 'groupModelShutdownTimes',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `是否多次复机（${renderNum('timeConfigOfIfResumeFrequently')}）天`,
      dataIndex: 'ifResumeFrequently',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `复机次数（${renderNum('timeConfigOfResumeTimes')}）天`,
      dataIndex: 'resumeTimes',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `涉案前是否被模型关停（${renderNum('timeConfigOfIfShutdownByModelBefore')}）天`,
      dataIndex: 'ifShutdownByModelBefore',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `复机方式`,
      dataIndex: 'resumeWay',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `是否SP`,
      dataIndex: 'ifSp',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `${renderNum('timeConfigOfSpMessageContent')}天SP短信内容\n（短码+次数）`,
      dataIndex: 'spMessageContent',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `是否新入网（${renderNum('timeConfigOfIfNewAccess')}个月内）`,
      dataIndex: 'ifNewAccess',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `号码是否注册互联网账号`,
      dataIndex: 'ifRegisterInternet',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `是否多次更换终端（${renderNum('timeConfigOfIfChangeTerminalFrequently')}天）`,
      dataIndex: 'ifChangeTerminalFrequently',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `更换终端次数（${renderNum('timeConfigOfChangeTerminalTimes')}天）`,
      dataIndex: 'ifChangeTerminalFrequently',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `异常备注`,
      dataIndex: 'exceptionRemark',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `是否校园卡`,
      dataIndex: 'ifCampusCard',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `是否加黑`,
      dataIndex: 'ifAddBlack',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `加黑到期时间`,
      dataIndex: 'blackExpireTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `加黑原因`,
      dataIndex: 'blackReason',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `关停时间`,
      dataIndex: 'shutdownTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `备注`,
      dataIndex: 'remark',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `本地网`,
      dataIndex: 'localnetwork',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `营业区`,
      dataIndex: 'businessArea',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `入网时间`,
      dataIndex: 'internetAccessTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `渠道类型`,
      dataIndex: 'channelType',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `受理网点`,
      dataIndex: 'acceptancePoint',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `年龄`,
      dataIndex: 'age',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `销售品`,
      dataIndex: 'saleCategory',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `销售品价格`,
      dataIndex: 'salePrice',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `是否主副卡`,
      dataIndex: 'masterCard',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `使用客户`,
      dataIndex: 'usingCustomer',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `使用人证件号码`,
      dataIndex: 'certificatesNumber',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `使用人证件类型`,
      dataIndex: 'certificatesType',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `使用客户地址`,
      dataIndex: 'usingCustomerAddress',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `产权客户`,
      dataIndex: 'propertyCustomer',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `产权证件号码`,
      dataIndex: 'propertyCertificatesNumber',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `产权证件类型`,
      dataIndex: 'propertyCertificatesType',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `产权客户地址`,
      dataIndex: 'propertyCustomerAddress',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `客户类型`,
      dataIndex: 'customerType',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `产品类型`,
      dataIndex: 'productType',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `号码标识`,
      dataIndex: 'phoneNumTag',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `用户装机地址`,
      dataIndex: 'userInstallAddress',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `复机单号`,
      dataIndex: 'resumeNumbers',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `复机时间`,
      dataIndex: 'resumeTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `复机工号`,
      dataIndex: 'resumeWorkId',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `复机人`,
      dataIndex: 'resumeOperator',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `受理员工`,
      dataIndex: 'acceptStaff',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `受理工号`,
      dataIndex: 'acceptanceWorkId',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `揽机人工号`,
      dataIndex: 'grabMachineWorkId',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `揽机人名称`,
      dataIndex: 'grabMachineName',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `揽机人组织`,
      dataIndex: 'grabMachineOrganize',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `揽机人组织渠道类型`,
      dataIndex: 'grabMachineOrganizeChannelType',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `甩单时间`,
      dataIndex: 'throwOrderTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `甩单工号`,
      dataIndex: 'throwOrderWorkId',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `甩单人`,
      dataIndex: 'throwOrderOperator',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `输单时间`,
      dataIndex: 'inputOrderTime',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `输单工号`,
      dataIndex: 'inputOrderWorkId',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: `输单人`,
      dataIndex: 'inputOrderOperator',
      width: 110,
      align: 'center',
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '操作',
      width: 140,
      align: 'center',
      fixed: 'right',
      render: (record) => {
        return (
          <>
            <Licensee license="mobileReplay_update">
              <a onClick={(e) => editInfo(e, record)}>编辑</a>
              <Divider type="vertical" />
            </Licensee>
            <Licensee license="mobileReplay_delete">
              <a onClick={(e) => delInfo(e, record.id)}>删除</a>
            </Licensee>
          </>
        );
      },
    },
  ];
  const editInfo = (e, record) => {
    e.stopPropagation();
    e.preventDefault();
    setEditVisible(true);
    getMobileReplayDetailById(record.id);
  };

  const hideEdit = () => {
    setEditVisible(false);
    setIsDetail(false);
  };
  const getMobileReplayDetailById = (id) => {
    dispatch({
      type: 'mobileFraudVerificationResume/getMobileReplayDetailById',
      payload: { id },
    });
  };
  const handleEditOk = (values, callback) => {
    dispatch({
      type: 'mobileFraudVerificationResume/updateMobileReplayDetailById',
      payload: values,
      callback: (res) => {
        if (res.code == 200) {
          message.success(res.message || '修改成功');
          callback();
          hideEdit();
          getPageInvolvedReplay({ ...params, ...defaultPagination });
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const delInfo = (e, id) => {
    e.stopPropagation();
    e.preventDefault();
    Modal.confirm({
      title: '复盘任务删除',
      content: '是否确认删除该条复盘任务?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        deleteFun(id);
      },
    });
  };

  const deleteFun = (id) => {
    dispatch({
      type: 'mobileFraudVerificationResume/deleteMobileReplayById',
      payload: { id },
      callback: (res) => {
        if (res.code == 200) {
          message.success(res.message || '删除成功');
          getPageInvolvedReplay({ ...params, ...defaultPagination });
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const handleExport = () => {
    const newParams = {
      ...params,
      pageNum: 1,
      pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/mobileReplay/exportInvolvedReplay',
      decode: true,
      params: newParams,
      method: 'POST',
    });
  };

  const handleImport = () => {
    setImportVisible(true);
  };

  const hideImport = () => {
    setImportVisible(false);
  };

  const [confirmLoading, setConfirmLoading] = useState(false);

  const onImport = (values, callback) => {
    setConfirmLoading(true);
    dispatch({
      type: 'mobileFraudVerificationResume/batchImportMobileReplayInfo',
      payload: values,
      callback: (res) => {
        setConfirmLoading(false);
        if (res.code == 200) {
          message.success(res.message || '导入成功');
          getPageInvolvedReplay({ ...params, ...defaultPagination });
          hideImport();
        } else {
          if (res.code == 401) {
            callback();
          } else {
            message.error(res.message);
          }
        }
      },
    });
  };

  const getPageInvolvedReplay = (params) => {
    dispatch({
      type: 'mobileFraudVerificationResume/pageMobileReplay',
      payload: params,
    });
    setParams(params);
  };

  const [netWorkList, setNetWorkList] = useState([]);

  const getOrganizationByUser = () => {
    dispatch({
      type: 'mobileFraudVerificationResume/getOrganizationByUser',
      callback: (res) => {
        if (res && res.code == '200' && res.data) {
          setNetWorkList(res.data || []);
        } else {
          setNetWorkList([]);
        }
      },
    });
  };

  //查询
  const handleSearch = () => {
    validateFields(
      (
        err,
        {
          notificationDateStart,
          miitAssessmentStart,
          provincialOfficeAssessmentStart,
          gmtCreateStart,
          ageMax,
          ageMin,
          ...rest
        },
      ) => {
        if (err) {
          return;
        }

        const newParams = {
          ...params,
          ...defaultPagination,
          ...rest,
          notificationDateStart:
            notificationDateStart && notificationDateStart.length > 0
              ? moment(notificationDateStart[0]).format('YYYYMMDD')
              : '',
          notificationDateEnd:
            notificationDateStart && notificationDateStart.length > 0
              ? moment(notificationDateStart[1]).format('YYYYMMDD')
              : '',
          miitAssessmentStart:
            miitAssessmentStart && miitAssessmentStart.length > 0
              ? moment(miitAssessmentStart[0]).format('YYYYMM01')
              : '',
          miitAssessmentEnd:
            miitAssessmentStart && miitAssessmentStart.length > 0
              ? moment(miitAssessmentStart[1])?.endOf('months')?.format('YYYYMMDD')
              : '',
          provincialOfficeAssessmentStart:
            provincialOfficeAssessmentStart && provincialOfficeAssessmentStart.length > 0
              ? moment(provincialOfficeAssessmentStart[0]).format('YYYYMM01')
              : '',
          provincialOfficeAssessmentEnd:
            provincialOfficeAssessmentStart && provincialOfficeAssessmentStart.length > 0
              ? moment(provincialOfficeAssessmentStart[1])?.endOf('months')?.format('YYYYMMDD')
              : '',
          gmtCreateStart:
            gmtCreateStart && gmtCreateStart.length > 0
              ? moment(gmtCreateStart[0]).format('YYYYMMDD')
              : '',
          gmtCreateEnd:
            gmtCreateStart && gmtCreateStart.length > 0
              ? moment(gmtCreateStart[1]).format('YYYYMMDD')
              : '',
          ageMax,
          ageMin,
        };
        getPageInvolvedReplay(newParams);
      },
    );
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    const newParms = { ...params, pageNum: pagination.current, pageSize: pagination.pageSize };
    getPageInvolvedReplay(newParms);
  };

  const handleInfo = (record) => {
    setIsDetail(true);
    setEditVisible(true);
    getMobileReplayDetailById(record.id);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultPagination });
    getPageInvolvedReplay({ ...defaultPagination });
  };

  // const getOrganizationByUser = () => {
  //     dispatch({
  //         type: "caseNotifyVerify/getOrganizationByUser",
  //     });
  // }

  const getCaseTypeList = () => {
    dispatch({
      type: 'mobileFraudVerificationResume/getSystemConfigListByConfigType',
      payload: { configType: 'case_type' },
      callback: (res) => {
        if (res.code == 200) {
          setCaseTypeList(res.data);
        }
      },
    });
  };

  const getAllClassification = () => {
    dispatch({
      type: 'mobileFraudVerificationResume/getAllClassification',
    });
  };

  useEffect(() => {
    getOrganizationByUser();
    getAllClassification();
    getPageInvolvedReplay({ ...defaultPagination });
  }, []);
  const min = getFieldsValue()?.ageMin == 0 ? '0' : getFieldsValue()?.ageMin;
  const max = getFieldsValue()?.ageMax == 0 ? '0' : getFieldsValue()?.ageMax;
  useEffect(() => {
    if (min && max) {
      if (Number(min) >= Number(max)) {
        setFieldsValue({
          ageMin: undefined,
        });
      }
      if (Number(min) < 0) {
        setFieldsValue({
          ageMin: undefined,
        });
      }
    } else {
      setFieldsValue({
        ageMin: min,
      });
    }
  }, [min]);

  useEffect(() => {
    if (min && max) {
      if (Number(min) >= Number(max)) {
        setFieldsValue({
          ageMax: undefined,
        });
      }
      if (Number(max) < 0) {
        setFieldsValue({
          ageMax: undefined,
        });
      }
    } else {
      setFieldsValue({
        ageMax: max,
      });
    }
  }, [max]);
  useEffect(() => {
    if (gopageParams) {
      getPageInvolvedReplay({
        ...params,
        caseType: gopageParams.caseType,
        classification: gopageParams.classification,
      });
      setFieldsValue({
        caseType: gopageParams.caseType,
        classification: gopageParams.classification,
      });
      dispatch({
        type: 'mobileFraudVerificationResume/save',
        payload: {
          gopageParams: undefined,
        },
      });
    }
  }, [gopageParams]);

  const showInfo = true;
  // useLicensee('caseNotifyVerify_getInvolvedReplayDetailById')
  // useLicensee('caseNotifyVerify_getInvolvedReplayDetailById')
  // useEffect(() => {
  //     getOrganizationByUser();
  // }, [])

  const [showSearch, setShowSearch] = useState(false);

  const changeDate = (v, type) => {
    setFieldsValue({ [type]: v });
  };

  const changePanel = (v, type) => {
    setFieldsValue({ [type]: v });
  };
  const filterListStr =
    '任务ID,通报时间,分类,本地网,号码,案件类别,受害人电话,工信部考核,省联席办考核,是否已复盘,导入时间,操作';
  const filterList = filterListStr.split(',');
  let newfilterList = [];
  columns.forEach((item) => {
    if (
      filterList.some((items) => {
        return items == item.title;
      })
    ) {
      newfilterList.push({ dataIndex: item.dataIndex });
    }
  });
  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Licensee license="mobileReplay_pageQuery">
          <div style={{ display: 'flex' }}>
            <div style={{ flex: 1, width: 0 }}>
              <Row>
                <Col span={8}>
                  <Form.Item label="通报时间">
                    {getFieldDecorator('notificationDateStart')(
                      <DatePicker.RangePicker format={'YYYY-MM-DD'} allowClear />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="号码">
                    {getFieldDecorator('reportedPhoneNum')(
                      <Input placeholder="请输入" allowClear />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="分类">
                    {getFieldDecorator('classification')(
                      <Select placeholder="请输入" allowClear showSearch>
                        {classificationList.map((value) => (
                          <Option value={value}>{value}</Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ display: showSearch ? 'block' : 'none' }}>
                {
                  <Fragment>
                    <Col span={8}>
                      <Form.Item label="是否已复盘">
                        {getFieldDecorator('ifReplay')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="本地网">
                        {getFieldDecorator('localNetwork')(
                          <Select placeholder="请输入" allowClear showSearch>
                            {netWorkList.map((item) => (
                              <Option value={item.name}>{item.name}</Option>
                            ))}
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="是否通联">
                        {getFieldDecorator('ifCommunications')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                            <Option value="疑似">疑似</Option>
                            <Option value="无">无</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="案件类别">
                        {getFieldDecorator('caseType')(<Input placeholder="请输入" allowClear />)}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="发话地/省/区" className={styles.myStylesFormItem}>
                        {getFieldDecorator('area')(<Input placeholder="请输入" allowClear />)}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="省内模型是否关停" className={styles.myStylesFormItem}>
                        {getFieldDecorator('ifShutdownProvinceModel')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="集团模型是否关停" className={styles.myStylesFormItem}>
                        {getFieldDecorator('ifShutdownGroupModel')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="工信部考核">
                        {getFieldDecorator('miitAssessmentStart')(
                          <DatePicker.RangePicker
                            format={'YYYY-MM'}
                            mode={['month', 'month']}
                            onPanelChange={(v) => changePanel(v, 'miitAssessmentStart')}
                            onChange={(v) => changeDate(v, 'miitAssessmentStart')}
                          />,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="省联席办考核" className={styles.myStylesFormItem}>
                        {getFieldDecorator('provincialOfficeAssessmentStart')(
                          <DatePicker.RangePicker
                            format={'YYYY-MM'}
                            mode={['month', 'month']}
                            onPanelChange={(v) => changePanel(v, 'provincialOfficeAssessmentStart')}
                            onChange={(v) => changeDate(v, 'provincialOfficeAssessmentStart')}
                          />,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="导入时间">
                        {getFieldDecorator('gmtCreateStart')(
                          <DatePicker.RangePicker format={'YYYY-MM-DD'} allowClear />,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="渠道类型">
                        {getFieldDecorator('channelType')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="实体渠道">实体渠道</Option>
                            <Option value="电子渠道">电子渠道</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <div className={styles.age}>
                        <Form.Item label="年龄">
                          {getFieldDecorator('ageMin', {
                            rules: [
                              {
                                required: max && true,
                                message: '请选择起始年龄',
                              },
                            ],
                          })(<InputNumber allowClear placeholder="请输入"></InputNumber>)}
                        </Form.Item>
                        <div style={{ margin: '0 10px' }}> - </div>
                        <Form.Item>
                          {getFieldDecorator('ageMax', {
                            rules: [
                              {
                                required: min && true,
                                message: '请选择起始年龄',
                              },
                            ],
                          })(<InputNumber allowClear placeholder="请输入"></InputNumber>)}
                        </Form.Item>
                      </div>
                    </Col>
                  </Fragment>
                }
              </Row>
            </div>
            <div style={{ width: 60, flexShrink: 0, paddingTop: 10 }}>
              <span
                onClick={() => {
                  setShowSearch(!showSearch);
                }}
                style={{ color: '#1190ff', cursor: 'pointer' }}
              >
                {showSearch ? '折叠' : '展开'}
                <Icon type={showSearch ? 'up' : 'down'} />
              </span>
            </div>
          </div>
        </Licensee>
        <Row>
          <Col span={24} style={{ marginBottom: 20, textAlign: 'right' }}>
            <Licensee license="mobileReplay_pageQuery">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 10 }}>
                重置
              </Button>
            </Licensee>
            <Licensee license="mobileReplay_batchExport">
              <Button type="primary" onClick={handleExport} style={{ margin: '0px 10px' }}>
                导出
              </Button>
            </Licensee>
            <Licensee license="mobileReplay_batchImport">
              {/* {isEditAuth && */}
              <Button type="primary" onClick={handleImport}>
                通报信息导入
              </Button>
              {/* } */}
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        detailColumns={columns.map((item) => {
          return { ...item, key: item?.dataIndex, ellipsis: true, width: '200', align: 'center' };
        })}
        tools={true}
        float="right"
        rowKey="id"
        columns={newfilterList}
        loading={loading}
        data={{
          list: tableData.items || [],
          pagination: {
            current: params.pageNum,
            pageSize: params.pageSize,
            total: tableData?.totalNum || 0,
          },
        }}
        onChange={handlePaginationTable}
        showSelectCount={false}
        rowSelectionProps={false}
        // scroll={{ x: true }}
        onRow={(record) => {
          return {
            onDoubleClick: () => {
              showInfo && handleInfo(record);
            },
          };
        }}
      />
      {importVisible && (
        <BatchImportModal
          title="批量导入"
          visible={importVisible}
          onImport={onImport}
          onClose={hideImport}
          loading={confirmLoading}
          errorExportUrl={'/api/hn/mobileReplay/downloadErrorExcel'}
          downTemplateUrl={`template/getTemplate?templateCode=mobileReplay`}
        />
      )}
      {editVisible && (
        <EditModal
          visible={editVisible}
          onClose={hideEdit}
          handleEditOk={handleEditOk}
          isDetail={isDetail}
          details={details}
        />
      )}
    </Card>
  );
};

export default connect(({ mobileFraudVerificationResume, loading }) => ({
  mobileFraudVerificationResume,
  loading: loading.effects['mobileFraudVerificationResume/pageMobileReplay'],
}))(Form.create()(MobileFraudVerificationResume));
