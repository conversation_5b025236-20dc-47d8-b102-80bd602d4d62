import {
  Button,
  Col,
  Form,
  message,
  Row,
  Select,
  DatePicker,
  Card,
  Input,
  Divider,
  Modal,
  Icon,
  InputNumber,
  Tooltip,
} from 'antd';
import { connect } from 'dryad';
import { Fragment, useEffect, useState } from 'react';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal';
import EditModal from './EditModal';
import { useAuth } from 'ponshine';
import { onEnterPage } from '@/utils/openTab';
import { Licensee, useLicensee } from 'ponshine';
import styles from './index.less';
import { getSystemConfigListByConfigType } from '@/services/common';
import { withContext } from 'demasia-pro-layout';

import ExportApprove from '@/components/ExportApprove';

const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
  pageSize: 10,
  pageNum: 1,
};
const formItemLayout = {
  labelCol: {
    span: 10,
  },
  wrapperCol: {
    span: 14,
  },
};

const MobileFraudVerificationResume = Form.create()((props) => {
  const {
    dispatch,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue, getFieldsValue },
    mobileFraudVerificationResume: {
      tableData,
      details,
      localNetworkList,
      classificationList,
      searchParams,
    },
    location: { query },
    loading,
    reloadTab,
  } = props;

  const [params, setParams] = useState(query === '{}' ? defaultPagination : searchParams);
  const [importVisible, setImportVisible] = useState(false);
  const [importBatchResultVisible, setImportBatchResultVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [isDetail, setIsDetail] = useState(false);
  const { authState } = useAuth() || {};
  const [selectedRows, setSelectedRows] = useState([]);
  const [allColumns, setAllColumns] = useState([]);
  const initDate = [moment(), moment()];

  const {
    user: { role = {} },
  } = authState || {};
  const isHightAuth = role.id == 1 || role.id == 5;
  const isEditAuth = role.id == 1 || role.id == 2 || role.id == 5;
  const [caseTypeList, setCaseTypeList] = useState([]);

  // 批量查询
  const [reviewVisible, setReviewVisible] = useState(false);

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'taskId',
      align: 'center',
      ellipsis: true,
      width: 80,
    },
    {
      title: '通报时间',
      dataIndex: 'notificationDate',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'classification',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '本地网',
      dataIndex: 'localNetwork',
      width: 80,
      align: 'center',
      ellipsis: true,
    },

    {
      title: '号码',
      dataIndex: 'reportedPhoneNum',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '案件类别',
      dataIndex: 'caseType',
      width: 100,
      align: 'left',
      ellipsis: true,
    },
    {
      title: '受害人电话',
      dataIndex: 'victimPhoneNum',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '工信部考核',
      dataIndex: 'miitAssessment',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '省联席办考核',
      dataIndex: 'provincialOfficeAssessment',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '是否已复盘',
      dataIndex: 'ifReplay',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '导入时间',
      dataIndex: 'gmtCreate',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'opt',
      width: 60,
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <>
            <Licensee license="mobileReplay_update">
              <a onClick={(e) => editInfo(e, record)}>编辑</a>
              {/* <Divider type="vertical" /> */}
            </Licensee>
            {/* <Licensee license="mobileReplay_delete">
              <a onClick={(e) => delInfo(e, record.id)}>删除</a>
            </Licensee> */}
          </>
        );
      },
    },
  ];

  const editInfo = (e, record) => {
    e.stopPropagation();
    e.preventDefault();
    setEditVisible(true);
    getMobileReplayDetailById(record.id);
  };

  const hideEdit = () => {
    setEditVisible(false);
    setIsDetail(false);
  };
  const getMobileReplayDetailById = (id) => {
    dispatch({
      type: 'mobileFraudVerificationResume/getMobileReplayDetailById',
      payload: { id },
    });
  };
  const handleEditOk = (values, callback) => {
    dispatch({
      type: 'mobileFraudVerificationResume/updateMobileReplayDetailById',
      payload: values,
      callback: (res) => {
        if (res.code == 200) {
          message.success(res.message || '修改成功');
          callback();
          hideEdit();
          getPageInvolvedReplay({ ...params, ...defaultPagination });
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const onSelectRow = (selectedRows) => {
    setSelectedRows(selectedRows);
  };

  const delInfo = (e, id) => {
    e.stopPropagation();
    e.preventDefault();
    Modal.confirm({
      title: '复盘任务删除',
      content: '是否确认删除该条复盘任务?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        deleteFun(id);
      },
    });
  };

  const deleteFun = (id) => {
    dispatch({
      type: 'mobileFraudVerificationResume/deleteMobileReplayById',
      payload: { id },
      callback: (res) => {
        if (res.code == 200) {
          message.success(res.message || '删除成功');
          getPageInvolvedReplay({ ...params, ...defaultPagination });
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const handleExport = () => {
    const newParams = {
      ...params,
      pageNum: 1,
      pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/mobileReplay/exportInvolvedReplay',
      decode: true,
      params: newParams,
      method: 'POST',
    });
  };

  const handleImport = () => {
    setImportVisible(true);
  };

  const hideImport = () => {
    setImportVisible(false);
  };

  const [confirmLoading, setConfirmLoading] = useState(false);

  const onImport = (values, callback) => {
    setConfirmLoading(true);
    dispatch({
      type: 'mobileFraudVerificationResume/batchImportMobileReplayInfo',
      payload: values,
      callback: (res) => {
        setConfirmLoading(false);
        if (res.code == 200) {
          message.success(res.message || '导入成功');
          setSelectedRows([]);
          getPageInvolvedReplay({ ...params, ...defaultPagination });
          hideImport();
        } else {
          if (res.code == 401) {
            callback();
          } else {
            message.error(res.message);
          }
        }
      },
    });
  };

  // 确认导入批量复盘结果
  const onImportBatchResult = (values, callback) => {
    setConfirmLoading(true);
    dispatch({
      type: 'mobileFraudVerificationResume/batchImportReplayResult',
      payload: values,
      callback: (res) => {
        setConfirmLoading(false);

        if (res.code == 200) {
          message.success(res.message || '导入成功');
          setSelectedRows([]);
          getPageInvolvedReplay({ ...params, ...defaultPagination });
          setImportBatchResultVisible(false);
        } else {
          if (res.code == 401) {
            callback();
            getPageInvolvedReplay({ ...params, ...defaultPagination });
          } else {
            message.error(res.message);
          }
        }
      },
    });
  };

  const [reviewLoading, setReviewLoading] = useState(false);
  // 批量查询
  const onReviewImport = (values, callback) => {
    setReviewLoading(true);
    dispatch({
      type: 'mobileFraudVerificationResume/batchQueryRealtimeStatus',
      payload: values,
      callback: (res) => {
        setReviewLoading(false);
        if (res.data) {
          let fileName = res.response.headers.get('content-disposition')?.split('filename=')[1];
          fileName = fileName ? decodeURIComponent(decodeURI(fileName)) : `脚本.zip`;
          const blob = res.data;
          if ('download' in document.createElement('a')) {
            // 非IE下载
            const elink = document.createElement('a');
            elink.download = fileName || `脚本.zip`;
            elink.style.display = 'none';
            elink.href = URL.createObjectURL(blob);
            document.body.appendChild(elink);
            elink.click();
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, fileName);
          }
          setReviewVisible(false);
        } else if (res?.data?.size <= 52) {
          message.error('查询错误！');
        }
      },
    });
  };

  const getPageInvolvedReplay = (params) => {
    const { notificationDateStart, notificationDateEnd } = params;
    const newParams = {
      ...params,
      notificationDateStart: notificationDateStart || initDate?.[0].format('YYYYMMDD'),
      notificationDateEnd: notificationDateEnd || initDate?.[1].format('YYYYMMDD'),
    };
    dispatch({
      type: 'mobileFraudVerificationResume/pageMobileReplay',
      payload: newParams,
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
    setParams(newParams);
  };

  const [netWorkList, setNetWorkList] = useState([]);

  const getOrganizationByUser = () => {
    dispatch({
      type: 'mobileFraudVerificationResume/getOrganizationByUser',
      callback: (res) => {
        if (res && res.code == '200' && res.data) {
          setNetWorkList(res.data || []);
        } else {
          setNetWorkList([]);
        }
      },
    });
  };

  //查询
  const handleSearch = () => {
    validateFields(
      (
        err,
        {
          notificationDateStart,
          miitAssessmentStart,
          provincialOfficeAssessmentStart,
          gmtCreateStart,
          ageMax,
          ageMin,
          ...rest
        },
      ) => {
        if (err) {
          return;
        }

        const newParams = {
          ...params,
          ...defaultPagination,
          ...rest,
          notificationDateStart: notificationDateStart?.[0]?.format('YYYYMMDD'),
          notificationDateEnd: notificationDateStart?.[1]?.format('YYYYMMDD'),
          miitAssessmentStart:
            miitAssessmentStart && miitAssessmentStart.length > 0
              ? moment(miitAssessmentStart[0])?.format('YYYYMM01')
              : '',
          miitAssessmentEnd:
            miitAssessmentStart && miitAssessmentStart.length > 0
              ? moment(miitAssessmentStart[1])?.endOf('months')?.format('YYYYMMDD')
              : '',
          provincialOfficeAssessmentStart:
            provincialOfficeAssessmentStart && provincialOfficeAssessmentStart.length > 0
              ? moment(provincialOfficeAssessmentStart[0])?.format('YYYYMM01')
              : '',
          provincialOfficeAssessmentEnd:
            provincialOfficeAssessmentStart && provincialOfficeAssessmentStart.length > 0
              ? moment(provincialOfficeAssessmentStart[1])?.endOf('months')?.format('YYYYMMDD')
              : '',
          gmtCreateStart:
            gmtCreateStart && gmtCreateStart.length > 0
              ? moment(gmtCreateStart[0])?.format('YYYYMMDD')
              : '',
          gmtCreateEnd:
            gmtCreateStart && gmtCreateStart.length > 0
              ? moment(gmtCreateStart[1])?.format('YYYYMMDD')
              : '',
          ageMax,
          ageMin,
        };
        setSelectedRows([]);
        getPageInvolvedReplay(newParams);
      },
    );
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    const newParms = { ...params, pageNum: pagination.current, pageSize: pagination.pageSize };
    console.log(newParms, '0000');
    getPageInvolvedReplay(newParms);
  };

  const handleInfo = (record) => {
    setIsDetail(true);
    setEditVisible(true);
    getMobileReplayDetailById(record.id);
  };

  const handleReset = () => {
    resetFields();
    setSelectedRows([]);
    setParams({ ...defaultPagination });
    getPageInvolvedReplay({ ...defaultPagination });
    dispatch({
      type: 'mobileFraudVerificationResume/saveGoSearchParamas',
      payload: {},
    });
  };

  const handleDelete = () => {
    if (!selectedRows.length) return message.warning('请选择要删除的复盘任务');

    Modal.confirm({
      title: '复盘任务删除',
      content: '是否确认删除该复盘任务?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        dispatch({
          type: 'mobileFraudVerificationResume/batchDeleteById',
          payload: { idList: selectedRows?.map((ele) => ele.id) },
          callback: (res) => {
            if (res.code == 200) {
              message.success(res.message || '删除成功');
              setSelectedRows([]);
              getPageInvolvedReplay({ ...params, ...defaultPagination });
            } else {
              message.error(res.message);
            }
          },
        });
      },
    });
  };

  // const getOrganizationByUser = () => {
  //     dispatch({
  //         type: "caseNotifyVerify/getOrganizationByUser",
  //     });
  // }

  const getCaseTypeList = () => {
    dispatch({
      type: 'mobileFraudVerificationResume/getSystemConfigListByConfigType',
      payload: { configType: 'case_type' },
      callback: (res) => {
        if (res.code == 200) {
          setCaseTypeList(res.data);
        }
      },
    });
  };

  const getAllClassification = () => {
    dispatch({
      type: 'mobileFraudVerificationResume/getAllClassification',
    });
  };

  const setWidth = (text) => {
    let width = text?.length * 14 + 32;
    if (text?.includes('时间') || text?.includes('号码') || text?.includes('工号')) {
      width = 160;
    }
    return width;
  };

  useEffect(() => {
    getOrganizationByUser();
    getAllClassification();
    // getPageInvolvedReplay({ ...defaultPagination });

    getSystemConfigListByConfigType({
      configType: 'MobileReplay',
    }).then((res) => {
      if (res.code === 200) {
        const columnsArr =
          res?.data?.map((ele) => {
            return {
              dataIndex: ele.name,
              title: ele.value,
              width: columns?.find((item) => item.dataIndex === ele.name)?.width
                ? columns?.find((item) => item.dataIndex === ele.name)?.width
                : setWidth(ele.value),

              ellipsis: true,
              render: (text) => {
                return (
                  <Tooltip title={text}>
                    <span>{text ?? '--'}</span>
                  </Tooltip>
                );
              },
            };
          }) || [];
        columnsArr.push(columns[columns.length - 1]);
        setAllColumns(columnsArr);
      }
    });
  }, []);
  const min = getFieldsValue()?.ageMin == 0 ? '0' : getFieldsValue()?.ageMin;
  const max = getFieldsValue()?.ageMax == 0 ? '0' : getFieldsValue()?.ageMax;
  useEffect(() => {
    if (min && max) {
      if (Number(min) >= Number(max)) {
        setFieldsValue({
          ageMin: undefined,
        });
      }
      if (Number(min) < 0) {
        setFieldsValue({
          ageMin: undefined,
        });
      }
    } else {
      setFieldsValue({
        ageMin: min,
      });
    }
  }, [min]);

  useEffect(() => {
    if (min && max) {
      if (Number(min) >= Number(max)) {
        setFieldsValue({
          ageMax: undefined,
        });
      }
      if (Number(max) < 0) {
        setFieldsValue({
          ageMax: undefined,
        });
      }
    } else {
      setFieldsValue({
        ageMax: max,
      });
    }
  }, [max]);

  useEffect(() => {
    let newParams = {};
    if (JSON.stringify(query) === '{}') {
      dispatch({
        type: 'mobileFraudVerificationResume/saveGoSearchParamas',
        payload: {},
      });
      newParams = defaultPagination;
    } else {
      newParams = searchParams;
    }
    getPageInvolvedReplay({
      ...defaultPagination,
      caseType: newParams?.caseType,
      classification: newParams?.classification,
      localNetwork: newParams?.localNetwork,
      notificationDateStart: moment(newParams?.notificationDate)?.format('YYYYMMDD'),
      notificationDateEnd: moment(newParams?.notificationDate)?.format('YYYYMMDD'),
    });
    resetFields();
    setParams({
      ...defaultPagination,
      caseType: newParams?.caseType,
      classification: newParams?.classification,
      localNetwork: newParams?.localNetwork,
      notificationDateStart: moment(newParams?.notificationDate)?.format('YYYYMMDD'),
      notificationDateEnd: moment(newParams?.notificationDate)?.format('YYYYMMDD'),
    });
  }, [JSON.stringify(query)]);

  const showInfo = true;

  const [showSearch, setShowSearch] = useState(false);

  const changeDate = (v, type) => {
    setFieldsValue({ [type]: v });
  };

  const changePanel = (v, type) => {
    setFieldsValue({ [type]: v });
  };

  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Licensee license="mobileReplay_pageQuery">
          <div style={{ display: 'flex' }}>
            <div style={{ flex: 1, width: 0 }}>
              <Row>
                <Col span={8}>
                  <Form.Item label="通报时间">
                    {getFieldDecorator('notificationDateStart', {
                      // initialValue: initDate,
                      initialValue: searchParams?.notificationDate
                        ? [
                            moment(searchParams?.notificationDate),
                            moment(searchParams?.notificationDate),
                          ]
                        : initDate,
                    })(<DatePicker.RangePicker format={'YYYY-MM-DD'} allowClear />)}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="号码">
                    {getFieldDecorator('reportedPhoneNum')(
                      <Input placeholder="请输入" allowClear />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="分类">
                    {getFieldDecorator('classification', {
                      initialValue: searchParams.classification,
                    })(
                      <Select placeholder="请输入" allowClear showSearch>
                        {classificationList.map((value) => (
                          <Option value={value}>{value}</Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ display: showSearch ? 'block' : 'none' }}>
                {
                  <Fragment>
                    <Col span={8}>
                      <Form.Item label="是否已复盘">
                        {getFieldDecorator('ifReplay')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="本地网">
                        {getFieldDecorator('localNetwork', {
                          initialValue: searchParams?.localNetwork,
                        })(
                          <Select placeholder="请输入" allowClear showSearch>
                            {netWorkList.map((item) => (
                              <Option value={item.name}>{item.name}</Option>
                            ))}
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="是否通联">
                        {getFieldDecorator('ifCommunications')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                            <Option value="疑似">疑似</Option>
                            <Option value="无">无</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="案件类别">
                        {getFieldDecorator('caseType', {
                          initialValue: searchParams.caseType,
                        })(<Input placeholder="请输入" allowClear />)}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="发话地/省/区" className={styles.myStylesFormItem}>
                        {getFieldDecorator('area')(<Input placeholder="请输入" allowClear />)}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="省内模型是否关停" className={styles.myStylesFormItem}>
                        {getFieldDecorator('ifShutdownProvinceModel')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="集团模型是否关停" className={styles.myStylesFormItem}>
                        {getFieldDecorator('ifShutdownGroupModel')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="是">是</Option>
                            <Option value="否">否</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="工信部考核">
                        {getFieldDecorator('miitAssessmentStart')(
                          <DatePicker.RangePicker
                            format={'YYYY-MM'}
                            mode={['month', 'month']}
                            onPanelChange={(v) => changePanel(v, 'miitAssessmentStart')}
                            onChange={(v) => changeDate(v, 'miitAssessmentStart')}
                          />,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="省联席办考核" className={styles.myStylesFormItem}>
                        {getFieldDecorator('provincialOfficeAssessmentStart')(
                          <DatePicker.RangePicker
                            format={'YYYY-MM'}
                            mode={['month', 'month']}
                            onPanelChange={(v) => changePanel(v, 'provincialOfficeAssessmentStart')}
                            onChange={(v) => changeDate(v, 'provincialOfficeAssessmentStart')}
                          />,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="导入时间">
                        {getFieldDecorator('gmtCreateStart')(
                          <DatePicker.RangePicker format={'YYYY-MM-DD'} allowClear />,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="渠道类型">
                        {getFieldDecorator('channelType')(
                          <Select placeholder="请选择" allowClear>
                            <Option value="实体渠道">实体渠道</Option>
                            <Option value="电子渠道">电子渠道</Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      {/* <div className={styles.age}> */}
                      <Form.Item label="年龄">
                        {getFieldDecorator('ageMin', {
                          rules: [
                            {
                              required: max && true,
                              message: '请选择起始年龄',
                            },
                          ],
                        })(<InputNumber allowClear placeholder="请输入"></InputNumber>)}
                        <span style={{ marginLeft: 8, marginRight: 8 }}>-</span>
                        {getFieldDecorator('ageMax', {
                          rules: [
                            {
                              required: min && true,
                              message: '请选择起始年龄',
                            },
                          ],
                        })(<InputNumber allowClear placeholder="请输入"></InputNumber>)}
                      </Form.Item>
                      {/* <div style={{ margin: '0 10px' }}> - </div>
                        <Form.Item>
                          {getFieldDecorator('ageMax', {
                            rules: [
                              {
                                required: min && true,
                                message: '请选择起始年龄',
                              },
                            ],
                          })(<InputNumber allowClear placeholder="请输入"></InputNumber>)}
                        </Form.Item>
                      </div> */}
                    </Col>
                  </Fragment>
                }
              </Row>
            </div>
            <div style={{ width: 60, flexShrink: 0, paddingTop: 10, paddingLeft: 8 }}>
              <span
                onClick={() => {
                  setShowSearch(!showSearch);
                }}
                style={{ color: '#1190ff', cursor: 'pointer' }}
              >
                {showSearch ? '折叠' : '展开'}
                <Icon type={showSearch ? 'up' : 'down'} />
              </span>
            </div>
          </div>
        </Licensee>
        <Row>
          <Col span={24} style={{ marginBottom: 20, textAlign: 'right' }}>
            <Licensee license="mobileReplay_pageQuery">
              <Button type="primary" onClick={handleSearch} style={{ marginRight: 16 }}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginRight: 16 }}>
                重置
              </Button>
            </Licensee>
            <Licensee license="mobileReplay_batchExport">
              {/* <Button type="primary" onClick={handleExport} style={{ margin: '0px 10px' }}>
                导出
              </Button> */}
              <ExportApprove
                buttonText="导出"
                buttonStyle={{ marginRight: 16 }}
                exportParams={{
                  urlAPi: '/api/hn/mobileReplay/exportInvolvedReplay',
                  decode: true,
                  params: {
                    ...params,
                    notificationDateStart:
                      params?.notificationDateStart || initDate?.[0].format('YYYYMMDD'),
                    notificationDateEnd:
                      params?.notificationDateEnd || initDate?.[1].format('YYYYMMDD'),
                    pageNum: 1,
                    pageSize: 10,
                  },
                  method: 'POST',
                }}
                moduleTile="复盘管理"
                // 是否校验商业秘密电子文件相关
                isVerifyhEncryption={true}
                disabledExport={!tableData?.items?.length}
              />
            </Licensee>
            <Licensee license="mobileReplay_batchReplayResult">
              <Button
                type="primary"
                onClick={() => {
                  setImportBatchResultVisible(true);
                }}
                style={{ marginRight: 16 }}
              >
                批量复盘结果导入
              </Button>
            </Licensee>
            <Licensee license="mobileReplay_batchDelete">
              <Button type="danger" onClick={() => handleDelete()} style={{ marginRight: 16 }}>
                删除
              </Button>
            </Licensee>
            <Licensee license="mobileReplay_batchQueryRealtimeStatus">
              <Button
                type="primary"
                onClick={() => setReviewVisible(true)}
                style={{ marginRight: 16 }}
              >
                批量实时复盘信息查询
              </Button>
            </Licensee>
            <Licensee license="mobileReplay_batchImport">
              {/* {isEditAuth && */}
              <Button type="primary" onClick={handleImport}>
                通报信息导入
              </Button>
              {/* } */}
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        detailColumns={allColumns}
        tools={true}
        loading={loading}
        data={{
          list: tableData?.items || [],
          pagination: {
            current: params.pageNum,
            pageSize: params.pageSize,
            total: tableData?.totalNum || 0,
          },
        }}
        onChange={handlePaginationTable}
        rowKey="id"
        showSelectCount={true}
        rowSelectionProps={true}
        selectedRows={selectedRows}
        onSelectRow={onSelectRow}
        onRow={(record) => {
          return {
            onDoubleClick: () => {
              showInfo && handleInfo(record);
            },
          };
        }}
      />

      {/* 批量复盘结果导入 */}
      {importBatchResultVisible && (
        <BatchImportModal
          title="批量复盘结果导入"
          tipsText="*每个文件不超过1000条号码"
          visible={importBatchResultVisible}
          onImport={onImportBatchResult}
          onClose={() => {
            setImportBatchResultVisible(false);
          }}
          loading={confirmLoading}
          errorExportUrl={'/api/hn/mobileReplay/downloadResultError'}
          downTemplateUrl={`template/getTemplate?templateCode=batchReplayResult`}
        />
      )}
      {importVisible && (
        <BatchImportModal
          title="批量导入"
          visible={importVisible}
          onImport={onImport}
          onClose={hideImport}
          loading={confirmLoading}
          errorExportUrl={'/api/hn/mobileReplay/downloadErrorExcel'}
          downTemplateUrl={`template/getTemplate?templateCode=mobileReplay`}
        />
      )}
      {reviewVisible && (
        <BatchImportModal
          title="批量实时复盘信息查询"
          tipsText="*每个文件不得超过50条数据"
          visible={reviewVisible}
          onImport={onReviewImport}
          onClose={() => setReviewVisible(false)}
          loading={reviewLoading}
          errorExportUrl={'/api/hn/mobileReplay/batchQueryRealtimeStatus'}
          downTemplateUrl={`template/getTemplate?templateCode=realtimeStatusBatchQuery`}
        />
      )}
      {editVisible && (
        <EditModal
          visible={editVisible}
          onClose={hideEdit}
          handleEditOk={handleEditOk}
          isDetail={isDetail}
          details={details}
        />
      )}
    </Card>
  );
});

const config = [['closeSingleTab', 'tabKey', 'reloadTab']];

const YourComponent = withContext(...config)((props) => {
  return <MobileFraudVerificationResume {...props} />;
});

export default connect(({ mobileFraudVerificationResume, loading }) => ({
  mobileFraudVerificationResume,
  loading: loading.effects['mobileFraudVerificationResume/pageMobileReplay'],
}))(YourComponent);
