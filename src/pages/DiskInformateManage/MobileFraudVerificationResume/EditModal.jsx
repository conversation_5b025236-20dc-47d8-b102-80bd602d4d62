import React, { useEffect, useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  InputNumber,
  Spin,
  Collapse,
} from 'antd';

import moment from 'moment';
import styles from './index.less';
import { useAuth } from 'ponshine';
import UserInfoForm from '../components/UserInfoForm';
import { connect } from 'dryad';
import request from '@/utils/request';

const { Option } = Select;
const { Panel } = Collapse;

const EditModal = (props) => {
  const {
    visible,
    onClose,
    details,
    handleEditOk,
    form,
    form: { getFieldDecorator, validateFieldsAndScroll, resetFields, setFieldsValue },
    loading,
    isDetail,
    infoLoading,
    dispatch,
    mobileFraudVerificationResume: { classificationList, caseTypeList },
  } = props;
  const { authState } = useAuth() || {};
  const {
    user: { role = {} },
  } = authState || {};
  const noEdit = role.id == 2;
  const handleOk = () => {
    validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      const params = {
        ...values,
        id: details.id,
        latestShutdownTime: values?.latestShutdownTime?.format('YYYY-MM-DD HH:mm:ss'),
        notificationDate: values.notificationDate
          ? moment(values.notificationDate).format('YYYYMMDD')
          : '',
        incidentEndTime: values.incidentEndTime
          ? moment(values.incidentEndTime).format('YYYYMMDDHHmmss')
          : '',
        incidentStartTime: values.incidentStartTime
          ? moment(values.incidentStartTime).format('YYYYMMDDHHmmss')
          : '',
        callTime: values.callTime ? moment(values.callTime).format('YYYYMMDDHHmmss') : '',
        reportTime: values.reportTime ? moment(values.reportTime).format('YYYYMMDDHHmmss') : '',
        // talkTime: values.talkTime ? moment(values.talkTime).format('YYYYMMDDHHmmss') : '',
        blackExpireTime: values.blackExpireTime
          ? moment(values.blackExpireTime).format('YYYYMMDD')
          : '',
        shutdownTime: values.shutdownTime ? moment(values.shutdownTime).format('YYYYMMDD') : '',
        miitAssessment: values.miitAssessment
          ? moment(values.miitAssessment).format('YYYY-MM')
          : '',
        provincialOfficeAssessment: values.provincialOfficeAssessment
          ? moment(values.provincialOfficeAssessment).format('YYYY-MM')
          : '',
        mobileFraudUser: {
          ...values.mobileFraudUser,
          internetAccessTime: values.mobileFraudUser.internetAccessTime
            ? moment(values.mobileFraudUser.internetAccessTime).format('YYYY-MM-DD HH:mm:ss')
            : '',
          resumeTime: values.mobileFraudUser.resumeTime
            ? moment(values.mobileFraudUser.resumeTime).format('YYYY-MM-DD HH:mm:ss')
            : '',
          throwOrderTime: values.mobileFraudUser.throwOrderTime
            ? moment(values.mobileFraudUser.throwOrderTime).format('YYYY-MM-DD HH:mm:ss')
            : '',
          inputOrderTime: values.mobileFraudUser.inputOrderTime
            ? moment(values.mobileFraudUser.inputOrderTime).format('YYYY-MM-DD HH:mm:ss')
            : '',
        },
      };
      handleEditOk(params, () => {
        resetFields();
      });
    });
  };

  const onCancle = () => {
    resetFields();
    onClose();
  };

  const renderNum = (type) => {
    return configData[type] || 60;
  };

  const changeDate = (v, type) => {
    setFieldsValue({ [type]: v });
  };

  const changePanel = (v, type) => {
    setFieldsValue({ [type]: v });
  };

  useEffect(() => {
    getConfig();
  }, []);

  useEffect(() => {
    if (details?.classification) {
      onClassificationChange(details?.classification);
    }
  }, [details]);

  const getConfig = () => {
    setConfigLoading(true);
    request('/api/hn/mobileReplay/getTimeConfig', {
      method: 'get',
    })
      .then((res) => {
        if (res.code == 200) {
          setConfigData(res.data || {});
        }
      })
      .finally(() => {
        setConfigLoading(false);
      });
  };

  const [configData, setConfigData] = useState({});
  const [configLoading, setConfigLoading] = useState(false);

  const onClassificationChange = (value) => {
    if (['12321用户举报', '公安侦办平台']?.includes(value)) {
      dispatch({
        type: 'mobileFraudVerificationResume/getAllClassification',
        payload: {
          name: value,
        },
      });
    } else {
      dispatch({
        type: 'mobileFraudVerificationResume/save',
        payload: {
          caseTypeList: [],
        },
      });
    }
  };

  return (
    <Modal
      title={isDetail ? '查看复盘详情' : '复盘编辑'}
      visible={visible}
      onCancel={onCancle}
      onOk={isDetail ? onCancle : handleOk}
      confirmLoading={loading}
      width={'90vw'}
      style={{ top: 40, left: 50 }}
      bodyStyle={{ height: 560, overflow: 'auto', padding: '8px 16px' }}
    >
      <Spin spinning={infoLoading || configLoading}></Spin>
      <Form className={styles.myStylesForm}>
        <Collapse defaultActiveKey={['2', '3']}>
          <Panel header="通报信息" key="1">
            <Row gutter={[24]}>
              <Col span={6}>
                <Form.Item label="通报时间">
                  {getFieldDecorator('notificationDate', {
                    initialValue: details?.notificationDate
                      ? moment(details.notificationDate, 'YYYY-MM-DD')
                      : undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择通报时间',
                      },
                    ],
                  })(<DatePicker style={{ width: '100%' }} disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="分类">
                  {getFieldDecorator('classification', {
                    initialValue: details?.classification || undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择分类',
                      },
                    ],
                  })(
                    <Select
                      placeholder="请输入"
                      showSearch
                      allowClear
                      disabled={isDetail || noEdit}
                      onChange={(value) => {
                        onClassificationChange(value);
                        setFieldsValue({ caseType: undefined });
                      }}
                    >
                      {classificationList.map((value) => (
                        <Option value={value}>{value}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="提供单位">
                  {getFieldDecorator('provider', {
                    initialValue: details?.provider,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label="号码">
                  {getFieldDecorator('reportedPhoneNum', {
                    initialValue: details?.reportedPhoneNum,
                    rules: [
                      {
                        required: true,
                        message: '请输入号码',
                      },
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={true} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="归属地">
                  {getFieldDecorator('reportedLocation', {
                    initialValue: details?.reportedLocation,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入归属地'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="案件类别">
                  {getFieldDecorator('caseType', {
                    initialValue: details?.caseType || undefined,
                    rules: [],
                  })(
                    ['12321用户举报', '公安侦办平台']?.includes(
                      form.getFieldValue('classification'),
                    ) ? (
                      <Select
                        placeholder="请输入"
                        allowClear
                        disabled={isDetail || noEdit}
                        showSearch
                      >
                        {caseTypeList.map((value) => (
                          <Option value={value}>{value}</Option>
                        ))}
                      </Select>
                    ) : (
                      <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                    ),
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发案开始时间">
                  {getFieldDecorator('incidentStartTime', {
                    initialValue: details?.incidentStartTime
                      ? moment(details.incidentStartTime, 'YYYY-MM-DD HH:mm:ss')
                      : undefined,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请选择发案开始时间'
                      // }
                    ],
                  })(
                    <DatePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      style={{ width: '100%' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发案结束时间">
                  {getFieldDecorator('incidentEndTime', {
                    initialValue: details.incidentEndTime
                      ? moment(details.incidentEndTime, 'YYYY-MM-DD HH:mm:ss')
                      : undefined,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请选择发案结束时间'
                      // }
                    ],
                  })(
                    <DatePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      style={{ width: '100%' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="受害人电话">
                  {getFieldDecorator('victimPhoneNum', {
                    initialValue: details?.victimPhoneNum,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入受害人电话'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="举报号码运营商">
                  {getFieldDecorator('reportIsp', {
                    initialValue: details?.reportIsp || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      {['移动', '联通', '电信', '广电'].map((v) => (
                        <Option value={v}>{v}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="举报号码归属省">
                  {getFieldDecorator('reportProvince', {
                    initialValue: details?.reportProvince,
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="举报号码归属市">
                  {getFieldDecorator('reportCity', {
                    initialValue: details?.reportCity,
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              {/* <Col span={6}>
            <Form.Item label="举报时间">
              {getFieldDecorator('reportTime', {
                initialValue: details.reportTime
                  ? moment(details.reportTime, 'YYYY-MM-DD HH:mm:ss')
                  : undefined,
                rules: [],
              })(
                <DatePicker
                  showTime={{ format: 'HH:mm:ss' }}
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: '100%' }}
                  disabled={isDetail || noEdit}
                />,
              )}
            </Form.Item>
          </Col> */}
              <Col span={6}>
                <Form.Item label="来电时间">
                  {getFieldDecorator('callTime', {
                    initialValue: details?.callTime
                      ? moment(details.callTime, 'YYYY-MM-DD HH:mm:ss')
                      : undefined,
                    rules: [],
                  })(
                    <DatePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      style={{ width: '100%' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="关停时间">
                  {getFieldDecorator('shutdownTime', {
                    initialValue: details?.shutdownTime
                      ? moment(details.shutdownTime, 'YYYY-MM-DD')
                      : undefined,
                    rules: [],
                  })(
                    <DatePicker
                      format="YYYY-MM-DD"
                      style={{ width: '100%' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="举报内容" labelCol={{ span: 2 }}>
                  {getFieldDecorator('reportContent', {
                    initialValue: details?.reportContent,
                  })(
                    <Input.TextArea
                      rows={3}
                      style={{ width: '100%' }}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item label="备注" labelCol={{ span: 2 }}>
                  {getFieldDecorator('remark', {
                    initialValue: details?.remark,
                  })(
                    <Input.TextArea
                      rows={3}
                      style={{ width: '100%' }}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label="工信部考核">
                  {getFieldDecorator('miitAssessment', {
                    initialValue: details?.miitAssessment
                      ? moment(details.miitAssessment, 'YYYY-MM')
                      : undefined,
                  })(
                    <DatePicker
                      onPanelChange={(v) => changePanel(v, 'miitAssessment')}
                      onChange={(v) => changeDate(v, 'miitAssessment')}
                      format="YYYY-MM"
                      mode="month"
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                      style={{ width: '100%' }}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省联席办考核">
                  {getFieldDecorator('provincialOfficeAssessment', {
                    initialValue: details?.provincialOfficeAssessment
                      ? moment(details.provincialOfficeAssessment, 'YYYY-MM')
                      : undefined,
                  })(
                    <DatePicker
                      onPanelChange={(v) => changePanel(v, 'provincialOfficeAssessment')}
                      onChange={(v) => changeDate(v, 'provincialOfficeAssessment')}
                      format="YYYY-MM"
                      mode="month"
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                      style={{ width: '100%' }}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Panel>
          <Panel header="复盘核查" key="2">
            <Row gutter={[24]}>
              <Col span={6}>
                <Form.Item label="话务是否异常">
                  {getFieldDecorator('ifTelephoneException', {
                    initialValue: details?.ifTelephoneException || undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择话务是否异常',
                      },
                    ],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                      <Option value="疑似">疑似</Option>
                      <Option value="无">无</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="是否存在通联">
                  {getFieldDecorator('ifCommunications', {
                    initialValue: details?.ifCommunications || undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择是否存在通联',
                      },
                    ],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                      <Option value="疑似">疑似</Option>
                      <Option value="无">无</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="通话时间">
                  {getFieldDecorator('talkTime', {
                    initialValue: details?.talkTime,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入通话时间'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发话地市">
                  {getFieldDecorator('cityOfOrigin', {
                    initialValue: details?.cityOfOrigin,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入通话时间'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发话区">
                  {getFieldDecorator('areaOfOrigin', {
                    initialValue: details?.areaOfOrigin,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入通话时间'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发话省">
                  {getFieldDecorator('provinceOfOrigin', {
                    initialValue: details?.provinceOfOrigin,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入通话时间'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="高危基站">
                  {getFieldDecorator('highRiskBaseStationNum', {
                    initialValue: details?.highRiskBaseStationNum,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入通话时间'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="是否诈骗">
                  {getFieldDecorator('ifFraud', {
                    initialValue: details?.ifFraud || undefined,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请选择是否诈骗'
                      // }
                    ],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                      <Option value="疑似">疑似</Option>
                      <Option value="无">无</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="下发时是否关停">
                  {getFieldDecorator('ifShutdownWhenIssued', {
                    initialValue: details?.ifShutdownWhenIssued,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省内模型是否关停">
                  {getFieldDecorator('ifShutdownProvinceModel', {
                    initialValue: details?.ifShutdownProvinceModel,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省模型关停时间差（小时）" className={styles.widthForm}>
                  {getFieldDecorator('provinceShutdownInterval', {
                    initialValue: details?.provinceShutdownInterval,
                    // rules: [
                    //   {
                    //     pattern: /^\\d+(\\.\\d{1,2})?$/,
                    //     message: '请输入数字保留两位小数',
                    //   },
                    // ],
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={2}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省内模型分类">
                  {getFieldDecorator('provinceModelType', {
                    initialValue: details?.provinceModelType,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`省内模型关停次数（${renderNum(
                    'timeConfigOfProvinceModelShutdownTimes',
                  )}）天内`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('provinceModelShutdownTimes', {
                    initialValue: details?.provinceModelShutdownTimes,
                    rules: [],
                  })(
                    <InputNumber precision={0} min={0} allowClear disabled={isDetail || noEdit} />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团模型是否关停">
                  {getFieldDecorator('ifShutdownGroupModel', {
                    initialValue: details?.ifShutdownGroupModel,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团关停时间差（小时）" className={styles.widthForm}>
                  {getFieldDecorator('groupShutdownInterval', {
                    initialValue: details?.groupShutdownInterval,
                    // rules: [
                    //   {
                    //     pattern: /^\\d+(\\.\\d{1,2})?$/,
                    //     message: '请输入数字保留两位小数',
                    //   },
                    // ],
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={2}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团模型分类">
                  {getFieldDecorator('groupModelType', {
                    initialValue: details?.groupModelType,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`集团模型关停次数（${renderNum(
                    'timeConfigOfGroupModelShutdownTimes',
                  )}）天内`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('groupModelShutdownTimes', {
                    initialValue: details?.groupModelShutdownTimes,
                    rules: [],
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={0}
                      min={0}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否多次复机（${renderNum('timeConfigOfIfResumeFrequently')}）天`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifResumeFrequently', {
                    initialValue: details?.ifResumeFrequently,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`复机次数（${renderNum('timeConfigOfResumeTimes')}）天`}
                  // className={styles.widthForm}
                >
                  {getFieldDecorator('resumeTimes', {
                    initialValue: details?.resumeTimes,
                    rules: [],
                  })(
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      precision={0}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`涉案前是否被模型关停（${renderNum(
                    'timeConfigOfIfShutdownByModelBefore',
                  )}）天`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifShutdownByModelBefore', {
                    initialValue: details?.ifShutdownByModelBefore,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="复机方式">
                  {getFieldDecorator('resumeWay', {
                    initialValue: details?.resumeWay,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="是否SP">
                  {getFieldDecorator('ifSp', {
                    initialValue: details?.ifSp || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`${renderNum('timeConfigOfSpMessageContent')}天SP短信内容\n（短码+次数）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('spMessageContent', {
                    initialValue: details?.spMessageContent,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否新入网（${renderNum('timeConfigOfIfNewAccess')}个月内）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifNewAccess', {
                    initialValue: details?.ifNewAccess || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`是否新入网（6个月内）`} className={styles.widthForm}>
                  {getFieldDecorator('ifNewAccessSix', {
                    initialValue: details?.ifNewAccessSix || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="号码是否注册互联网账号" className={styles.widthForm}>
                  {getFieldDecorator('ifRegisterInternet', {
                    initialValue: details?.ifRegisterInternet || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否多次更换终端（${renderNum(
                    'timeConfigOfIfChangeTerminalFrequently',
                  )}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifChangeTerminalFrequently', {
                    initialValue: details?.ifChangeTerminalFrequently || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`更换终端次数（${renderNum('timeConfigOfChangeTerminalTimes')}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('changeTerminalTimes', {
                    initialValue: details?.changeTerminalTimes,
                    rules: [],
                  })(
                    <InputNumber
                      precision={0}
                      min={0}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否更换苹果终端（${renderNum(
                    'timeConfigOfChangeAppleTerminalCount',
                  )}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifChangeAppleTerminal', {
                    initialValue: details?.ifChangeAppleTerminal,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`更换苹果终端次数（${renderNum(
                    'timeConfigOfChangeAppleTerminalCount',
                  )}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('changeAppleTerminalCount', {
                    initialValue: details?.changeAppleTerminalCount,
                    rules: [],
                  })(
                    <InputNumber
                      precision={0}
                      min={0}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否更换过苹果低端机（${renderNum(
                    'timeConfigOfChangeAppleLowEndCount',
                  )}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifChangeAppleLowEnd', {
                    initialValue: details?.ifChangeAppleLowEnd,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`苹果低端机更换次数（${renderNum(
                    'timeConfigOfChangeAppleLowEndCount',
                  )}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('changeAppleLowEndCount', {
                    initialValue: details?.changeAppleLowEndCount,
                    rules: [],
                  })(
                    <InputNumber
                      precision={0}
                      min={0}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`是否校园卡`}>
                  {getFieldDecorator('ifCampusCard', {
                    initialValue: details?.ifCampusCard || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`是否一号双终端`}>
                  {getFieldDecorator('ifDoubleTerminal', {
                    initialValue: details?.ifDoubleTerminal || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`案发前上月话单数量`} className={styles.widthForm}>
                  {getFieldDecorator('lastMonthCdrCount', {
                    initialValue: details?.lastMonthCdrCount,
                    rules: [],
                  })(
                    <InputNumber
                      min={0}
                      precision={0}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否开通国漫`}
                  // className={styles.widthForm}
                >
                  {getFieldDecorator('whetherOpenChineseAnime', {
                    initialValue: details?.whetherOpenChineseAnime,
                    rules: [],
                  })(
                    <Select allowClear placeholder="请选择" disabled={isDetail || noEdit}>
                      <Option value={'是'}>是</Option>
                      <Option value={'否'}>否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`最近一次关停时间`}>
                  {getFieldDecorator('latestShutdownTime', {
                    initialValue: details?.latestShutdownTime
                      ? moment(details.latestShutdownTime, 'YYYY-MM-DD HH:mm:ss')
                      : undefined,
                    // rules: [
                    //   {
                    //     required: true,
                    //     message: '请选择最近一次关停时间',
                    //   },
                    // ],
                  })(
                    <DatePicker
                      style={{ width: '100%' }}
                      showTime
                      format={'YYYY-MM-DD HH:mm:ss'}
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否模型关停（省内模型大于${renderNum(
                    'timeConfigOfIfModelShutdown',
                  )}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifModelShutdown', {
                    initialValue: details?.ifModelShutdown,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`模型分类（省内模型大于${renderNum('timeConfigOfIfModelShutdown')}天）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('modelType', {
                    initialValue: details?.modelType,
                  })(<Input allowClear disabled={isDetail || noEdit} placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`关停时间(省内大于${renderNum('timeConfigOfIfModelShutdown')}天)`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('modelShutdownTime', {
                    initialValue: details?.modelShutdownTime,
                  })(<Input allowClear disabled={isDetail || noEdit} placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`是否外呼关停`}>
                  {getFieldDecorator('ifOutboundShutdown', {
                    initialValue: details?.ifOutboundShutdown || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否外呼关停(人工)`}
                  // className={styles.widthForm}
                >
                  {getFieldDecorator('ifOutboundShutdownArtificial', {
                    initialValue: details?.ifOutboundShutdownArtificial || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="异常备注">
                  {getFieldDecorator('exceptionRemark', {
                    initialValue: details?.exceptionRemark,
                  })(
                    <Input.TextArea
                      rows={2}
                      style={{ width: '100%' }}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label={`是否加黑`}>
                  {getFieldDecorator('ifAddBlack', {
                    initialValue: details?.ifAddBlack || undefined,
                    rules: [],
                  })(
                    <Select
                      placeholder="请选择"
                      allowClear
                      disabled={isDetail || noEdit || details?.ifAddBlack == '是'}
                    >
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`加黑到期时间`}>
                  {getFieldDecorator('blackExpireTime', {
                    initialValue: details?.blackExpireTime
                      ? moment(details.blackExpireTime, 'YYYY-MM-DD')
                      : undefined,
                    rules: [
                      {
                        required: form.getFieldValue('ifAddBlack') == '是' ? true : false,
                        message: '请选择加黑到期时间',
                      },
                    ],
                  })(
                    <DatePicker
                      style={{ width: '100%' }}
                      format={'YYYY-MM-DD'}
                      allowClear
                      disabled={isDetail || noEdit || details?.ifAddBlack == '是'}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`加黑原因`}>
                  {getFieldDecorator('blackReason', {
                    initialValue: details?.blackReason,
                    rules: [
                      {
                        required: form.getFieldValue('ifAddBlack') == '是' ? true : false,
                        message: '请输入加黑原因',
                      },
                    ],
                  })(
                    <Input
                      allowClear
                      disabled={isDetail || noEdit || details?.ifAddBlack == '是'}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Panel>
          <Panel header="用户信息" key="3">
            <UserInfoForm form={form} isDetail={isDetail} details={details} />
          </Panel>
        </Collapse>
      </Form>
    </Modal>
  );
};

export default connect(({ mobileFraudVerificationResume, loading }) => ({
  mobileFraudVerificationResume,
  loading: loading.effects['mobileFraudVerificationResume/updateMobileReplayDetailById'],
  infoLoading: loading.effects['mobileFraudVerificationResume/getMobileReplayDetailById'],
}))(Form.create()(EditModal));
