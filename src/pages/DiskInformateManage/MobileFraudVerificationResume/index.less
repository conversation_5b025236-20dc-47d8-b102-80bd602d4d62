.myStylesForm {
  :global {
    .ant-form-item {
      display: flex !important;
      margin-bottom: 0px;

    }

    .ant-form-item-label {
      flex-shrink: 0;
      width: 138px;
      // label {
      //   font-size: 12px;
      // }
    }



    .ant-form-item-control-wrapper {
      width: 0;
      flex: 1;
    }

    .ant-input[disabled],
    .ant-select-disabled,
    .ant-input-number-disabled {
      background-color: #f1f1f1;
      color: #414141;

      .ant-select-selection {
        background-color: #f1f1f1;
      }
    }
  }

}

.title {
  font-size: 16px;
  font-weight: 600;
}

.widthForm {
  :global {
    .ant-form-item-label {
      white-space: pre-wrap;
      width: 138px;
      line-height: 19px;
      padding-right: 4px;
    }

    .ant-input-number {
      width: 100% !important;
    }

  }
}

.myStylesFormItem {
  :global {
    .ant-form-item-label {
      white-space: pre-wrap;
      line-height: 18px;
      padding-right: 5px;
    }
  }

}

.age {
  display: flex;

  :global {
    .ant-form-item {
      display: flex !important;
    }

    .ant-form-item-label {
      flex-shrink: 0;
      width: 70px;
    }
  }


}
