import React, { useState } from 'react';
import { Modal, Form, Input, Select, DatePicker, Row, Col, InputNumber } from 'antd';
import moment from 'moment';
import styles from './index.less'
import { useAuth } from 'ponshine';
import UserInfoForm from '../components/UserInfoForm'

const { Option } = Select;

const EditModal = (props) => {
    const { visible, onClose, handleEditOk, details, form, form: { getFieldDecorator, validateFields, resetFields }, loading, isDetail } = props
    const { authState } = useAuth() || {};
    const { user: { role = {} } } = authState || {};
    const noEdit = role.id == 2
    const handleOk = () => {
        validateFields((err, values) => {
            if (err) {
                return;
            }
            const params = {
                ...values,
                id:details.id,
                reportTime: values.reportTime ? moment(values.reportTime).format('YYYYMMDDHHmmss') : '',
                incomingCallTime: values.incomingCallTime ? moment(values.incomingCallTime).format('YYYYMMDDHHmmss') : '',
                talkTime: values.talkTime ? moment(values.talkTime).format('YYYYMMDDHHmmss') : '',
                userInformation: {
                    ...values.userInformation,
                    internetAccessTime: values.userInformation.internetAccessTime ? moment(values.userInformation.internetAccessTime).format('YYYYMMDDHHmmss') : '',
                    resumeTime: values.userInformation.resumeTime ? moment(values.userInformation.resumeTime).format('YYYYMMDDHHmmss') : '',
                    throwOrderTime: values.userInformation.throwOrderTime ? moment(values.userInformation.throwOrderTime).format('YYYYMMDDHHmmss') : '',
                    inputOrderTime: values.userInformation.inputOrderTime ? moment(values.userInformation.inputOrderTime).format('YYYYMMDDHHmmss') : '',
                }
            }
            handleEditOk(params, () => {
                resetFields()
            })
        });
    }

    const onCancle = () => {
        resetFields()
        onClose()
    }

    return (
        <Modal
            title={isDetail ? '查看复盘详情' : '复盘编辑'}
            visible={visible}
            onCancel={onCancle}
            onOk={isDetail ? onCancle : handleOk}
            // confirmLoading={loading}
            width={1000}
            style={{ top: 40, left: 50 }}
            bodyStyle={{ height: 500, overflow: 'auto' }}
        >
            <Form className={styles.myStylesForm}>
                <Row gutter={[24]}>
                    <h3 className={styles.title}>举报信息</h3>
                    <Col span={12}>
                        <Form.Item label="举报号码">
                            {getFieldDecorator("reportPhoneNum", {
                                initialValue: details.reportPhoneNum,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入举报号码'
                                    },
                                    // {
                                    //     pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                                    //     message: '您输入的号码格式有误！',
                                    // },
                                ]
                            })(
                                <Input placeholder="请输入举报号码" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="举报号码运营商" className={styles.widthForm}>
                            {getFieldDecorator("isp", {
                                initialValue: details.isp,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入举报号码运营商'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="举报号码归属省" className={styles.widthForm}>
                            {getFieldDecorator("reportPhoneNumProvince", {
                                initialValue: details.reportPhoneNumProvince,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入举报号码归属省'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="举报号码归属市" className={styles.widthForm}>
                            {getFieldDecorator("reportPhoneNumCity", {
                                initialValue: details.reportPhoneNumCity,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入举报号码归属市'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="被举报号码">
                            {getFieldDecorator("reportedPhoneNum", {
                                initialValue: details.reportedPhoneNum,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入被举报号码'
                                    },
                                    // {
                                    //     pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                                    //     message: '您输入的号码格式有误！',
                                    // },
                                ]
                            })(
                                <Input placeholder="请输入被举报号码" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="被举报号码归属市" className={styles.widthForm}>
                            {getFieldDecorator("reportedPhoneNumCity", {
                                initialValue: details.reportedPhoneNumCity,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入被举报号码归属市'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="举报时间">
                            {getFieldDecorator("reportTime", {
                                initialValue: details?.reportTime ? moment(details.reportTime, 'YYYY-MM-DD HH:mm:ss') : '',
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择举报时间'
                                    }
                                ]
                            })(
                                <DatePicker
                                    style={{ width: '100%' }}
                                    disabled={isDetail || noEdit}
                                    showTime={{ format: 'HH:mm:ss' }}
                                    format="YYYY-MM-DD HH:mm:ss"
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="举报来源">
                            {getFieldDecorator("reportOrigin", {
                                initialValue: details.reportOrigin,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入举报来源'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="来电时间">
                            {getFieldDecorator("incomingCallTime", {
                                initialValue: details.incomingCallTime ? moment(details.incomingCallTime,'YYYY-MM-DD HH:mm:ss') : '',
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择来电时间'
                                    }
                                ]
                            })(
                                <DatePicker
                                    showTime={{ format: 'HH:mm:ss' }}
                                    format="YYYY-MM-DD HH:mm:ss"
                                    style={{ width: '100%' }}
                                    disabled={isDetail || noEdit}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label="举报内容">
                            {getFieldDecorator("reportContent", {
                                initialValue: details.reportContent,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入举报内容'
                                    }
                                ]
                            })(
                                <Input.TextArea
                                    rows={3}
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    allowClear
                                    disabled={isDetail || noEdit}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="不良类型">
                            {getFieldDecorator("badType", {
                                initialValue: details.badType,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入不良类型'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="工信部考核">
                            {getFieldDecorator("miitAssessment", {
                                initialValue: details?.miitAssessment,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                <Row gutter={[24]}>
                    <h3 className={styles.title}>复盘核查</h3>
                    <Col span={12}>
                        <Form.Item label="话务是否异常">
                            {getFieldDecorator("ifTelephoneException", {
                                initialValue: details.ifTelephoneException,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择话务是否异常'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                    <Option value='疑似'>疑似</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否省内大数据发现" className={styles.widthForm}>
                            {getFieldDecorator("ifFoundByProvincialBigData", {
                                initialValue: details.ifFoundByProvincialBigData,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否省内大数据发现'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否集团大数据关停" className={styles.widthForm}>
                            {getFieldDecorator("ifShutdownByGroupBigData", {
                                initialValue: details.ifShutdownByGroupBigData,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否集团大数据关停'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否真实主叫">
                            {getFieldDecorator("ifRealCalling", {
                                initialValue: details.ifRealCalling,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否真实主叫'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="漫入地市">
                            {getFieldDecorator("roamCity", {
                                initialValue: details.roamCity,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="漫入区县">
                            {getFieldDecorator("roamCounty", {
                                initialValue: details.roamCounty,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="漫入省份">
                            {getFieldDecorator("roamProvince", {
                                initialValue: details.roamProvince,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="通话时间" >
                            {getFieldDecorator("talkTime", {
                                initialValue: details.talkTime ? moment(details.talkTime,'YYYY-MM-DD HH:mm:ss') : '',
                            })(
                                <DatePicker 
                                showTime={{ format: 'HH:mm:ss' }}
                                format="YYYY-MM-DD HH:mm:ss" 
                                style={{ width: '100%' }} 
                                disabled={isDetail}
                                 />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="高危基站号">
                            {getFieldDecorator("highRiskBaseStationNum", {
                                initialValue: details.highRiskBaseStationNum,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="集团认定是否真实主叫" className={styles.widthForm}>
                            {getFieldDecorator("ifRealCallingByGroup", {
                                initialValue: details.ifRealCallingByGroup,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否主被叫">
                            {getFieldDecorator("ifCallingOrCalled", {
                                initialValue: details.ifCallingOrCalled,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='主叫'>主叫</Option>
                                    <Option value='被叫'>被叫</Option>
                                    <Option value='无'>无</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    {/* <Col span={12}>
                        <Form.Item label="是否在发案期内">
                            {getFieldDecorator("ifDuringTheCrime", {
                                initialValue: details.ifDuringTheCrime,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                     <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col> */}
                    <Col span={24}>
                        <Form.Item label="问题">
                            {getFieldDecorator("issue", {
                                initialValue: details.issue,
                            })(
                                <Input.TextArea
                                    rows={3}
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    allowClear
                                    disabled={isDetail}
                                />
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                <UserInfoForm form={form} isDetail={isDetail} details={details} />
            </Form>
        </Modal>
    )
}

export default Form.create()(EditModal)