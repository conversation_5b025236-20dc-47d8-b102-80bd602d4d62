import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input, Divider, Modal } from "antd";
import { connect } from 'dryad';
import { useEffect, useState } from "react";
import moment from 'moment';
import StandardTable from "@/components/StandardTable";
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal'
import EditModal from './EditModal'
import { useAuth } from 'ponshine';
import { Licensee, useLicensee } from 'ponshine';


const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
    pageSize: 10,
    pageNum: 1,
};
const formItemLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 15,
    },
};

const FraudulentTelephoneReport = (props) => {
    const { dispatch, form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
        fraudulentTelephoneReport: { tableData, details, localNetworkList }, loading, location: { query } } = props;
    const [params, setParams] = useState(defaultPagination);
    const [importVisible, setImportVisible] = useState(false);
    const [editVisible, setEditVisible] = useState(false);
    const [isDetail, setIsDetail] = useState(false);
    const { authState } = useAuth() || {};
    const { user: { role = {} } } = authState || {};
    const isHightAuth = role.id == 1 || role.id == 2 || role.id == 5
    const isEditAuth = role.id == 1 || role.id == 2 || role.id == 5
    const [badTypeList, setBadTypeList] = useState([])
    const [reportOriginList, setReportOriginList] = useState([])

    const columns = [
        {
            title: "任务ID",
            dataIndex: "taskId",
            align: "center",
            width: 130,
        },
        {
            title: "被举报号码",
            dataIndex: "reportedPhoneNum",
            align: "center",
            width: 150,
            render: (text) => text || '--'
        },
        {
            title: "举报人号码",
            dataIndex: "reportPhoneNum",
            align: "center",
            width: 150,
            render: (text) => text || '--'
        },
        {
            title: "举报来源",
            dataIndex: "reportOrigin",
            align: "center",
            width: 150,
            render: (text) => text || '--'
        },
        {
            title: "举报时间",
            dataIndex: "reportTime",
            width: 160,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "本地网",
            dataIndex: "userInformation.localNetwork",
            width: 140,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "不良类型",
            dataIndex: "badType",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "导入时间",
            dataIndex: "importTime",
            width: 160,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "是否已复盘",
            dataIndex: "ifReplay",
            width: 100,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "操作",
            width: 150,
            align: "center",
            fixed: 'right',
            render: (record) => {
                return (
                    <>
                        {/* {isEditAuth && <> */}
                        <Licensee license='fraudulentTelephoneReport_updatePhoneReportReplayDetailById'>

                            <a onClick={(e) => editInfo(e, record)}>编辑</a>
                            <Divider type="vertical" />
                        </Licensee>

                        {/* </>} */}
                        <Licensee license='fraudulentTelephoneReport_deletePhoneReportReplayById'>
                            {/* {isHightAuth &&  */}
                            <a onClick={(e) => delInfo(e, record.id)}>删除</a>
                            {/* } */}
                        </Licensee>
                    </>
                )
            }
        },
    ];

    const editInfo = (e, record) => {
        e.stopPropagation()
        e.preventDefault()
        setEditVisible(true)
        getPhoneReportReplayDetail(record.id)
    }

    const hideEdit = () => {
        setEditVisible(false)
        setIsDetail(false)
    }
    const handleEditOk = (values, callback) => {
        dispatch({
            type: 'fraudulentTelephoneReport/updatePhoneReportReplay',
            payload: values,
            callback: (res) => {
                if (res.code == 200) {
                    message.success(res.message || '修改成功');
                    getPhoneReportReplay({ ...params, ...defaultPagination })
                    callback()
                    hideEdit()
                } else {
                    message.error(res.message);
                }
            },
        });
    }

    const getPhoneReportReplayDetail = (id) => {
        dispatch({
            type: 'fraudulentTelephoneReport/getPhoneReportReplayDetail',
            payload: { id }
        })
    }

    const delInfo = (e, id) => {
        e.stopPropagation()
        e.preventDefault()
        Modal.confirm({
            title: '复盘任务删除',
            content: '是否确认删除该条复盘任务?',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                deleteFun(id)
            },
        });
    }

    const deleteFun = (id) => {
        dispatch({
            type: 'fraudulentTelephoneReport/deletePhoneReportReplayById',
            payload: { id },
            callback: (res) => {
                if (res.code == 200) {
                    message.success(res.message || '删除成功');
                    getPhoneReportReplay({ ...params, ...defaultPagination })
                } else {
                    message.error(res.message);
                }
            },
        });
    };


    const handleExport = () => {
        const newParams = {
            ...params,
            pageNum: 1,
            pageSize: 10,
        }
        exportFile({
            urlAPi: '/api/hn/fraudPhoneReportReplay/exportPhoneReportReplay',
            decode: true,
            params: newParams,
            method: 'POST'
        });
    };

    const handleImport = () => {
        setImportVisible(true)
    }

    const hideImport = () => {
        setImportVisible(false)
    }

    const onImport = (values, callback) => {
        dispatch({
            type: "fraudulentTelephoneReport/batchImportPhoneReportReplayInfo",
            payload: values,
            callback: (res) => {
                if (res.code == 200) {
                    message.success(res.message || '导入成功');
                    getPhoneReportReplay({ ...params, ...defaultPagination })
                    hideImport()
                } else {
                    if (res.code == 401) {
                        callback()
                    } else {
                        message.error(res.message);
                    }
                }
            },
        });
    }

    const getPhoneReportReplay = (params) => {
        dispatch({
            type: "fraudulentTelephoneReport/getPhoneReportReplay",
            payload: params,
        });
        setParams(params)
    }

    //查询
    const handleSearch = () => {
        validateFields((err, { months, ...rest }) => {
            if (err) {
                return;
            }
            getPhoneReportReplay({
                ...params,
                ...defaultPagination,
                ...rest,
                reportTimeStart: months && months.length > 0 ? moment(months[0]).format('YYYYMMDD000000') : '',
                reportTimeEnd: months && months.length > 0 ? moment(months[1]).format('YYYYMMDD235959') : '',
            });
        });
    };

    //表格切换
    const handlePaginationTable = (pagination) => {
        getPhoneReportReplay({ ...params, pageNum: pagination.current, pageSize: pagination.pageSize });
    };

    const handleInfo = (record) => {
        setIsDetail(true)
        setEditVisible(true)
        getPhoneReportReplayDetail(record.id)
    }
    const handleReset = () => {
        resetFields();
        getPhoneReportReplay({ ...defaultPagination })
    };

    const getOrganizationByUser = () => {
        dispatch({
            type: "fraudulentTelephoneReport/getOrganizationByUser",
        });
    }

    const getBadTypeList = () => {
        dispatch({
            type: "fraudulentTelephoneReport/getSystemConfigListByConfigType",
            payload: { configType: 'bad_type' },
            callback: (res) => {
                if (res.code == 200) {
                    setBadTypeList(res.data)
                }
            }
        });
    }

    const getReportOriginList = () => {
        dispatch({
            type: "fraudulentTelephoneReport/getSystemConfigListByConfigType",
            payload: { configType: 'report_origin' },
            callback: (res) => {
                if (res.code == 200) {
                    setReportOriginList(res.data)
                }
            }
        });
    }

    useEffect(() => {
        getOrganizationByUser()
        getBadTypeList()
        getReportOriginList()
    }, [])

    useEffect(()=>{
        getPhoneReportReplay({ ...params, badType: query.badType });
        setFieldsValue({ badType: query.badType })
    },[query.badType])

    const showInfo = useLicensee('fraudulentTelephoneReport_getPhoneReportReplayDetailById')

    return (
        <Card bordered={false}>
            <Form {...formItemLayout}>
                <Row>
                    <Licensee license='fraudulentTelephoneReport_pagePhoneReportReplay'>

                        <Col span={8}>
                            <Form.Item label="任务ID">
                                {getFieldDecorator("taskId")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="被举报号码">
                                {getFieldDecorator("reportedPhoneNum")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="举报人号码">
                                {getFieldDecorator("reportPhoneNum")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="是否已复盘">
                                {getFieldDecorator("ifReplay")(
                                    <Select placeholder="请选择" allowClear>
                                        <Option value='是'>是</Option>
                                        <Option value='否'>否</Option>
                                    </Select>
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="举报时间">
                                {getFieldDecorator("months")(
                                    <RangePicker />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="本地网">
                                {getFieldDecorator("localNetwork")(
                                    <Select placeholder="请选择" allowClear>
                                        {
                                            localNetworkList?.map((item => {
                                                return (
                                                    <Option value={item.name} key={item.id}>{item.name}</Option>
                                                )
                                            }))
                                        }
                                    </Select>
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="不良类型">
                                {getFieldDecorator("badType", {
                                    initialValue: query?.badType,
                                })(
                                    <Select placeholder="请选择" allowClear>
                                        {
                                            badTypeList?.map((item => {
                                                return (
                                                    <Option value={item.name} key={item.id}>{item.name}</Option>
                                                )
                                            }))
                                        }
                                    </Select>
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="举报来源">
                                {getFieldDecorator("reportOrigin")(
                                    <Select placeholder="请选择" allowClear>
                                        {
                                            reportOriginList?.map((item => {
                                                return (
                                                    <Option value={item.name} key={item.id}>{item.name}</Option>
                                                )
                                            }))
                                        }
                                    </Select>
                                )}
                            </Form.Item>
                        </Col>
                    </Licensee>
                    <Col span={8} style={{ marginTop: 5 }}>
                        <Licensee license='fraudulentTelephoneReport_pagePhoneReportReplay'>

                            <Button
                                type="primary"
                                onClick={handleSearch}
                            >
                                查询
                            </Button>

                            <Button onClick={handleReset} style={{ marginLeft: 10 }}>重置</Button>
                        </Licensee>
                        <Licensee license='fraudulentTelephoneReport_exportPhoneReportReplay'>

                            <Button
                                style={{ marginLeft: 10 }}
                                type="primary"
                                onClick={handleExport}
                            >
                                导出
                            </Button>
                        </Licensee>
                        <Licensee license='fraudulentTelephoneReport_batchImportPhoneReportReplayInfo'>

                            {/* {isHightAuth &&  */}
                            <Button
                                type="primary"
                                onClick={handleImport}
                                style={{ marginLeft: 10 }}
                            >
                                举报信息导入
                            </Button>
                            {/* } */}
                        </Licensee>
                    </Col>
                </Row>
            </Form>
            <StandardTable
                columns={columns}
                loading={loading}
                data={{
                    list: tableData?.items || [],
                    pagination: {
                        current: params.pageNum,
                        pageSize: params.pageSize,
                        total: tableData?.totalNum || 0
                    }
                }}
                onChange={handlePaginationTable}
                rowKey="id"
                showSelectCount={false}
                rowSelectionProps={false}
                scroll={{ x: true }}
                onRow={record => {
                    return {
                        onDoubleClick: () => showInfo && handleInfo(record)
                    }
                }}
            />
            {
                importVisible && <BatchImportModal
                    title='批量导入'
                    visible={importVisible}
                    onImport={onImport}
                    onClose={hideImport}
                    downTemplateUrl={`template/getTemplate?templateCode=phoneReplay`}
                    errorExportUrl='/api/hn/fraudPhoneReportReplay/downloadErrorExcel '
                />
            }
            {
                editVisible && <EditModal
                    visible={editVisible}
                    onClose={hideEdit}
                    handleEditOk={handleEditOk}
                    isDetail={isDetail}
                    details={details}
                />
            }
        </Card>
    );
}

export default connect(({ fraudulentTelephoneReport, loading }) => ({
    fraudulentTelephoneReport,
    loading: loading.effects["fraudulentTelephoneReport/getPhoneReportReplay"],
}))(Form.create()(FraudulentTelephoneReport));
