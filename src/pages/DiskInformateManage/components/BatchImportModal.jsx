import React, { useState } from 'react';
import { Modal, Upload, Button, Icon, Row, Col, message } from 'antd';
import styles from './BatchImportModal.less';
import { basePath } from '@/services/basePath';
import { exportFile } from '@/utils/utils';

const BatchImportModal = (props) => {
  const { title, visible, onImport, onClose, downTemplateUrl, errorExportUrl, loading ,tipsText } = props
  const [fileList, setFileList] = useState([])
  const [downloadVisible, setDownloadVisible] = useState(false)


  const okHandle = () => {
    if (fileList.length) {
      const fileData = new FormData();
      fileData.append('file', fileList[0]);
      onImport(fileData, () => {
        setDownloadVisible(true)
      })
    } else {
      message.warning('请选择文件');
    }
  };

  const download = (url) => {
    exportFile({
      urlAPi: url,
      decode: true,
      method: 'GET',
    });
    setDownloadVisible(false)
    onClose()
  }

  const hideDownModal = () => {
    setDownloadVisible(false)
  }

  // 模板下载
  const handleDownload = () => {
    try {
      window.open(`${basePath}${downTemplateUrl}`);
    } catch (e) {
      console.log(e);
    }
  };

  const uploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList)
    },
    beforeUpload: (file) => {
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      if (tag === '.xlsx' || tag === '.xls') {
        setFileList([file])
        return false;
      } else {
        message.warning('文件格式不正确');
      }
    },
    fileList,
  };

  return (
    <>
      <Modal
        title={title}
        visible={visible}
        onCancel={onClose}
        destroyOnClose
        afterClose={() => {
          setFileList([])
        }}
        footer={
          [
            <Button key='1' onClick={onClose}>取消</Button>,
            <Button key='2' type="primary" onClick={okHandle} loading={loading}>确认添加</Button>
          ]
        }
      >
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col span={24} style={{ marginLeft: 10 }}>
            <div className={styles.uploadPanel}>
              <Upload {...uploadProps} accept='.xlsx,.xls'>
                <Button>
                  <Icon type="upload" /> 选择文件
                </Button>
                {uploadProps.fileList.length === 0 ? '  未选择任何文件' : ''}
              </Upload>
            </div>
            <div className={styles.tips}>{tipsText?tipsText:"*每个文件不得超过10万条数据"}</div>
            <a className={styles.download} onClick={handleDownload}>
              模板下载
            </a>
          </Col>
        </Row>
      </Modal>
      {downloadVisible && <Modal
        visible={downloadVisible}
        title="下载错误文件"
        destroyOnClose
        maskClosable={false}
        onCancel={hideDownModal}
        footer={[
          <Button
            onClick={hideDownModal}
            key='1'
          >
            取消
          </Button>,
          <Button
            type="primary"
            onClick={() => download(errorExportUrl)}
            key='2'
          >
            确认下载
          </Button>,
        ]}
      >
        <div className={styles.confirmModalContent}>
          <span>导入的文件内容有误，请下载错误文件</span>
        </div>
      </Modal>}
    </>
  );
}

export default BatchImportModal
