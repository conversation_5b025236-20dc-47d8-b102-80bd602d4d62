import React from 'react';
import { Form, Input, DatePicker, Row, Col, Select, InputNumber } from 'antd';
import styles from '../CaseNotifyVerify/index.less';
import moment from 'moment';

const { Option } = Select;

const UserInfoForm = (props) => {
  const {
    form: { getFieldDecorator },
    isDetail,
    details,
  } = props;
  return (
    <Row gutter={[24]}>
      <Col span={6}>
        <Form.Item label="手机号码">
          {getFieldDecorator('mobileFraudUser.phoneNum', {
            initialValue: details?.mobileFraudUser?.phoneNum,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="本地网">
          {getFieldDecorator('mobileFraudUser.localNetwork', {
            initialValue: details?.mobileFraudUser?.localNetwork,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="营业区">
          {getFieldDecorator('mobileFraudUser.businessArea', {
            initialValue: details?.mobileFraudUser?.businessArea,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="入网时间">
          {getFieldDecorator('mobileFraudUser.internetAccessTime', {
            initialValue: details?.mobileFraudUser?.internetAccessTime
              ? moment(details?.mobileFraudUser.internetAccessTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              style={{ width: '100%' }}
              showTime
              format={'YYYY-MM-DD HH:mm:ss'}
              disabled={isDetail}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="渠道类型">
          {getFieldDecorator('mobileFraudUser.channelType', {
            initialValue: details?.mobileFraudUser?.channelType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="受理网点">
          {getFieldDecorator('mobileFraudUser.acceptancePoint', {
            initialValue: details?.mobileFraudUser?.acceptancePoint,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="销售品">
          {getFieldDecorator('mobileFraudUser.saleCategory', {
            initialValue: details?.mobileFraudUser?.saleCategory,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="年龄">
          {getFieldDecorator('mobileFraudUser.age', {
            initialValue: details?.mobileFraudUser?.age,
            rules: [
              {
                pattern: /(^[0-9]\d*$)/,
                message: '请输入正整数',
              },
            ],
          })(
            <Input style={{ width: '100%' }} placeholder="请输入" allowClear disabled={isDetail} />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="销售品价格">
          {getFieldDecorator('mobileFraudUser.salePrice', {
            initialValue: details?.mobileFraudUser?.salePrice,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="是否主副卡">
          {getFieldDecorator('mobileFraudUser.masterCard', {
            initialValue: details?.mobileFraudUser?.masterCard,
          })(
            <Select placeholder="请选择" allowClear disabled={isDetail}>
              <Option value="主卡">主卡</Option>
              <Option value="副卡">副卡</Option>
            </Select>,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="使用客户">
          {getFieldDecorator('mobileFraudUser.usingCustomer', {
            initialValue: details?.mobileFraudUser?.usingCustomer,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item
          label="使用人证件号码"
          //  className={styles.widthForm}
        >
          {getFieldDecorator('mobileFraudUser.certificatesNumber', {
            initialValue: details?.mobileFraudUser?.certificatesNumber,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item
          label="使用人证件类型"
          // className={styles.widthForm}
        >
          {getFieldDecorator('mobileFraudUser.certificatesType', {
            initialValue: details?.mobileFraudUser?.certificatesType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="使用客户地址">
          {getFieldDecorator('mobileFraudUser.usingCustomerAddress', {
            initialValue: details?.mobileFraudUser?.usingCustomerAddress,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权客户">
          {getFieldDecorator('mobileFraudUser.propertyCustomer', {
            initialValue: details?.mobileFraudUser?.propertyCustomer,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权证件号码">
          {getFieldDecorator('mobileFraudUser.propertyCertificatesNumber', {
            initialValue: details?.mobileFraudUser?.propertyCertificatesNumber,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权证件类型">
          {getFieldDecorator('mobileFraudUser.propertyCertificatesType', {
            initialValue: details?.mobileFraudUser?.propertyCertificatesType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权客户地址">
          {getFieldDecorator('mobileFraudUser.propertyCustomerAddress', {
            initialValue: details?.mobileFraudUser?.propertyCustomerAddress,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="客户类型">
          {getFieldDecorator('mobileFraudUser.customerType', {
            initialValue: details?.mobileFraudUser?.customerType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产品类型">
          {getFieldDecorator('mobileFraudUser.productType', {
            initialValue: details?.mobileFraudUser?.productType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="号码标识">
          {getFieldDecorator('mobileFraudUser.phoneNumTag', {
            initialValue: details?.mobileFraudUser?.phoneNumTag,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="用户装机地址">
          {getFieldDecorator('mobileFraudUser.userInstallAddress', {
            initialValue: details?.mobileFraudUser?.userInstallAddress,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>

      <Col span={6}>
        <Form.Item label="复机单号">
          {getFieldDecorator('mobileFraudUser.resumeNumbers', {
            initialValue: details?.mobileFraudUser?.resumeNumbers,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="复机时间">
          {getFieldDecorator('mobileFraudUser.resumeTime', {
            initialValue: details?.mobileFraudUser?.resumeTime
              ? moment(details?.mobileFraudUser?.resumeTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: '100%' }}
              disabled={isDetail}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="复机工号">
          {getFieldDecorator('mobileFraudUser.resumeWorkId', {
            initialValue: details?.mobileFraudUser?.resumeWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="复机人">
          {getFieldDecorator('mobileFraudUser.resumeOperator', {
            initialValue: details?.mobileFraudUser?.resumeOperator,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="受理员工">
          {getFieldDecorator('mobileFraudUser.acceptStaff', {
            initialValue: details?.mobileFraudUser?.acceptStaff,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="受理工号">
          {getFieldDecorator('mobileFraudUser.acceptanceWorkId', {
            initialValue: details?.mobileFraudUser?.acceptanceWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人工号">
          {getFieldDecorator('mobileFraudUser.grabMachineWorkId', {
            initialValue: details?.mobileFraudUser?.grabMachineWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人名称">
          {getFieldDecorator('mobileFraudUser.grabMachineName', {
            initialValue: details?.mobileFraudUser?.grabMachineName,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人组织">
          {getFieldDecorator('mobileFraudUser.grabMachineOrganize', {
            initialValue: details?.mobileFraudUser?.grabMachineOrganize,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人组织渠道类型" className={styles.widthForm}>
          {getFieldDecorator('mobileFraudUser.grabMachineOrganizeChannelType', {
            initialValue: details?.mobileFraudUser?.grabMachineOrganizeChannelType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="甩单时间">
          {getFieldDecorator('mobileFraudUser.throwOrderTime', {
            initialValue: details?.mobileFraudUser?.throwOrderTime
              ? moment(details?.mobileFraudUser.throwOrderTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: '100%' }}
              disabled={isDetail}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="甩单工号">
          {getFieldDecorator('mobileFraudUser.throwOrderWorkId', {
            initialValue: details?.mobileFraudUser?.throwOrderWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="甩单人">
          {getFieldDecorator('mobileFraudUser.throwOrderOperator', {
            initialValue: details?.mobileFraudUser?.throwOrderOperator,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="输单时间">
          {getFieldDecorator('mobileFraudUser.inputOrderTime', {
            initialValue: details?.mobileFraudUser?.inputOrderTime
              ? moment(details?.mobileFraudUser?.inputOrderTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
              disabled={isDetail}
              style={{ width: '100%' }}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="输单工号">
          {getFieldDecorator('mobileFraudUser.inputOrderWorkId', {
            initialValue: details?.mobileFraudUser?.inputOrderWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="输单人">
          {getFieldDecorator('mobileFraudUser.inputOrderOperator', {
            initialValue: details?.mobileFraudUser?.inputOrderOperator,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>

      {/* <Col span={6}>
                <Form.Item label="是否校园卡">
                    {getFieldDecorator("mobileFraudUser.ifCampusCard", {
                        initialValue: details?.mobileFraudUser?.ifCampusCard,
                    })(
                        <Select placeholder="请选择" allowClear disabled={isDetail}>
                            <Option value='是'>是</Option>
                            <Option value='否'>否</Option>
                        </Select>
                    )}
                </Form.Item>
            </Col> */}
    </Row>
  );
};

export default UserInfoForm;
