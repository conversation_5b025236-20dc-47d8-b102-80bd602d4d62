/**
 * 这页面搬得移动涉诈复盘的，稍微改动了一点，代码冗余不是我干的，我也很懵逼
 */
import {
  Button,
  Col,
  Form,
  message,
  Row,
  Select,
  DatePicker,
  Card,
  Input,
  Divider,
  Modal,
  Icon,
  InputNumber,
  Tooltip,
} from 'antd';
import { connect } from 'dryad';
import { Fragment, useEffect, useState } from 'react';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal';
import EditModal from './EditModal';
import { useAuth } from 'ponshine';
import { onEnterPage } from '@/utils/openTab';
import { Licensee, useLicensee } from 'ponshine';
import styles from './index.less';
import { getSystemConfigListByConfigType } from '@/services/common';

import ExportApprove from '@/components/ExportApprove';

const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
  pageSize: 10,
  pageNum: 1,
};
const formItemLayout = {
  labelCol: {
    span: 10,
  },
  wrapperCol: {
    span: 14,
  },
};

const FixedLineFraudVerificationResume = (props) => {
  const {
    dispatch,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue, getFieldsValue },
    fixedLineFraudVerificationResume: {
      tableData,
      details,
      localNetworkList,
      gopageParams,
      classificationList,
    },
    loading,
    location: { query },
  } = props;
  const [params, setParams] = useState(defaultPagination);
  const [importVisible, setImportVisible] = useState(false);
  const [importBatchResultVisible, setImportBatchResultVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [isDetail, setIsDetail] = useState(false);
  const { authState } = useAuth() || {};
  const [selectedRows, setSelectedRows] = useState([]);
  const [allColumns, setAllColumns] = useState([]);
  const initDate = [moment(), moment()];

  const {
    user: { role = {} },
  } = authState || {};
  const isHightAuth = role.id == 1 || role.id == 5;
  const isEditAuth = role.id == 1 || role.id == 2 || role.id == 5;

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'taskId',
      align: 'center',
      ellipsis: true,
      width: 80,
    },
    {
      title: '通报时间',
      dataIndex: 'notificationDate',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'classification',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '本地网',
      dataIndex: 'localNetwork',
      width: 80,
      align: 'center',
      ellipsis: true,
    },

    {
      title: '号码',
      dataIndex: 'reportedPhoneNum',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '案件类别',
      dataIndex: 'caseType',
      width: 100,
      align: 'left',
      ellipsis: true,
    },
    {
      title: '受害人电话',
      dataIndex: 'victimPhoneNum',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '是否已经复盘',
      dataIndex: 'ifReplay',
      width: 100,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '导入时间',
      dataIndex: 'gmtCreate',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'opt',
      width: 60,
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <>
            <Licensee license="fixedLineFraudVerificationResume_update">
              <a onClick={(e) => editInfo(e, record)}>编辑</a>
              {/* <Divider type="vertical" /> */}
            </Licensee>
            {/* <Licensee license="mobileReplay_delete">
              <a onClick={(e) => delInfo(e, record.id)}>删除</a>
            </Licensee> */}
          </>
        );
      },
    },
  ];

  const editInfo = (e, record) => {
    e.stopPropagation();
    e.preventDefault();
    setEditVisible(true);
    getFixTelReplayDetailById(record.id);
  };

  const hideEdit = () => {
    setEditVisible(false);
    setIsDetail(false);
  };
  const getFixTelReplayDetailById = (id) => {
    dispatch({
      type: 'fixedLineFraudVerificationResume/getFixTelReplayDetailById',
      payload: { id },
    });
  };
  const handleEditOk = (values, callback) => {
    dispatch({
      type: 'fixedLineFraudVerificationResume/updateFixTelReplayById',
      payload: values,
      callback: (res) => {
        if (res.code == 200) {
          message.success(res.message || '修改成功');
          callback();
          hideEdit();
          getPageInvolvedReplay({ ...params, ...defaultPagination });
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const onSelectRow = (selectedRows) => {
    setSelectedRows(selectedRows);
  };

  const handleImport = () => {
    setImportVisible(true);
  };

  const hideImport = () => {
    setImportVisible(false);
  };

  const [confirmLoading, setConfirmLoading] = useState(false);

  const onImport = (values, callback) => {
    setConfirmLoading(true);
    dispatch({
      type: 'fixedLineFraudVerificationResume/batchImportFixTelInfo',
      payload: values,
      callback: (res) => {
        setConfirmLoading(false);
        if (res.code == 200) {
          message.success(res.message || '导入成功');
          setSelectedRows([]);
          getPageInvolvedReplay({ ...params, ...defaultPagination });
          hideImport();
        } else {
          if (res.code == 401) {
            callback();
          } else {
            message.error(res.message);
          }
        }
      },
    });
  };

  const getPageInvolvedReplay = (params) => {
    const { notificationDateStart, notificationDateEnd } = params;
    dispatch({
      type: 'fixedLineFraudVerificationResume/pageFixTelReplay',
      payload: {
        ...params,
        notificationDateStart: notificationDateStart || initDate?.[0].format('YYYYMMDD'),
        notificationDateEnd: notificationDateEnd || initDate?.[1].format('YYYYMMDD'),
      },
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
    setParams(params);
  };

  const [netWorkList, setNetWorkList] = useState([]);

  const getOrganizationByUser = () => {
    dispatch({
      type: 'fixedLineFraudVerificationResume/getOrganizationByUser',
      callback: (res) => {
        if (res && res.code == '200' && res.data) {
          setNetWorkList(res.data || []);
        } else {
          setNetWorkList([]);
        }
      },
    });
  };

  //查询
  const handleSearch = () => {
    validateFields(
      (
        err,
        {
          notificationDateStart,
          miitAssessmentStart,
          provincialOfficeAssessmentStart,
          gmtCreateStart,
          ageMax,
          ageMin,
          ...rest
        },
      ) => {
        if (err) {
          return;
        }

        const newParams = {
          ...params,
          ...defaultPagination,
          ...rest,
          notificationDateStart: notificationDateStart?.[0]?.format('YYYYMMDD'),
          notificationDateEnd: notificationDateStart?.[1]?.format('YYYYMMDD'),
          miitAssessmentStart:
            miitAssessmentStart && miitAssessmentStart.length > 0
              ? moment(miitAssessmentStart[0])?.format('YYYYMM01')
              : '',
          miitAssessmentEnd:
            miitAssessmentStart && miitAssessmentStart.length > 0
              ? moment(miitAssessmentStart[1])?.endOf('months')?.format('YYYYMMDD')
              : '',
          provincialOfficeAssessmentStart:
            provincialOfficeAssessmentStart && provincialOfficeAssessmentStart.length > 0
              ? moment(provincialOfficeAssessmentStart[0])?.format('YYYYMM01')
              : '',
          provincialOfficeAssessmentEnd:
            provincialOfficeAssessmentStart && provincialOfficeAssessmentStart.length > 0
              ? moment(provincialOfficeAssessmentStart[1])?.endOf('months')?.format('YYYYMMDD')
              : '',
          gmtCreateStart:
            gmtCreateStart && gmtCreateStart.length > 0
              ? moment(gmtCreateStart[0])?.format('YYYYMMDD')
              : '',
          gmtCreateEnd:
            gmtCreateStart && gmtCreateStart.length > 0
              ? moment(gmtCreateStart[1])?.format('YYYYMMDD')
              : '',
          ageMax,
          ageMin,
        };
        setSelectedRows([]);
        getPageInvolvedReplay(newParams);
      },
    );
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    const newParms = { ...params, pageNum: pagination.current, pageSize: pagination.pageSize };
    getPageInvolvedReplay(newParms);
  };

  const handleInfo = (record) => {
    setIsDetail(true);
    setEditVisible(true);
    getFixTelReplayDetailById(record.id);
  };

  const handleReset = () => {
    resetFields();
    setSelectedRows([]);
    setParams({ ...defaultPagination });
    getPageInvolvedReplay({ ...defaultPagination });
  };

  const handleDelete = () => {
    if (!selectedRows.length) return message.warning('请选择要删除的复盘任务');

    Modal.confirm({
      title: '复盘任务删除',
      content: '是否确认删除该复盘任务?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        dispatch({
          type: 'fixedLineFraudVerificationResume/batchDeleteById',
          payload: { idList: selectedRows?.map((ele) => ele.id)?.join(',') },
          callback: (res) => {
            if (res.code == 200) {
              message.success(res.message || '删除成功');
              setSelectedRows([]);
              getPageInvolvedReplay({ ...params, ...defaultPagination });
            } else {
              message.error(res.message);
            }
          },
        });
      },
    });
  };

  const getFixTelClassification = () => {
    dispatch({
      type: 'fixedLineFraudVerificationResume/getFixTelClassification',
    });
  };

  useEffect(() => {
    getOrganizationByUser();
    getFixTelClassification();
    getPageInvolvedReplay({ ...defaultPagination });

    getSystemConfigListByConfigType({
      configType: 'FixTelReplay',
    }).then((res) => {
      if (res.code === 200) {
        const columnsArr =
          res?.data?.map((ele) => {
            return {
              dataIndex: ele.name,
              title: ele.value,
              width: columns?.find((item) => item.dataIndex === ele.name)?.width
                ? columns?.find((item) => item.dataIndex === ele.name)?.width
                : ele.value.length < 6
                ? 100
                : 130,
              ellipsis: true,
              render: (text) => {
                return (
                  <Tooltip title={text}>
                    <span>{text || '--'}</span>
                  </Tooltip>
                );
              },
            };
          }) || [];
        columnsArr.push(columns[columns.length - 1]);
        setAllColumns(columnsArr);
      }
    });
  }, []);
  const min = getFieldsValue()?.ageMin == 0 ? '0' : getFieldsValue()?.ageMin;
  const max = getFieldsValue()?.ageMax == 0 ? '0' : getFieldsValue()?.ageMax;
  useEffect(() => {
    if (min && max) {
      if (Number(min) >= Number(max)) {
        setFieldsValue({
          ageMin: undefined,
        });
      }
      if (Number(min) < 0) {
        setFieldsValue({
          ageMin: undefined,
        });
      }
    } else {
      setFieldsValue({
        ageMin: min,
      });
    }
  }, [min]);

  useEffect(() => {
    if (min && max) {
      if (Number(min) >= Number(max)) {
        setFieldsValue({
          ageMax: undefined,
        });
      }
      if (Number(max) < 0) {
        setFieldsValue({
          ageMax: undefined,
        });
      }
    } else {
      setFieldsValue({
        ageMax: max,
      });
    }
  }, [max]);
  useEffect(() => {
    if (gopageParams) {
      getPageInvolvedReplay({
        ...params,
        caseType: gopageParams.caseType,
        classification: gopageParams.classification,
      });
      setFieldsValue({
        caseType: gopageParams.caseType,
        classification: gopageParams.classification,
      });
      dispatch({
        type: 'fixedLineFraudVerificationResume/save',
        payload: {
          gopageParams: undefined,
        },
      });
    }
  }, [gopageParams]);

  const showInfo = true;

  const [showSearch, setShowSearch] = useState(false);
  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        {/* <Licensee license="mobileReplay_pageQuery"> */}
        <div style={{ display: 'flex' }}>
          <div style={{ flex: 1, width: 0 }}>
            <Row>
              <Col span={8}>
                <Form.Item label="通报时间">
                  {getFieldDecorator('notificationDateStart', {
                    initialValue: initDate,
                  })(<DatePicker.RangePicker format={'YYYY-MM-DD'} allowClear />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="号码">
                  {getFieldDecorator('reportedPhoneNum')(<Input placeholder="请输入" allowClear />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="分类">
                  {getFieldDecorator('classification')(
                    <Select placeholder="请输入" allowClear showSearch>
                      {classificationList.map((value) => (
                        <Option value={value}>{value}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ display: showSearch ? 'block' : 'none' }}>
              {
                <Fragment>
                  <Col span={8}>
                    <Form.Item label="是否已复盘">
                      {getFieldDecorator('ifReplay')(
                        <Select placeholder="请选择" allowClear>
                          <Option value="是">是</Option>
                          <Option value="否">否</Option>
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="本地网">
                      {getFieldDecorator('localNetwork')(
                        <Select placeholder="请输入" allowClear showSearch>
                          {netWorkList.map((item) => (
                            <Option value={item.name}>{item.name}</Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item label="案件类别">
                      {getFieldDecorator('caseType')(<Input placeholder="请输入" allowClear />)}
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item label="省内模型是否关停" className={styles.myStylesFormItem}>
                      {getFieldDecorator('ifShutdownProvinceModel')(
                        <Select placeholder="请选择" allowClear>
                          <Option value="是">是</Option>
                          <Option value="否">否</Option>
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="集团模型是否关停" className={styles.myStylesFormItem}>
                      {getFieldDecorator('ifShutdownGroupModel')(
                        <Select placeholder="请选择" allowClear>
                          <Option value="是">是</Option>
                          <Option value="否">否</Option>
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item label="导入时间">
                      {getFieldDecorator('gmtCreateStart')(
                        <DatePicker.RangePicker format={'YYYY-MM-DD'} allowClear />,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="渠道类型">
                      {getFieldDecorator('channelType')(
                        <Select placeholder="请选择" allowClear>
                          <Option value="实体渠道">实体渠道</Option>
                          <Option value="电子渠道">电子渠道</Option>
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    {/* <div className={styles.age}> */}
                    <Form.Item label="年龄">
                      {getFieldDecorator('ageMin', {
                        rules: [
                          {
                            required: max && true,
                            message: '请选择起始年龄',
                          },
                        ],
                      })(<InputNumber allowClear placeholder="请输入"></InputNumber>)}
                      <span style={{ marginLeft: 8, marginRight: 8 }}>-</span>
                      {getFieldDecorator('ageMax', {
                        rules: [
                          {
                            required: min && true,
                            message: '请选择起始年龄',
                          },
                        ],
                      })(<InputNumber allowClear placeholder="请输入"></InputNumber>)}
                    </Form.Item>
                  </Col>
                </Fragment>
              }
            </Row>
          </div>
          <div style={{ width: 60, flexShrink: 0, paddingTop: 10, paddingLeft: 8 }}>
            <span
              onClick={() => {
                setShowSearch(!showSearch);
              }}
              style={{ color: '#1190ff', cursor: 'pointer' }}
            >
              {showSearch ? '折叠' : '展开'}
              <Icon type={showSearch ? 'up' : 'down'} />
            </span>
          </div>
        </div>
        {/* </Licensee> */}
        <Row>
          <Col span={24} style={{ marginBottom: 20, textAlign: 'right' }}>
            <Licensee license="fixedLineFraudVerificationResume_page">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 10 }}>
                重置
              </Button>
            </Licensee>
            <Licensee license="fixedLineFraudVerificationResume_export">
              <ExportApprove
                buttonText="导出"
                buttonStyle={{ marginLeft: 10, marginRight: 10 }}
                exportParams={{
                  urlAPi: '/api/hn/fixTelReplay/exportFixTelReplay',
                  decode: true,
                  params: {
                    ...params,
                    notificationDateStart:
                      params?.notificationDateStart || initDate?.[0].format('YYYYMMDD'),
                    notificationDateEnd:
                      params?.notificationDateEnd || initDate?.[1].format('YYYYMMDD'),
                    pageNum: 1,
                    pageSize: 10,
                  },
                  method: 'POST',
                }}
                moduleTile="复盘管理"
                // 是否校验商业秘密电子文件相关
                isVerifyhEncryption={true}
                disabledExport={!tableData?.items?.length}
              />
            </Licensee>

            <Licensee license="fixedLineFraudVerificationResume_delete">
              <Button type="danger" onClick={handleDelete} style={{ marginRight: 10 }}>
                删除
              </Button>
            </Licensee>

            <Licensee license="fixedLineFraudVerificationResume_import">
              {/* {isEditAuth && */}
              <Button type="primary" onClick={handleImport}>
                通报信息导入
              </Button>
              {/* } */}
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        detailColumns={allColumns}
        tools={true}
        loading={loading}
        data={{
          list: tableData?.items || [],
          pagination: {
            current: params.pageNum,
            pageSize: params.pageSize,
            total: tableData?.totalNum || 0,
          },
        }}
        onChange={handlePaginationTable}
        rowKey="id"
        showSelectCount={true}
        rowSelectionProps={true}
        selectedRows={selectedRows}
        onSelectRow={onSelectRow}
        onRow={(record) => {
          return {
            onDoubleClick: () => {
              showInfo && handleInfo(record);
            },
          };
        }}
      />

      {importVisible && (
        <BatchImportModal
          title="批量导入"
          visible={importVisible}
          onImport={onImport}
          onClose={hideImport}
          loading={confirmLoading}
          errorExportUrl={'/api/hn/fixTelReplay/downloadErrorExcel'}
          downTemplateUrl={`template/getTemplate?templateCode=fixTelReplayImport`}
          tipsText="*每个文件不得超过10万条号码"
        />
      )}

      {editVisible && (
        <EditModal
          visible={editVisible}
          onClose={hideEdit}
          handleEditOk={handleEditOk}
          isDetail={isDetail}
          details={details}
          classificationList={classificationList}
        />
      )}
    </Card>
  );
};

export default connect(({ fixedLineFraudVerificationResume, loading }) => ({
  fixedLineFraudVerificationResume,
  loading: loading.effects['fixedLineFraudVerificationResume/pageFixTelReplay'],
}))(Form.create()(FixedLineFraudVerificationResume));
