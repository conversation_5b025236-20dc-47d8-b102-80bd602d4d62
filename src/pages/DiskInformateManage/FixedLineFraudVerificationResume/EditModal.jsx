import React, { useEffect, useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  InputNumber,
  Spin,
  Collapse,
  message,
} from 'antd';

import moment from 'moment';
import styles from './index.less';
import { useAuth } from 'ponshine';
import UserInfoForm from './UserInfoForm';
import { connect } from 'dryad';
import request from '@/utils/request';

const { Option } = Select;
const { Panel } = Collapse;

const EditModal = (props) => {
  const {
    visible,
    onClose,
    details,
    handleEditOk,
    form,
    form: { getFieldDecorator, validateFieldsAndScroll, resetFields, setFieldsValue },
    loading,
    isDetail,
    infoLoading,
    dispatch,
    mobileFraudVerificationResume: { caseTypeList },
    classificationList,
  } = props;
  const { authState } = useAuth() || {};

  const {
    user: { role = {} },
  } = authState || {};

  const noEdit = role.id == 2;
  const handleOk = () => {
    validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      const params = {
        ...values,
        id: details.id,
        latestShutdownTime: values?.latestShutdownTime?.format('YYYY-MM-DD HH:mm:ss'),
        notificationDate: values.notificationDate
          ? moment(values.notificationDate).format('YYYYMMDD')
          : '',
        incidentEndTime: values.incidentEndTime
          ? moment(values.incidentEndTime).format('YYYYMMDDHHmmss')
          : '',
        incidentStartTime: values.incidentStartTime
          ? moment(values.incidentStartTime).format('YYYYMMDDHHmmss')
          : '',
        callTime: values.callTime ? moment(values.callTime).format('YYYYMMDDHHmmss') : '',
        // reportTime: values.reportTime ? moment(values.reportTime).format('YYYYMMDDHHmmss') : '',
        // talkTime: values.talkTime ? moment(values.talkTime).format('YYYYMMDDHHmmss') : '',
        blackExpireTime: values.blackExpireTime
          ? moment(values.blackExpireTime).format('YYYYMMDD')
          : '',
        shutdownTime: values.shutdownTime ? moment(values.shutdownTime).format('YYYYMMDD') : '',
        fixTelFraudUser: {
          ...values.fixTelFraudUser,
          internetAccessTime: values.fixTelFraudUser.internetAccessTime
            ? moment(values.fixTelFraudUser.internetAccessTime).format('YYYYMMDDHHmmss')
            : '',
          resumeTime: values.fixTelFraudUser.resumeTime
            ? moment(values.fixTelFraudUser.resumeTime).format('YYYYMMDDHHmmss')
            : '',
          throwOrderTime: values.fixTelFraudUser.throwOrderTime
            ? moment(values.fixTelFraudUser.throwOrderTime).format('YYYYMMDDHHmmss')
            : '',
          inputOrderTime: values.fixTelFraudUser.inputOrderTime
            ? moment(values.fixTelFraudUser.inputOrderTime).format('YYYYMMDDHHmmss')
            : '',
        },
      };
      handleEditOk(params, () => {
        resetFields();
      });
    });
  };

  const onCancle = () => {
    resetFields();
    onClose();
  };

  const renderNum = (type) => {
    return configData[type] || 60;
  };

  const changeDate = (v, type) => {
    setFieldsValue({ [type]: v });
  };

  const changePanel = (v, type) => {
    setFieldsValue({ [type]: v });
  };

  useEffect(() => {
    getConfig();
  }, []);

  useEffect(() => {
    if (details?.classification) {
      onClassificationChange(details?.classification);
    }
  }, [details]);

  const getConfig = () => {
    setConfigLoading(true);
    request('/api/hn/mobileReplay/getTimeConfig', {
      method: 'get',
    })
      .then((res) => {
        if (res.code == 200) {
          setConfigData(res.data || {});
        }
      })
      .finally(() => {
        setConfigLoading(false);
      });
  };

  const [configData, setConfigData] = useState({});
  const [configLoading, setConfigLoading] = useState(false);

  const onClassificationChange = (value) => {
    if (['12321用户举报', '公安侦办平台']?.includes(value)) {
      dispatch({
        type: 'mobileFraudVerificationResume/getAllClassification',
        payload: {
          name: value,
        },
      });
    } else {
      dispatch({
        type: 'mobileFraudVerificationResume/save',
        payload: {
          caseTypeList: [],
        },
      });
    }
  };

  return (
    <Modal
      title={isDetail ? '查看复盘详情' : '复盘编辑'}
      visible={visible}
      onCancel={onCancle}
      onOk={isDetail ? onCancle : handleOk}
      confirmLoading={loading}
      width={'90vw'}
      style={{ top: 40, left: 50 }}
      bodyStyle={{ height: 560, overflow: 'auto', padding: '8px 16px' }}
    >
      <Spin spinning={infoLoading || configLoading}></Spin>
      <Form className={styles.myStylesForm}>
        <Collapse defaultActiveKey={['2', '3']}>
          <Panel header="通报信息" key="1">
            <Row gutter={[24]}>
              <Col span={6}>
                <Form.Item label="通报时间">
                  {getFieldDecorator('notificationDate', {
                    initialValue: details?.notificationDate
                      ? moment(details.notificationDate, 'YYYY-MM-DD')
                      : undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择通报时间',
                      },
                    ],
                  })(<DatePicker style={{ minWidth: 'auto' }} disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="分类">
                  {getFieldDecorator('classification', {
                    initialValue: details?.classification || undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择分类',
                      },
                    ],
                  })(
                    <Select
                      placeholder="请输入"
                      showSearch
                      allowClear
                      disabled={isDetail || noEdit}
                      onChange={(value) => {
                        onClassificationChange(value);
                        setFieldsValue({ caseType: undefined });
                      }}
                    >
                      {classificationList.map((value) => (
                        <Option value={value}>{value}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="提供单位">
                  {getFieldDecorator('provider', {
                    initialValue: details?.provider,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label="号码">
                  {getFieldDecorator('reportedPhoneNum', {
                    initialValue: details?.reportedPhoneNum,
                    rules: [
                      {
                        required: true,
                        message: '请输入号码',
                      },
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={true} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="归属地">
                  {getFieldDecorator('reportedLocation', {
                    initialValue: details?.reportedLocation,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入归属地'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="案件类别">
                  {getFieldDecorator('caseType', {
                    initialValue: details?.caseType || undefined,
                    rules: [],
                  })(
                    ['12321用户举报', '公安侦办平台']?.includes(
                      form.getFieldValue('classification'),
                    ) ? (
                      <Select
                        placeholder="请输入"
                        allowClear
                        disabled={isDetail || noEdit}
                        showSearch
                      >
                        {caseTypeList.map((value) => (
                          <Option value={value}>{value}</Option>
                        ))}
                      </Select>
                    ) : (
                      <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                    ),
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发案开始时间">
                  {getFieldDecorator('incidentStartTime', {
                    initialValue: details?.incidentStartTime
                      ? moment(details.incidentStartTime, 'YYYY-MM-DD HH:mm:ss')
                      : undefined,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请选择发案开始时间'
                      // }
                    ],
                  })(
                    <DatePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      style={{ minWidth: 'auto' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发案结束时间">
                  {getFieldDecorator('incidentEndTime', {
                    initialValue: details.incidentEndTime
                      ? moment(details.incidentEndTime, 'YYYY-MM-DD HH:mm:ss')
                      : undefined,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请选择发案结束时间'
                      // }
                    ],
                  })(
                    <DatePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      style={{ minWidth: 'auto' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="受害人电话">
                  {getFieldDecorator('victimPhoneNum', {
                    initialValue: details?.victimPhoneNum,
                    rules: [
                      // {
                      //     required: true,
                      //     message: '请输入受害人电话'
                      // }
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="举报号码运营商">
                  {getFieldDecorator('reportIsp', {
                    initialValue: details?.reportIsp || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      {['移动', '联通', '电信', '广电'].map((v) => (
                        <Option value={v}>{v}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="举报号码归属省">
                  {getFieldDecorator('reportProvince', {
                    initialValue: details?.reportProvince,
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="举报号码归属市">
                  {getFieldDecorator('reportCity', {
                    initialValue: details?.reportCity,
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              {/* <Col span={6}>
            <Form.Item label="举报时间">
              {getFieldDecorator('reportTime', {
                initialValue: details.reportTime
                  ? moment(details.reportTime, 'YYYY-MM-DD HH:mm:ss')
                  : undefined,
                rules: [],
              })(
                <DatePicker
                  showTime={{ format: 'HH:mm:ss' }}
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: '100%' }}
                  disabled={isDetail || noEdit}
                />,
              )}
            </Form.Item>
          </Col> */}
              <Col span={6}>
                <Form.Item label="来电时间">
                  {getFieldDecorator('callTime', {
                    initialValue: details?.callTime
                      ? moment(details.callTime, 'YYYY-MM-DD HH:mm:ss')
                      : undefined,
                    rules: [],
                  })(
                    <DatePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      style={{ minWidth: 'auto' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="关停时间">
                  {getFieldDecorator('shutdownTime', {
                    initialValue: details?.shutdownTime
                      ? moment(details.shutdownTime, 'YYYY-MM-DD')
                      : undefined,
                    rules: [],
                  })(
                    <DatePicker
                      format="YYYY-MM-DD"
                      style={{ minWidth: 'auto' }}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="举报内容" labelCol={{ span: 2 }}>
                  {getFieldDecorator('reportContent', {
                    initialValue: details?.reportContent,
                  })(
                    <Input.TextArea
                      rows={3}
                      style={{ width: '100%' }}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item label="备注" labelCol={{ span: 2 }}>
                  {getFieldDecorator('remark', {
                    initialValue: details?.remark,
                  })(
                    <Input.TextArea
                      rows={3}
                      style={{ width: '100%' }}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Panel>
          <Panel header="复盘核查" key="2">
            <Row gutter={[24]}>
              <Col span={6}>
                <Form.Item label="是否处置成功">
                  {getFieldDecorator('ifDisposeSuccess', {
                    initialValue: details?.ifDisposeSuccess || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="是否外呼确认">
                  {getFieldDecorator('ifOutboundCheck', {
                    initialValue: details?.ifOutboundCheck || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="绑定关系">
                  {getFieldDecorator('bindingRela', {
                    initialValue: details?.bindingRela || undefined,
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="业务类型">
                  {getFieldDecorator('businessType', {
                    initialValue: details?.businessType || undefined,
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省内处置前状态">
                  {getFieldDecorator('statusBeforeProDispose', {
                    initialValue: details?.statusBeforeProDispose || undefined,
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="是否黑名单二次入网" className={styles.widthForm}>
                  {getFieldDecorator('ifBlackSecondNetwork', {
                    initialValue: details?.ifBlackSecondNetwork || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      {['是', '否']?.map((ele, index) => (
                        <Option value={ele} key={index}>
                          {ele}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="名下固话数量">
                  {getFieldDecorator('telCount', {
                    initialValue: details?.telCount || undefined,
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={0}
                      disabled={isDetail || noEdit}
                      min="0"
                      style={{ width: '100%' }}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="名下固话安装地址数量" className={styles.widthForm}>
                  {getFieldDecorator('telAddressCount', {
                    initialValue: details?.telAddressCount || undefined,
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={0}
                      disabled={isDetail || noEdit}
                      min="0"
                      style={{ width: '100%' }}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="涉案固话安装地是否存在其它户名固话" className={styles.widthForm}>
                  {getFieldDecorator('ifOtherTelWithSameAddress', {
                    initialValue: details?.ifOtherTelWithSameAddress || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      {['是', '否']?.map((ele, index) => (
                        <Option value={ele} key={index}>
                          {ele}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="安装地址是否为宾馆或酒店" className={styles.widthForm}>
                  {getFieldDecorator('ifHotel', {
                    initialValue: details?.ifHotel || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      {['是', '否']?.map((ele, index) => (
                        <Option value={ele} key={index}>
                          {ele}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="涉案场景描述">
                  {getFieldDecorator('involvedSceneDesc', {
                    initialValue: details?.involvedSceneDesc || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      {['外呼', '引流被叫', '呼转']?.map((ele, index) => (
                        <Option value={ele} key={index}>
                          {ele}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="是否为首次入网新用户" className={styles.widthForm}>
                  {getFieldDecorator('ifFirstNetwork', {
                    initialValue: details?.ifFirstNetwork || undefined,
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      {['是', '否']?.map((ele, index) => (
                        <Option value={ele} key={index}>
                          {ele}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否新入网（${renderNum('timeConfigOfIfNewAccess')}个月内）`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifNewAccess', {
                    initialValue: details?.ifNewAccess || undefined,
                    rules: [],
                  })(
                    <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省内模型是否关停" className={styles.widthForm}>
                  {getFieldDecorator('ifShutdownProvinceModel', {
                    initialValue: details?.ifShutdownProvinceModel,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省模型关停时间差（小时）" className={styles.widthForm}>
                  {getFieldDecorator('provinceShutdownInterval', {
                    initialValue: details?.provinceShutdownInterval,
                    // rules: [
                    //   {
                    //     pattern: /^\\d+(\\.\\d{1,2})?$/,
                    //     message: '请输入数字保留两位小数',
                    //   },
                    // ],
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={2}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="省内模型分类">
                  {getFieldDecorator('provinceModelType', {
                    initialValue: details?.provinceModelType,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`省内模型关停次数（${renderNum(
                    'timeConfigOfProvinceModelShutdownTimes',
                  )}）天内`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('provinceModelShutdownTimes', {
                    initialValue: details?.provinceModelShutdownTimes,
                    rules: [],
                  })(
                    <InputNumber precision={0} min={0} allowClear disabled={isDetail || noEdit} />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团模型是否关停" className={styles.widthForm}>
                  {getFieldDecorator('ifShutdownGroupModel', {
                    initialValue: details?.ifShutdownGroupModel,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团关停时间差（小时）" className={styles.widthForm}>
                  {getFieldDecorator('groupShutdownInterval', {
                    initialValue: details?.groupShutdownInterval,
                    // rules: [
                    //   {
                    //     pattern: /^\\d+(\\.\\d{1,2})?$/,
                    //     message: '请输入数字保留两位小数',
                    //   },
                    // ],
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={2}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="集团模型分类" className={styles.widthForm}>
                  {getFieldDecorator('groupModelType', {
                    initialValue: details?.groupModelType,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`集团模型关停次数（${renderNum(
                    'timeConfigOfGroupModelShutdownTimes',
                  )}）天内`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('groupModelShutdownTimes', {
                    initialValue: details?.groupModelShutdownTimes,
                    rules: [],
                  })(
                    <InputNumber
                      placeholder="请输入"
                      allowClear
                      precision={0}
                      min={0}
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`是否多次复机（${renderNum('timeConfigOfIfResumeFrequently')}）天`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifResumeFrequently', {
                    initialValue: details?.ifResumeFrequently,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`复机次数（${renderNum('timeConfigOfResumeTimes')}）天`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('resumeTimes', {
                    initialValue: details?.resumeTimes,
                    rules: [],
                  })(
                    <InputNumber
                      min={0}
                      precision={0}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={`涉案前是否被模型关停（${renderNum(
                    'timeConfigOfIfShutdownByModelBefore',
                  )}）天`}
                  className={styles.widthForm}
                >
                  {getFieldDecorator('ifShutdownByModelBefore', {
                    initialValue: details?.ifShutdownByModelBefore,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label="复机方式">
                  {getFieldDecorator('resumeWay', {
                    initialValue: details?.resumeWay,
                    rules: [],
                  })(<Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />)}
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item label="异常备注">
                  {getFieldDecorator('exceptionRemark', {
                    initialValue: details?.exceptionRemark,
                  })(
                    <Input.TextArea
                      rows={2}
                      style={{ width: '100%' }}
                      placeholder="请输入"
                      allowClear
                      disabled={isDetail || noEdit}
                    />,
                  )}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label={`是否加黑`}>
                  {getFieldDecorator('ifAddBlack', {
                    initialValue: details?.ifAddBlack || undefined,
                    rules: [],
                  })(
                    <Select
                      placeholder="请选择"
                      allowClear
                      disabled={isDetail || noEdit || details?.ifAddBlack == '是'}
                    >
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`加黑到期时间`}>
                  {getFieldDecorator('blackExpireTime', {
                    initialValue: details?.blackExpireTime
                      ? moment(details.blackExpireTime, 'YYYY-MM-DD')
                      : undefined,
                    rules: [
                      {
                        required: form.getFieldValue('ifAddBlack') == '是' ? true : false,
                        message: '请选择加黑到期时间',
                      },
                    ],
                  })(
                    <DatePicker
                      format={'YYYY-MM-DD'}
                      allowClear
                      disabled={isDetail || noEdit || details?.ifAddBlack == '是'}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={`加黑原因`}>
                  {getFieldDecorator('blackReason', {
                    initialValue: details?.blackReason,
                    rules: [
                      {
                        required: form.getFieldValue('ifAddBlack') == '是' ? true : false,
                        message: '请输入加黑原因',
                      },
                    ],
                  })(
                    <Input
                      allowClear
                      disabled={isDetail || noEdit || details?.ifAddBlack == '是'}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Panel>
          <Panel header="用户信息" key="3">
            <UserInfoForm form={form} isDetail={isDetail} details={details} />
          </Panel>
        </Collapse>
      </Form>
    </Modal>
  );
};

export default connect(({ mobileFraudVerificationResume, loading }) => ({
  mobileFraudVerificationResume,
  loading: loading.effects['mobileFraudVerificationResume/updateMobileReplayDetailById'],
  infoLoading: loading.effects['mobileFraudVerificationResume/getMobileReplayDetailById'],
}))(Form.create()(EditModal));
