import React from 'react';
import { Form, Input, DatePicker, Row, Col, Select, InputNumber } from 'antd';
import styles from '../CaseNotifyVerify/index.less';
import moment from 'moment';

const { Option } = Select;

const UserInfoForm = (props) => {
  const {
    form: { getFieldDecorator },
    isDetail,
    details,
  } = props;
  return (
    <Row gutter={[24]}>
      <Col span={6}>
        <Form.Item label="手机号码">
          {getFieldDecorator('fixTelFraudUser.phoneNum', {
            initialValue: details?.fixTelFraudUser?.phoneNum,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="本地网">
          {getFieldDecorator('fixTelFraudUser.localNetwork', {
            initialValue: details?.fixTelFraudUser?.localNetwork,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="营业区">
          {getFieldDecorator('fixTelFraudUser.businessArea', {
            initialValue: details?.fixTelFraudUser?.businessArea,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="入网时间">
          {getFieldDecorator('fixTelFraudUser.internetAccessTime', {
            initialValue: details?.fixTelFraudUser?.internetAccessTime
              ? moment(details?.fixTelFraudUser.internetAccessTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              style={{ minWidth: 'auto' }}
              showTime
              format={'YYYY-MM-DD HH:mm:ss'}
              disabled={isDetail}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="渠道类型">
          {getFieldDecorator('fixTelFraudUser.channelType', {
            initialValue: details?.fixTelFraudUser?.channelType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="受理网点">
          {getFieldDecorator('fixTelFraudUser.acceptancePoint', {
            initialValue: details?.fixTelFraudUser?.acceptancePoint,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="销售品">
          {getFieldDecorator('fixTelFraudUser.saleCategory', {
            initialValue: details?.fixTelFraudUser?.saleCategory,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="年龄">
          {getFieldDecorator('fixTelFraudUser.age', {
            initialValue: details?.fixTelFraudUser?.age,
            rules: [
              {
                pattern: /(^[0-9]\d*$)/,
                message: '请输入正整数',
              },
            ],
          })(
            <Input style={{ width: '100%' }} placeholder="请输入" allowClear disabled={isDetail} />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="销售品价格">
          {getFieldDecorator('fixTelFraudUser.salePrice', {
            initialValue: details?.fixTelFraudUser?.salePrice,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="是否主副卡">
          {getFieldDecorator('fixTelFraudUser.masterCard', {
            initialValue: details?.fixTelFraudUser?.masterCard,
          })(
            <Select placeholder="请选择" allowClear disabled={isDetail}>
              <Option value="主卡">主卡</Option>
              <Option value="副卡">副卡</Option>
            </Select>,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="使用客户">
          {getFieldDecorator('fixTelFraudUser.usingCustomer', {
            initialValue: details?.fixTelFraudUser?.usingCustomer,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="使用人证件号码" className={styles.widthForm}>
          {getFieldDecorator('fixTelFraudUser.certificatesNumber', {
            initialValue: details?.fixTelFraudUser?.certificatesNumber,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="使用人证件类型" className={styles.widthForm}>
          {getFieldDecorator('fixTelFraudUser.certificatesType', {
            initialValue: details?.fixTelFraudUser?.certificatesType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="使用客户地址">
          {getFieldDecorator('fixTelFraudUser.usingCustomerAddress', {
            initialValue: details?.fixTelFraudUser?.usingCustomerAddress,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权客户">
          {getFieldDecorator('fixTelFraudUser.propertyCustomer', {
            initialValue: details?.fixTelFraudUser?.propertyCustomer,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权证件号码">
          {getFieldDecorator('fixTelFraudUser.propertyCertificatesNumber', {
            initialValue: details?.fixTelFraudUser?.propertyCertificatesNumber,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权证件类型">
          {getFieldDecorator('fixTelFraudUser.propertyCertificatesType', {
            initialValue: details?.fixTelFraudUser?.propertyCertificatesType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产权客户地址">
          {getFieldDecorator('fixTelFraudUser.propertyCustomerAddress', {
            initialValue: details?.fixTelFraudUser?.propertyCustomerAddress,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="客户类型">
          {getFieldDecorator('fixTelFraudUser.customerType', {
            initialValue: details?.fixTelFraudUser?.customerType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="产品类型">
          {getFieldDecorator('fixTelFraudUser.productType', {
            initialValue: details?.fixTelFraudUser?.productType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="号码标识">
          {getFieldDecorator('fixTelFraudUser.phoneNumTag', {
            initialValue: details?.fixTelFraudUser?.phoneNumTag,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="用户装机地址">
          {getFieldDecorator('fixTelFraudUser.userInstallAddress', {
            initialValue: details?.fixTelFraudUser?.userInstallAddress,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>

      <Col span={6}>
        <Form.Item label="复机单号">
          {getFieldDecorator('fixTelFraudUser.resumeNumbers', {
            initialValue: details?.fixTelFraudUser?.resumeNumbers,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="复机时间">
          {getFieldDecorator('fixTelFraudUser.resumeTime', {
            initialValue: details?.fixTelFraudUser?.resumeTime
              ? moment(details?.fixTelFraudUser?.resumeTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
              style={{ minWidth: 'auto' }}
              disabled={isDetail}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="复机工号">
          {getFieldDecorator('fixTelFraudUser.resumeWorkId', {
            initialValue: details?.fixTelFraudUser?.resumeWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="复机人">
          {getFieldDecorator('fixTelFraudUser.resumeOperator', {
            initialValue: details?.fixTelFraudUser?.resumeOperator,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="受理员工">
          {getFieldDecorator('fixTelFraudUser.acceptStaff', {
            initialValue: details?.fixTelFraudUser?.acceptStaff,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="受理工号">
          {getFieldDecorator('fixTelFraudUser.acceptanceWorkId', {
            initialValue: details?.fixTelFraudUser?.acceptanceWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人工号">
          {getFieldDecorator('fixTelFraudUser.grabMachineWorkId', {
            initialValue: details?.fixTelFraudUser?.grabMachineWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人名称">
          {getFieldDecorator('fixTelFraudUser.grabMachineName', {
            initialValue: details?.fixTelFraudUser?.grabMachineName,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人组织">
          {getFieldDecorator('fixTelFraudUser.grabMachineOrganize', {
            initialValue: details?.fixTelFraudUser?.grabMachineOrganize,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="揽机人组织渠道类型" className={styles.widthForm}>
          {getFieldDecorator('fixTelFraudUser.grabMachineOrganizeChannelType', {
            initialValue: details?.fixTelFraudUser?.grabMachineOrganizeChannelType,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="甩单时间">
          {getFieldDecorator('fixTelFraudUser.throwOrderTime', {
            initialValue: details?.fixTelFraudUser?.throwOrderTime
              ? moment(details?.fixTelFraudUser.throwOrderTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
              style={{ minWidth: 'auto' }}
              disabled={isDetail}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="甩单工号">
          {getFieldDecorator('fixTelFraudUser.throwOrderWorkId', {
            initialValue: details?.fixTelFraudUser?.throwOrderWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="甩单人">
          {getFieldDecorator('fixTelFraudUser.throwOrderOperator', {
            initialValue: details?.fixTelFraudUser?.throwOrderOperator,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="输单时间">
          {getFieldDecorator('fixTelFraudUser.inputOrderTime', {
            initialValue: details?.fixTelFraudUser?.inputOrderTime
              ? moment(details?.fixTelFraudUser?.inputOrderTime, 'YYYY-MM-DD HH:mm:ss')
              : '',
          })(
            <DatePicker
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
              disabled={isDetail}
              style={{ minWidth: 'auto' }}
            />,
          )}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="输单工号">
          {getFieldDecorator('fixTelFraudUser.inputOrderWorkId', {
            initialValue: details?.fixTelFraudUser?.inputOrderWorkId,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item label="输单人">
          {getFieldDecorator('fixTelFraudUser.inputOrderOperator', {
            initialValue: details?.fixTelFraudUser?.inputOrderOperator,
          })(<Input placeholder="请输入" allowClear disabled={isDetail} />)}
        </Form.Item>
      </Col>

      {/* <Col span={6}>
                <Form.Item label="是否校园卡">
                    {getFieldDecorator("fixTelFraudUser.ifCampusCard", {
                        initialValue: details?.fixTelFraudUser?.ifCampusCard,
                    })(
                        <Select placeholder="请选择" allowClear disabled={isDetail}>
                            <Option value='是'>是</Option>
                            <Option value='否'>否</Option>
                        </Select>
                    )}
                </Form.Item>
            </Col> */}
    </Row>
  );
};

export default UserInfoForm;
