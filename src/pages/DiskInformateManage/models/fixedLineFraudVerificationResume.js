import {
  pageFixTelReplay,
  batchImportFixTelInfo,
  batchImportReplayResult,
  updateFixTelReplayById,
  getFixTelReplayDetailById,
  deleteMobileReplayById,
  batchDeleteById,
  getOrganizationByUser,
  getSystemConfigListByConfigType,
  getFixTelClassification,
  batchQueryRealtimeStatus,
} from '@/services/DiskInformateManage/fixedLineFraudVerificationResume';

const defaultState = {
  tableData: {},
  details: {},
  localNetworkList: [],
  caseTypeList: [],
  classificationList: [],
  gopageParams: undefined,
};

export default {
  namespace: 'fixedLineFraudVerificationResume',
  state: defaultState,
  effects: {
    // 分页查询
    *pageFixTelReplay({ payload, callback }, { call, put }) {
      const response = yield call(pageFixTelReplay, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { tableData: response.data },
      });
    },
    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { localNetworkList: response.data },
      });
    },

    // 获取分类
    *getFixTelClassification({ payload, callback }, { call, put }) {
      const response = yield call(getFixTelClassification, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { [payload?.name ? 'caseTypeList' : 'classificationList']: response.data || [] },
      });
    },

    *getSystemConfigListByConfigType({ payload, callback }, { call, put }) {
      const response = yield call(getSystemConfigListByConfigType, payload);
      if (callback) callback(response);
    },

    // 导入
    *batchImportFixTelInfo({ payload, callback }, { call, put }) {
      const response = yield call(batchImportFixTelInfo, payload);
      if (callback) callback(response);
    },
    // 批量结果导入
    *batchImportReplayResult({ payload, callback }, { call, put }) {
      const response = yield call(batchImportReplayResult, payload);
      if (callback) callback(response);
    },

    // 编辑
    *updateFixTelReplayById({ payload, callback }, { call, put }) {
      const response = yield call(updateFixTelReplayById, payload);
      if (callback) callback(response);
    },

    // 详情
    *getFixTelReplayDetailById({ payload, callback }, { call, put }) {
      const response = yield call(getFixTelReplayDetailById, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { details: response.data },
      });
    },
    // 删除
    *deleteMobileReplayById({ payload, callback }, { call, put }) {
      const response = yield call(deleteMobileReplayById, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    // 批量删除
    *batchDeleteById({ payload, callback }, { call, put }) {
      const response = yield call(batchDeleteById, payload);
      if (!response) return;
      if (callback) callback(response);
    },

    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
    },

    // 批量查询
    *batchQueryRealtimeStatus({ payload, callback }, { call, put }) {
      const response = yield call(batchQueryRealtimeStatus, payload);
      if (callback) callback(response);
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
