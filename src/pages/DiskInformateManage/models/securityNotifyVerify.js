import {
  getPagePoliceReplay,
  batchImportPoliceReplayInfo,
  deletePoliceReplayById,
  getPoliceReplayDetail,
  updatePoliceReplay
} from '@/services/DiskInformateManage/securityNotifyVerify';
import {
  getOrganizationByUser,
} from '@/services/DiskInformateManage/caseNotifyVerify';

const defaultState = {
  tableData:{},
  details:{},
  localNetworkList:[],
};

export default {
  namespace: 'securityNotifyVerify',
  state: defaultState,
  effects: {
    *getPagePoliceReplay({ payload, callback }, { call, put }) {
      const response = yield call(getPagePoliceReplay, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { tableData:response.data },
      });
    },
    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { localNetworkList:response.data },
      });
    },
    *batchImportPoliceReplayInfo({ payload, callback }, { call, put }) {
      const response = yield call(batchImportPoliceReplayInfo, payload);
      if (callback) callback(response);
    },
    *deletePoliceReplayById({ payload, callback }, { call, put }) {
      const response = yield call(deletePoliceReplayById, payload);
      if (callback) callback(response);
    },
    *getPoliceReplayDetail({ payload, callback }, { call, put }) {
      const response = yield call(getPoliceReplayDetail, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { details:response.data },
      });
    },
    *updatePoliceReplay({ payload, callback }, { call, put }) {
      const response = yield call(updatePoliceReplay, payload);
      if (!response) return;
      if (callback) callback(response);
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
