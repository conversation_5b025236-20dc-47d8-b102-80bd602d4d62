import {
  getPhoneReportReplay,
  batchImportPhoneReportReplayInfo,
  deletePhoneReportReplayById,
  getPhoneReportReplayDetail,
  updatePhoneReportReplay,
} from '@/services/DiskInformateManage/fraudulentTelephoneReport';
import {
  getOrganizationByUser,
  getSystemConfigListByConfigType
} from '@/services/DiskInformateManage/caseNotifyVerify';

const defaultState = {
  tableData:{},
  details:{},
  localNetworkList:[],
};

export default {
  namespace: 'fraudulentTelephoneReport',
  state: defaultState,
  effects: {
    *getPhoneReportReplay({ payload, callback }, { call, put }) {
      const response = yield call(getPhoneReportReplay, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { tableData:response.data },
      });
    },
    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { localNetworkList:response.data },
      });
    },
    *getSystemConfigListByConfigType({ payload, callback }, { call, put }) {
      const response = yield call(getSystemConfigListByConfigType, payload);
      if (callback) callback(response);
    },
    *batchImportPhoneReportReplayInfo({ payload, callback }, { call, put }) {
      const response = yield call(batchImportPhoneReportReplayInfo, payload);
      if (callback) callback(response);
    },
    *deletePhoneReportReplayById({ payload, callback }, { call, put }) {
      const response = yield call(deletePhoneReportReplayById, payload);
      if (callback) callback(response);
    },
    *getPhoneReportReplayDetail({ payload, callback }, { call, put }) {
      const response = yield call(getPhoneReportReplayDetail, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { details:response.data },
      });
    },
    *updatePhoneReportReplay({ payload, callback }, { call, put }) {
      const response = yield call(updatePhoneReportReplay, payload);
      if (!response) return;
      if (callback) callback(response);
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
