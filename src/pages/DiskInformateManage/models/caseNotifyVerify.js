import {
  getPageInvolvedReplay,
  batchImportInvolvedReplayInfo,
  updateInvolvedReplay,
  getInvolvedReplayDetail,
  deleteInvolvedReplay,
  getOrganizationByUser,
  getSystemConfigListByConfigType
} from '@/services/DiskInformateManage/caseNotifyVerify';

const defaultState = {
  tableData:{},
  details:{},
  localNetworkList:[],
};

export default {
  namespace: 'caseNotifyVerify',
  state: defaultState,
  effects: {
    *getPageInvolvedReplay({ payload, callback }, { call, put }) {
      const response = yield call(getPageInvolvedReplay, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { tableData:response.data },
      });
    },
    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { localNetworkList:response.data },
      });
    },
    *getSystemConfigListByConfigType({ payload, callback }, { call, put }) {
      const response = yield call(getSystemConfigListByConfigType, payload);
      if (callback) callback(response);
    },
    *batchImportInvolvedReplayInfo({ payload, callback }, { call, put }) {
      const response = yield call(batchImportInvolvedReplayInfo, payload);
      if (callback) callback(response);
    },
    *updateInvolvedReplay({ payload, callback }, { call, put }) {
      const response = yield call(updateInvolvedReplay, payload);
      if (callback) callback(response);
    },
    *getInvolvedReplayDetail({ payload, callback }, { call, put }) {
      const response = yield call(getInvolvedReplayDetail, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { details: response.data },
      });
    },
    *deleteInvolvedReplay({ payload, callback }, { call, put }) {
      const response = yield call(deleteInvolvedReplay, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getOrganizationByUser({ payload, callback }, { call, put }) {
      const response = yield call(getOrganizationByUser, payload);
      if (!response) return;
      if (callback) callback(response);
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
