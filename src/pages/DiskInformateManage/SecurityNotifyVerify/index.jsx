import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input, Divider, Modal } from "antd";
import { connect } from 'dryad';
import { useEffect, useState } from "react";
import moment from 'moment';
import StandardTable from "@/components/StandardTable";
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal'
import EditModal from './EditModal'
import { useAuth } from 'ponshine';
import { Licensee, useLicensee } from 'ponshine';

const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
    pageSize: 10,
    pageNum: 1,
};
const formItemLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 15,
    },
};

const SecurityNotifyVerify = (props) => {
    const { dispatch, form: { getFieldDecorator, validateFields, resetFields }, securityNotifyVerify: { tableData, details, localNetworkList }, loading } = props;
    const [params, setParams] = useState(defaultPagination);
    const [importVisible, setImportVisible] = useState(false);
    const [editVisible, setEditVisible] = useState(false);
    const [isDetail, setIsDetail] = useState(false);
    const { authState } = useAuth() || {};
    const { user: { role = {} } } = authState || {};
    const isHightAuth = role.id == 1 || role.id == 2 || role.id == 5
    const isEditAuth = role.id == 1 || role.id == 2 || role.id == 5

    const columns = [
        {
            title: "任务ID",
            dataIndex: "taskId",
            align: "center",
            width: 100,
        },
        {
            title: "通报时间",
            dataIndex: "notificationDate",
            width: 180,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "通报号码",
            dataIndex: "notificationPhoneNum",
            align: "center",
            width: 130,
            render: (text) => text || '--'
        },
        {
            title: "通报来源",
            dataIndex: "notificationOrigin",
            align: "center",
            width: 130,
            render: (text) => text || '--'
        },
        {
            title: "备注",
            dataIndex: "remark",
            width: 160,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "复通方式",
            dataIndex: "reopenType",
            width: 130,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "本地网",
            dataIndex: "userInformation.localNetwork",
            width: 140,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "导入时间",
            dataIndex: "importTime",
            width: 180,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "是否已复盘",
            dataIndex: "ifReplay",
            width: 120,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "操作",
            width: 160,
            align: "center",
            fixed: 'right',
            render: (record) => {
                return (
                    <>
                        {/* {isEditAuth && <> */}
                        <Licensee license='securityNotifyVerify_updatePoliceReplayDetailById'>

                            <a onClick={(e) => editInfo(e, record)}>编辑</a>
                            <Divider type="vertical" />
                        </Licensee>
                        {/* </>} */}
                        {/* {isHightAuth && */}
                        <Licensee license='securityNotifyVerify_deletePoliceReplayById'>

                            <a onClick={(e) => delInfo(e, record.id)}>删除</a>
                        </Licensee>
                        {/* } */}
                    </>
                )
            }
        },
    ];

    const editInfo = (e, record) => {
        e.stopPropagation()
        e.preventDefault()
        setEditVisible(true)
        getPoliceReplayDetail(record.id)
    }

    const hideEdit = () => {
        setEditVisible(false)
        setIsDetail(false)
    }
    const handleEditOk = (values, callback) => {
        dispatch({
            type: 'securityNotifyVerify/updatePoliceReplay',
            payload: values,
            callback: (res) => {
                if (res.code == 200) {
                    message.success(res.message || '修改成功');
                    setParams({ ...params, ...defaultPagination })
                    callback()
                    hideEdit()
                } else {
                    message.error(res.message);
                }
            },
        });
    }

    const getPoliceReplayDetail = (id) => {
        dispatch({
            type: 'securityNotifyVerify/getPoliceReplayDetail',
            payload: { id }
        })
    }

    const delInfo = (e, id) => {
        e.stopPropagation()
        e.preventDefault()
        Modal.confirm({
            title: '复盘任务删除',
            content: '是否确认删除该条复盘任务?',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                deleteFun(id)
            },
        });
    }

    const deleteFun = (id) => {
        dispatch({
            type: 'securityNotifyVerify/deletePoliceReplayById',
            payload: { id },
            callback: (res) => {
                if (res.code == 200) {
                    message.success(res.message || '删除成功');
                    setParams({ ...params, ...defaultPagination })
                } else {
                    message.error(res.message);
                }
            },
        });
    };


    const handleExport = () => {
        const newParams = {
            ...params,
            pageNum: 1,
            pageSize: 10,
        }
        exportFile({
            urlAPi: '/api/hn/fraudPoliceNotificationReplay/exportPoliceReplay',
            decode: true,
            params: newParams,
            method: 'POST',
        });
    };

    const handleImport = () => {
        setImportVisible(true)
    }

    const hideImport = () => {
        setImportVisible(false)
    }

    const [confirmLoading, setConfirmLoading] = useState(false);


    const onImport = (values, callback) => {
        setConfirmLoading(true);
        dispatch({
            type: "securityNotifyVerify/batchImportPoliceReplayInfo",
            payload: values,
            callback: (res) => {
                setConfirmLoading(false);
                if (res.code == 200) {
                    message.success(res.message || '导入成功');
                    setParams({ ...params, ...defaultPagination })
                    hideImport()
                } else {
                    if (res.code == 401) {
                        callback()
                    } else {
                        message.error(res.message);
                    }
                }
            },
        });
    }

    const getPagePoliceReplay = () => {
        dispatch({
            type: "securityNotifyVerify/getPagePoliceReplay",
            payload: params,
        });
    }

    //查询
    const handleSearch = () => {
        validateFields((err, { months, ...rest }) => {
            if (err) {
                return;
            }
            setParams({
                ...params,
                ...defaultPagination,
                notificationDateStart: months && months.length > 0 ? moment(months[0]).format('YYYYMMDD') : '',
                notificationDateEnd: months && months.length > 0 ? moment(months[1]).format('YYYYMMDD') : '',
                ...rest,
            });
        });
    };

    //表格切换
    const handlePaginationTable = (pagination) => {
        setParams({ ...params, pageNum: pagination.current, pageSize: pagination.pageSize });
    };

    const handleInfo = (record) => {
        setIsDetail(true)
        setEditVisible(true)
        getPoliceReplayDetail(record.id)
    }
    const handleReset = () => {
        resetFields();
        setParams({ ...defaultPagination })
    };

    const getOrganizationByUser = () => {
        dispatch({
            type: 'securityNotifyVerify/getOrganizationByUser',
        })
    }

    useEffect(() => {
        getPagePoliceReplay();
    }, [params]);

    useEffect(() => {
        getOrganizationByUser()
    }, [])

    const showInfo = useLicensee('securityNotifyVerify_getPoliceReplayDetailById')

    return (
        <Card bordered={false}>
            <Form {...formItemLayout}>
                <Row>
                    <Licensee license='securityNotifyVerify_pagePoliceReplay'>

                        <Col span={8}>
                            <Form.Item label="任务ID">
                                {getFieldDecorator("taskId")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="通报号码">
                                {getFieldDecorator("notificationPhoneNum")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="是否已复盘">
                                {getFieldDecorator("ifReplay")(
                                    <Select placeholder="请选择" allowClear>
                                        <Option value='是'>是</Option>
                                        <Option value='否'>否</Option>
                                    </Select>
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="通报时间">
                                {getFieldDecorator("months")(
                                    <RangePicker />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="本地网">
                                {getFieldDecorator("localNetwork")(
                                    <Select placeholder="请选择" allowClear>
                                        {
                                            localNetworkList?.map((item => {
                                                return (
                                                    <Option value={item.name} key={item.id}>{item.name}</Option>
                                                )
                                            }))
                                        }
                                    </Select>
                                )}
                            </Form.Item>
                        </Col>
                    </Licensee>
                    <Col span={8} style={{ marginTop: 5 }}>
                        <Licensee license='securityNotifyVerify_pagePoliceReplay'>

                            <Button
                                type="primary"
                                onClick={handleSearch}
                            >
                                查询
                            </Button>
                            <Button onClick={handleReset} style={{ marginLeft: 10 }}>重置</Button>
                        </Licensee>
                        <Licensee license='securityNotifyVerify_exportPoliceReplay'>
                            <Button
                                type="primary"
                                onClick={handleExport}
                                style={{ margin: '0px 10px' }}
                            >
                                导出
                            </Button>
                        </Licensee>
                        {/* {isHightAuth &&  */}
                        <Licensee license='securityNotifyVerify_batchImportPoliceReplayInfo'>
                            <Button
                                type="primary"
                                onClick={handleImport}
                            >
                                通报信息导入
                            </Button>
                        </Licensee>
                        {/* } */}
                    </Col>
                </Row>
            </Form>
            <StandardTable
                columns={columns}
                loading={loading}
                data={{
                    list: tableData?.items || [],
                    pagination: {
                        current: params.pageNum,
                        pageSize: params.pageSize,
                        total: tableData?.totalNum || 0
                    }
                }}
                onChange={handlePaginationTable}
                rowKey="id"
                showSelectCount={false}
                rowSelectionProps={false}
                scroll={{ x: true }}
                onRow={record => {
                    return {
                        onDoubleClick: () => showInfo && handleInfo(record)
                    }
                }}
            />
            {
                importVisible && <BatchImportModal
                    title='批量导入'
                    visible={importVisible}
                    onImport={onImport}
                    onClose={hideImport}
                    loading={confirmLoading}
                    downTemplateUrl={`template/getTemplate?templateCode=policeReplay`}
                    errorExportUrl='/api/hn/fraudPoliceNotificationReplay/downloadErrorExcel'
                />
            }
            {
                editVisible && <EditModal
                    visible={editVisible}
                    onClose={hideEdit}
                    handleEditOk={handleEditOk}
                    isDetail={isDetail}
                    details={details}
                />
            }
        </Card>
    );
}

export default connect(({ securityNotifyVerify, loading }) => ({
    securityNotifyVerify,
    loading: loading.effects["securityNotifyVerify/getPhoneReportReplay"],
}))(Form.create()(SecurityNotifyVerify));
