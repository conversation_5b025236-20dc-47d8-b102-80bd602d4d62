import React, { useState } from 'react';
import { Modal, Form, Input, Select, DatePicker, Row, Col } from 'antd';
import moment from 'moment';
import styles from './index.less'
import { useAuth } from 'ponshine';
import UserInfoForm from '../components/UserInfoForm'

const { Option } = Select;

const EditModal = (props) => {
    const { visible, onClose, handleEditOk, details, form, form: { getFieldDecorator, validateFields, resetFields }, loading, isDetail } = props
    const { authState } = useAuth() || {};
    const { user: { role = {} } } = authState || {};
    const noEdit = role.id == 2
    const handleOk = () => {
        validateFields((err, values) => {
            if (err) {
                return;
            }
            const params = {
                ...values,
                id:details.id,
                notificationDate: values.notificationDate ? moment(values.notificationDate).format('YYYYMMDD') : '',
                userInformation: {
                    ...values.userInformation,
                    internetAccessTime: values.userInformation.internetAccessTime ? moment(values.userInformation.internetAccessTime).format('YYYYMMDDHHmmss') : '',
                    resumeTime: values.userInformation.resumeTime ? moment(values.userInformation.resumeTime).format('YYYYMMDDHHmmss') : '',
                    throwOrderTime: values.userInformation.throwOrderTime ? moment(values.userInformation.throwOrderTime).format('YYYYMMDDHHmmss') : '',
                    inputOrderTime: values.userInformation.inputOrderTime ? moment(values.userInformation.inputOrderTime).format('YYYYMMDDHHmmss') : '',
                }
            }
            handleEditOk(params, () => {
                resetFields()
            })
        });
    }

    const onCancle = () => {
        resetFields()
        onClose()
    }

    return (
        <Modal
            title={isDetail ? '查看复盘详情' : '复盘编辑'}
            visible={visible}
            onCancel={onCancle}
            onOk={isDetail ? onCancle : handleOk}
            // confirmLoading={loading}
            width={1000}
            style={{ top: 40, left: 50 }}
            bodyStyle={{ height: 500, overflow: 'auto' }}
        >
            <Form className={styles.myStylesForm}>
                <Row gutter={[24]}>
                    <h3 className={styles.title}>通报信息</h3>
                    <Col span={12}>
                        <Form.Item label="通报时间">
                            {getFieldDecorator("notificationDate", {
                                initialValue: details.notificationDate ? moment(details.notificationDate,'YYYY-MM-DD') : '',
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择通报时间'
                                    }
                                ]
                            })(
                                <DatePicker
                                    style={{ width: '100%' }}
                                    disabled={isDetail || noEdit}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="通报号码">
                            {getFieldDecorator("notificationPhoneNum", {
                                initialValue: details.notificationPhoneNum,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入通报号码'
                                    },
                                    // {
                                    //     pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                                    //     message: '您输入的号码格式有误！',
                                    // },
                                ]
                            })(
                                <Input placeholder="请输入通报号码" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="通报来源">
                            {getFieldDecorator("notificationOrigin", {
                                initialValue: details.notificationOrigin,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入通报来源'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="归属地">
                            {getFieldDecorator("numberLocation", {
                                initialValue: details.numberLocation,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label="备注">
                            {getFieldDecorator("remark", {
                                initialValue: details.remark,
                            })(
                                <Input.TextArea
                                    rows={3}
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    allowClear
                                    disabled={isDetail || noEdit}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="复通方式">
                            {getFieldDecorator("reopenType", {
                                initialValue: details.reopenType,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                <Row gutter={[24]}>
                    <h3 className={styles.title}>复盘核查</h3>
                    <Col span={12}>
                        <Form.Item label="话务是否异常">
                            {getFieldDecorator("ifTelephoneException", {
                                initialValue: details.ifTelephoneException,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择话务是否异常'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                    <Option value='疑似'>疑似</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否呼转">
                            {getFieldDecorator("ifCallForwarding", {
                                initialValue: details.ifCallForwarding,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="互联网流量是否异常" className={styles.widthForm}>
                            {getFieldDecorator("ifFlowException", {
                                initialValue: details.ifFlowException,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否诈骗">
                            {getFieldDecorator("ifFraud", {
                                initialValue: details.ifFraud,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否诈骗'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                    <Option value='疑似'>疑似</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否省内大数据发现" className={styles.widthForm}>
                            {getFieldDecorator("ifFoundByProvincialBigData", {
                                initialValue: details.ifFoundByProvincialBigData,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否省内大数据发现'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否集团大数据发现" className={styles.widthForm}>
                            {getFieldDecorator("ifFoundByGroupBigData", {
                                initialValue: details.ifFoundByGroupBigData,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否集团大数据发现'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label="问题">
                            {getFieldDecorator("issue", {
                                initialValue: details.issue,
                            })(
                                <Input.TextArea
                                    rows={3}
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    allowClear
                                    disabled={isDetail}
                                />
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                <UserInfoForm form={form} isDetail={isDetail} details={details} />
            </Form>
        </Modal>
    )
}

export default Form.create()(EditModal)