import { Modal, Form, Input, Select, DatePicker, Row, Col, message } from 'antd';
import React, { useEffect, useState } from 'react';
import moment from 'moment';

import request from '@/utils/request';
import { editData } from './services';

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const FormItem = Form.Item;
export default Form.create()((props) => {
  const {
    visible,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
    currentRow = {},
    cancel,
    cellList,
    reload,
  } = props;

  const handleOk = () => {
    validateFields(async (err, fieldsValue) => {
      if (!err) {
        const response = await editData({ ...fieldsValue, id: currentRow?.id });
        if (response?.code === 200) {
          message.success(response.message);
          reload();
        } else {
          message.error(response.message);
        }
      }
    });
  };

  return (
    <Modal
      title={'编辑窝点信息'}
      width={700}
      visible={visible}
      onOk={handleOk}
      okText="确认"
      afterClose={() => {
        resetFields();
      }}
      onCancel={cancel}
    >
      <Form {...formItemLayout}>
        <Row>
          <Col span={12}>
            <Form.Item label="日期">
              {getFieldDecorator('pday', {
                initialValue: currentRow?.pday,
              })(<Input disabled />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <FormItem label="号码">
              {getFieldDecorator('callingNbr', {
                initialValue: currentRow?.callingNbr,
              })(<Input placeholder="请输入" disabled />)}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label="活跃时间">
              {getFieldDecorator('endTime', {
                initialValue: currentRow?.endTime,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="号码归属">
              {getFieldDecorator('landName', {
                initialValue: currentRow?.landName,
              })(
                <Select placeholder="请选择">
                  {cellList?.map((ele, index) => (
                    <Select.Option value={ele.name} key={ele.id}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="窝点地">
              {getFieldDecorator('regionName', {
                initialValue: currentRow?.regionName,
              })(
                <Select placeholder="请选择">
                  {cellList?.map((ele, index) => (
                    <Select.Option value={ele.name} key={ele.id}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="县区">
              {getFieldDecorator('cityName', {
                initialValue: currentRow?.cityName,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="IMEI">
              {getFieldDecorator('regImei', {
                initialValue: currentRow?.regImei,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="基站码">
              {getFieldDecorator('cellId', {
                initialValue: currentRow?.cellId,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={24}>
            <FormItem label="基站名" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
              {getFieldDecorator('relatedEnbUserlabel', {
                initialValue: currentRow?.relatedEnbUserlabel,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="窝点分类">
              {getFieldDecorator('wodianType', {
                initialValue: currentRow?.wodianType,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="案件类别">
              {getFieldDecorator('anjianType', {
                initialValue: currentRow?.anjianType,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="关联号码">
              {getFieldDecorator('relatedNumber', {
                initialValue: currentRow?.relatedNumber,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="入网时间">
              {getFieldDecorator('createDate', {
                initialValue: currentRow?.createDate,
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="备注">
              {getFieldDecorator('remark', {
                rules: [
                  {
                    max: 100,
                    message: '最多输入100个字符',
                  },
                ],
                initialValue: currentRow?.remark,
              })(<Input placeholder="请输入" />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="是否窝点">
              {getFieldDecorator('isWodian', {
                initialValue: currentRow?.isWodian,
              })(
                <Select placeholder="请选择" allowClear={false}>
                  {['是', '否']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
});
