import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import AddOrEdit from './AddOrEdit';
import BatchImportModal from '@/components/BatchImport';
import {
  Card,
  Button,
  message,
  Modal,
  Form,
  Tooltip,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Icon,
} from 'antd';
import { Licensee, useLicensee } from 'ponshine';
import { exportFile } from '@/utils/utils';
import moment from 'moment';
import { selectCellList, selectPage, deleteData, batchImport } from './services';

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const { RangePicker } = DatePicker;

const DensInfoManage = ({ form, form: { getFieldDecorator, resetFields, getFieldsValue } }) => {
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {},
  });
  const [cellList, setCellList] = useState([]);
  const [tableLoading, setTableLoading] = useState(false);
  const initialDate = [moment(), moment()];
  const [selectedRows, setSelectedRows] = useState([]);
  const [visible, setVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [currentRow, setCurrentRow] = useState({});
  const [importVisible, setImportVisible] = useState(false);
  const otherInitParams = {
    beginTime: initialDate?.[0]?.format('YYYY-MM-DD'),
    endTime: initialDate?.[1]?.format('YYYY-MM-DD'),
    isWodian: '是',
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      align: 'center',
      width: 80,
      ellipsis: true,
    },
    {
      title: '日期',
      dataIndex: 'pday',
      align: 'center',
      width: 100,
      ellipsis: true,
    },

    {
      title: '号码',
      dataIndex: 'callingNbr',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '活跃时间',
      dataIndex: 'endTime',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '号码归属',
      dataIndex: 'landName',
      align: 'center',
      width: 100,
      ellipsis: true,
    },

    {
      title: '窝点地',
      dataIndex: 'regionName',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '县区',
      dataIndex: 'cityName',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: 'IMEI',
      dataIndex: 'regImei',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '基站码',
      dataIndex: 'cellId',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '基站名',
      dataIndex: 'relatedEnbUserlabel',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '基站经度',
      dataIndex: 'longitude',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '基站纬度',
      dataIndex: 'latitude',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '窝点分类',
      dataIndex: 'wodianType',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '案件类别',
      dataIndex: 'anjianType',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '关联号码',
      dataIndex: 'relatedNumber',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '入网时间',
      dataIndex: 'createDate',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '是否窝点',
      dataIndex: 'isWodian',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作人',
      dataIndex: 'operatorUserName',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作时间',
      dataIndex: 'operatorTime',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '是否派单',
      dataIndex: 'ifIssue',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '派单工号',
      dataIndex: 'issueOrderId',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '派单时间',
      dataIndex: 'issueTime',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'opt',
      fixed: 'right',
      width: 80,
      render: (v, r) => {
        return (
          <Fragment>
            <Licensee license="hideout_updateHideout_buttol">
              <Tooltip title="编辑">
                <a>
                  <Icon type="edit" style={{ marginRight: 8 }} onClick={() => handleEdit(r)}></Icon>
                </a>
              </Tooltip>
            </Licensee>
            <Licensee license="hideout_delete_button">
              <Tooltip title="删除">
                <a>
                  <Icon type="delete" onClick={() => handleDelete(r)}></Icon>
                </a>
              </Tooltip>
            </Licensee>
          </Fragment>
        );
      },
    },
  ];

  const handleSelectRows = (selectedRows) => {
    setSelectedRows(selectedRows);
  };

  const handleImport = () => {
    setImportVisible(true);
  };

  const handleEdit = (r) => {
    setCurrentRow(r);
    setVisible(true);
  };

  const handleDelete = (r) => {
    if (!r && !selectedRows.length) {
      return message.info('至少选择一条窝点数据进行删除');
    }
    Modal.confirm({
      title: '窝点信息删除',
      content: '是否确认删除该窝点信息？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const response = await deleteData({
          ids: selectedRows?.map((ele) => ele.id)?.join(',') || r.id,
        });
        if (response.code === 200) {
          message.success(response.message);
          onReset();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const getInitialValue = (data) => {
    if (data?.length === 1) {
      return data?.[0]?.name;
    } else {
      return undefined;
    }
  };

  // 获取窝点下拉
  const getCellList = async () => {
    const response = await selectCellList();
    if (response.code === 200) {
      setCellList(response?.data || []);
      getListDatas({
        regionName: getInitialValue(response?.data || []),
        ...otherInitParams,
      });
    } else {
      message.error(response.message);
    }
  };

  // 获取表格数据
  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setTableLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setTableLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setTableData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  // 搜索
  const handleSearch = () => {
    const values = getFieldsValue();
    const { date } = values;
    getListDatas({
      ...values,
      beginTime: date?.[0]?.format('YYYY-MM-DD'),
      endTime: date?.[1]?.format('YYYY-MM-DD'),
      date: undefined,
    });
  };

  // 分頁
  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 重置
  const onReset = () => {
    resetFields();
    setSelectedRows([]);
    getListDatas({
      regionName: getInitialValue(cellList || []),
      ...otherInitParams,
    });
  };

  // 导出
  const handleExport = () => {
    exportFile({
      method: 'POST',
      urlAPi: '/api/hn/hideout/exportQueryHideOut',
      params: { ...searchParams },
      decode: true,
    });
  };

  useEffect(() => {
    getCellList();
  }, []);

  return (
    <Card>
      <Form {...formItemLayout}>
        <Row gutter={[24]}>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum', {
                getValueFromEvent: (e) => e?.target?.value?.trim(),
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="日期">
              {getFieldDecorator('date', {
                initialValue: initialDate,
              })(
                <RangePicker
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="窝点地">
              {getFieldDecorator('regionName', {
                initialValue: getInitialValue(cellList),
              })(
                <Select placeholder="请选择" allowClear={!Boolean(getInitialValue(cellList))}>
                  {cellList?.map((ele, index) => (
                    <Select.Option value={ele.name} key={ele.id}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="是否窝点">
              {getFieldDecorator('isWodian', {
                initialValue: '是',
              })(
                <Select placeholder="请选择" allowClear>
                  {['是', '否']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <div style={{ display: 'flex', justifyContent: 'end', marginBottom: 16 }}>
        {/* <Licensee license="whiteListInfoQuery_getWhiteInformation"> */}
        <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
          查询
        </Button>
        <Button style={{ marginRight: 16 }} onClick={onReset}>
          重置
        </Button>
        {/* </Licensee> */}
        <Button
          type="primary"
          style={{ marginRight: 16 }}
          onClick={handleExport}
          disabled={!tableData?.list?.length}
        >
          导出
        </Button>
        <Licensee license="hideout_importHideoutToBigdata_button">
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleImport}>
            待查询窝点信息导入
          </Button>
        </Licensee>

        <Licensee license="hideout_delete_button">
          <Button type="danger" onClick={() => handleDelete()} disabled={!selectedRows?.length}>
            删除
          </Button>
        </Licensee>
      </div>
      <StandardTable
        // showSelectCount={false}
        detailColumns={columns}
        tools={true}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        onSelectRow={handleSelectRows}
        selectedRows={selectedRows}
        loading={tableLoading}
        scroll={{ x: 1200 }}
      />
      <AddOrEdit
        visible={visible}
        cancel={() => {
          setVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        currentRow={currentRow}
        cellList={cellList}
        reload={() => {
          setVisible(false);
          currentRow?.id && setCurrentRow({});
          onReset();
        }}
      />
      {/* 批量导入 */}
      <BatchImportModal
        title="待查询窝点信息导入"
        tipsText="*每个文件不超过1000条号码"
        visible={importVisible}
        onClose={() => {
          setImportVisible(false);
        }}
        errorExportUrl={'/api/hn/hideout/download'}
        downTemplateUrl={`/api/template/getTemplate?templateCode=hideoutImport`}
        importRequest={batchImport}
        reload={() => {
          // onReset();
        }}
        closeModal={() => {
          setImportVisible(false);
        }}
      />
    </Card>
  );
};

export default Form.create()(DensInfoManage);
