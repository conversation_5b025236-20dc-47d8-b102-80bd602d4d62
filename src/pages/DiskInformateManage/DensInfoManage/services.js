import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/hideout/pageQueryHideOut`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取窝点地
export async function selectCellList(params) {
  return request(`/api/hn/systemConfig/getCityByUser`, {
    method: 'GET',
  });
}

// 删除
export async function deleteData(params) {
  return request(`/api/hn/hideout/delete`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 导入
export async function batchImport(params) {
  return request(`/api/hn/hideout/importHideoutToBigdata`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 编辑
export async function editData(params) {
  return request(`/api/hn/hideout/updateHideout`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
