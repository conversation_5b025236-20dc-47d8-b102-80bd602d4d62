import React, { useReducer, useEffect, useRef, useState } from 'react';
import { Row, Col, Form, Button, Select, message, Card, Input, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import moment from 'moment';
import { exportFile } from '@/utils/utils';
import request from '@/utils/request';
const defaultPagination = {
  pageSize: 10,
  current: 1,
};
const FormItem = Form.Item;
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const { RangePicker } = DatePicker;
const Index = (props) => {
  const { form, location } = props;
  const [state, setState] = useReducer(reducer, {});
  const {
    dataSource = [],
    total = 0,
    pagination = { ...defaultPagination },
    loading = false,
    searchValues = {},
    startTime = undefined,
  } = state;
  const localNetworkRef = useRef();

  const initialDate = [
    moment(moment(new Date()).subtract(1, 'days'), 'YYYY-MM-DD'),
    moment(moment(new Date()).subtract(1, 'days'), 'YYYY-MM-DD'),
  ];

  // useEffect(() => {
  //  initTable()

  // }, [0]);

  const columns = [
    {
      title: '日期',
      key: 'dateTime',
      dataIndex: 'dateTime',
      width: 140,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '本地网名称',
      key: 'localNetwork',
      dataIndex: 'localNetwork',
      render: (data) => data,
    },
    {
      title: '号码',
      key: 'phoneNumber',
      dataIndex: 'phoneNumber',
      render: (data) => data,
    },
    {
      title: '激活日期',
      key: 'activateDateTime',
      dataIndex: 'activateDateTime',
      width: 140,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '收货省份',
      key: 'receiveProvince',
      dataIndex: 'receiveProvince',
      render: (data) => data,
    },
    {
      title: '收货市',
      key: 'receiveCity',
      dataIndex: 'receiveCity',
      render: (data) => data,
    },
    {
      title: '话单漫游省份',
      key: 'roamProvince',
      dataIndex: 'roamProvince',
      width: 50,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '话单漫游市',
      key: 'roamCity',
      dataIndex: 'roamCity',
      width: 50,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '信令漫游省份',
      key: 'tokenRoamProvince',
      dataIndex: 'tokenRoamProvince',
      width: 50,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '信令漫游市',
      key: 'tokenRoamCity',
      dataIndex: 'tokenRoamCity',
      width: 50,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '近7天通话次数',
      key: 'callCount',
      dataIndex: 'callCount',
      width: 60,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '近7天主叫次数',
      key: 'activeCount',
      dataIndex: 'activeCount',
      width: 60,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '本月累计流量',
      key: 'monthCumulativeFlow',
      dataIndex: 'monthCumulativeFlow',
      width: 60,
      forceShow: true,
      render: (data) => data,
    },
    {
      title: '上月累计流量',
      key: 'cumulativeFlow',
      dataIndex: 'cumulativeFlow',
      width: 60,
      forceShow: true,
      render: (data) => data,
    },

    {
      title: '类型',
      key: 'style',
      dataIndex: 'style',
      render: (data) => data,
    },
  ];

  const initTable = async (params = {}) => {
    const { pagination = {}, ...props } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };

    setState({ pagination: newPagination, loading: true, searchValues: props });
    request({
      url: '/api/hn/roam_exception/roam_list',
      method: 'POST',
      requestType: 'json',
      data: {
        pageNum: newPagination.current,
        pageSize: newPagination.pageSize,
        startDateTime: props?.startDateTime || initialDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
        endDateTime: props?.endDateTime || initialDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
        ...props,
      },
    }).then((res) => {
      if (res && res.code === 200 && res.data) {
        setState({
          dataSource: res.data.items || [],
          total: res.data.totalNum || 0,
          loading: false,
        });
      } else {
        message.error(res.message);
        setState({
          dataSource: [],
          total: 0,
          loading: false,
        });
      }
    });
  };

  const handlePaginationTable = (pagination) => {
    initTable({ pagination, ...searchValues });
  };

  const handleSearch = () => {
    let values = form.getFieldsValue();
    const { time } = values;
    if (time && time.length) {
      values.startDateTime = moment(time[0]).format('YYYY-MM-DD') + ' ' + '00:00:00';
      values.endDateTime = moment(time[1]).format('YYYY-MM-DD') + ' ' + '23:59:59';
    }
    delete values.time;
    initTable({ ...values });
  };

  const handleReset = () => {
    form.resetFields();

    setState({ searchValues: { localNetwork: localNetworkRef.current.getInitialValue() } });
    initTable({ localNetwork: localNetworkRef.current.getInitialValue() });
  };
  const disabledDate = (current) => {
    if (startTime) {
      return (
        current > moment(startTime).add(3, 'months') ||
        current < moment(startTime).subtract(3, 'months') ||
        current > moment().subtract(0, 'day')
      );
    } else {
      return current > moment().subtract(0, 'day');
    }
  };
  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    setExportLoading(true);
    const newParams = {
      ...searchValues,
      pageNum: 1,
      pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/roam_exception/roam_export',
      decode: true,
      params: newParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };
  return (
    <Card>
      <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="号码">
              {form.getFieldDecorator('phoneNumber')(<Input allowClear placeholder="请输入" />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <FormItem label="起止日期">
              {form.getFieldDecorator('time', {
                initialValue: initialDate,
              })(
                <RangePicker
                  style={{ width: '100%' }}
                  onCalendarChange={(dates) => {
                    if (dates[1]) {
                      setState({ startTime: undefined });
                    } else {
                      setState({ startTime: dates[0] });
                    }
                  }}
                  disabledDate={disabledDate}
                  allowClear
                ></RangePicker>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem form={form} getListDatas={initTable} cref={localNetworkRef} />
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'right', marginBottom: 10 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={() => handleSearch()}>
                查询
              </Button>
              <Button type="default" onClick={() => handleReset()} style={{ marginRight: 10 }}>
                重置
              </Button>
              <Button type="primary" onClick={() => handleExport()} loading={exportLoading}>
                导出
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <StandardTable
        rowKey="id"
        loading={loading}
        onChange={handlePaginationTable}
        data={{
          list: dataSource,
          pagination: { ...pagination, total },
        }}
        columns={columns}
        isNeedAutoWidth={true}
        scroll={{ x: 'max-content' }}
        showSelectCount={false}
        rowSelectionProps={false}
      />
    </Card>
  );
};

export default Form.create()(Index);
