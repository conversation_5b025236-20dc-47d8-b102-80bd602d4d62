import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import AddOrEdit from './AddOrEdit';
import {
  Card,
  Button,
  message,
  Modal,
  Form,
  Tooltip,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Icon,
} from 'antd';

import { selectPage} from './services';


const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const { RangePicker } = DatePicker;

const DensInfoManage = ({ form, form: { getFieldDecorator, resetFields, getFieldsValue } }) => {
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {},
  });
  const [tableLoading, setTableLoading] = useState(false);
  const initialDate = [];
  const [visible, setVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [currentRow, setCurrentRow] = useState({});
  const [currentType, setCurrentType] = useState('');
  const otherInitParams = {
    startDate: initialDate?.[0]?.format('YYYY-MM-DD'),
    endDate: initialDate?.[1]?.format('YYYY-MM-DD'),
  };

  const columns = [

    {
      title: '模型类型',
      dataIndex: 'modelType',
      align: 'center',
      width: 100,
      ellipsis: true,
    },

    {
      title: '模型编码',
      dataIndex: 'modelCode',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '模型名称',
      dataIndex: 'modelName',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '模型状态',
      dataIndex: 'modelStatus',
      align: 'center',
      width: 100,
      ellipsis: true,
    },

    {
      title: '关停标签',
      dataIndex: 'shutdownTag',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '模型说明',
      dataIndex: 'modelRemark',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '添加人',
      dataIndex: 'operatorName',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '提交时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'gmtUpdate',
      align: 'center',
      width: 200,
      ellipsis: true,
    },

    {
      title: '操作',
      dataIndex: 'opt',
      fixed: 'right',
      width: 80,
      render: (v, r) => {
        return (
          <Fragment>
            {/* <Licensee license="hideout_updateHideout_buttol"> */}
            <Tooltip title="编辑">
              <a>
                <Icon type="edit" style={{ marginRight: 8 }} onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(r, 'edit')
                }}></Icon>
              </a>
            </Tooltip>
            {/* </Licensee> */}
            {/* <Licensee license="hideout_updateHideout_buttol"> */}
            <Tooltip title="查看">
              <a>
                <Icon type="eye" style={{ marginRight: 8 }} onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(r, 'details')
                }}></Icon>
              </a>
            </Tooltip>
            {/* </Licensee> */}

          </Fragment>
        );
      },
    },
  ];
  useEffect(() => {
    getListDatas()
  }, [])
  

  const handleEdit = (r, type) => {
    setCurrentRow(r);
    setVisible(true);
    setCurrentType(type)
  };



  // 获取表格数据
  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setTableLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setTableLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setTableData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  // 搜索
  const handleSearch = () => {
    const values = getFieldsValue();
    const { date } = values;
    getListDatas({
      ...values,
      startDate: date?.[0]?date?.[0]?.format('YYYY-MM-DD')+' 00:00:00':undefined,
      endDate: date?.[1]?date?.[1]?.format('YYYY-MM-DD')+' 23:59:59':undefined,
      date: undefined,
    });
  };

  // 分頁
  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 重置
  const onReset = () => {
    resetFields();
    getListDatas({
      ...otherInitParams,
    });
  };
  const handleAdd = () => {
    setCurrentType('add')
    setVisible(true)
    setCurrentRow({})
  }
  const tableDataMock = {
    list: [
      {
        key: 1,
        id: 1
      }
    ]
  }
  return (
    <Card>
      <Form {...formItemLayout}>
        <Row gutter={[24]}>
          <Col span={6}>
            <Form.Item label="模型类型">
              {getFieldDecorator('modelType', {
                getValueFromEvent: (e) => e?.target?.value?.trim(),
                rules: [
                  {
                    max: 30,
                    message: '最多30个字符'
                  }
                ]
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="模型编码">
              {getFieldDecorator('modelCode', {
                getValueFromEvent: (e) => e?.target?.value?.trim(),
                rules: [
                  {
                    max: 30,
                    message: '最多30个字符'
                  }
                ]
              })(
                <Input placeholder="请输入" allowClear />

              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="模型状态">
              {getFieldDecorator('modelStatus', {

              })(
                <Select placeholder="请选择" allowClear>
                  {
                    ['上线', '下线', '试运行'].map((v, i) => {
                      return <Select.Option key={i} value={v}>{v}</Select.Option>
                    })
                  }
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="模型名称">
              {getFieldDecorator('modelName', {
                getValueFromEvent: (e) => e?.target?.value?.trim(),
                rules: [
                  {
                    max: 30,
                    message: '最多30个字符'
                  }
                ]
              })(
                <Input placeholder="请输入" allowClear />

              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="关停标签">
              {getFieldDecorator('shutdownTag', {
                getValueFromEvent: (e) => e?.target?.value?.trim(),
                rules: [
                  {
                    max: 30,
                    message: '最多30个字符'
                  }
                ]
              })(
                <Input placeholder="请输入" allowClear />

              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="更新日期">
              {getFieldDecorator('date', {
                initialValue: initialDate,
              })(
                <RangePicker
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </Form.Item>
          </Col>
          <Col style={{ display: 'flex', justifyContent: 'end' }} span={12} >
            {/* <Licensee license="whiteListInfoQuery_getWhiteInformation"> */}
            <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
              查询
            </Button>
            <Button style={{ marginRight: 16 }} onClick={onReset}>
              重置
            </Button>
            {/* </Licensee> */}

            {/* <Licensee license="hideout_importHideoutToBigdata_button"> */}
            <Button type="primary"  onClick={handleAdd}>
              新增
            </Button>
            {/* </Licensee> */}


          </Col>
        </Row>
      </Form>

      <StandardTable
        showSelectCount={false}
        detailColumns={columns}
        tools={true}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        rowSelectionProps={false}
        // onSelectRow={handleSelectRows}
        // selectedRows={selectedRows}
        loading={tableLoading}
        scroll={{ x: 1200 }}
      />
     {visible&& <AddOrEdit
        visible={visible}
        cancel={() => {
          setVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        currentRow={currentRow}
        reload={() => {
          setVisible(false);
          currentRow?.id && setCurrentRow({});
          onReset();
        }}
        currentType={currentType}
      />}

    </Card>
  );
};

export default Form.create()(DensInfoManage);
