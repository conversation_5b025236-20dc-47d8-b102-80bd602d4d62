import { Modal, Form, Input, Select, DatePicker, Row, Col, message,Button } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import moment from 'moment';

import request from '@/utils/request';
import TextAreaWithCounter from '@/components/TextAreaWithCounter/index';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const FormItem = Form.Item;
export default Form.create()((props) => {
  let textAreaRef=useRef(null)
  const {
    visible,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
    currentRow = {},
    cancel,
    reload,
    currentType
  } = props;

  const handleOk = () => {
    validateFields(async (err, fieldsValue) => {
      if (!err) {
        let parmas = {
          ...fieldsValue,
          modelRemark: textAreaRef?.current?.getValue(),
          id: currentRow?.id
        }
     
        let urls = {
          add: '/api/hn/bizModel/addBizModel',
          edit: '/api/hn/bizModel/updateBizModel'
        }
        request(urls[currentType], {
          method: 'POST',
          data: parmas,
          requestType:'json'
        })
          .then((response) => {
            if (response?.code === 200) {
              message.success(response.message);
              reload();
            } else {
              message.error(response.message);
            }
          })

      }
    });
  };
  let titles = {
    add: '新增业务模型管理',
    edit: "编辑业务模型管理",
    details: "业务模型管理详情"
  }
  return (
    <Modal
      title={titles[currentType]}
      width={'70%'}
      visible={visible}
      // onOk={handleOk}
      okText="确认"
      afterClose={() => {
        resetFields();
      }}
      onCancel={cancel}
      footer={
        currentType === 'details' ? null : (
          <>
            <Button onClick={cancel}>取消</Button>
            <Button type='primary' onClick={handleOk}>确定</Button>
          </>
        )
      }
    >
      <Form {...formItemLayout}>
        <Row>
          <Col span={24}>
            <Form.Item label="模型类型">
              {getFieldDecorator('modelType', {
                initialValue: currentRow?.modelType,
                rules:[
                  {
                    max: 30,
                    message: '最多输入30个字符',
                  
                  },
                  {
                    required:true,
                    message: '请输入模型类型',
                  }
                ]
              })(<Input disabled={currentType==='details'} placeholder='请输入' />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型编码">
              {getFieldDecorator('modelCode', {
                initialValue: currentRow?.modelCode,
                rules:[
                  {
                    max: 30,
                    message: '最多输入30个字符',
                  
                  },
                  {
                    required:true,
                    message: '请输入模型编码',
                  }
                ]
              })(<Input disabled={currentType==='details'} placeholder='请输入' />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型名称">
              {getFieldDecorator('modelName', {
                initialValue: currentRow?.modelName,
                rules:[
                  {
                    max: 30,
                    message: '最多输入30个字符',
                  
                  },
                  {
                    required:true,
                    message: '请输入模型名称',
                  }
                ]
              })(<Input disabled={currentType==='details'} placeholder='请输入' />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型状态">
              {getFieldDecorator('modelStatus', {
                initialValue: currentRow?.modelStatus || undefined,
                rules:[
                
                  {
                    required:true,
                    message: '请选择模型状态',
                  }
                ]
              })(
                <Select placeholder="请选择" allowClear disabled={currentType==='details'}  getPopupContainer={(node) => node.parentNode}>
                {
                  ['上线', '下线', '试运行'].map((v, i) => {
                    return <Select.Option key={i} value={v}>{v}</Select.Option>
                  })
                }
              </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="关停标签">
              {getFieldDecorator('shutdownTag', {
                initialValue: currentRow?.shutdownTag,
                rules:[
                  {
                    max: 30,
                    message: '最多输入30个字符',
                  
                  },
                  {
                    required:true,
                    message: '请输入关停标签',
                  }
                ]
              })(<Input disabled={currentType==='details'} placeholder='请输入' />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型说明">
              {getFieldDecorator('modelRemark', {
                initialValue: currentRow?.modelRemark || '',
                rules:[
                  {
                    max: 1000,
                    message: '最多输入1000个字符',
                  
                  },
                 
                ]
              })(<TextAreaWithCounter  disabled={currentType==='details'} placeholder='请输入' allowClear autoSize={{maxRows:5,minRows:5}}  maxLength={1000}  ref={textAreaRef} currentInfo={currentRow.modelRemark}/>)}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
});
