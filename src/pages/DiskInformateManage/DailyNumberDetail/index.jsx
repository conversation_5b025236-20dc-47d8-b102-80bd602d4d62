import React, { useReducer, useEffect, useRef, useState } from 'react';
import { Row, Col, Form, Button, Select, message, Card, Input, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import moment from 'moment';
import { exportFile } from '@/utils/utils';
import request from '@/utils/request';
const defaultPagination = {
  pageSize: 10,
  current: 1,
};
const FormItem = Form.Item;
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const { RangePicker } = DatePicker;
const Index = (props) => {
  const { form, location } = props;
  const [state, setState] = useReducer(reducer, {});
  const {
    dataSource = [],
    total = 0,
    pagination = { ...defaultPagination },
    loading = false,
    searchValues = {},
    startTime = undefined,
  } = state;
  const localNetworkRef = useRef();

  const initialDate = [
    moment(moment(new Date()), 'YYYY-MM-DD'),
    moment(moment(new Date()), 'YYYY-MM-DD'),
  ];

  // useEffect(() => {
  //  initTable()

  // }, [0]);

  const columns = [
    {
      title: '日期',
      key: 'dateTime',
      dataIndex: 'dateTime',
      align: 'center',
      width: 140,
      forceShow: true,
    },
    {
      title: '本地网名称',
      key: 'localNetwork',
      dataIndex: 'localNetwork',
      align: 'center',
    },
    {
      title: '号码',
      key: 'phoneNumber',
      dataIndex: 'phoneNumber',
      align: 'center',
    },
    {
      title: '入网日期',
      key: 'joinNetworkDateTime',
      dataIndex: 'joinNetworkDateTime',
      width: 140,
      forceShow: true,
    },
    {
      title: '分析时间',
      key: 'analyzeDateTime',
      dataIndex: 'analyzeDateTime',
      align: 'center',
    },
    {
      title: '停机时间',
      key: 'shutdownDateTime',
      dataIndex: 'shutdownDateTime',
      align: 'center',
    },
    {
      title: '复机时间',
      key: 'restartDateTime',
      dataIndex: 'restartDateTime',
      align: 'center',
    },
    {
      title: '输出类型',
      key: 'exportStyle',
      dataIndex: 'exportStyle',
      align: 'center',
    },
    {
      title: '主套餐',
      key: 'mainMeal',
      dataIndex: 'mainMeal',
      align: 'center',
    },
    {
      title: '套餐档次',
      key: 'mainMealLevel',
      dataIndex: 'mainMealLevel',
      align: 'center',
      // width: 50,
      // forceShow: true,
    },
    {
      title: '入网渠道',
      key: 'accessChannel',
      dataIndex: 'accessChannel',
      align: 'center',
    },
    {
      title: '店中商名称',
      key: 'shopName',
      dataIndex: 'shopName',
      align: 'center',
    },
    {
      title: '漫游地',
      key: 'roamingAddress',
      dataIndex: 'roamingAddress',
      align: 'center',
    },
  ];

  const initTable = async (params = {}) => {
    const { pagination = {}, ...props } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };

    setState({ pagination: newPagination, loading: true, searchValues: props });
    request({
      url: '/api/hn/number_detail/number_list',
      method: 'POST',
      requestType: 'json',
      data: {
        pageNum: newPagination.current,
        pageSize: newPagination.pageSize,
        startDateTime: props?.startDateTime || initialDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
        endDateTime: props?.endDateTime || initialDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
        ...props,
      },
    }).then((res) => {
      if (res && res.code === 200 && res.data) {
        setState({
          dataSource: res.data.items || [],
          total: res.data.totalNum || 0,
          loading: false,
        });
      } else {
        message.error(res.message);
        setState({
          dataSource: [],
          total: 0,
          loading: false,
        });
      }
    });
  };

  const handlePaginationTable = (pagination) => {
    initTable({ pagination, ...searchValues });
  };

  const handleSearch = () => {
    let values = form.getFieldsValue();
    const { time } = values;
    if (time && time.length) {
      values.startDateTime = moment(time[0]).format('YYYY-MM-DD') + ' ' + '00:00:00';
      values.endDateTime = moment(time[1]).format('YYYY-MM-DD') + ' ' + '23:59:59';
    }
    delete values.time;
    initTable({ ...values });
  };

  const handleReset = () => {
    form.resetFields();
    setState({ searchValues: { localNetwork: localNetworkRef.current.getInitialValue() } });
    initTable({ localNetwork: localNetworkRef.current.getInitialValue() });
  };
  const disabledDate = (current) => {
    if (startTime) {
      return (
        current > moment(startTime).add(3, 'months') ||
        current < moment(startTime).subtract(3, 'months') ||
        current > moment().subtract(0, 'day')
      );
    } else {
      return current > moment().subtract(0, 'day');
    }
  };
  const [exportLoading, setExportLoading] = useState(false);
  const handleExport = () => {
    setExportLoading(true);
    const newParams = {
      ...searchValues,
      pageNum: 1,
      pageSize: 10,
    };
    exportFile({
      urlAPi: '/api/hn/number_detail/export_number_list',
      decode: true,
      params: newParams,
      method: 'POST',
      callback: () => {
        setExportLoading(false);
      },
    });
  };
  return (
    <Card>
      <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="号码">
              {form.getFieldDecorator('phoneNumber')(<Input allowClear placeholder="请输入" />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <FormItem label="起止日期">
              {form.getFieldDecorator('time', {
                initialValue: initialDate,
              })(
                <RangePicker
                  style={{ width: '100%' }}
                  onCalendarChange={(dates) => {
                    if (dates[1]) {
                      setState({ startTime: undefined });
                    } else {
                      setState({ startTime: dates[0] });
                    }
                  }}
                  disabledDate={disabledDate}
                  allowClear
                ></RangePicker>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem form={form} getListDatas={initTable} cref={localNetworkRef} />
          </Col>
          <Col span={6}>
            <div style={{ textAlign: 'right', marginBottom: 10 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={() => handleSearch()}>
                查询
              </Button>
              <Button type="default" onClick={() => handleReset()} style={{ marginRight: 10 }}>
                重置
              </Button>
              <Button type="primary" onClick={() => handleExport()} loading={exportLoading}>
                导出
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <StandardTable
        rowKey="id"
        loading={loading}
        onChange={handlePaginationTable}
        data={{
          list: dataSource,
          pagination: { ...pagination, total },
        }}
        columns={columns}
        isNeedAutoWidth={true}
        scroll={{ x: 'max-content' }}
        showSelectCount={false}
        rowSelectionProps={false}
      />
    </Card>
  );
};

export default Form.create()(Index);
