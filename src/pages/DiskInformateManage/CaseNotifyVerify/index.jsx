import { Button, Col, Form, message, Row, Select, DatePicker, Card, Input, Divider, Modal } from "antd";
import { connect } from 'dryad';
import { useEffect, useState } from "react";
import moment from 'moment';
import StandardTable from "@/components/StandardTable";
import { exportFile } from '@/utils/utils';
import BatchImportModal from '../components/BatchImportModal'
import EditModal from './EditModal'
import { useAuth } from 'ponshine';
import { onEnterPage } from '@/utils/openTab';
import { Licensee, useLicensee } from 'ponshine';


const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultPagination = {
    pageSize: 10,
    pageNum: 1,
};
const formItemLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 15,
    },
};

const CaseNotifyVerify = (props) => {
    const { dispatch, form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
        caseNotifyVerify: { tableData, details, localNetworkList }, loading, location: { query } } = props;
    const [params, setParams] = useState(defaultPagination);
    const [importVisible, setImportVisible] = useState(false);
    const [editVisible, setEditVisible] = useState(false);
    const [isDetail, setIsDetail] = useState(false);
    const { authState } = useAuth() || {};
    const { user: { role = {} } } = authState || {};
    const isHightAuth = role.id == 1 || role.id == 5
    const isEditAuth = role.id == 1 || role.id == 2 || role.id == 5
    const [caseTypeList, setCaseTypeList] = useState([])

    const columns = [
        {
            title: "任务ID",
            dataIndex: "taskId",
            align: "center",
            width: 100,
        },
        {
            title: "通报时间",
            dataIndex: "notificationDate",
            width: 160,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "涉案号码",
            dataIndex: "involvedPhoneNum",
            align: "center",
            width: 150,
            render: (text) => text || '--'
        },
        {
            title: "被害人号码",
            dataIndex: "victimPhoneNum",
            align: "center",
            width: 150,
            render: (text) => text || '--'
        },
        {
            title: "发案开始时间",
            dataIndex: "incidentStartTime",
            width: 180,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "发案结束时间",
            dataIndex: "incidentEndTime",
            width: 180,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "本地网",
            dataIndex: "userInformation.localNetwork",
            width: 180,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "案件类别",
            dataIndex: "caseType",
            width: 180,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "导入时间",
            dataIndex: "importTime",
            width: 180,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "是否已复盘",
            dataIndex: "ifReplay",
            width: 120,
            align: "center",
            render: (text) => text || '--'
        },
        {
            title: "操作",
            width: 150,
            align: "center",
            fixed: 'right',
            render: (record) => {
                return (
                    <>
                        {/* {isEditAuth && <> */}
                        <Licensee license='caseNotifyVerify_updateInvolvedReplayDetailById'>
                            <a onClick={(e) => editInfo(e, record)}>编辑</a>
                            <Divider type="vertical" />
                        </Licensee>
                        {/* </>} */}
                        <Licensee license='caseNotifyVerify_deleteInvolvedReplayById'>
                            {/* {isEditAuth &&  */}
                            <a onClick={(e) => delInfo(e, record.id)}>删除</a>
                            {/* } */}
                        </Licensee>
                    </>
                )
            }
        },
    ];

    const editInfo = (e, record) => {
        e.stopPropagation()
        e.preventDefault()
        setEditVisible(true)
        getInvolvedReplayDetail(record.id)
    }

    const hideEdit = () => {
        setEditVisible(false)
        setIsDetail(false)
    }
    const getInvolvedReplayDetail = (id) => {
        dispatch({
            type: 'caseNotifyVerify/getInvolvedReplayDetail',
            payload: { id }
        })
    }
    const handleEditOk = (values, callback) => {
        dispatch({
            type: 'caseNotifyVerify/updateInvolvedReplay',
            payload: values,
            callback: (res) => {
                if (res.code == 200) {
                    message.success(res.message || '修改成功');
                    callback()
                    hideEdit()
                    getPageInvolvedReplay({ ...params, ...defaultPagination })
                } else {
                    message.error(res.message);
                }
            },
        });
    }

    const delInfo = (e, id) => {
        e.stopPropagation()
        e.preventDefault()
        Modal.confirm({
            title: '复盘任务删除',
            content: '是否确认删除该条复盘任务?',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                deleteFun(id)
            },
        });
    }

    const deleteFun = (id) => {
        dispatch({
            type: 'caseNotifyVerify/deleteInvolvedReplay',
            payload: { id },
            callback: (res) => {
                if (res.code == 200) {
                    message.success(res.message || '删除成功');
                    getPageInvolvedReplay({ ...params, ...defaultPagination })
                } else {
                    message.error(res.message);
                }
            },
        });
    };


    const handleExport = () => {
        const newParams = {
            ...params,
            pageNum: 1,
            pageSize: 10,
        }
        exportFile({
            urlAPi: '/api/hn/fraudInvolvedReplay/exportInvolvedReplay',
            decode: true,
            params: newParams,
            method: 'POST',
        });
    };

    const handleImport = () => {
        setImportVisible(true)
    }

    const hideImport = () => {
        setImportVisible(false)
    }

    const [confirmLoading, setConfirmLoading] = useState(false);

    const onImport = (values, callback) => {
        setConfirmLoading(true);
        dispatch({
            type: 'caseNotifyVerify/batchImportInvolvedReplayInfo',
            payload: values,
            callback: (res) => {
                setConfirmLoading(false);
                if (res.code == 200) {
                    message.success(res.message || '导入成功');
                    getPageInvolvedReplay({ ...params, ...defaultPagination })
                    hideImport()
                } else {
                    if (res.code == 401) {
                        callback()
                    } else {
                        message.error(res.message);
                    }
                }
            },
        });
    }

    const getPageInvolvedReplay = (params) => {
        dispatch({
            type: "caseNotifyVerify/getPageInvolvedReplay",
            payload: params,
        });
        setParams(params)
    }

    const [netWorkList, setNetWorkList] = useState([])

    const getOrganizationByUser = () => {
        dispatch({
            type: 'caseNotifyVerify/getOrganizationByUser',
            callback: (res) => {
                if (res && res.code == '200' && res.data) {
                    setNetWorkList(res.data || []);
                } else {
                    setNetWorkList([]);
                }
            },
        });
    };

    //查询
    const handleSearch = () => {
        validateFields((err, { months, ...rest }) => {
            if (err) {
                return;
            }
            const newParams = {
                ...params,
                ...defaultPagination,
                ...rest,
                notificationDateStart: months && months.length > 0 ? moment(months[0]).format('YYYYMMDD') : '',
                notificationDateEnd: months && months.length > 0 ? moment(months[1]).format('YYYYMMDD') : '',
            }
            getPageInvolvedReplay(newParams)
        });
    };

    //表格切换
    const handlePaginationTable = (pagination) => {
        const newParms = { ...params, pageNum: pagination.current, pageSize: pagination.pageSize }
        getPageInvolvedReplay(newParms)
    };

    const handleInfo = (record) => {
        setIsDetail(true)
        setEditVisible(true)
        getInvolvedReplayDetail(record.id)
    }

    const handleReset = () => {
        resetFields();
        setParams({ ...defaultPagination })
        getPageInvolvedReplay({ ...defaultPagination })
    };

    // const getOrganizationByUser = () => {
    //     dispatch({
    //         type: "caseNotifyVerify/getOrganizationByUser",
    //     });
    // }

    const getCaseTypeList = () => {
        dispatch({
            type: "caseNotifyVerify/getSystemConfigListByConfigType",
            payload: { configType: 'case_type' },
            callback: (res) => {
                if (res.code == 200) {
                    setCaseTypeList(res.data)
                }
            }
        });
    }

    useEffect(() => {
        getOrganizationByUser()
        getCaseTypeList()

    }, [])

    useEffect(() => {
        getPageInvolvedReplay({ ...params, caseType: query.caseType });
        setFieldsValue({ caseType: query.caseType })
    }, [query.caseType])


    const showInfo = useLicensee('caseNotifyVerify_getInvolvedReplayDetailById')

    // useEffect(() => {
    //     getOrganizationByUser();
    // }, [])

    return (
        <Card bordered={false}>
            <Form {...formItemLayout}>
                <Row>
                    <Licensee license="caseNotifyVerify_pageInvolvedReplay">
                        <Col span={8}>
                            <Form.Item label="任务ID">
                                {getFieldDecorator("taskId")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="涉案号码">
                                {getFieldDecorator("involvedPhoneNum")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="被害人号码">
                                {getFieldDecorator("victimPhoneNum")(
                                    <Input placeholder="请输入" allowClear />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="是否已复盘">
                                {getFieldDecorator("ifReplay")(
                                    <Select placeholder="请选择" allowClear>
                                        <Option value='是'>是</Option>
                                        <Option value='否'>否</Option>
                                    </Select>
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="通报时间">
                                {getFieldDecorator("months")(
                                    <RangePicker />
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="本地网">
                                {getFieldDecorator('localNetwork')(
                                    <Select placeholder="请输入" allowClear >
                                        {netWorkList.map((item) => (
                                            <Option value={item.name}>{item.name}</Option>
                                        ))}
                                    </Select>,
                                )}
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="案件类别">
                                {getFieldDecorator('caseType')(
                                    <Select placeholder="请输入" allowClear >
                                        {['冒充电商物流客服类', '冒充公检法及政府机关类', '冒充军警购物诈骗类', '刷单返利类', '虚假贷款类', '虚假购物、服务类', '虚假投资理财类', '虚假征信类', '其他类'].map((value) => (
                                            <Option value={value}>{value}</Option>
                                        ))}
                                    </Select>,
                                )}
                            </Form.Item>
                        </Col>
                    </Licensee>
                    <Col span={8} style={{ marginTop: 5 }}>
                        <Licensee license="caseNotifyVerify_pageInvolvedReplay">
                            <Button
                                type="primary"
                                onClick={handleSearch}
                            >
                                查询
                            </Button>
                            <Button onClick={handleReset} style={{ marginLeft: 10 }}>重置</Button>
                        </Licensee>
                        <Licensee license="caseNotifyVerify_exportInvolvedReplay">
                            <Button
                                type="primary"
                                onClick={handleExport}
                                style={{ margin: '0px 10px' }}
                            >
                                导出
                            </Button>
                        </Licensee>
                        <Licensee license="caseNotifyVerify_batchImportInvolvedReplayInfo">

                            {/* {isEditAuth && */}
                            <Button
                                type="primary"
                                onClick={handleImport}
                            >
                                通报信息导入
                            </Button>
                            {/* } */}
                        </Licensee>
                    </Col>
                </Row>
            </Form>
            <StandardTable
                columns={columns}
                loading={loading}
                data={{
                    list: tableData.items || [],
                    pagination: {
                        current: params.pageNum,
                        pageSize: params.pageSize,
                        total: tableData?.totalNum || 0
                    }
                }}
                onChange={handlePaginationTable}
                rowKey="id"
                showSelectCount={false}
                rowSelectionProps={false}
                scroll={{ x: true }}
                onRow={record => {
                    return {
                        onDoubleClick: () => {
                            showInfo && handleInfo(record)
                        }
                    }
                }}
            />
            {
                importVisible && <BatchImportModal
                    title='批量导入'
                    visible={importVisible}
                    onImport={onImport}
                    onClose={hideImport}
                    loading={confirmLoading}
                    errorExportUrl={'/api/hn/fraudInvolvedReplay/downloadErrorExcel'}
                    downTemplateUrl={`template/getTemplate?templateCode=involvedReplay`}
                />
            }
            {
                editVisible && <EditModal
                    visible={editVisible}
                    onClose={hideEdit}
                    handleEditOk={handleEditOk}
                    isDetail={isDetail}
                    details={details}
                />
            }
        </Card>
    );
}

export default connect(({ caseNotifyVerify, loading }) => ({
    caseNotifyVerify,
    loading: loading.effects["caseNotifyVerify/getPageInvolvedReplay"],
}))(Form.create()(CaseNotifyVerify));
