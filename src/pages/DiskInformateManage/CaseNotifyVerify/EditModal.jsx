import React, { useState } from 'react';
import { Modal, Form, Input, Select, DatePicker, Row, Col, InputNumber } from 'antd';
import moment from 'moment';
import styles from './index.less'
import { useAuth } from 'ponshine';
import UserInfoForm from '../components/UserInfoForm'

const { Option } = Select;

const EditModal = (props) => {
    const { visible, onClose, details, handleEditOk, form, form: { getFieldDecorator, validateFields, resetFields }, loading, isDetail } = props
    const { authState } = useAuth() || {};
    const { user: { role = {} } } = authState || {};
    const noEdit = role.id == 2
    const handleOk = () => {
        validateFields((err, values) => {
            if (err) {
                return;
            }
            const params = {
                ...values,
                id: details.id,
                notificationDate: values.notificationDate ? moment(values.notificationDate).format('YYYYMMDD') : '',
                incidentEndTime: values.incidentEndTime ? moment(values.incidentEndTime).format('YYYYMMDDHHmmss') : '',
                incidentStartTime: values.incidentStartTime ? moment(values.incidentStartTime).format('YYYYMMDDHHmmss') : '',
                talkTime: values.talkTime ? moment(values.talkTime).format('YYYYMMDDHHmmss') : '',
                userInformation: {
                    ...values.userInformation,
                    internetAccessTime: values.userInformation.internetAccessTime ? moment(values.userInformation.internetAccessTime).format('YYYYMMDDHHmmss') : '',
                    resumeTime: values.userInformation.resumeTime ? moment(values.userInformation.resumeTime).format('YYYYMMDDHHmmss') : '',
                    throwOrderTime: values.userInformation.throwOrderTime ? moment(values.userInformation.throwOrderTime).format('YYYYMMDDHHmmss') : '',
                    inputOrderTime: values.userInformation.inputOrderTime ? moment(values.userInformation.inputOrderTime).format('YYYYMMDDHHmmss') : '',
                }
            }
            handleEditOk(params, () => {
                resetFields()
            })
        });
    }

    const onCancle = () => {
        resetFields()
        onClose()
    }

    return (
        <Modal
            title={isDetail ? '查看复盘详情' : '复盘编辑'}
            visible={visible}
            onCancel={onCancle}
            onOk={isDetail ? onCancle : handleOk}
            // confirmLoading={loading}
            width={1000}
            style={{ top: 40, left: 50 }}
            bodyStyle={{ height: 500, overflow: 'auto' }}
        >
            <Form className={styles.myStylesForm}>
                <Row gutter={[24]}>
                    <h3 className={styles.title}>通报信息</h3>
                    <Col span={12}>
                        <Form.Item label="通报时间">
                            {getFieldDecorator("notificationDate", {
                                initialValue: details?.notificationDate ? moment(details.notificationDate, 'YYYY-MM-DD') : undefined,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择通报时间'
                                    }
                                ]
                            })(
                                <DatePicker style={{ width: '100%' }} disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="涉案电话">
                            {getFieldDecorator("involvedPhoneNum", {
                                initialValue: details?.involvedPhoneNum,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入涉案电话'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="运营商">
                            {getFieldDecorator("isp", {
                                initialValue: details?.isp,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入运营商'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="归属地">
                            {getFieldDecorator("numberLocation", {
                                initialValue: details?.numberLocation,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入归属地'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="案件类别">
                            {getFieldDecorator("caseType", {
                                initialValue: details?.caseType,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入案件类别'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="发案开始时间">
                            {getFieldDecorator("incidentStartTime", {
                                initialValue: details?.incidentStartTime ? moment(details.incidentStartTime, 'YYYY-MM-DD HH:mm:ss') : undefined,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择发案开始时间'
                                    }
                                ]
                            })(
                                <DatePicker
                                    showTime={{ format: 'HH:mm:ss' }}
                                    format="YYYY-MM-DD HH:mm:ss"
                                    style={{ width: '100%' }}
                                    disabled={isDetail || noEdit}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="发案结束时间">
                            {getFieldDecorator("incidentEndTime", {
                                initialValue: details.incidentEndTime ? moment(details.incidentEndTime, 'YYYY-MM-DD HH:mm:ss') : undefined,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择发案结束时间'
                                    }
                                ]
                            })(
                                <DatePicker
                                    showTime={{ format: 'HH:mm:ss' }}
                                    format="YYYY-MM-DD HH:mm:ss"
                                    style={{ width: '100%' }}
                                    disabled={isDetail || noEdit}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="受害人电话">
                            {getFieldDecorator("victimPhoneNum", {
                                initialValue: details?.victimPhoneNum,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入受害人电话'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="立案单位">
                            {getFieldDecorator("filingUnit", {
                                initialValue: details?.filingUnit,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label="案件内容" labelCol={{ span: 2 }}>
                            {getFieldDecorator("caseContent", {
                                initialValue: details?.caseContent,
                            })(
                                <Input.TextArea
                                    rows={3}
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    allowClear
                                    disabled={isDetail || noEdit}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否纳入考核">
                            {getFieldDecorator("ifAssessment", {
                                initialValue: details?.ifAssessment,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail || noEdit}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="工信部考核">
                            {getFieldDecorator("miitAssessment", {
                                initialValue: details?.miitAssessment,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="省联席办考核">
                            {getFieldDecorator("provincialOfficeAssessment", {
                                initialValue: details?.provincialOfficeAssessment,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail || noEdit} />
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                <Row gutter={[24]}>
                    <h3 className={styles.title}>复盘核查</h3>
                    <Col span={12}>
                        <Form.Item label="话务是否异常">
                            {getFieldDecorator("ifTelephoneException", {
                                initialValue: details?.ifTelephoneException,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择话务是否异常'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                    <Option value='疑似'>疑似</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否呼转">
                            {getFieldDecorator("ifCallForwarding", {
                                initialValue: details?.ifCallForwarding,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="互联网流量是否异常" className={styles.widthForm}>
                            {getFieldDecorator("ifFlowException", {
                                initialValue: details?.ifFlowException,
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否诈骗">
                            {getFieldDecorator("ifFraud", {
                                initialValue: details?.ifFraud,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否诈骗'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                    <Option value='疑似'>疑似</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否省内大数据发现" className={styles.widthForm}>
                            {getFieldDecorator("ifFoundByProvincialBigData", {
                                initialValue: details?.ifFoundByProvincialBigData,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否省内大数据发现'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否集团大数据关停" className={styles.widthForm}>
                            {getFieldDecorator("ifShutdownByGroupBigData", {
                                initialValue: details?.ifShutdownByGroupBigData,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否集团大数据关停'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>
                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否真实主叫">
                            {getFieldDecorator("ifRealCalling", {
                                initialValue: details?.ifRealCalling,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择是否真实主叫'
                                    }
                                ]
                            })(
                                <Select placeholder="请选择" allowClear disabled={isDetail}>

                                    <Option value='是'>是</Option>
                                    <Option value='否'>否</Option>
                                    {
                                        ['是（被叫）', '是（被叫且不在案发时间）', '无', '是（不在案发时间）'].map(v => {
                                            return (
                                                <Option value={v}>{v}</Option>
                                            )
                                        })
                                    }
                                </Select>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="通话时间" >
                            {getFieldDecorator("talkTime", {
                                initialValue: details?.talkTime ? moment(details.talkTime, 'YYYY-MM-DD HH:mm:ss') : undefined,
                                // rules: [
                                //     {
                                //         required: true,
                                //         message: '请选择通话时间'
                                //     }
                                // ]
                            })(
                                <DatePicker
                                    showTime={{ format: 'HH:mm:ss' }}
                                    format="YYYY-MM-DD HH:mm:ss"
                                    style={{ width: '100%' }}
                                    disabled={isDetail}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="发话地市">
                            {getFieldDecorator("cityOfOrigin", {
                                initialValue: details?.cityOfOrigin,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入发话地市'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="发话省份">
                            {getFieldDecorator("provinceOfOrigin", {
                                initialValue: details?.provinceOfOrigin,
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入发话省份'
                                    }
                                ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="发话区">
                            {getFieldDecorator("speakingArea", {
                                initialValue: details?.speakingArea,
                                // rules: [
                                //     {
                                //         required: true,
                                //         message: '请输入发话区'
                                //     }
                                // ]
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="高危基站号">
                            {getFieldDecorator("highRiskBaseStationNum", {
                                initialValue: details?.highRiskBaseStationNum,
                            })(
                                <Input placeholder="请输入" allowClear disabled={isDetail} />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="受害人数量">
                            {getFieldDecorator("victimCount", {
                                initialValue: details?.victimCount,
                            })(
                                <InputNumber
                                    min={0}
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    allowClear
                                    disabled={isDetail}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item label="问题">
                            {getFieldDecorator("issue", {
                                initialValue: details?.issue,
                            })(
                                <Input.TextArea
                                    rows={3}
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    allowClear
                                    disabled={isDetail}
                                />
                            )}
                        </Form.Item>
                    </Col>
                </Row>
                <UserInfoForm form={form} isDetail={isDetail} details={details} />
            </Form>
        </Modal>
    )
}

export default Form.create()(EditModal)