import { Modal, Form, Input, Select, DatePicker, Row, Col, message,Button } from 'antd';
import React, { useEffect, useState, useRef } from 'react';
import moment from 'moment';

import request from '@/utils/request';
import { editData } from './services';
import TextAreaWithCounter from '@/components/TextAreaWithCounter/index';
const { RangePicker } = DatePicker;
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const FormItem = Form.Item;
export default Form.create()((props) => {
  let currentChangeContentRef = useRef(null)
  let currentVersionContentRef = useRef(null)
  let remarksRef = useRef(null)
  const {
    visible,
    form,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
    currentRow = {},
    cancel,
    reload,
    currentType
  } = props;
  const [modelList, setModelList] = useState([])//模型类型
  const [modelCode, setMdelCode] = useState([])//模型编码-与模型类型联动
  const [modelName, setModelName] = useState('')//模型名称-与编码联动
  const [shutdownTag, setShutdownTag] = useState('')//关停标签-与编码联动
  const [modelStatus, setModelStatus] = useState('')//模型状态-与编码联动
  useEffect(() => {
    getTypeList()
  }, [currentRow])
  // 获取所有类型
  const getTypeList = async () => {
    request("/api/hn/bizModel/getAllModelType", {
      method: 'POST',

    })
      .then((response) => {
        if (response?.code === 200) {
          setModelList(response?.data || [])
        } else {

        }
      })
  }
  const handleModelType = (e) => {
    form.setFieldsValue({
      modelCode: undefined,
      modelName: undefined,
      shutdownTag: undefined,
    })
    if (!e) {
      setMdelCode([])
    } else {
      getMdelCode(e)
    }
  }
  // 获取模型编码
  const getMdelCode = (v) => {
    request("/api/hn/bizModel/getModelByType", {
      method: 'POST',
      data: {
        modelType: v
      }
    })
      .then((response) => {
        if (response?.code === 200) {
          setMdelCode(response?.data || [])
        } else {
          message.destroy()
          message.error(response?.message);
        }
      })
  }
  // 编码变化
  const handleModelCode = (e) => {
    form.setFieldsValue({
      modelName: undefined,
      shutdownTag: undefined,
      modelStatus: undefined,
    })
    if (!e) {
      setModelName([])
      setShutdownTag([])
    } else {
      getMdelNameAndShutdownTag(e)
    }
  }
  const getMdelNameAndShutdownTag = (v) => {
    // let mockDtat={
    //   modelName:'名称',
    //   shutdownTag:'标签',
    //   modelStatus:'上线'
    // }
    // // setModelName([mockDtat.modelName])
    // // setShutdownTag([mockDtat.shutdownTag])
    // // setModelStatus([mockDtat.modelStatus])
    // form.setFieldsValue({
    //   modelName:mockDtat.modelName,
    //   shutdownTag:mockDtat.shutdownTag,
    //   modelStatus:mockDtat.modelStatus
    // })
    request("/api/hn/bizModel/getDetailByCode", {
      method: 'POST',
      data: {
        modelCode: v
      }
    })
      .then((response) => {
        if (response.code == 200) {
          let data = response?.data || {}
          form.setFieldsValue({
            modelName: data.modelName,
            shutdownTag: data.shutdownTag,
            modelStatus: data.modelStatus
          })
        } else {
          message.destroy()
          message.error(response?.message);
        }
      })

  }

  const handleOk = () => {
    validateFields(async (err, fieldsValue) => {
      if (!err) {
        let parmas = {
          ...fieldsValue,
          currentChangeContent: currentChangeContentRef?.current?.getValue(),
          currentVersionContent: currentVersionContentRef?.current?.getValue(),
          remarks: remarksRef?.current?.getValue(),
          onLineDate:fieldsValue?.onLineDate?moment(fieldsValue?.onLineDate).format('YYYY-MM-DD'):undefined,
          offLineDate:fieldsValue?.offLineDate?moment(fieldsValue?.offLineDate).format('YYYY-MM-DD'):undefined,
          id: currentRow?.id
        }
        let urls = {
          add: '/api/hn/bizModelOperateRecord/addBizModelOperateRecord',
          edit: '/api/hn/bizModelOperateRecord/addBizModelOperateRecord'
        }
        request(urls[currentType], {
          method: 'POST',
          data: parmas,
          requestType:'json'
        })
          .then((response) => {
            if (response?.code === 200) {
              message.success(response.message);
              reload();
            } else {
              message.error(response.message);
            }
          })

      }
    });
  };
  let titles = {
    add: '新增',
    edit: "编辑",
    details: "详情"
  }
  let currentChangeContentRefData=currentChangeContentRef?.current?.getValue()
  // useEffect(() => {
  //   form.setFieldsValue({
  //     currentChangeContent: currentChangeContentRefData
  //   })
  // }, [currentChangeContentRefData])
  const handleTextAreaChange = (name,newValue) => {
    form.setFieldsValue({ [name]: newValue });
  };
  return (
    <Modal
      title={titles[currentType]}
      width={'70%'}
      visible={visible}
      onOk={handleOk}
      okText="确认"
      afterClose={() => {
        resetFields();
      }}
      onCancel={cancel}
      footer={
        currentType === 'details' ? null : (
          <>
            <Button onClick={cancel}>取消</Button>
            <Button type='primary' onClick={handleOk}>确定</Button>
          </>
        )
      }
    >
      <Form {...formItemLayout}>
        <Row>
          <Col span={24}>
            <Form.Item label="模型类型">
              {getFieldDecorator('modelType', {
                initialValue: currentRow?.modelType || undefined,
                rules: [
                  {
                    required: true,
                    message: '请选择模型类型',
                  }
                ]
              })(
                <Select placeholder="请选择" allowClear disabled={currentType === 'details' || currentType == 'edit'} getPopupContainer={(node) => node.parentNode}
                  onChange={handleModelType}
                >
                  {
                    modelList.map((v, i) => {
                      return <Select.Option key={i} value={v}>{v}</Select.Option>
                    })
                  }
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型编码">
              {getFieldDecorator('modelCode', {
                initialValue: currentRow?.modelCode || undefined,
                rules: [

                  {
                    required: true,
                    message: '请输入模型编码',
                  }
                ]
              })(
                <Select placeholder="请选择" allowClear disabled={currentType === 'details' || currentType == 'edit'} getPopupContainer={(node) => node.parentNode}
                  onChange={handleModelCode}
                >
                  {
                    modelCode.map((v, i) => {
                      return <Select.Option key={i} value={v}>{v}</Select.Option>
                    })
                  }
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型名称">
              {getFieldDecorator('modelName', {
                initialValue: currentRow?.modelName || undefined,
                rules: [

                  {
                    required: true,
                    message: '请输入模型名称',
                  }
                ]
              })(<Input disabled placeholder='请输入' />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="关停标签">
              {getFieldDecorator('shutdownTag', {
                initialValue: currentRow?.shutdownTag || undefined,
                rules: [

                  {
                    required: true,
                    message: '请输入关停标签',
                  }
                ]
              })(<Input disabled placeholder='请输入' />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型状态">
              {getFieldDecorator('modelStatus', {
                initialValue: currentRow?.modelStatus || undefined,
                rules: [

                  {
                    required: true,
                    message: '请输入模型状态',
                  }
                ]
              })(
                <Input disabled placeholder='请输入' />
                //   <Select placeholder="请选择" allowClear disabled={currentType==='details' || currentType =='edit'}  getPopupContainer={(node) => node.parentNode}>
                //   {
                //     ['上线', '下线', '试运行'].map((v, i) => {
                //       return <Select.Option key={i} value={v}>{v}</Select.Option>
                //     })
                //   }
                // </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="操作类型">
              {getFieldDecorator('operateType', {
                initialValue: currentRow?.operateType || undefined,
                rules: [

                  {
                    required: true,
                    message: '请选择操作类型',
                  }
                ]
              })(
                <Select placeholder="请选择" allowClear disabled={currentType === 'details' || currentType == 'edit'} getPopupContainer={(node) => node.parentNode}>
                  {
                    ['上线', '下线', '变更'].map((v, i) => {
                      return <Select.Option key={i} value={v}>{v}</Select.Option>
                    })
                  }
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="颗粒度">
              {getFieldDecorator('granularity', {
                initialValue: currentRow?.granularity || undefined,
                rules: [

                  {
                    required: true,
                    message: '请选择颗粒度',
                  }
                ]
              })(
                <Select placeholder="请选择" allowClear disabled={currentType === 'details' || currentType == 'edit'} getPopupContainer={(node) => node.parentNode}>
                  {
                    ['年', '月', '周', '日', '小时', '分钟'].map((v, i) => {
                      return <Select.Option key={i} value={v}>{v}</Select.Option>
                    })
                  }
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="模型运行时间">
              {getFieldDecorator('modelRunTime', {
                initialValue: currentRow?.modelRunTime,
                rules: [

                  {
                    required: true,
                    message: '请输入模型运行时间',
                  }
                ]
              })(<Input disabled={currentType === 'details' || currentType == 'edit'} placeholder='请输入' />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="上线日期">
              {getFieldDecorator('onLineDate', {
                initialValue: currentRow?.onLineDate ? moment(currentRow?.onLineDate,'YYYY-MM-DD'): undefined,
                rules: [

                  {
                    required: form.getFieldValue('operateType') == '上线',
                    message: '请输入上线日期',
                  }
                ]
              })(
                <DatePicker disabled={currentType === 'details' || currentType == 'edit'} format={'YYYY-MM-DD'} />
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="下线日期">
              {getFieldDecorator('offLineDate', {
                initialValue: currentRow?.offLineDate ? moment(currentRow?.offLineDate,'YYYY-MM-DD') : undefined,
                rules: [

                  {
                    required: form.getFieldValue('operateType') == '下线',
                    message: '请输入下线日期',
                  }
                ]
              })(
                <DatePicker disabled={currentType === 'details' || currentType == 'edit'} format={'YYYY-MM-DD'} />
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="当前变更内容">
              {getFieldDecorator('currentChangeContent', {
                initialValue: currentRow?.currentChangeContent,
                rules: [
                  {
                    max: 5000,
                    message: '5000',

                  },
                  {
                    required: true,
                    message: '请输入当前变更内容',
                  }

                ],
               
              })(<TextAreaWithCounter disabled={currentType === 'details' || currentType == 'edit'} placeholder='请输入' allowClear autoSize={{ maxRows: 5, minRows: 5 }} 
              maxLength={5000} 
              currentInfo={currentRow.currentChangeContent}
               ref={currentChangeContentRef} 
              //  onChange={handleTextAreaChange}
               onChange={(value) => handleTextAreaChange('currentChangeContent', value)}
               />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="当前版本内容">
              {getFieldDecorator('currentVersionContent', {
                initialValue: currentRow?.currentVersionContent,
                rules: [
                  {
                    max: 5000,
                    message: '5000',

                  },
                  {
                    required: true,
                    message: '请输入当前版本内容',
                  }

                ]
              })(<TextAreaWithCounter disabled={currentType === 'details' || currentType == 'edit'} placeholder='请输入' allowClear autoSize={{ maxRows: 5, minRows: 5 }} 
              maxLength={5000} 
              currentInfo={currentRow.currentVersionContent} 
              ref={currentVersionContentRef} 
              onChange={(value) => handleTextAreaChange('currentVersionContent', value)}

              />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="备注">
              {getFieldDecorator('remarks', {
                initialValue: currentRow?.remarks,
                rules: [
                  {
                    max: 5000,
                    message: '5000',

                  },

                ]
              })(<TextAreaWithCounter disabled={currentType === 'details'} placeholder='请输入' allowClear autoSize={{ maxRows: 5, minRows: 5 }} maxLength={5000} 
              currentInfo={currentRow.remarks}
               ref={remarksRef} 
              onChange={(value) => handleTextAreaChange('remarks', value)}

               />)}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
});
