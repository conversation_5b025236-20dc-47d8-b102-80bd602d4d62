/*
 * @Description: 
 * @Author: ss
 * @Date: 2022-09-16 14:43:24
 * @LastEditTime: 2022-09-16 15:11:18
 * @LastEditors: ss
 * @Reference: 
 */
import { Button, Result } from 'antd';
import { withSilence } from 'demasia-pro-layout';
import { router } from 'ponshine'; // eslint-disable-next-line import/no-extraneous-dependencies
import { formatMessage } from 'ponshine-plugin-react/locale';
const ServerErrorPage = () => (
  <Result
    status="500"
    title={formatMessage({
      id: 'auth.500.title',
      defaultMessage: '500',
    })}
    subTitle={formatMessage({
      id: 'auth.500.sub-title',
      defaultMessage: 'Sorry, the server is reporting an error.',
    })}
    extra={
      <Button type="primary" onClick={() => router.push('/')}>
        {formatMessage({
          id: 'auth.500.back-home',
          defaultMessage: 'Back Home',
        })}
      </Button>
    }
  />
);

export default withSilence(ServerErrorPage);
