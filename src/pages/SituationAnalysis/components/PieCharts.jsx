import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/pie';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';
import { goPage } from '@/utils/openTab';
import { connect } from 'dryad';
import { withContext } from 'demasia-pro-layout';
const BlackAndWhiteListPie = (props) => {
  const { eventType, currentCity, closeSingleTab, dispatch, onJump } = props;
  let myCharts;
  const domRef = useRef(null);
  const initCharts = (dom) => {
    myCharts = echarts.init(dom);
    if (eventType) {
      myCharts.on('click', function (params) {
        if (closeSingleTab)
          closeSingleTab(
            '{"path":"/diskInformateManage/mobileFraudVerificationResume","isExact":true,"params":{}}',
          );
        dispatch({
          type: 'mobileFraudVerificationResume/saveGoSearchParamas',
          payload: {
            caseType: params.name,
            classification: eventType === 'caseType' ? '公安侦办平台' : '12321用户举报',
            localNetwork: currentCity,
          },
        });
        goPage('/diskInformateManage/mobileFraudVerificationResume', {
          type: 'pathJump',
        });
      });
    }
    myCharts.setOption({
      ...props.options,
    });
  };

  useEffect(() => {
    if (domRef.current) {
      initCharts(domRef.current);
    }
  }, [domRef.current, props.options]);

  useEffect(() => {
    return () => {
      if (domRef.current) {
        myCharts = echarts.init(domRef.current);
        echarts.dispose(myCharts);
      }
    };
  }, []);
  return (
    <div
      className="commonCharts"
      ref={domRef}
      style={{ width: props.width || '100%', height: props.height }}
    ></div>
  );
};

const config = [['closeSingleTab', 'tabKey']];

const YourComponent = withContext(...config)((props) => {
  return <BlackAndWhiteListPie {...props} />;
});

export default connect(({ mobileFraudVerificationResume }) => ({
  mobileFraudVerificationResume,
}))(YourComponent);
