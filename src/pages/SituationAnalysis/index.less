.wrap {
  display: flex;
  width: 1800px;
  background: #fff;
  padding: 15px;
  .tableWrap {
    min-height: 420px;
  }

  .title {
    font-weight: bold;
    font-size: 16px;
  }

  .leftWrap {
    width: 20%;
  }

  .rightWrap {
    width: 80%;

    .rightTopWrap {
      width: 100%;
      min-height: 500px;
      display: flex;

      .fraudWrap {
        width: 65%;
        .typeWrap{
          padding: 15px;
        }
      }

      .listWrap {
        width: 35%;
        .blackListWrap{
            .blackList{
              padding-left: 166px;
                .listItem{
                    // text-align: center;
                    font-size: 16px;
                    .name{
                        color: #000;
                    }
                    .value{
                        margin:0px 20px;
                        font-weight: 600;
                        color: #000;
                    }
                }
            }
        }
      }
    }
    .rightBottomWrap{
        display: flex;
        width: 100%;
        min-height: 300px;
        .casePhoneWrap{
            width: 62%;
            .chartsWrap{
                display: flex;
                align-items: center;
            }
        }
        .fraudPhoneWrap{
            width: 38%;
            .phoneWrap{
                display: flex;
                .reportSource{
                    width: 50%;
                    .content{
                      :global{
                        .ant-progress-status-success .ant-progress-text{
                          color: rgba(0,0,0,.45);
                        }
                      }
                      .label,.sourceTitle{
                        text-align: center;
                      }
                      .label{
                        margin-top: 5px;
                      }
                      .sourceTitle{
                        color: #000;
                      }
                    }
                }
            }
        }
    }
  }
}
