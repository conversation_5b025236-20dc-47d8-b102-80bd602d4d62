import React, { useState, useEffect, memo } from 'react';
import PieCharts from '../components/PieCharts';
import { Progress, Row, Col } from 'antd';
import styles from './index.less';
import { connect } from 'dryad';
import { Empty } from 'antd';

const FraudPhone = memo((props) => {
  const { dispatch, currentCity, cityTitle } = props;
  const [reportOriginList, setReportOriginList] = useState([]);
  const [pieData, setPieData] = useState([]);
  const [fraudOptions, setFraudOptions] = useState({
    title: {
      text: '不良类型',
      textStyle: {
        fontSize: 14,
        fontWeight: '500',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        return params.name + '：' + params.value + '%';
      },
    },
    legend: {
      orient: 'vertical',
      top: 'middle',
      icon: 'circle',
      right: 10,
      itemHeight: 8,
      itemWidth: 8,
      width: 100,

      textStyle: {
        overflow: 'truncate',
        ellipsis: '...',
      },
      formatter: function (params) {
        var val = '';
        if (params.length > 6) {
          val = params.substr(0, 6) + '...';
          return val;
        } else {
          return params;
        }
      },
    },
  });
  const getPieData = () => {
    dispatch({
      type: 'citySituationAnalysis/getReportReplay',
      payload: { localNetwork: currentCity },
      callback: (res) => {
        const data = res.data || [];
        // const data = [
        //   [
        //     {
        //       mix: 100,
        //       specificName: '冒充公检法及政府机关类',
        //       totalCount: 29,
        //     },
        //     {
        //       mix: 100,
        //       specificName: '饼2',
        //       totalCount: 40,
        //     },
        //   ],
        //   [
        //     {
        //       mix: 100,
        //       specificName: '冒充公检法及政府机关类',
        //       totalCount: 29,
        //     },
        //     {
        //       mix: 100,
        //       specificName: '饼2',
        //       totalCount: 40,
        //     },
        //   ],
        //   [
        //     {
        //       mix: 100,
        //       specificName: '饼1',
        //       totalCount: 29,
        //     },
        //     {
        //       mix: 100,
        //       specificName: '饼2',
        //       totalCount: 40,
        //     },
        //   ],
        //   [
        //     {
        //       mix: 100,
        //       specificName: '饼1',
        //       totalCount: 29,
        //     },
        //     {
        //       mix: 100,
        //       specificName: '饼2',
        //       totalCount: 40,
        //     },
        //   ],
        // ];
        setReportOriginList(data?.[2]);
        setPieData(data?.[1]);
        setFraudOptions({
          ...fraudOptions,
          legend: {
            ...fraudOptions.legend,
            tooltip: {
              show: true,
              //   trigger: 'item', //鼠标移动上去展示全称
              formatter: (params) => {
                return (
                  params.name +
                  '：' +
                  (data?.[1]?.find((ele) => ele.specificName === params.name)?.mix * 100)?.toFixed(
                    0,
                  ) +
                  '%'
                );
              },
            },
          },
          series: [
            {
              type: 'pie',
              radius: '45%',
              center: ['25%', '60%'],
              data: data,
              emphasis: {
                scale: false,
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              data: data?.[1].map((item) => {
                const count = (item.mix * 100)?.toFixed(0);
                return { name: item.specificName, value: count, totalCount: item.totalCount };
              }),
              label: {
                position: 'inside',
                formatter: (params) => {
                  return params.value + '%';
                },
              },
            },
          ],
        });
      },
    });
  };
  useEffect(() => {
    if (currentCity) {
      getPieData();
    }
  }, [currentCity]);
  return (
    <div>
      <h3 className={styles.title}>12321诈骗电话({cityTitle})</h3>
      <div className={styles.fraudPhoneWrap}>
        <div className={styles.phoneWrap}>
          {pieData?.length > 0 ? (
            <PieCharts
              width={'100%'}
              currentCity={currentCity}
              options={fraudOptions}
              height={280}
              eventType={'badType'}
            />
          ) : (
            <Empty style={{ marginTop: 80, width: '100%' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>
        <div className={styles.reportSource}>
          <div style={{ marginBottom: 16, fontWeight: 500 }}>举报来源</div>
          <Row className={styles.content}>
            <Col span={8} className={styles.sourceTitle}>
              举报企业
            </Col>
            <Col span={8} className={styles.sourceTitle}>
              占比
            </Col>
            <Col span={8} className={styles.sourceTitle}>
              数量
            </Col>
          </Row>
          {reportOriginList?.length > 0 ? (
            reportOriginList?.map((item, index) => {
              return (
                <Row className={styles.content} key={index}>
                  <Col span={8} className={styles.label}>
                    {item?.specificName}
                  </Col>
                  <Col span={8} className={styles.label}>
                    <Progress
                      strokeLinecap={'square'}
                      percent={parseInt(item?.mix * 100)}
                      size="small"
                      strokeColor="#1890ff"
                      format={(percent) => {
                        return percent + '%';
                      }}
                    />
                  </Col>
                  <Col span={8} className={styles.label}>
                    {item?.totalCount}
                  </Col>
                </Row>
              );
            })
          ) : (
            <Empty style={{ marginTop: 80 }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>
      </div>
    </div>
  );
});

export default connect(({ citySituationAnalysis }) => ({
  citySituationAnalysis,
}))(FraudPhone);
