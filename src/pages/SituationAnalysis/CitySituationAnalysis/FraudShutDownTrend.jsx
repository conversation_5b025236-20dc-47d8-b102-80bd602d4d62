import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import { Row, Col, Checkbox, Form } from 'antd';
import { connect } from 'dryad';
import CommonChart from '@/components/Echarts';

import DateRangePicker from './DateRangePicker';

const CheckboxGroup = Checkbox.Group;

const FraudShutDownTrend = (props) => {
  const { dispatch, currentCity, cityTitle } = props;
  const echartsRef = useRef();
  const [checkedList, setCheckedList] = useState([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [lineChartData, setLineChartData] = useState({});
  const originalLineChartData = useRef();
  const [phoneCloseSubtag, setPhoneCloseSubtag] = useState([]);

  const dateRangePickerRef = useRef(null);

  const option = {
    tooltip: {
      trigger: 'axis',
      extraCssText: 'z-index:9999;',
      appendToBody: true,
      position: function (point, params, dom, rect, size) {
        // 固定在顶部
        return [point[0] + 10, point[1] - 150];
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: lineChartData?.[Object.keys(lineChartData)?.[0]]?.map((item) => item.statisticsTime),
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
    },
    series: Object.keys(lineChartData)?.map((ele) => {
      return {
        name: ele,
        type: 'line',
        data: lineChartData?.[ele]?.map((item) => item.totalCount),
      };
    }),
  };

  const filterLineData = (checkedList) => {
    let newObj = {};
    checkedList?.map((item) => {
      for (let key in originalLineChartData.current) {
        if (item == key) {
          newObj[item] = originalLineChartData.current?.[item];
        }
      }
    });
    setLineChartData(newObj);
  };

  const onChange = (checkedList) => {
    setCheckAll(checkedList.length === phoneCloseSubtag.length);
    setIndeterminate(!!checkedList.length && checkedList.length < phoneCloseSubtag.length);
    setCheckedList(checkedList);
    filterLineData(checkedList);
  };

  const onCheckAllChange = (e) => {
    setCheckAll(e.target.checked);
    setIndeterminate(false);
    setCheckedList(e.target.checked ? phoneCloseSubtag.map((item) => item.subtag) : []);
    filterLineData(e.target.checked ? phoneCloseSubtag.map((item) => item.subtag) : []);
  };

  const getPhoneCloseTrend = (params = {}) => {
    dispatch({
      type: 'citySituationAnalysis/getPhoneCloseTrend',
      payload: params,
      callback: (res) => {
        if (res.code === 200) {
          const data = res.data || {};
          originalLineChartData.current = data;
          setLineChartData(data);
        }
      },
    });
  };

  const handleChangeDate = (value) => {
    const params = {
      startTime: value?.[0]?.format('YYYY-MM-DD'),
      endTime: value?.[1]?.format('YYYY-MM-DD'),
      localNetwork: currentCity,
    };
    getPhoneCloseTrend(params);
    getTagData(params);
  };

  const getTagData = (params) => {
    dispatch({
      type: 'citySituationAnalysis/getPhoneCloseSubtag',
      payload: params,
      callback: (res) => {
        const tagData = res.data || [];
        setPhoneCloseSubtag(tagData);
        setCheckAll(true);
        setIndeterminate(false);
        setCheckedList(tagData?.map((item) => item.subtag));
      },
    });
  };

  useEffect(() => {
    const date = dateRangePickerRef?.current?.getFieldValue('date');

    const defaultParams = {
      startTime: date?.[0]?.format('YYYY-MM-DD'),
      endTime: date?.[1]?.format('YYYY-MM-DD'),
      localNetwork: currentCity,
    };
    if (currentCity) {
      getPhoneCloseTrend(defaultParams);
      getTagData(defaultParams);
    }
  }, [currentCity]);

  return (
    <div className={styles.fraudWrap}>
      <div className={styles.fraudTitleWrap}>
        <h3 className={styles.title}>省内模型防欺诈关停情况({cityTitle})</h3>
        <DateRangePicker changeDate={handleChangeDate} wrappedComponentRef={dateRangePickerRef} />
      </div>
      <CommonChart
        show={Object.keys(lineChartData)?.length}
        echartsId="FraudShutDownTrend"
        ref={echartsRef}
        option={option}
        style={{ height: 382 }}
      />

      <Row className={styles.typeWrap}>
        {phoneCloseSubtag.length > 0 && (
          <Col span={2}>
            <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
              全部
            </Checkbox>
          </Col>
        )}
        <Col span={22}>
          <CheckboxGroup value={checkedList} onChange={onChange} style={{ width: '100%' }}>
            <Row>
              {phoneCloseSubtag?.map((item, index) => {
                return (
                  <Col span={5} key={index}>
                    <Checkbox value={item.subtag}>{item?.subtag}</Checkbox>
                    {item?.showEception == 1 && '(异常)'}
                  </Col>
                );
              })}
            </Row>
          </CheckboxGroup>
        </Col>
      </Row>
    </div>
  );
};

export default connect(({ citySituationAnalysis }) => ({
  citySituationAnalysis,
}))(Form.create()(FraudShutDownTrend));
