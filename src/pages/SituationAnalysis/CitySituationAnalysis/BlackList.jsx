import React, { useEffect } from 'react';
import styles from './index.less';
import { connect } from 'dryad';

const BlackList = (props) => {
  const {
    dispatch,
    citySituationAnalysis: { blackWhiteGrayCount },
  } = props;

  useEffect(() => {
    dispatch({
      type: 'citySituationAnalysis/getBlackWhiteGrayCount',
    });
  }, []);
  return (
    <div className={styles.blackListWrap}>
      <h3 className={styles.title}>湖南省黑白灰名单数量统计</h3>
      <div className={styles.blackList}>
        <p className={styles.listItem}>
          <span className={styles.name}>白名单</span>
          <span className={styles.value}>{blackWhiteGrayCount?.whiteCount || 0}</span>
          <span>个</span>
        </p>
        <p className={styles.listItem}>
          <span className={styles.name}>黑名单</span>
          <span className={styles.value}>{blackWhiteGrayCount?.blackCount || 0}</span>
          <span>个</span>
        </p>
        <p className={styles.listItem}>
          <span className={styles.name}>灰名单</span>
          <span className={styles.value}>{blackWhiteGrayCount?.grayCount || 0}</span>
          <span>个</span>
        </p>
      </div>
    </div>
  );
};

export default connect(({ citySituationAnalysis }) => ({
  citySituationAnalysis,
}))(BlackList);
