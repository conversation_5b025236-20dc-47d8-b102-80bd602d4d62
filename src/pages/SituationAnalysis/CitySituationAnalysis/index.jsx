import React, { useEffect, useState } from 'react';
import styles from './index.less';
import ShutDownAnalysis from './ShutDownAnalysis';
import ShutDownTrend from './ShutDownTrend';
import FraudShutDownTrend from './FraudShutDownTrend';
import BlackList from './BlackList';
import WhiteListStatics from './WhiteListStatics';
import CasePhone from './CasePhone';
import FraudPhone from './FraudPhone';
import { connect } from 'dryad';
import { Form, Select } from 'antd';

const SituationAnalysis = (props) => {
  const {
    form,
    citySituationAnalysis: { cityLists },
    dispatch,
  } = props;

  const [currentCity, setCurrentCity] = useState('');

  const getCityLists = () => {
    dispatch({
      type: 'citySituationAnalysis/getCityListsData',
      callback: (res) => {
        if (res.code === 200) {
          setCurrentCity(getDefaultCity(res?.data || []));
        }
      },
    });
  };

  const getDefaultCity = (list) => {
    const data = list || cityLists;
    let cityValue = '';
    if (data?.length > 1) {
      cityValue = data?.find((ele) => ele?.name?.includes('长沙'))?.name;
    } else {
      cityValue = data?.[0]?.name;
    }
    return cityValue;
  };

  const handleChangeCity = (value) => {
    setCurrentCity(value);
  };

  useEffect(() => {
    getCityLists();
  }, []);

  const childrenProps = {
    currentCity: currentCity,
    cityTitle: currentCity?.replace('本地网', ''),
  };
  return (
    <div className={styles.container}>
      <div className={styles.cityWrapper}>
        <Form.Item label="所属地市" style={{ display: 'flex' }}>
          {form.getFieldDecorator('localNetwork', {
            initialValue: getDefaultCity(),
          })(
            <Select
              style={{ width: 200 }}
              placeholder="请选择"
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              onChange={handleChangeCity}
              size="small"
              // allowClear={false}
            >
              {cityLists?.map((ele, index) => (
                <Select.Option value={ele.name} key={index}>
                  {ele.name.replace('本地网', '')}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </div>
      <div className={styles.wrap}>
        <div className={styles.leftWrap}>
          <ShutDownAnalysis {...childrenProps} />
          <ShutDownTrend {...childrenProps} />
        </div>
        <div className={styles.centerWrap}>
          <FraudShutDownTrend {...childrenProps} />
          <div className={styles.centerBottomWrap}>
            <CasePhone {...childrenProps} />
          </div>
        </div>
        <div className={styles.rightWrap}>
          <BlackList />
          <WhiteListStatics />
          <FraudPhone {...childrenProps} />
        </div>
      </div>
    </div>
  );
};

export default connect(({ citySituationAnalysis, loading }) => ({
  citySituationAnalysis,
  loading: loading.effects['citySituationAnalysis/getSourdownAndRestartCount'],
}))(Form.create()(SituationAnalysis));
