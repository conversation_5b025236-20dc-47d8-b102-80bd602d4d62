import React, { useEffect, useState, memo } from 'react';
import StandardTable from '@/components/StandardTable';
import styles from './index.less';
import { connect } from 'dryad';
import { router } from 'ponshine';
import { getSourdownAndRestartCount } from '@/services/CitySituationAnalysis';
import moment from 'moment';
import { goPage } from '@/utils/openTab';
import { withContext } from 'demasia-pro-layout';

const ShutDownAnalysis = memo((props) => {
  const { loading, dispatch, currentCity, cityTitle, closeSingleTab, tabKey } = props;
  const [tableData, setTableData] = useState([]);
  const columns = [
    {
      title: '日期',
      dataIndex: 'statisticsTime',
      align: 'center',
      // width: 100,
      render: (text, record) => (
        <a onClick={() => handleJump(record)} style={{ color: 'rgba(0,0,0,0.65)' }}>
          {text}
        </a>
      ),
    },
    {
      title: '关停号码量',
      width: 100,
      dataIndex: 'shutdownPhoneCount',
      align: 'center',
    },
    {
      title: '关停次数',
      dataIndex: 'shutdownCount',
      align: 'center',
    },
    {
      title: '复机量',
      dataIndex: 'restartCount',
      align: 'center',
    },
  ];

  useEffect(() => {
    if (currentCity) {
      dispatch({
        type: 'citySituationAnalysis/getSourdownAndRestartCount',
        payload: { localNetwork: currentCity },
        callback: (res) => {
          const data = res?.data;
          setTableData(data);
        },
      });
    }
  }, [currentCity]);

  const handleJump = (record) => {
    if (closeSingleTab)
      closeSingleTab(
        '{"path":"/situationAnalysis/cityAndWhiteListStatistics","isExact":true,"params":{}}',
      );
    dispatch({
      type: 'citySituationAnalysis/saveGoSearchParamas',
      payload: {
        timeUnit: 'day',
        dimension: 'businessArea',
        responsibleUnit: currentCity,
        startTime: moment(record?.statisticsTime)?.format('YYYY-MM-DD'),
        endTime: moment(record?.statisticsTime).format('YYYY-MM-DD'),
      },
    });
    router.push({
      pathname: '/situationAnalysis/cityAndWhiteListStatistics',
      query: {
        type: 'pathJump',
      },
    });
  };

  return (
    <div className={styles.tableWrap}>
      <h3 className={styles.title}>{cityTitle}关停复机统计</h3>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: tableData,
          pagination: false,
        }}
        rowKey="id"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ y: 360 }}
      />
    </div>
  );
});

const config = [['closeSingleTab', 'tabKey']];

const YourComponent = withContext(...config)((props) => {
  return <ShutDownAnalysis {...props} />;
});

export default connect(({ citySituationAnalysis, loading }) => ({
  citySituationAnalysis,
  loading: loading.effects['citySituationAnalysis/getSourdownAndRestartCount'],
}))(YourComponent);
