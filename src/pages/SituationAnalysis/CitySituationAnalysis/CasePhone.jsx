import React, { useState, useEffect } from 'react';
import PieCharts from '../components/PieCharts';
import styles from './index.less';
import { connect } from 'dryad';
import { Empty } from 'antd';

const CasePhone = (props) => {
  const { dispatch, currentCity, cityTitle } = props;
  const [pieData, setPieData] = useState({});
  const [caseOptions, setCaseOptions] = useState({
    title: {
      text: '案件类型',
      textStyle: {
        fontSize: 14,
        fontWeight: '500',
      },
    },
    tooltip: {
      trigger: 'item',
      // show: false,
      formatter: (params) => {
        return params.name + '：' + params.value + '%';
      },
    },
    legend: {
      orient: 'vertical',
      top: 'middle',
      icon: 'circle',
      right: '0px',
      itemHeight: 8,
      itemWidth: 8,
    },
  });
  const [ageOptions, setAgeOptions] = useState({
    title: {
      text: '年龄分布',
      textStyle: {
        fontSize: 14,
        fontWeight: '500',
      },
    },
    tooltip: {
      trigger: 'item',
      // show: false,
      formatter: (params) => {
        return params.name + '：' + params.value + '%';
      },
    },
    legend: {
      orient: 'vertical',
      top: 'middle',
      icon: 'circle',
      right: '30px',
      itemHeight: 8,
      itemWidth: 8,
    },
  });
  const [netWorkOptions, setNetWorkOptions] = useState({
    title: {
      text: '入网时间分布',
      textStyle: {
        fontSize: 14,
        fontWeight: '500',
      },
    },
    tooltip: {
      trigger: 'item',
      // show: false,
      formatter: (params) => {
        return params.name + '：' + params.value + '%';
      },
    },
    legend: {
      orient: 'vertical',
      top: 'middle',
      icon: 'circle',
      right: '30px',
      itemHeight: 8,
      itemWidth: 8,
    },
  });
  const getCaseOptions = (data) => {
    setCaseOptions({
      ...caseOptions,
      series: [
        {
          type: 'pie',
          radius: '45%',
          center: ['25%', '60%'],
          emphasis: {
            scale: false,
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          data: data?.map((item) => {
            const count = (item.mix * 100)?.toFixed(0);
            return { name: item.specificName, value: count, totalCount: item.totalCount };
          }),
          label: {
            position: 'inside',
            formatter: (params) => {
              return params.value + '%';
            },
          },
        },
      ],
    });
  };
  const getAgeOptions = (data) => {
    setAgeOptions({
      ...ageOptions,
      series: [
        {
          type: 'pie',
          radius: '45%',
          center: ['25%', '60%'],
          emphasis: {
            scale: false,
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          data: data?.map((item) => {
            const count = (item.mix * 100)?.toFixed(0);
            return { name: item.specificName, value: count, totalCount: item.totalCount };
          }),
          label: {
            position: 'inside',
            formatter: (params) => {
              return params.value + '%';
            },
          },
        },
      ],
    });
  };
  const getNetWorkOptions = (data) => {
    setNetWorkOptions({
      ...netWorkOptions,
      series: [
        {
          type: 'pie',
          radius: '45%',
          center: ['25%', '60%'],
          data: data?.map((item) => {
            const count = (item.mix * 100)?.toFixed(0);
            return { name: item.specificName, value: count, totalCount: item.totalCount };
          }),
          emphasis: {
            scale: false,
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          label: {
            position: 'inside',
            formatter: (params) => {
              return params.value + '%';
            },
          },
        },
      ],
    });
  };
  const getPieData = () => {
    dispatch({
      type: 'citySituationAnalysis/getInvolvedNotificationReplay',
      payload: { localNetwork: currentCity },
      callback: (res) => {
        const data = res.data || {};

        getCaseOptions(data?.[1]);
        getAgeOptions(data?.[2]);
        getNetWorkOptions(data?.[3]);
        setPieData(data);
      },
    });
  };
  useEffect(() => {
    if (currentCity) {
      getPieData();
    }
  }, [currentCity]);

  return (
    <div className={styles.casePhoneWrap}>
      <h3 className={styles.title}>涉案号码分布({cityTitle})</h3>
      <div className={styles.chartsWrap}>
        {pieData?.[1]?.length > 0 ? (
          <PieCharts
            options={caseOptions}
            width={'40%'}
            height={280}
            eventType={'caseType'}
            currentCity={currentCity}
          />
        ) : (
          <Empty style={{ marginTop: 80, width: '40%' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
        {pieData?.[2]?.length > 0 ? (
          <PieCharts options={ageOptions} width={'30%'} height={280} />
        ) : (
          <Empty style={{ marginTop: 80, width: '30%' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
        {pieData?.[3]?.length > 0 ? (
          <PieCharts options={netWorkOptions} width={'30%'} height={280} />
        ) : (
          <Empty style={{ marginTop: 80, width: '30%' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
    </div>
  );
};

export default connect(({ citySituationAnalysis }) => ({
  citySituationAnalysis,
}))(CasePhone);
