import React, { memo, forwardRef, useImperativeHandle, useState } from 'react';
import { Form, DatePicker } from 'antd';
import moment from 'moment';

const defaultDate = [moment().subtract(15, 'days'), moment().subtract(1, 'days')];
const SearchForm = forwardRef((props, ref) => {
  const { form, changeDate } = props;
  const [startTime, setStartTime] = useState();

  const { getFieldDecorator } = form;

  useImperativeHandle(ref, () => form);

  const disabledDate = (current) => {
    if (!startTime) return current > moment().startOf('day');

    return (
      (current > moment(startTime).subtract(6, 'days') &&
        current < moment(startTime).startOf('day')) ||
      (current < moment(startTime).add(6, 'days') && current > moment(startTime).endOf('day')) ||
      current < moment(startTime).subtract(29, 'days') ||
      current > moment(startTime).add(30, 'days') ||
      current > moment().startOf('day')
    );
  };
  return (
    // <Form layout="inline" {...formItemLayout}>
    <Form.Item label="日期" style={{ display: 'flex', marginBottom: 0, alignItems: 'center' }}>
      {getFieldDecorator('date', {
        initialValue: defaultDate,
      })(
        <DatePicker.RangePicker
          size="small"
          disabledDate={disabledDate}
          onCalendarChange={(dates) => {
            if (dates && dates.length === 1) {
              setStartTime(dates[0]);
            } else if (dates && dates.length === 2) {
              setStartTime();
            }
          }}
          onOpenChange={(status) => {
            setStartTime();
          }}
          placeholder="请选择"
          onChange={changeDate}
          allowClear={false}
        />,
      )}
    </Form.Item>
    // </Form>
  );
});

export default memo(Form.create()(SearchForm));
