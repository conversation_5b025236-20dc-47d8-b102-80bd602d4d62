import React, { useEffect, useRef, useState } from 'react';

import styles from './index.less';
import { connect } from 'dryad';
import CommonChart from '@/components/Echarts'

const ShutDownTrend = (props) => {
  const { dispatch, currentCity, cityTitle } = props;
  const echartsRef = useRef();
  const [trendData, setTrendData] = useState([]);

  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['关停号码量', '关停次数', '复机量'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    toolbox: {
      show: false
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: trendData?.map((item) => item.statisticsTime),
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
    },
    series: [
      {
        name: '关停号码量',
        type: 'line',
        data: trendData?.map((item) => {
          return item?.shutdownPhoneCount || 0;
        }),
      },
      {
        name: '关停次数',
        type: 'line',
        data: trendData?.map((item) => item.shutdownCount) || 0,
      },

      {
        name: '复机量',
        type: 'line',
        data: trendData?.map((item) => item.restartCount) || 0,
      },
    ],
  };
 

  useEffect(() => {
    if (currentCity) {
      dispatch({
        type: 'citySituationAnalysis/getSourdownAndRestartTrend',
        payload: { localNetwork: currentCity },
        callback: (res) => {
          setTrendData(res?.data);
        },
      });
    }
  
  }, [currentCity]);


  return (
    <>
      <h3 className={styles.title} style={{ marginTop: 10 }}>
        {cityTitle}关停复机趋势图
      </h3>
      <CommonChart show={trendData?.length} echartsId="ShutDownTrend" ref={echartsRef} option={option} style={{ height: 300 }} />
    </>
  );
};

export default connect(({ citySituationAnalysis, loading }) => ({
  citySituationAnalysis,
  loading: loading.effects['citySituationAnalysis/getSourdownAndRestartTrend'],
}))(ShutDownTrend);
