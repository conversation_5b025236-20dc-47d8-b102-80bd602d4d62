.container {
  background: #fff;
  width: 1800px;
  padding: 16px;

  .wrap {
    display: flex;
    .leftWrap {
      width: 20%;
    }
    .centerWrap {
      width: 50%;
      padding: 0 16px;
      .fraudTitleWrap {
        display: flex;
        justify-content: space-between;
      }
      .centerBottomWrap {
        // display: flex;
        .chartsWrap{
            display: flex;
            align-items: center;
          }
      }
    }
    .rightWrap {
      width: 30%;
      .blackListWrap{
        .blackList{
          padding-left: 194px;
            .listItem{
                // text-align: center;
                font-size: 16px;
                .name{
                    color: #000;
                }
                .value{
                    margin:0px 20px;
                    font-weight: 600;
                    color: #000;
                }
            }
        }
      } 
      .fraudPhoneWrap{
        display: flex;
        .phoneWrap{
          width: 50%;

        }
        .reportSource{
          width: 50%;
          .content{
            :global{
              .ant-progress-status-success .ant-progress-text{
                color: rgba(0,0,0,.45);
              }
            }
            .label,.sourceTitle{
              text-align: center;
            }
            .label{
              margin-top: 5px;
            }
            .sourceTitle{
              color: #000;
            }
          }
      }
      }
      
    }


    .title {
      font-weight: bold;
      font-size: 16px;
    }

    
  }
}
