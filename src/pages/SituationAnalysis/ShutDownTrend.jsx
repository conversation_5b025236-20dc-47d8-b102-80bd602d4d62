import React, { useEffect, useRef, useState } from 'react';
import { Empty } from 'antd';
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';
import styles from './index.less';
import { connect } from 'dryad';

const ShutDownTrend = (props) => {
  const { dispatch } = props;
  const [trendData, setTrendData] = useState([]);
  let myCharts;
  const domRef = useRef(null);
  const initCharts = (dom, data) => {
    myCharts = echarts.init(dom);
    const options = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['关停号码量', '关停次数', '复机量'],
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      // toolbox: {
      //   feature: {
      //     saveAsImage: {},
      //   },
      // },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data?.map((item) => item.statisticsTime),
      },
      yAxis: {
        type: 'value',
        minInterval: 1,
      },
      series: [
        {
          name: '关停号码量',
          type: 'line',
          data: data?.map((item) => {
            return item?.shutdownPhoneCount || 0;
          }),
        },
        {
          name: '关停次数',
          type: 'line',
          data: data?.map((item) => item.shutdownCount) || 0,
        },

        {
          name: '复机量',
          type: 'line',
          data: data?.map((item) => item.restartCount) || 0,
        },
      ],
    };
    myCharts.setOption({
      ...options,
    });
  };

  useEffect(() => {
    dispatch({
      type: 'situationAnalysis/getSourdownAndRestartTrend',
      callback: (res) => {
        setTrendData(res?.data);
      },
    });
    return () => {
      if (domRef.current) {
        myCharts = echarts.init(domRef.current);
        echarts.dispose(myCharts);
      }
    };
  }, []);

  useEffect(() => {
    if (domRef.current) {
      initCharts(domRef.current, trendData);
    }
  }, [domRef.current, trendData?.length]);

  return (
    <>
      <h3 className={styles.title} style={{ marginTop: 10 }}>
        关停复机趋势图
      </h3>
      {trendData?.length > 0 ? (
        <div className="commonCharts" ref={domRef} style={{ width: '100%', height: 300 }}></div>
      ) : (
        <Empty style={{ marginTop: 40 }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </>
  );
};

export default connect(({ situationAnalysis, loading }) => ({
  situationAnalysis,
  loading: loading.effects['situationAnalysis/getSourdownAndRestartTrend'],
}))(ShutDownTrend);
