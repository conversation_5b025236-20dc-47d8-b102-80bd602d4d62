import React from 'react'
import styles from './index.less'
import ShutDownAnalysis from './ShutDownAnalysis'
import ShutDownTrend from './ShutDownTrend'
import FraudShutDownTrend from './FraudShutDownTrend'
import BlackList from './BlackList'
import WhiteListStatics from './WhiteListStatics'
import CasePhone from './CasePhone'
import FraudPhone from './FraudPhone'
import { connect } from 'dryad';

const SituationAnalysis = (props) => {

  return (
    <div className={styles.wrap}>
      <div className={styles.leftWrap}>
        <ShutDownAnalysis />
        <ShutDownTrend />
      </div>
      <div className={styles.rightWrap}>
        <div className={styles.rightTopWrap}>
          <FraudShutDownTrend />
          <div className={styles.listWrap}>
            <BlackList />
            <WhiteListStatics />
          </div>
        </div>
        <div className={styles.rightBottomWrap}>
          <CasePhone />
          <FraudPhone />
        </div>
      </div>
    </div>
  )
}

export default connect(({ situationAnalysis, loading }) => ({
  situationAnalysis,
  loading: loading.effects["situationAnalysis/getSourdownAndRestartCount"],
}))(SituationAnalysis)