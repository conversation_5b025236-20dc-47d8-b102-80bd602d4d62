import React, { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/grid';
import 'echarts/lib/component/title';
import styles from './index.less'
import { Row, Col, Checkbox, Empty, Icon, Tooltip } from 'antd'
import { connect } from 'dryad';

const CheckboxGroup = Checkbox.Group;

const FraudShutDownTrend = (props) => {
    const { dispatch } = props
    const [checkedList, setCheckedList] = useState([])
    const [indeterminate, setIndeterminate] = useState(false)
    const [checkAll, setCheckAll] = useState(false)
    const [lineChartData, setLineChartData] = useState({})
    const [phoneCloseSubtag, setPhoneCloseSubtag] = useState([])
    const domRef = useRef(null);
    const myChartRef = useRef(null);

    const initCharts = (dom, data) => {
        myChartRef.current = dom && echarts.init(dom);
        myChartRef.current.clear()
        let seriesData = []
        let xAxisData = []
        for (let key in data) {
            seriesData.push({
                name: key,
                type: 'line',
                data: data?.[key]?.map((item => item.totalCount))
            })
            xAxisData.push(data?.[key]?.map((item => item.statisticsTime)))
        }
        const options = {
            tooltip: {
                trigger: 'axis',
                extraCssText: 'z-index:9999;',
                appendToBody: true,
                position: function (point, params, dom, rect, size) {
                    // 固定在顶部
                    return [point[0] + 10, point[1] - 150];
                }
            },
            legend: {
                show: false,
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },

            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xAxisData?.[0]
            },
            yAxis: {
                type: 'value',
                minInterval: 1,
            },
            series: seriesData
        };
        myChartRef.current.setOption({
            ...options,
        });
    };

    const filterLineData = (checkedList) => {
        let newObj = {}
        checkedList.map((item => {
            for (let key in lineChartData) {
                if (item == key) {
                    newObj[item] = lineChartData[item]
                }
            }
        }))
        if (domRef.current) {
            initCharts(domRef.current, newObj);
        }
    }

    const onChange = checkedList => {
        setCheckAll(checkedList.length === phoneCloseSubtag.length)
        setIndeterminate(!!checkedList.length && checkedList.length < phoneCloseSubtag.length)
        setCheckedList(checkedList)
        filterLineData(checkedList)
    };

    const onCheckAllChange = e => {
        setCheckAll(e.target.checked)
        setIndeterminate(false)
        setCheckedList(e.target.checked ? phoneCloseSubtag.map((item => item.subtag)) : [])
        filterLineData(e.target.checked ? phoneCloseSubtag.map((item => item.subtag)) : [])
    };

    const getPhoneCloseTrend = () => {
        dispatch({
            type: "situationAnalysis/getPhoneCloseTrend",
            callback: (res) => {
                const data = res.data || {}
                setLineChartData(data)
            }
        });
    }

    useEffect(() => {
        getPhoneCloseTrend()
        dispatch({
            type: "situationAnalysis/getPhoneCloseSubtag",
            callback: (res) => {
                const tagData = res.data || []
                setPhoneCloseSubtag(tagData)
                setCheckAll(true)
                setIndeterminate(false)
                setCheckedList(tagData?.map((item => item.subtag)))
            }
        });
        return () => {
            if (domRef.current) {
                myChartRef.current = echarts.init(domRef.current);
                echarts.dispose(myChartRef.current);
            }
        };
    }, []);


    useEffect(() => {
        if (domRef.current) {
            initCharts(domRef.current, lineChartData);
        }
    }, [domRef.current, JSON.stringify(lineChartData) !== '{}'])

    return (
        <div className={styles.fraudWrap}>
            <h3 className={styles.title}>省内模型防欺诈关停情况</h3>
            {
                JSON.stringify(lineChartData) !== '{}' ? <div
                    className="commonCharts"
                    ref={domRef}
                    style={{ width: '100%', height: 300 }}
                ></div> : <Empty style={{ marginTop: 150 }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
            }
            <Row className={styles.typeWrap}>
                {phoneCloseSubtag.length > 0 && <Col span={2}>
                    <Checkbox
                        indeterminate={indeterminate}
                        onChange={onCheckAllChange}
                        checked={checkAll}>全部</Checkbox>
                </Col>}
                <Col span={22}>
                    <CheckboxGroup
                        value={checkedList}
                        onChange={onChange}
                        style={{ width: '100%' }}
                    >
                        <Row>
                            {
                                phoneCloseSubtag?.map(((item, index) => {
                                    return (
                                        <Col span={5} key={index}>
                                            <Checkbox value={item.subtag}>{item?.subtag}</Checkbox>
                                            {item?.showEception==1&&'(异常)'}
                                        </Col>
                                    )
                                }))
                            }
                        </Row>
                    </CheckboxGroup>
                </Col>
            </Row>
        </div>
    )
}

export default connect(({ situationAnalysis }) => ({
    situationAnalysis
}))(FraudShutDownTrend)