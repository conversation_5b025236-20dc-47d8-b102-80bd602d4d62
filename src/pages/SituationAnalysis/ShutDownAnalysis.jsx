import React, { useEffect, useState } from 'react';
import StandardTable from '@/components/StandardTable';
import styles from './index.less';
import { connect } from 'dryad';

const ShutDownAnalysis = (props) => {
  const { loading, dispatch } = props;
  const [tableData, setTableData] = useState([]);
  const columns = [
    {
      title: '日期',
      dataIndex: 'statisticsTime',
      align: 'center',
      width: 100,
    },
    {
      title: '关停号码量',
      dataIndex: 'shutdownPhoneCount',
      align: 'center',
    },
    {
      title: '关停次数',
      dataIndex: 'shutdownCount',
      align: 'center',
    },
    {
      title: '复机量',
      dataIndex: 'restartCount',
      align: 'center',
    },
  ];
  useEffect(() => {
    dispatch({
      type: 'situationAnalysis/getSourdownAndRestartCount',
      callback: (res) => {
        const data = res?.data?.reverse();
        setTableData(data);
      },
    });
  }, []);

  return (
    <div className={styles.tableWrap}>
      <h3 className={styles.title}>关停复机统计</h3>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: tableData,
          pagination: false,
        }}
        rowKey="id"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ y: 360 }}
      />
    </div>
  );
};

export default connect(({ situationAnalysis, loading }) => ({
  situationAnalysis,
  loading: loading.effects['situationAnalysis/getSourdownAndRestartCount'],
}))(ShutDownAnalysis);
