import React, { useState, useEffect } from 'react'
import PieCharts from './components/PieCharts'
import styles from './index.less'
import { connect } from 'dryad';
import { Empty } from 'antd'

const WhiteListStatics = (props) => {
    const { dispatch } = props
    const [pieData, setPieData] = useState([])
    const [options, setOptions] = useState({
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return params.name + '：' + params.value + '%'
            }
        },
        legend: {
            orient: 'vertical',
            top: 'middle',
            icon: 'circle',
            right: '60px',
            textStyle: {
                width: 148,
                fontSize: 12,
            }
        },
        series: [
            {
                type: 'pie',
                radius: '70%',
                center: ['30%', '50%'],
                emphasis: {
                    scale: false,
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    position: 'inside',
                    formatter: (params) => {
                        return params.value
                    }
                }
            }
        ]
    })
    const getPieData = (data) => {
        setOptions({
            ...options,
            series: [
                {
                    type: 'pie',
                    radius: '70%',
                    center: ['30%', '50%'],
                    data: data?.map((item) => {
                        const count = (item.mix * 100)?.toFixed(0)
                        return { name: item.whiteType, value: count, totalCount: item.totalCount }
                    }),
                    emphasis: {
                        scale: false,
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        position: 'inside',
                        formatter: (params) => {
                            return params.value + '%'
                        }
                    }
                }
            ]
        })
    }
    useEffect(() => {
        dispatch({
            type: "situationAnalysis/getWhiteType",
            callback: (res) => {
                const data = res.data || []
                getPieData(data)
                setPieData(data)
            }
        })
    }, [])

    return (
        <div className={styles.whiteListWrap}>
            <h3 className={styles.title}>白名单类型统计</h3>
            {pieData.length > 0 ? <PieCharts options={options} height={280} /> :
                <Empty style={{ marginTop: 80 }} image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        </div>
    )
}

export default connect(({ situationAnalysis }) => ({
    situationAnalysis
}))(WhiteListStatics)