import {
  Button,
  Col,
  Form,
  message,
  Row,
  Select,
  DatePicker,
  Card,
  Input,
  Divider,
  Modal,
} from 'antd';
import { connect } from 'dryad';
import { useEffect, useState } from 'react';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import { Licensee } from 'ponshine';
import { withContext } from 'demasia-pro-layout';
import request from '@/utils/request';
const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultStartDay = moment().startOf('month').format('YYYY-MM-DD');
const defaultEndDay = moment().format('YYYY-MM-DD');
const defaultMonth = moment().format('YYYY-MM');

const defaultParams = {
  timeUnit: 'month',
  startTime: defaultMonth,
  endTime: defaultMonth,
  dimension: 'responsibleUnit',
  // pageNum: 1,
  // pageSize: 10,
};

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const CityAndWhiteListStatistics = Form.create()((props) => {
  const {
    dispatch,
    form: { getFieldDecorator, validateFields, resetFields, getFieldValue, setFieldsValue },
    situationAnalysis: { cityShutdownList },
    citySituationAnalysis: { searchParams },
    history: {
      location: { query },
    },
  } = props;


  const [params, setParams] = useState(
    JSON.stringify(searchParams) !== '{}' ? searchParams : defaultParams,
  );
  const [data, setData] = useState({});
  const [ModalData, setModalData] = useState({});
  const [loading, setloading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState({});
  useEffect(() => {
    if (!visible) {
      setRecord({});
    }
  }, [visible]);
  const timeUnit = getFieldValue('timeUnit');

  const columns =
    params?.dimension == 'businessArea'
      ? [
          {
            title: '统计时间',
            dataIndex: 'countTime',
            width: 120,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '责任单位',
            dataIndex: 'responsibleUnit',
            width: 130,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '营业区',
            dataIndex: 'businessArea',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '关停号码数',
            dataIndex: 'shutdownPhoneNumCount',
            width: 100,
            align: 'center',
            render: (text, record) =>
              String(text) != 'null' ? (
                <Button
                  type="link"
                  onClick={() => {
                    exportModal(record);
                  }}
                >
                  {String(text)}
                </Button>
              ) : (
                '--'
              ),
          },
          {
            title: '关停次数',
            dataIndex: 'shutdownCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '当天关停&复机次数',
            dataIndex: 'shutdownAndResumeTodayCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机次数',
            dataIndex: 'resumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机号码量',
            dataIndex: 'resumePhoneNumCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
        ]
      : [
          {
            title: '统计时间',
            dataIndex: 'countTime',
            width: 120,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '责任单位',
            dataIndex: 'responsibleUnit',
            width: 130,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '关停号码数',
            dataIndex: 'shutdownPhoneNumCount',
            width: 100,
            align: 'center',
            render: (text, r) =>
              String(text) != 'null' ? (
                <Button
                  type="link"
                  onClick={() => {
                    exportModal(r);
                  }}
                >
                  {String(text)}
                </Button>
              ) : (
                '--'
              ),
          },
          {
            title: '关停次数',
            dataIndex: 'shutdownCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '当天关停&复机次数',
            dataIndex: 'shutdownAndResumeTodayCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机次数',
            dataIndex: 'resumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机号码量',
            dataIndex: 'resumePhoneNumCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '白名单变化量',
            dataIndex: 'whiteChangeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '白名单量',
            dataIndex: 'whiteCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
        ];
  const handleExport = () => {
    const newParams = {
      ...params,
    };
    exportFile({
      urlAPi: '/api/hn/cityShutdown/exportCityShutdown',
      decode: true,
      params: newParams,
      method: 'POST',
    });
  };
  const exportModal = (r) => {
    setVisible(true);
    setRecord(r);
    request(`/api/hn/cityShutdown/listFraudTypeShutdown`, {
      method: 'POST',
      data:
        params.dimension == 'businessArea'
          ? {
              responsibleUnit: r?.responsibleUnit,
              businessArea: r?.businessArea,
              countTime: r?.countTime,
              dimension: params?.dimension,
              timeUnit: params?.timeUnit,
            }
          : {
              responsibleUnit: r?.responsibleUnit,
              countTime: r?.countTime,
              dimension: params?.dimension,
              timeUnit: params?.timeUnit,
            },
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setModalData({
          list: res?.data?.list || [],
          total: res?.data?.total || 0,
        });
      } else {
        setModalData({
          list: [],
          total: 0,
        });
      }
    });
  };
  const getCityShutdownAndResumeAndWhiteCount = (params) => {
    // if (
    //   !getFieldValue('responsibleUnit') ||
    //   !getFieldValue('months') ||
    //   !getFieldValue('timeUnit')
    // ) {
    //   message.error('颗粒度,起止日期,统计维度不能为空');
    //   return;
    // }
    setloading(true);
    const requstData = {
      ...params,
    };
    delete requstData?.current;
    delete requstData?.pageSize;

    request(`/api/hn/cityShutdown/listCityShutdown`, {
      method: 'POST',
      data: requstData,
      requestType: 'json',
    }).then((res) => {
      setloading(false);
      if (res.code == 200) {
        setData({
          list: res.data.list || [],
          total: res.data.total || 0,
          current: params?.current || 1,
          pageSize: params?.pageSize || 10,
        });
      } else {
        setData({
          list: [],
          total: 0,
          current: params?.current || 1,
          pageSize: params?.pageSize || 10,
        });
      }
    });
    setParams(params);
  };

  //查询
  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const dateFormate = timeUnit == 'month' ? 'YYYY-MM' : 'YYYY-MM-DD';
      const newParams = {
        ...params,
        startTime: months && months.length > 0 ? moment(months[0]).format(dateFormate) : '',
        endTime: months && months.length > 0 ? moment(months[1]).format(dateFormate) : '',
        ...rest,
      };
      getCityShutdownAndResumeAndWhiteCount(newParams);
      // setCurrent(1)
    });
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    // setCurrent(pagination.current);
    const newParms = { ...params, current: pagination.current, pageSize: pagination.pageSize };
    setParams(newParms);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultParams });
    getCityShutdownAndResumeAndWhiteCount({ ...defaultParams });
    dispatch({
      type: 'citySituationAnalysis/saveGoSearchParamas',
      payload: {},
    });
  };

  const changeDate = (value) => {
    setFieldsValue({ months: value });
  };

  const changePanel = (v) => {
    setFieldsValue({ months: v });
  };
  const changeGraininess = (value) => {
    // setFieldsValue({ months: undefined });
    if (value == 'day') {
      setFieldsValue({
        months: [moment(defaultStartDay), moment()],
      });
    } else {
      setFieldsValue({
        months: [moment(), moment()],
      });
    }
  };

  useEffect(() => {
    // getCityShutdownAndResumeAndWhiteCount(params);
    getOrganizationByUser();
  }, []);

  useEffect(() => {
    let newParams = {};
    if (JSON.stringify(query) === '{}') {
      dispatch({
        type: 'citySituationAnalysis/saveGoSearchParamas',
        payload: {},
      });
      newParams = defaultParams;
    } else {
      newParams = JSON.stringify(searchParams) !== '{}' ? searchParams : defaultParams;
    }
    getCityShutdownAndResumeAndWhiteCount(newParams);
    resetFields();
    setParams(newParams);
  }, [JSON.stringify(query)]);

  const [responsibleUnitList, setResponsibleUnitList] = useState([]);
  const getOrganizationByUser = () => {
    request(`/api/hn/systemConfig/getOrganizationByUser2`, {
      method: 'get',
      params: {},
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setResponsibleUnitList(res?.data || []);
      } else {
        setResponsibleUnitList([]);
      }
    });
  };
  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="颗粒度">
              {getFieldDecorator('timeUnit', {
                initialValue: params.timeUnit,
                rules: [{ required: true, message: '颗粒度必填' }],
              })(
                <Select placeholder="请选择" allowClear onChange={changeGraininess}>
                  <Option value="month">按月</Option>
                  <Option value="day">按天</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="起止日期">
              {getFieldDecorator('months', {
                // initialValue: [moment(defaultStartDay), moment()],
                initialValue: [moment(params.startTime), moment(params.endTime)],
                // JSON.stringify(query) !== '{}'
                //   ? [moment(query.startTime), moment(query.endTime)]
                //   : [moment(defaultStartDay), moment()],
                rules: [{ required: true, message: '起止日期必填' }],
              })(
                timeUnit == 'month' ? (
                  <RangePicker
                    format={'YYYY-MM'}
                    mode={['month', 'month']}
                    onPanelChange={changePanel}
                    onChange={changeDate}
                  />
                ) : (
                  <RangePicker format={'YYYY-MM-DD'} />
                ),
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="责任单位">
              {getFieldDecorator('responsibleUnit', {
                initialValue: params.responsibleUnit,
              })(
                <Select placeholder="请选择" allowClear>
                  {responsibleUnitList?.map((item, index) => (
                    <Option value={item?.name} key={index}>
                      {item?.name}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="统计维度">
              {getFieldDecorator('dimension', {
                initialValue: params.dimension,
                rules: [{ required: true, message: '统计维度必填' }],
              })(
                <Select placeholder="请选择" allowClear>
                  <Option value={'responsibleUnit'} key={'responsibleUnit'}>
                    地市
                  </Option>
                  <Option value={'businessArea'} key={'businessArea'}>
                    区县
                  </Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8} offset={8} style={{ textAlign: 'right', marginBottom: 24 }}>
            <Licensee license="situationAnalysis_cityAndWhiteListStatistics">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ margin: '0px 10px' }}>
                重置
              </Button>
            </Licensee>
            <Licensee license="situationAnalysis_cityAndWhiteListStatistics">
              <Button type="primary" onClick={handleExport}>
                导出
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: data.list,
          pagination: false,
          pagination: {
            current: params?.current || 1,
            pageSize: params?.pageSize || 10,
            total: data.total,
          },
        }}
        onChange={handlePaginationTable}
        rowKey="key"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ x: 'max-content' }}
      />
      <Modal
        footer={<></>}
        maskClosable={false}
        title={`涉诈类型统计`}
        visible={visible}
        onCancel={() => {
          setVisible(false);
        }}
        width={700}
        destroyOnClose
      >
        <Form>
          <Row>
            <Button
              type="primary"
              onClick={() => {
                exportFile({
                  urlAPi: '/api/hn/cityShutdown/exportFraudTypeShutdown',
                  decode: true,
                  params:
                    params.dimension == 'businessArea'
                      ? {
                          responsibleUnit: record?.responsibleUnit,
                          businessArea: record?.businessArea,
                          countTime: record?.countTime,
                          dimension: params?.dimension,
                          timeUnit: params?.timeUnit,
                        }
                      : {
                          responsibleUnit: record?.responsibleUnit,
                          countTime: record?.countTime,
                          dimension: params?.dimension,
                          timeUnit: params?.timeUnit,
                        },
                  method: 'POST',
                });
              }}
            >
              导出
            </Button>
          </Row>
          <Row>
            <StandardTable
              loading={modalLoading}
              columns={
                params.dimension == 'businessArea'
                  ? [
                      {
                        title: '统计时间',
                        dataIndex: 'countTime',
                        key: 'countTime',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                      {
                        title: '责任单位',
                        dataIndex: 'responsibleUnit',
                        key: 'responsibleUnit',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                      {
                        title: '营业区',
                        dataIndex: 'businessArea',
                        key: 'businessArea',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                      {
                        title: '涉诈类型',
                        dataIndex: 'fraudType',
                        key: 'fraudType',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                      {
                        title: '关停号码数量',
                        dataIndex: 'shutdownPhoneNumCount',
                        key: 'shutdownPhoneNumCount',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                    ]
                  : [
                      {
                        title: '统计时间',
                        dataIndex: 'countTime',
                        key: 'countTime',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                      {
                        title: '责任单位',
                        dataIndex: 'responsibleUnit',
                        key: 'responsibleUnit',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                      {
                        title: '涉诈类型',
                        dataIndex: 'fraudType',
                        key: 'fraudType',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                      {
                        title: '关停号码数量',
                        dataIndex: 'shutdownPhoneNumCount',
                        key: 'shutdownPhoneNumCount',
                        align: 'center',
                        width: 100,
                        ellipsis: true,
                      },
                    ]
              }
              showSelectCount={false}
              rowSelection={null}
              rowSelectionProps={false}
              data={{
                list: ModalData?.list || [],
                pagination: false,
              }}
            />
          </Row>
        </Form>
      </Modal>
    </Card>
  );
});

const config = [['closeSingleTab', 'tabKey']];

const YourComponent = withContext(...config)((props) => {
  return <CityAndWhiteListStatistics {...props} />;
});

export default connect(({ situationAnalysis, citySituationAnalysis, loading }) => ({
  situationAnalysis,
  citySituationAnalysis,
  loading: loading.effects['situationAnalysis/getCityShutdownAndResumeAndWhiteCount'],
}))(YourComponent);
