import {
  getSourdownAndRestartCount,
  getPhoneCloseTrend,
  getInvolvedNotificationReplay,
  getReportReplay,
  getBlackWhiteGrayCount,
  getWhiteType,
  getPhoneCloseSubtag,
  getCityShutdownAndResumeAndWhiteCount
} from '@/services/SituationAnalysis';

const defaultState = {
  blackWhiteGrayCount:{},
  localNetworkList:[],
  cityShutdownList:[]
};

export default {
  namespace: 'situationAnalysis',
  state: defaultState,
  effects: {
    *getSourdownAndRestartCount({ payload, callback }, { call, put }) {
      const response = yield call(getSourdownAndRestartCount, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getSourdownAndRestartTrend({ payload, callback }, { call, put }) {
      const response = yield call(getSourdownAndRestartCount, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getPhoneCloseTrend({ payload, callback }, { call, put }) {
      const response = yield call(getPhoneCloseTrend, payload);
      if (callback) callback(response);
    },
    *getPhoneCloseSubtag({ payload, callback }, { call, put }) {
      const response = yield call(getPhoneCloseSubtag, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getInvolvedNotificationReplay({ payload, callback }, { call, put }) {
      const response = yield call(getInvolvedNotificationReplay, payload);
      if (callback) callback(response);
    },
    *getReportReplay({ payload, callback }, { call, put }) {
      const response = yield call(getReportReplay, payload);
      if (callback) callback(response);
    },
    *getBlackWhiteGrayCount({ payload, callback }, { call, put }) {
      const response = yield call(getBlackWhiteGrayCount, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { blackWhiteGrayCount: response.data },
      });
    },
    *getWhiteType({ payload, callback }, { call, put }) {
      const response = yield call(getWhiteType, payload);
      if (callback) callback(response);
    },
    *getCityShutdownAndResumeAndWhiteCount({ payload, callback }, { call, put }) {
      const response = yield call(getCityShutdownAndResumeAndWhiteCount, payload);
      if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { cityShutdownList: response?.data?.map((item,index)=>{
          return {...item,key:index+1}
        }) },
      });
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
