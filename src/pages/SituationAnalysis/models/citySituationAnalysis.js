import {
  getSourdownAndRestartCount,
  getSourdownAndRestartTrend,
  getPhoneCloseTrend,
  getInvolvedNotificationReplay,
  getReportReplay,
  getBlackWhiteGrayCount,
  getWhiteType,
  getPhoneCloseSubtag,
  getCityListsData,
} from '@/services/CitySituationAnalysis';

const defaultState = {
  blackWhiteGrayCount: {},
  localNetworkList: [],
  cityShutdownList: [],
  cityLists: [],
  searchParams: {},
};

export default {
  namespace: 'citySituationAnalysis',
  state: defaultState,
  effects: {
    *getCityListsData({ payload, callback }, { call, put }) {
      const response = yield call(getCityListsData, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
      yield put({
        type: 'save',
        payload: {
          cityLists: response.data || [],
        },
      });
    },
    *getSourdownAndRestartCount({ payload, callback }, { call, put }) {
      const response = yield call(getSourdownAndRestartCount, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getSourdownAndRestartTrend({ payload, callback }, { call, put }) {
      const response = yield call(getSourdownAndRestartTrend, payload);
      if (!response) return;
      if (callback) callback(response);
    },

    *getPhoneCloseTrend({ payload, callback }, { call, put }) {
      const response = yield call(getPhoneCloseTrend, payload);
      if (callback) callback(response);
    },
    *getPhoneCloseSubtag({ payload, callback }, { call, put }) {
      const response = yield call(getPhoneCloseSubtag, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getInvolvedNotificationReplay({ payload, callback }, { call, put }) {
      const response = yield call(getInvolvedNotificationReplay, payload);
      if (callback) callback(response);
    },
    *getReportReplay({ payload, callback }, { call, put }) {
      const response = yield call(getReportReplay, payload);
      if (callback) callback(response);
    },
    *getBlackWhiteGrayCount({ payload, callback }, { call, put }) {
      const response = yield call(getBlackWhiteGrayCount, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
      yield put({
        type: 'save',
        payload: { blackWhiteGrayCount: response.data },
      });
    },
    *getWhiteType({ payload, callback }, { call, put }) {
      const response = yield call(getWhiteType, payload);
      if (callback) callback(response);
    },
    *saveGoSearchParamas({ payload, callback }, { call, put }) {
      yield put({
        type: 'save',
        payload: {
          searchParams: payload,
        },
      });
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
