import {
  Button,
  Col,
  Form,
  message,
  Row,
  Select,
  DatePicker,
  Card,
  Input,
  Divider,
  Modal,
} from 'antd';
import { connect } from 'dryad';
import { useEffect, useState } from 'react';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import { Licensee } from 'ponshine';
import request from '@/utils/request';
const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultStartDay = moment().startOf('month').format('YYYY-MM-DD');
const defaultEndDay = moment().format('YYYYMMDD');
const defaultMonthStart = moment().startOf('month').format('YYYYMM01');
const defaultMonthEnd = moment().format('YYYYMM01');

const defaultParams = {
  timeDimension: 2,
  startTime: defaultMonthStart,
  endTime: defaultMonthEnd,
  statisticsDimension: 1,
  currentPage: 1,
  pageSize: 10,
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const CityAndWhiteListStatistics = (props) => {
  const {
    dispatch,
    form: { getFieldDecorator, validateFields, resetFields, getFieldValue, setFieldsValue },
    situationAnalysis: { cityShutdownList },
  } = props;
  const [params, setParams] = useState(defaultParams);
  const [data, setData] = useState({});
  const [loading, setloading] = useState(false);
  const timeDimension = getFieldValue('timeDimension');

  const [responsibleUnitList, setResponsibleUnitList] = useState([]);
  useEffect(() => {
    getCityShutdownAndResumeAndWhiteCount({ ...params });
    getOrganizationByUser();
  }, []);
  const columns =
    params?.statisticsDimension == 1
      ? [
          {
            title: '统计时间',
            dataIndex: 'statisticsTime',
            width: 120,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '责任单位',
            dataIndex: 'responsibleUnit',
            width: 130,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机量',
            dataIndex: 'resumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机',
            dataIndex: 'thousandResumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机',
            dataIndex: 'onlineResumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机',
            dataIndex: 'offlineResumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡',
            dataIndex: 'offlineCardCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机占比',
            dataIndex: 'thousandResumePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机占比',
            dataIndex: 'onlineResumePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机占比',
            dataIndex: 'offlineResumePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡占比',
            dataIndex: 'offlineCardPercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机号码数',
            dataIndex: 'resumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机号码数',
            dataIndex: 'thousandResumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机号码数',
            dataIndex: 'onlineResumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机号码数',
            dataIndex: 'offlineResumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡号码数',
            dataIndex: 'offlineCardPhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机号码占比',
            dataIndex: 'thousandResumePhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机号码占比',
            dataIndex: 'onlineResumePhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机号码占比',
            dataIndex: 'offlineResumePhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡号码占比',
            dataIndex: 'offlineCardPhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
        ]
      : [
          {
            title: '统计时间',
            dataIndex: 'statisticsTime',
            width: 120,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '责任单位',
            dataIndex: 'responsibleUnit',
            width: 130,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '营业区',
            dataIndex: 'businessArea',
            width: 130,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机量',
            dataIndex: 'resumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机',
            dataIndex: 'thousandResumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机',
            dataIndex: 'onlineResumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机',
            dataIndex: 'offlineResumeCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡',
            dataIndex: 'offlineCardCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机占比',
            dataIndex: 'thousandResumePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机占比',
            dataIndex: 'onlineResumePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机占比',
            dataIndex: 'offlineResumePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡占比',
            dataIndex: 'offlineCardPercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '复机号码数',
            dataIndex: 'resumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机号码数',
            dataIndex: 'thousandResumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机号码数',
            dataIndex: 'onlineResumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机号码数',
            dataIndex: 'offlineResumePhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡号码数',
            dataIndex: 'offlineCardPhoneCount',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '10000号复机号码占比',
            dataIndex: 'thousandResumePhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线上复机号码占比',
            dataIndex: 'onlineResumePhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下复机号码占比',
            dataIndex: 'offlineResumePhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
          {
            title: '线下读卡号码占比',
            dataIndex: 'offlineCardPhonePercent',
            width: 100,
            align: 'center',
            render: (text) => (String(text) != 'null' ? String(text) : '--'),
          },
        ];
  const handleExport = () => {
    const newParams = {
      ...params,
    };
    exportFile({
      urlAPi: '/api/hn/statisticsResume/exportStatisticsResume',
      decode: true,
      params: newParams,
      method: 'POST',
    });
  };

  const getCityShutdownAndResumeAndWhiteCount = (params) => {
    setloading(true);
    const requstData = {
      ...params,
    };
    delete requstData?.current;
    delete requstData?.pageSize;
    request(`/api/hn/statisticsResume/getStatisticsResume`, {
      method: 'POST',
      data: params,
      requestType: 'json',
    }).then((res) => {
      setloading(false);
      if (res.code == 200) {
        setData({
          list: res?.data?.items || [],
          total: res?.data?.totalNum || 0,
          currentPage: params?.currentPage || 1,
          pageSize: params?.pageSize || 10,
        });
      } else {
        setData({
          list: [],
          total: 0,
          currentPage: params?.currentPage || 1,
          pageSize: params?.pageSize || 10,
        });
      }
    });
    setParams(params);
  };

  //查询
  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const dateFormateStart = timeDimension == '2' ? 'YYYYMM01' : 'YYYYMMDD';
      const dateFormateEnd = timeDimension == '2' ? 'YYYYMM01' : 'YYYYMMDD';
      const newParams = {
        ...params,
        startTime: months && months.length > 0 ? moment(months[0]).format(dateFormateStart) : '',
        endTime: months && months.length > 0 ? moment(months[1]).format(dateFormateEnd) : '',
        ...rest,
        timeDimension: Number(rest?.timeDimension),
        statisticsDimension: Number(rest?.statisticsDimension),
        currentPage: params?.currentPage || 1,
        pageSize: params?.pageSize || 10,
      };
      getCityShutdownAndResumeAndWhiteCount(newParams);
      // setCurrent(1)
    });
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    // setCurrent(pagination.current);
    const newParms = { ...params, currentPage: pagination.current, pageSize: pagination.pageSize };
    getCityShutdownAndResumeAndWhiteCount(newParms);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultParams });
    getCityShutdownAndResumeAndWhiteCount({ ...defaultParams });
  };

  const changeDate = (value) => {
    setFieldsValue({ months: value });
  };

  const changePanel = (v) => {
    setFieldsValue({ months: v });
  };
  const changeGraininess = (value) => {
    // setFieldsValue({ months: undefined });
    if (value == 1) {
      setFieldsValue({
        months: [moment(defaultStartDay), moment()],
      });
    } else {
      setFieldsValue({
        months: [moment(), moment()],
      });
    }
  };

  const getOrganizationByUser = () => {
    request(`/api/hn/systemConfig/getOrganizationByUser2`, {
      method: 'get',
      params: {},
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setResponsibleUnitList(res?.data || []);
      } else {
        setResponsibleUnitList([]);
      }
    });
  };
  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="颗粒度">
              {getFieldDecorator('timeDimension', {
                initialValue: params.timeDimension,
                rules: [{ required: true, message: '颗粒度必填' }],
              })(
                <Select placeholder="请选择" allowClear onChange={changeGraininess}>
                  <Option value={2}>按月</Option>
                  <Option value={1}>按天</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="起止日期">
              {getFieldDecorator('months', {
                initialValue:
                  timeDimension == 2
                    ? [moment(params.startTime, 'YYYY-MM'), moment(params.endTime, 'YYYY-MM')]
                    : [moment(defaultStartDay, 'YYYY-MM-DD'), moment(defaultEndDay, 'YYYY-MM-DD')],
                rules: [{ required: true, message: '起止日期必填' }],
              })(
                timeDimension == 2 ? (
                  <RangePicker
                    format={'YYYY-MM'}
                    mode={['month', 'month']}
                    onPanelChange={changePanel}
                    onChange={changeDate}
                  />
                ) : (
                  <RangePicker format={'YYYY-MM-DD'} />
                ),
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="责任单位">
              {getFieldDecorator('responsibleUnit')(
                <Select placeholder="请选择" allowClear>
                  {responsibleUnitList?.map((item, index) => (
                    <Option value={item.name} key={index}>
                      {item.name}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="统计维度">
              {getFieldDecorator('statisticsDimension', {
                initialValue: params.statisticsDimension,
                rules: [{ required: true, message: '统计维度必填' }],
              })(
                <Select placeholder="请选择" allowClear>
                  <Option value={1} key={'responsibleUnit'}>
                    地市
                  </Option>
                  <Option value={2} key={'businessArea'}>
                    区县
                  </Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8} offset={8} style={{ textAlign: 'right', marginBottom: 24 }}>
            <Licensee license="getStatisticsResume_query">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ margin: '0px 10px' }}>
                重置
              </Button>
            </Licensee>
            <Licensee license="exportStatisticsResume_export">
              <Button type="primary" onClick={handleExport}>
                导出
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        loading={loading}
        data={{
          list: data.list,
          pagination: false,
          pagination: {
            current: data?.currentPage || 1,
            pageSize: data?.pageSize || 10,
            total: data.total,
          },
        }}
        onChange={handlePaginationTable}
        rowKey="key"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ x: 'max-content' }}
      />
    </Card>
  );
};

export default connect(({ situationAnalysis, loading }) => ({
  situationAnalysis,
  loading: loading.effects['situationAnalysis/getCityShutdownAndResumeAndWhiteCount'],
}))(Form.create()(CityAndWhiteListStatistics));
