import {
  Button,
  Col,
  Form,
  message,
  Row,
  Select,
  DatePicker,
  Card,
  Input,
  Divider,
  Modal,
} from 'antd';
import { connect } from 'dryad';
import { useEffect, useState } from 'react';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { exportFile } from '@/utils/utils';
import { Licensee } from 'ponshine';
import request from '@/utils/request';
const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultStartDay = moment().startOf('month').format('YYYY-MM-DD');
const defaultEndDay = moment().format('YYYY-MM-DD');
const defaultMonth = moment().format('YYYY-MM');

const defaultParams = {
  timeUnit: 'month',
  startTime: defaultMonth,
  endTime: defaultMonth,
  dimension: 'shutdownTag',
  // pageNum: 1,
  // pageSize: 10
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};
//统计维度
const responsibleUnitList = [
  '长沙本地网',
  '株洲本地网',
  '湘潭本地网',
  '衡阳本地网',
  '邵阳本地网',
  '岳阳本地网',
  '常德本地网',
  '张家界本地网',
  '益阳本地网',
  '郴州本地网',
  '永州本地网',
  '怀化本地网',
  '娄底本地网',
  '湘西本地网',
  '省电渠',
];
//
const StatisticalDimensionType = {
  fraudType: {
    title: '涉诈类型',
    data: ['公安通报', '用户举报', '关联关停', '三同分析', '大数据分析'],
  },
  shutdownTag: {
    title: '关停标签',
    data: ['公安通报', '用户举报', '关联关停', '三同分析', '大数据分析'],
  },
  subTag: {
    title: '子标签',
    data: ['公安通报', '用户举报', '关联关停', '三同分析', '大数据分析'],
  },
};
//维度，涉诈类型 fraudType ,关停标签 shutdownTag , 子标签 subTag
const StatisticalDimension = [
  { key: '涉诈类型', value: 'fraudType' },
  { key: '关停标签', value: 'shutdownTag' },
  { key: '子标签', value: 'subTag' },
];

const CityAndWhiteListStatistics = (props) => {
  const {
    dispatch,
    form: { getFieldDecorator, validateFields, resetFields, getFieldValue, setFieldsValue },
    situationAnalysis: { cityShutdownList },
  } = props;
  const [params, setParams] = useState(defaultParams);
  const [data, setData] = useState({});
  const [loading, setloading] = useState(false);
  const timeUnit = getFieldValue('timeUnit');

  const columns = [
    {
      title: '统计时间',
      dataIndex: 'countTime',
      width: 120,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '涉诈类型',
      dataIndex: 'fraudType',
      width: 130,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '关停标签',
      dataIndex: 'shutdownTag',
      width: 150,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '子标签',
      dataIndex: 'subTag',
      width: 100,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '关停次数',
      dataIndex: 'shutdownCount',
      width: 100,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '复机次数',
      dataIndex: 'resumeCount',
      width: 100,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '复机次数比例',
      dataIndex: 'resumeCountPercent',
      width: 100,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '关停号码数',
      dataIndex: 'shutdownPhoneNumCount',
      width: 100,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '复机号码数',
      dataIndex: 'resumePhoneNumCount',
      width: 100,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
    {
      title: '复机号码比例',
      dataIndex: 'resumePhoneNumCountPercent',
      width: 100,
      align: 'center',
      render: (text) => (String(text) != 'null' ? String(text) : '--'),
    },
  ];
  const typecolumns = (type) => {
    //维度，涉诈类型 fraudType ,关停标签 shutdownTag , 子标签 subTag
    const newcolumnstype = {
      fraudType: ['关停标签', '子标签'],
      shutdownTag: ['子标签'],
      subTag: [],
    };
    const newcolumns = [];
    columns.forEach((item) => {
      if (
        !newcolumnstype[type].some((items) => {
          return items == item?.title;
        })
      ) {
        newcolumns.push(item);
      }
    });

    return newcolumns;
  };
  const [AllFraudType, setAllFraudType] = useState([]);
  const [ShutdownTagListByFraudType, setShutdownTagListByFraudType] = useState([]);
  const getAllFraudType = () => {
    request(`/api/hn/shutdownTag/getAllFraudTypeAll`, {
      method: 'get',
      params: {},
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setAllFraudType(res?.data || []);
      } else {
        setAllFraudType([]);
      }
    });
  };

  const getShutdownTagListByFraudType = (type) => {
    setFieldsValue({ shutdownTag: undefined });
    if (type) {
      request(`/api/hn/shutdownTag/getShutdownTagListByFraudTypeAll`, {
        method: 'get',
        params: { fraudType: type },
        requestType: 'json',
      }).then((res) => {
        if (res.code == 200) {
          setShutdownTagListByFraudType(res?.data || []);
        } else {
          setShutdownTagListByFraudType([]);
        }
      });
    } else {
      setShutdownTagListByFraudType([]);
    }
  };
  const handleExport = () => {
    const newParams = {
      ...params,
    };
    exportFile({
      urlAPi: '/api/hn/shutdownTagStatistics/exportShutdownTagStatistics',
      decode: true,
      params: newParams,
      method: 'POST',
    });
  };

  const getCityShutdownAndResumeAndWhiteCount = (params) => {
    setloading(true);
    const requstData = {
      ...params,
    };
    delete requstData?.current;
    delete requstData?.pageSize;
    request(`/api/hn/shutdownTagStatistics/listShutdownTagStatistics`, {
      method: 'POST',
      data: requstData,
      requestType: 'json',
    }).then((res) => {
      setloading(false);
      if (res.code == 200) {
        setData({
          list: res.data.list || [],
          total: res.data.total || 0,
          current: params?.current || 1,
          pageSize: params?.pageSize || 10,
        });
      } else {
        setData({
          list: [],
          total: 0,
          current: params?.current || 1,
          pageSize: params?.pageSize || 10,
        });
      }
    });
    setParams(params);
  };

  //查询
  const handleSearch = () => {
    validateFields((err, { months, ...rest }) => {
      if (err) {
        return;
      }
      const dateFormate = timeUnit == 'month' ? 'YYYY-MM' : 'YYYY-MM-DD';
      const newParams = {
        ...params,
        startTime: months && months.length > 0 ? moment(months[0]).format(dateFormate) : '',
        endTime: months && months.length > 0 ? moment(months[1]).format(dateFormate) : '',
        ...rest,
      };
      getCityShutdownAndResumeAndWhiteCount(newParams);
      // setCurrent(1)
    });
  };

  //表格切换
  const handlePaginationTable = (pagination) => {
    // setCurrent(pagination.current);
    const newParms = { ...params, current: pagination.current, pageSize: pagination.pageSize };
    setParams(newParms);
  };

  const handleReset = () => {
    resetFields();
    setParams({ ...defaultParams });
    getCityShutdownAndResumeAndWhiteCount({ ...defaultParams });
  };

  const changeDate = (value) => {
    setFieldsValue({ months: value });
  };

  const changePanel = (v) => {
    setFieldsValue({ months: v });
  };
  const changeGraininess = (value) => {
    // setFieldsValue({ months: undefined });
    if (value == 'day') {
      setFieldsValue({
        months: [moment(defaultStartDay), moment()],
      });
    } else {
      setFieldsValue({
        months: [moment(), moment()],
      });
    }
  };
  useEffect(() => {
    getCityShutdownAndResumeAndWhiteCount({ ...params });
    getAllFraudType();
  }, []);

  return (
    <Card bordered={false}>
      <Form {...formItemLayout}>
        <Row>
          <Col span={8}>
            <Form.Item label="颗粒度">
              {getFieldDecorator('timeUnit', {
                initialValue: params.timeUnit,
                rules: [{ required: true, message: '颗粒度必填' }],
              })(
                <Select placeholder="请选择" allowClear onChange={changeGraininess}>
                  <Option value="month">按月</Option>
                  <Option value="day">按天</Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="起止日期">
              {getFieldDecorator('months', {
                initialValue:
                  timeUnit == 'month'
                    ? [moment(params.startTime, 'YYYY-MM'), moment(params.endTime, 'YYYY-MM')]
                    : [moment(defaultStartDay, 'YYYY-MM-DD'), moment(defaultEndDay, 'YYYY-MM-DD')],
                rules: [{ required: true, message: '起止日期必填' }],
              })(
                timeUnit == 'month' ? (
                  <RangePicker
                    format={'YYYY-MM'}
                    mode={['month', 'month']}
                    onPanelChange={changePanel}
                    onChange={changeDate}
                  />
                ) : (
                  <RangePicker format={'YYYY-MM-DD'} />
                ),
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="统计维度">
              {getFieldDecorator('dimension', {
                initialValue: params?.dimension,
                rules: [{ required: true, message: '统计维度必填' }],
              })(
                <Select placeholder="请选择" allowClear>
                  {StatisticalDimension?.map((item, index) => (
                    <Option value={item.value} key={index}>
                      {item.key}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={'涉诈类型'}>
              {getFieldDecorator('fraudType')(
                <Select
                  onChange={(value) => {
                    getShutdownTagListByFraudType(value);
                  }}
                  placeholder="请选择"
                  allowClear
                >
                  {AllFraudType?.map((item, index) => (
                    <Option value={item} key={index}>
                      {item}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          {(getFieldValue('dimension') == 'shutdownTag' ||
            getFieldValue('dimension') == 'subTag') && (
            <Col span={8}>
              <Form.Item label={'关停标签'}>
                {getFieldDecorator('shutdownTag')(
                  <Select placeholder="请选择" allowClear>
                    {ShutdownTagListByFraudType?.map((item, index) => (
                      <Option value={item} key={index}>
                        {item}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          )}
          {getFieldValue('dimension') == 'subTag' && (
            <Col span={8}>
              <Form.Item label={'子标签'}>
                {getFieldDecorator('subTag')(<Input placeholder="请填写"></Input>)}
              </Form.Item>
            </Col>
          )}
          <Col span={8} style={{ textAlign: 'right', marginBottom: 24 }}>
            <Licensee license="shutdownTagStatistic_query">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ margin: '0px 10px' }}>
                重置
              </Button>
            </Licensee>
            <Licensee license="shutdownTagStatistic_export">
              <Button type="primary" onClick={handleExport}>
                导出
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={typecolumns(getFieldValue('dimension'))}
        loading={loading}
        data={{
          list: data.list,
          pagination: false,
          pagination: {
            current: params?.current || 1,
            pageSize: params?.pageSize || 10,
            total: data.total,
          },
        }}
        onChange={handlePaginationTable}
        rowKey="key"
        showSelectCount={false}
        rowSelectionProps={false}
        scroll={{ x: 'max-content' }}
      />
    </Card>
  );
};

export default connect(({ situationAnalysis, loading }) => ({
  situationAnalysis,
  loading: loading.effects['situationAnalysis/getCityShutdownAndResumeAndWhiteCount'],
}))(Form.create()(CityAndWhiteListStatistics));
