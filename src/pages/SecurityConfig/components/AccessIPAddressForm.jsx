import React from 'react';
import { Form, Radio, Row, Col, Button, Input } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const { TextArea } = Input;
const AccessIPAddressForm = Form.create()(
  class extends React.Component {
    componentDidMount() {
      const { form, formValues } = this.props;
      if (formValues) form.setFieldsValue({ ...formValues });
    }

    componentDidUpdate(prevProps) {
      if (JSON.stringify(prevProps.formValues) !== JSON.stringify(this.props.formValues)) {
        const { form, formValues } = this.props;
        form.setFieldsValue({ ...formValues });
      }
    }

    handleSubmit = (e) => {
      e.preventDefault();
      const { form, onSave } = this.props;
      form.validateFieldsAndScroll((err, values) => {
        if (err) {
          return;
        }

        if (onSave) onSave(values);
      });
    };
    compareToIP = (rule, value, callback) => {
      if (!value) callback();
      const textArray = value.toString().split('\n');
      const isValidIP = textArray.every((item) => {
        const reg =
          /((^\s*((([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]))\s*$)|(^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$))/;
        return reg.test(item?.split('/')?.[0] || '');
      });

      if (!isValidIP) {
        callback(
          formatMessage({
            id: 'securityconfig.ipAddress.pattern',
          }),
        );
      }

      callback();
    };

    render() {
      const { form } = this.props;
      const { getFieldDecorator } = form;
      return (
        <Form
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
        >
          <FormItem
            label={formatMessage({
              id: 'securityconfig.explain',
            })}
            style={{
              marginBottom: '-20px',
            }}
          >
            {formatMessage({
              id: 'securityconfig.explain.des',
            })}
          </FormItem>
          <FormItem
            label={formatMessage({
              id: 'securityconfig.accessRight',
            })}
          >
            <RadioGroup value={true}>
              <Radio value={true}>
                {formatMessage({
                  id: 'securityconfig.accessRight.value',
                })}
              </Radio>
            </RadioGroup>
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('ipAddress', {
                  rules: [
                    {
                      validator: this.compareToIP,
                    },
                  ],
                })(
                  <TextArea
                    rows={4}
                    placeholder={formatMessage({
                      id: 'securityconfig.ipAddress.placeholder',
                    })}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>

          <FormItem
            wrapperCol={{
              span: 16,
              offset: 8,
            }}
          >
            <Button type="primary" htmlType="submit" onClick={this.handleSubmit}>
              {formatMessage({
                id: 'securityconfig.submit',
              })}
            </Button>
          </FormItem>
        </Form>
      );
    }
  },
);
export default AccessIPAddressForm;
