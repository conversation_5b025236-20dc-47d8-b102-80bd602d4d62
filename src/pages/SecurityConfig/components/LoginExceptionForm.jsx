import React from 'react';
import { Form, Row, Col, Tooltip, Icon, Button, Input } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
const FormItem = Form.Item;
const LoginExceptionForm = Form.create()(
  // @ts-ignore
  class extends React.Component {
    componentDidMount() {
      const { form, formValues } = this.props;
      if (formValues) form.setFieldsValue({ ...formValues });
    }

    componentDidUpdate(prevProps) {
      if (JSON.stringify(prevProps.formValues) !== JSON.stringify(this.props.formValues)) {
        const { form, formValues } = this.props;
        const newFormValues = { ...formValues };
        delete newFormValues['enable'];
        form.setFieldsValue(newFormValues);
      }
    }

    handleSubmit = (e) => {
      e.preventDefault();
      const { form, onSave } = this.props;
      form.validateFieldsAndScroll((err, values) => {
        if (err) {
          return;
        }

        if (onSave) onSave(values);
      });
    };

    render() {
      const { form } = this.props;
      const { getFieldDecorator } = form;
      return (
        <Form
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
        >
          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.accountLoginMaxAttempts',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.accountLoginMaxAttempts.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('loginMaxAttempts', {
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'securityconfig.accountLoginMaxAttempts.required',
                      }),
                    },
                    {
                      pattern: /^[1-9][0-9]*$/,
                      message: formatMessage({
                        id: 'securityconfig.accountLoginMaxAttempts.pattern',
                      }),
                    },
                  ],
                })(
                  <Input // style={{width: '100%'}}
                    // precision={0}
                    // min={1}
                    // step={1}
                    placeholder={formatMessage({
                      id: 'securityconfig.input.placeholder',
                    })}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>
          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.accountLoginLockDelayMaxTime',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.accountLoginLockDelayMaxTime.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('loginDelayMaxTime', {
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'securityconfig.accountLoginLockDelayMaxTime.required',
                      }),
                    },
                    {
                      pattern: /^[1-9][0-9]*$/,
                      message: formatMessage({
                        id: 'securityconfig.accountLoginLockDelayMaxTime.pattern',
                      }),
                    },
                  ],
                })(
                  <Input // style={{width: '100%'}}
                    // precision={0}
                    // min={1}
                    // step={1}
                    placeholder={formatMessage({
                      id: 'securityconfig.input.placeholder',
                    })}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>
          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.freeTime',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.freeTime.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('sessionTimeout', {
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'securityconfig.freeTime.required',
                      }),
                    },
                    {
                      pattern: /^([1-5][0-9]|60)$/,
                      message: formatMessage({
                        id: 'securityconfig.freeTime.pattern',
                      }),
                    },
                  ],
                })(
                  <Input // style={{width: '100%'}}
                    // precision={0}
                    // min={10}
                    // max={60}
                    // step={1}
                    placeholder={formatMessage({
                      id: 'securityconfig.input.placeholder',
                    })}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>

          <FormItem
            wrapperCol={{
              span: 16,
              offset: 8,
            }}
          >
            <Button type="primary" htmlType="submit" onClick={this.handleSubmit}>
              {formatMessage({
                id: 'securityconfig.submit',
              })}
            </Button>
          </FormItem>
        </Form>
      );
    }
  },
);
export default LoginExceptionForm;
