import React from 'react';
import { Form, Input, Radio, Row, Col, Switch, Tooltip, Icon, Button } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const AddNodeForm = Form.create()(
  // @ts-ignore
  class extends React.Component {
    componentDidMount() {
      const { form, formValues } = this.props;
      if (formValues) form.setFieldsValue({ ...formValues });
    }

    componentDidUpdate(prevProps) {
      if (JSON.stringify(prevProps.formValues) !== JSON.stringify(this.props.formValues)) {
        const { form, formValues } = this.props;
        const newFormValues = { ...formValues }; // @ts-ignore

        delete newFormValues['enable'];
        form.setFieldsValue({
          ...newFormValues,
          matchHistoryNum: formValues?.matchHistoryEnable ? formValues?.matchHistoryNum : undefined,
          validity: formValues?.validityEnable ? formValues?.validity : undefined,
          expireWarn: formValues?.expireWarnEnable ? formValues?.expireWarn : undefined,
          minimumLength: formValues?.minimumLengthEnable ? formValues?.minimumLength : undefined,
        });
      }
    }

    handleSubmit = (e) => {
      e.preventDefault();
      const { form, onSave } = this.props;
      form.validateFieldsAndScroll((err, values) => {
        if (err) {
          return;
        }

        if (onSave) onSave(values);
      });
    };

    render() {
      const { form } = this.props;
      const { getFieldDecorator, getFieldValue, setFieldsValue } = form; // <FormItem
      //   label={formatMessage({
      //     id: 'securityconfig.enable',
      //   })}
      // >
      //   {getFieldDecorator('enable', {
      //     initialValue: formValues?.enable ?? true,
      //   })(
      //     <RadioGroup>
      //       <Radio value={true}>
      //         {formatMessage({
      //           id: 'securityconfig.enable.yes',
      //         })}
      //       </Radio>
      //       <Radio value={false}>
      //         {formatMessage({
      //           id: 'securityconfig.enable.no',
      //         })}
      //       </Radio>
      //     </RadioGroup>,
      //   )}
      // </FormItem>

      return (
        <Form
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
        >
          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.changePasswordFirstLogin',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.changePasswordFirstLogin.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator(
                  'changePasswordFirstLogin',
                  {},
                )(
                  <RadioGroup>
                    <Radio value={true}>
                      {formatMessage({
                        id: 'securityconfig.changePasswordFirstLogin.yes',
                      })}
                    </Radio>
                    <Radio value={false}>
                      {formatMessage({
                        id: 'securityconfig.changePasswordFirstLogin.no',
                      })}
                    </Radio>
                  </RadioGroup>,
                )}
              </Col>
            </Row>
          </FormItem>
          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.history-password-record',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.history-password-record.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('matchHistoryNum', {
                  rules: [
                    {
                      required: getFieldValue('matchHistoryEnable'),
                      message: formatMessage({
                        id: 'securityconfig.history-password-record.required',
                      }),
                    },
                    {
                      pattern: /^([0-6])$/,
                      message: formatMessage({
                        id: 'securityconfig.history-password-record.pattern',
                      }),
                    },
                  ],
                })(
                  <Input
                    disabled={!getFieldValue('matchHistoryEnable')}
                    placeholder={
                      !getFieldValue('matchHistoryEnable')
                        ? ''
                        : formatMessage({
                          id: 'securityconfig.input.placeholder',
                        })
                    }
                  />,
                )}
              </Col>

              <Col span={11}>
                {getFieldDecorator('matchHistoryEnable', {
                  valuePropName: 'checked',
                })(
                  <Switch
                    onChange={(e) => {
                      if (!e) {
                        setFieldsValue({
                          matchHistoryNum: undefined,
                        });
                      } else {
                        setFieldsValue({
                          matchHistoryNum: 3,
                        });
                      }
                    }}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>

          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.validity',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.validity.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('validity', {
                  rules: [
                    {
                      required: getFieldValue('validityEnable'),
                      message: formatMessage({
                        id: 'securityconfig.validity.required',
                      }),
                    },
                    {
                      pattern: /^\+?[1-9]{1}[0-9]{0,2}\d{0,0}$/,
                      message: formatMessage({
                        id: 'securityconfig.validity.pattern',
                      }),
                    },
                  ],
                })(
                  <Input
                    disabled={!getFieldValue('validityEnable')}
                    placeholder={
                      !getFieldValue('validityEnable')
                        ? ''
                        : formatMessage({
                          id: 'securityconfig.input.placeholder',
                        })
                    }
                  />,
                )}
              </Col>

              <Col span={11}>
                {getFieldDecorator('validityEnable', {
                  valuePropName: 'checked',
                })(
                  <Switch
                    onChange={(e) => {
                      if (!e) {
                        setFieldsValue({
                          validity: undefined,
                        });
                      } else {
                        setFieldsValue({
                          validity: 90,
                        });
                      }
                    }}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>

          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.expireWarn',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.expireWarn.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('expireWarn', {
                  rules: [
                    {
                      required: getFieldValue('expireWarnEnable'),
                      message: formatMessage({
                        id: 'securityconfig.expireWarn.required',
                      }),
                    },
                    {
                      pattern: /^\+?[1-9]{1}[0-9]{0,2}\d{0,0}$/,
                      message: formatMessage({
                        id: 'securityconfig.expireWarn.pattern',
                      }),
                    },
                  ],
                })(
                  <Input
                    disabled={!getFieldValue('expireWarnEnable')}
                    placeholder={
                      !getFieldValue('expireWarnEnable')
                        ? ''
                        : formatMessage({
                          id: 'securityconfig.input.placeholder',
                        })
                    }
                  />,
                )}
              </Col>

              <Col span={11}>
                {getFieldDecorator('expireWarnEnable', {
                  valuePropName: 'checked',
                })(
                  <Switch
                    onChange={(e) => {
                      if (!e) {
                        setFieldsValue({
                          expireWarn: undefined,
                        });
                      } else {
                        setFieldsValue({
                          expireWarn: 15,
                        });
                      }
                    }}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>

          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'securityconfig.minimumLength',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'securityconfig.minimumLength.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
          >
            <Row gutter={8}>
              <Col span={12}>
                {getFieldDecorator('minimumLength', {
                  rules: [
                    {
                      required: getFieldValue('minimumLengthEnable'),
                      message: formatMessage({
                        id: 'securityconfig.minimumLength.required',
                      }),
                    },
                    {
                      pattern: /^8$|^9$|^1[0-5]$/,
                      message: formatMessage({
                        id: 'securityconfig.minimumLength.pattern',
                      }),
                    },
                  ],
                })(
                  <Input
                    disabled={!getFieldValue('minimumLengthEnable')}
                    placeholder={
                      !getFieldValue('minimumLengthEnable')
                        ? ''
                        : formatMessage({
                          id: 'securityconfig.input.placeholder',
                        })
                    }
                  />,
                )}
              </Col>
              <Col span={11}>
                {getFieldDecorator('minimumLengthEnable', {
                  valuePropName: 'checked',
                })(
                  <Switch
                    onChange={(e) => {
                      if (!e) {
                        setFieldsValue({
                          minimumLength: undefined,
                        });
                      } else {
                        setFieldsValue({
                          minimumLength: 8,
                        });
                      }
                    }}
                  />,
                )}
              </Col>
            </Row>
          </FormItem>
          <FormItem
            label={formatMessage({
              id: 'securityconfig.passwordComplexity',
            })}
          >
            <Row>
              <Col span={14}>
                {formatMessage({
                  id: 'securityconfig.passwordComplexity.des',
                })}
              </Col>
            </Row>
          </FormItem>
          <FormItem
            label={formatMessage({
              id: 'securityconfig.passwordFreezingRequirement',
            })}
          >
            <Row>
              <Col span={14}>
                {formatMessage({
                  id: 'securityconfig.passwordFreezingRequirement.des',
                })}
              </Col>
            </Row>
          </FormItem>
          <FormItem
            wrapperCol={{
              span: 16,
              offset: 8,
            }}
          >
            <Button type="primary" htmlType="submit" onClick={this.handleSubmit}>
              {formatMessage({
                id: 'securityconfig.submit',
              })}
            </Button>
          </FormItem>
        </Form>
      );
    }
  },
);
export default AddNodeForm;
