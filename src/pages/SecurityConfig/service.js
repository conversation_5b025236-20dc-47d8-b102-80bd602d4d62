import request from '@/utils/request';
// 密码策略查询
export async function getPasswordPolicy() {
  return request('/api/dict/passwordPolicy', {
    method: 'GET',
  });
} // 密码策略配置

export async function savePasswordPolicy(params) {
  return request('/api/dict/passwordPolicy', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
} // 查询允许访问的IP地址接口

export async function getAllowAccessIPAddress() {
  return request('/api/dict/allowAccessIPAddress', {
    method: 'GET',
  });
} // 修改允许访问的IP地址接口

export async function saveAllowAccessIPAddress(params) {
  return request('/api/dict/allowAccessIPAddress', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
} // 登录策略查询

export async function getAuthenticationPolicy() {
  return request('/api/dict/authenticationPolicy', {
    method: 'GET',
  });
} // 登录策略配置

export async function saveAuthenticationPolicy(params) {
  return request('/api/dict/authenticationPolicy', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
