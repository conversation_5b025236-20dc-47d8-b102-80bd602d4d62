let globalMock = null;

try {
  delete require.cache[require.resolve('../../mock')]; // eslint-disable-next-line global-require

  globalMock = require('../../mock'); // eslint-disable-next-line no-empty
} catch (e) {}

export default {
  ...globalMock,
  'GET /api/dict/passwordPolicy': {
    enable: true,
    changePasswordFirstLogin: true,
    matchHistoryNum: 6,
    validity: 100,
    expireWarn: 99,
    minimumLength: 8,
    matchHistoryEnable: false,
    validityEnable: true,
    expireWarnEnable: true,
    minimumLengthEnable: true,
  },
  'POST /api/dict/passwordPolicy': {
    state: 'SUCCESS',
    message: '设置成功',
  },
  'GET /api/dict/allowAccessIPAddress': (req, res) => {
    res.send('*******\n2.2.2.2');
  },
  'POST /api/dict/allowAccessIPAddress': {
    state: 'SUCCESS',
    message: '设置成功',
  },
  'GET /api/dict/authenticationPolicy': {
    enable: true,
    loginMaxAttempts: 4,
    loginDelayMaxTime: 11,
    sessionTimeout: 30,
  },
  'POST /api/dict/authenticationPolicy': {
    state: 'SUCCESS',
    message: '设置成功',
  },
};
