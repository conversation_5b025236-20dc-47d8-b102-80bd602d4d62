export default {
  'securityconfig.submit': 'Submit',
  'securityconfig.passwordpolicy': 'Password Policy',
  'securityconfig.loginLimit': 'Login Limit',
  'securityconfig.loginLimit.accessIPAddress': 'Source IP login limit',
  'securityconfig.loginLimit.loginException': 'Login exception management',
  'securityconfig.enable': 'Whether to enable password policy',
  'securityconfig.enable.yes': 'Yes',
  'securityconfig.enable.no': 'No',
  'securityconfig.input.placeholder': 'Please enter',
  'securityconfig.changePasswordFirstLogin': 'Forced to change the password for the first login',
  'securityconfig.changePasswordFirstLogin.yes': 'Yes',
  'securityconfig.changePasswordFirstLogin.no': 'No',
  'securityconfig.changePasswordFirstLogin.tooltip':
    'If you select "Yes", the user clicks to login when logging ]in for the first time to enter the mandatory password modification page',
  'securityconfig.history-password-record': 'Password history record',
  'securityconfig.history-password-record.tooltip': 'The range is 0～6, the default value is "3"',
  'securityconfig.history-password-record.required': 'This field is required!',
  'securityconfig.history-password-record.pattern': 'Please enter data within the correct range!',
  'securityconfig.validity': 'Password validity period (days)',
  'securityconfig.validity.tooltip': 'The range is 1～999, the default value is "90"',
  'securityconfig.validity.required': 'This field is required! ',
  'securityconfig.validity.pattern': 'Please enter data within the correct range!',
  'securityconfig.expireWarn': 'Remind before expiration (days)',
  'securityconfig.expireWarn.tooltip': 'The range is 1～999, the default value is "15"',
  'securityconfig.expireWarn.required': 'This field is required! ',
  'securityconfig.expireWarn.pattern': 'Please enter data within the correct range!',
  'securityconfig.minimumLength': 'Minimum password length',
  'securityconfig.minimumLength.tooltip': 'The range is 8-15, the default is "8"',
  'securityconfig.minimumLength.required': 'This field is required!',
  'securityconfig.minimumLength.pattern': 'Please enter data within the correct range!',
  'securityconfig.passwordComplexity': 'Password complexity description',
  'securityconfig.passwordComplexity.des':
    'Password must meet the following requirements:\n' +
    'Cannot contain the username,\n' +
    'Contains at least three of the following four types of characters: \n' +
    'English uppercase letters (A-Z)\n' +
    'English lowercase letters (a-z)\n' +
    'Number (0-9)\n' +
    'Special characters (such as: @, #, ￥, %)',
  'securityconfig.explain': 'Description',
  'securityconfig.explain.des': 'All addresses are accessible by default',
  'securityconfig.accessRight': 'Access Rights',
  'securityconfig.accessRight.value': 'Authorized access',
  'securityconfig.ipAddress.placeholder':
    'Please enter the IP address, separated by line breaks, maximum 1000 lines',
  'securityconfig.ipAddress.required': 'This field is required!',
  'securityconfig.ipAddress.pattern': 'Please enter data within the correct range!',
  'securityconfig.accountLoginMaxAttempts': 'Number of failures lock threshold',
  'securityconfig.accountLoginMaxAttempts.tooltip':
    'The range is greater than or equal to 1, and the default value is 10',
  'securityconfig.accountLoginMaxAttempts.required': 'This field is required!',
  'securityconfig.accountLoginMaxAttempts.pattern': 'Please enter data within the correct range!',
  'securityconfig.accountLoginLockDelayMaxTime': 'Lock duration threshold (seconds)',
  'securityconfig.accountLoginLockDelayMaxTime.tooltip':
    'The range is greater than or equal to 1, and the default value is 60',
  'securityconfig.accountLoginLockDelayMaxTime.required': 'This field is required!',
  'securityconfig.accountLoginLockDelayMaxTime.pattern':
    'Please enter data within the correct range!',
  'securityconfig.freeTime': 'Free time (minutes)',
  'securityconfig.freeTime.tooltip': 'The range is 10～60, the default value is 30',
  'securityconfig.freeTime.required': 'This field is required!',
  'securityconfig.freeTime.pattern': 'Please enter data within the correct range!',
};
