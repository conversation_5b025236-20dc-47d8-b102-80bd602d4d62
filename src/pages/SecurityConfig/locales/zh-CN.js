export default {
  'securityconfig.submit': '应用',
  'securityconfig.passwordpolicy': '密码策略',
  'securityconfig.loginLimit': '登录限制',
  'securityconfig.loginLimit.accessIPAddress': '源IP登录限制',
  'securityconfig.loginLimit.loginException': '登录异常管理',
  'securityconfig.enable': '是否开启密码策略',
  'securityconfig.enable.yes': '是',
  'securityconfig.enable.no': '否',
  'securityconfig.input.placeholder': '请输入',
  'securityconfig.changePasswordFirstLogin': '初次登录强制修改密码',
  'securityconfig.changePasswordFirstLogin.yes': '是',
  'securityconfig.changePasswordFirstLogin.no': '否',
  'securityconfig.changePasswordFirstLogin.tooltip':
    '若选择了“是”，则用户在第一次登录时点击登录进入强制修改密码页面',
  'securityconfig.history-password-record': '密码历史记录',
  'securityconfig.history-password-record.tooltip': '范围是0～6，默认值为“3”',
  'securityconfig.history-password-record.required': '该字段为必填字段！',
  'securityconfig.history-password-record.pattern': '请输入正确范围内的数据！',
  'securityconfig.validity': '密码有效期(天)',
  'securityconfig.validity.tooltip': '范围是1～999，默认值为“90”',
  'securityconfig.validity.required': '该字段为必填字段！',
  'securityconfig.validity.pattern': '请输入正确范围内的数据！',
  'securityconfig.expireWarn': '过期前提醒(天)',
  'securityconfig.expireWarn.tooltip': '范围是1～999，默认值为“15”',
  'securityconfig.expireWarn.required': '该字段为必填字段！',
  'securityconfig.expireWarn.pattern': '请输入正确范围内的数据！',
  'securityconfig.minimumLength': '最小密码长度',
  'securityconfig.minimumLength.tooltip': '范围是8-15，默认是为“8”',
  'securityconfig.minimumLength.required': '该字段为必填字段！',
  'securityconfig.minimumLength.pattern': '请输入正确范围内的数据！',
  'securityconfig.passwordComplexity': '密码复杂度说明',
  'securityconfig.passwordComplexity.des':
    '密码必须符合下列最低要求：\n' +
    '不能包含用户的账号名，\n' +
    '至少包含以下四类字符中的三类字符：\n' +
    '英文大写字母（A-Z）\n' +
    '英文小写字母（a-z）\n' +
    '数字（0-9）\n' +
    '特殊字符（如：@、#、￥、%）',
  'securityconfig.passwordFreezingRequirement': '密码冻结及锁定要求',
  'securityconfig.passwordFreezingRequirement.des':
    '1.账号三个月未登录冻结;\n' + '2.4个月未登录锁定。',
  'securityconfig.explain': '说明',
  'securityconfig.explain.des': '默认情况下所有地址都可以访问',
  'securityconfig.accessRight': '访问权限',
  'securityconfig.accessRight.value': '授权访问',
  'securityconfig.ipAddress.placeholder': '请输入IP地址，使用换行分隔，最大1000行',
  'securityconfig.ipAddress.required': '该字段为必填字段！',
  'securityconfig.ipAddress.pattern': '请输入正确范围内的数据！',
  'securityconfig.accountLoginMaxAttempts': '失败次数锁定阀值',
  'securityconfig.accountLoginMaxAttempts.tooltip': '范围是大于等于1，默认值为10',
  'securityconfig.accountLoginMaxAttempts.required': '该字段为必填字段！',
  'securityconfig.accountLoginMaxAttempts.pattern': '请输入正确范围内的数据！',
  'securityconfig.accountLoginLockDelayMaxTime': '锁定时长阀值（秒）',
  'securityconfig.accountLoginLockDelayMaxTime.tooltip': '范围是大于等于1，默认值为60',
  'securityconfig.accountLoginLockDelayMaxTime.required': '该字段为必填字段！',
  'securityconfig.accountLoginLockDelayMaxTime.pattern': '请输入正确范围内的数据！',
  'securityconfig.freeTime': '空闲时间（分）',
  'securityconfig.freeTime.tooltip': '范围是10～60，默认值为30',
  'securityconfig.freeTime.required': '该字段为必填字段！',
  'securityconfig.freeTime.pattern': '请输入正确范围内的数据！',
};
