import React, { useEffect, useState } from 'react';
import { Card, message, Tabs, Spin } from 'antd';
import { withSilence } from 'demasia-pro-layout';
import { refreshAuth } from 'ponshine';
import PasswordPolicyForm from './components/PasswordPolicyForm';
import AccessIPAddressForm from './components/AccessIPAddressForm';
import LoginExceptionForm from './components/LoginExceptionForm';
import {
  getPasswordPolicy,
  savePasswordPolicy,
  getAllowAccessIPAddress,
  saveAllowAccessIPAddress,
  getAuthenticationPolicy,
  saveAuthenticationPolicy,
} from './service';
import { formatMessage } from 'ponshine-plugin-react/locale';
const { TabPane } = Tabs;

const SecurityConfig = () => {
  const [passwordPolicy, setPasswordPolicy] = useState(undefined);
  const [allowAccessIPAddress, setAllowAccessIPAddress] = useState(undefined);
  const [authenticationPolicy, setAuthenticationPolicy] = useState(undefined);
  const [loading, setLoading] = useState(true);
  const [loading1, setLoading1] = useState(false);
  const [loading2, setLoading2] = useState(false);
  const [loading3, setLoading3] = useState(false);
  const [loading4, setLoading4] = useState(false);
  useEffect(() => {
    setLoading(loading1 || loading2 || loading3 || loading4);
  }, [loading1, loading2, loading3, loading4]);
  /**
   * 查询密码策略
   */

  const handleQueryPasswordPolicy = async () => {
    setLoading1(true);
    const response = await getPasswordPolicy();
    setLoading1(false);
    setPasswordPolicy(response);
  };
  /**
   * 配置密码策略
   */

  const handleSavePasswordPolicy = async (values) => {
    setLoading4(true);
    const response = await savePasswordPolicy({
      enable: true,
      ...values,
    });
    setLoading4(false);

    if (response?.state === 'SUCCESS') {
      refreshAuth();
      message.success(response.message);
    } else {
      message.error(response.message);
    }
  };
  /**
   * 查询源IP登录限制
   */

  const handleQueryAllowAccessIPAddress = async () => {
    setLoading2(true);
    const response = await getAllowAccessIPAddress();
    setLoading2(false); // @ts-ignore

    setAllowAccessIPAddress({
      ipAddress: response ?? '',
    });
  };
  /**
   * 配置源IP登录限制
   */

  const handleSaveAllowAccessIPAddress = async (values) => {
    setLoading4(true);
    const response = await saveAllowAccessIPAddress(values);
    setLoading4(false);

    if (response?.state === 'SUCCESS') {
      message.success(response.message);
    } else {
      message.error(response.message);
    }
  };
  /**
   * 查询登录异常管理
   */

  const handleQueryAuthenticationPolicy = async () => {
    setLoading3(true);
    const response = await getAuthenticationPolicy();
    setLoading3(false);
    setAuthenticationPolicy(response);
  };
  /**
   * 配置登录异常管理
   */

  const HandleSaveAuthenticationPolicy = async (values) => {
    setLoading4(true);
    const response = await saveAuthenticationPolicy({
      enable: true,
      ...values,
    });
    setLoading4(false);

    if (response?.state === 'SUCCESS') {
      message.success(response.message);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    handleQueryPasswordPolicy();
    handleQueryAllowAccessIPAddress();
    handleQueryAuthenticationPolicy();
  }, []);
  return (
    <Spin spinning={loading}>
      <Card>
        <Tabs defaultActiveKey="1">
          <TabPane
            tab={formatMessage({
              id: 'securityconfig.passwordpolicy',
            })}
            key="1"
          >
            <Card>
              <PasswordPolicyForm formValues={passwordPolicy} onSave={handleSavePasswordPolicy} />
            </Card>
          </TabPane>
          <TabPane
            tab={formatMessage({
              id: 'securityconfig.loginLimit',
            })}
            key="2"
          >
            <Card
              title={formatMessage({
                id: 'securityconfig.loginLimit.accessIPAddress',
              })}
              style={{
                marginBottom: '20px',
              }}
            >
              <AccessIPAddressForm
                formValues={allowAccessIPAddress}
                onSave={handleSaveAllowAccessIPAddress}
              />
            </Card>
            <Card
              title={formatMessage({
                id: 'securityconfig.loginLimit.loginException',
              })}
            >
              <LoginExceptionForm
                formValues={authenticationPolicy}
                onSave={HandleSaveAuthenticationPolicy}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </Spin>
  );
};

export default withSilence(SecurityConfig);
