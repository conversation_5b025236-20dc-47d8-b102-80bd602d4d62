export default {
  'usermanage.columns.username': 'Username',
  'usermanage.columns.nickname': 'Nickname',
  'usermanage.columns.phonenum': 'Phone',
  'usermanage.columns.email': 'Email',
  'usermanage.columns.roleName': 'User Group',
  'usermanage.columns.createTime': 'Create Time',
  'usermanage.columns.creator': 'Creator',
  'usermanage.columns.modifyTime': 'Modify Time',
  'usermanage.columns.modifier': 'Modifier',
  'usermanage.columns.enabled': 'Status',
  'usermanage.columns.enabled.default': 'Disabled',
  'usermanage.columns.enabled.success': 'Activate',
  'usermanage.columns.locked': 'Lock status',
  'usermanage.columns.locked.error': 'Locked',
  'usermanage.columns.locked.success': 'Not locked',
  'usermanage.columns.attempts': 'Number of login failures',
  'usermanage.columns.lockExpirationTime': 'Lock expiration time',
  'usermanage.columns.action': 'Action',
  'usermanage.showTotal': '{start}-{end} of {total} items',
  'usermanage.selected': 'Selected {all} item, of which {locked} item is locked',
  'usermanage.add': 'Add',
  'usermanage.add.title': 'Add user',
  'usermanage.add.success.message': 'Add success!',
  'usermanage.add.error.message': 'Add failed!',
  'usermanage.edit': 'Edit',
  'usermanage.edit.title': 'Edit user',
  'usermanage.edit.success.message': 'Edit successfully!',
  'usermanage.edit.error.message': 'Edit failed!',
  'usermanage.delete': 'Delete',
  'usermanage.delete.title': 'Delete user',
  'usermanage.delete.confirm': 'Are you sure you want to delete the selected user?',
  'usermanage.delete.success.message': 'Delete successfully!',
  'usermanage.unlock': 'Unlock',
  'usermanage.unlock.title': 'Unlock user',
  'usermanage.unlock.confirm': 'Are you sure you want to unlock the selected user?',
  'usermanage.unlock.success.message': 'Unlock successfully!',
  'usermanage.basicInformation': 'Basic Information',
  'usermanage.basicInformation.username': 'Username',
  'usermanage.basicInformation.username.required': 'This field is required!',
  'usermanage.basicInformation.username.maxLength':
    'User name cannot be greater than 20 characters! ',
  'usermanage.basicInformation.username.placeholder': 'Please enter',
  'usermanage.basicInformation.password': 'Password',
  'usermanage.basicInformation.password.tooltip':
    'The password must meet the following requirements: It cannot contain the username, and at least contains three of the following four types of characters: English uppercase letters (A-Z) English lowercase letters (a-z) Numbers (0-9) Special characters (such as: @,#,￥,%)',
  'usermanage.basicInformation.password.required': 'This field is required!',
  'usermanage.basicInformation.password.minLength':
    'The password cannot be less than {minimumLength}!',
  'usermanage.basicInformation.password.callback': 'The password does not meet the rules!',
  'usermanage.basicInformation.password.placeholder': 'Please enter',
  'usermanage.basicInformation.confirm': 'Confirm password',
  'usermanage.basicInformation.confirm.required': 'This field is required!',
  'usermanage.basicInformation.confirm.callback':
    'Does not match the last password, please confirm! ',
  'usermanage.basicInformation.confirm.placeholder': 'Please enter',
  'usermanage.basicInformation.nickname': 'Nickname',
  'usermanage.basicInformation.nickname.tooltip': 'Maximum 20 Chinese and English characters',
  'usermanage.basicInformation.nickname.required': 'This field is required!',
  'usermanage.basicInformation.nickname.maxLength':
    'The nickname cannot be greater than 20 characters!',
  'usermanage.basicInformation.nickname.pattern':
    'The input format is wrong, only Chinese and English can be input!',
  'usermanage.basicInformation.nickname.placeholder': 'Please enter',
  'usermanage.basicInformation.phonenum': 'Phone',
  'usermanage.basicInformation.phonenum.required': 'This field is required!',
  'usermanage.basicInformation.phonenum.pattern': 'Mobile phone number format is incorrect!',
  'usermanage.basicInformation.phonenum.placeholder': 'Please enter',
  'usermanage.basicInformation.email': 'Email',
  'usermanage.basicInformation.email.required': 'This field is required!',
  'usermanage.basicInformation.email.pattern': 'The email format is incorrect!',
  'usermanage.basicInformation.email.placeholder': 'Please enter',
  'usermanage.basicInformation.enabled': 'User Status',
  'usermanage.basicInformation.enabled.success': 'Enable',
  'usermanage.basicInformation.enabled.default': 'Disabled',
  'usermanage.userGroup': 'User Group',
  'usermanage.userGroup.callback': 'Need to select a user group!',
  'usermanage.password.modal.modify': 'Modify',
  'usermanage.password.modal.title': 'Modify password',
  'usermanage.password.modal.compareToFirstPassword':
    'Does not match the last password, please confirm!',
  'usermanage.password.modal.password.label': 'Password',
  'usermanage.password.modal.password.placeholder': 'Please enter a new password',
  'usermanage.password.modal.password.tooltip':
    'The password must meet the following requirements: It cannot contain the username, and at least contains three of the following four types of characters: English uppercase letters (A-Z) English lowercase letters (a-z) Numbers (0-9) Special characters (such as: @,#,￥,%)',
  'usermanage.password.modal.password.required': 'This field cannot be empty!',
  'usermanage.password.modal.password.minLength':
    'The password cannot be less than {minimumLength}!',
  'usermanage.password.modal.password.pattern':
    'Password complexity does not meet the requirements!',
  'usermanage.password.modal.confirm.label': 'Confirm password',
  'usermanage.password.modal.confirm.placeholder':
    'Please enter the new password again to ensure that the new password is the same two times',
  'usermanage.password.modal.confirm.required': 'This field cannot be empty!',
  'usermanage.phonenum.description': 'Mobile phone has been bound:',
  'usermanage.phonenum.modal.modify': 'Modify',
  'usermanage.phonenum.modal.title': 'Modify phone number',
  'usermanage.phonenum.modal.label': 'Modify phone',
  'usermanage.phonenum.modal.required': 'This field is required!',
  'usermanage.phonenum.modal.pattern': 'Mobile phone number format is incorrect!',
  'usermanage.phonenum.modal.placeholder': 'Please enter a new phone number',
};
