export default {
  'usermanage.columns.username': '用户名',
  'usermanage.columns.nickname': '昵称',
  'usermanage.columns.phonenum': '手机号码',
  'usermanage.columns.email': '邮箱',
  'usermanage.columns.roleName': '用户组',
  'usermanage.columns.createTime': '创建时间',
  'usermanage.columns.creator': '创建人',
  'usermanage.columns.modifyTime': '修改时间',
  'usermanage.columns.modifier': '修改人',
  'usermanage.columns.enabled': '状态',
  'usermanage.columns.enabled.default': '禁用',
  'usermanage.columns.enabled.success': '激活',
  'usermanage.columns.locked': '锁定状态',
  'usermanage.columns.locked.error': '已锁定',
  'usermanage.columns.locked.success': '未锁定',
  'usermanage.columns.attempts': '登录失败次数',
  'usermanage.columns.lockExpirationTime': '锁定过期时间',
  'usermanage.columns.action': '操作',
  'usermanage.showTotal': '第{start}-{end}条/总共{total}条',
  'usermanage.selected': '已选择 {all} 项，其中已锁定 {locked} 项',
  'usermanage.add': '新增',
  'usermanage.add.title': '新增用户',
  'usermanage.add.success.message': '新增成功！',
  'usermanage.add.error.message': '新增失败！',
  'usermanage.edit': '编辑',
  'usermanage.edit.title': '编辑用户',
  'usermanage.edit.success.message': '编辑成功！',
  'usermanage.edit.error.message': '编辑失败！',
  'usermanage.delete': '删除',
  'usermanage.delete.title': '删除用户',
  'usermanage.delete.confirm': '确定删除所选用户吗？',
  'usermanage.delete.success.message': '删除成功！',
  'usermanage.unlock': '解锁',
  'usermanage.unlock.title': '解锁用户',
  'usermanage.unlock.confirm': '确定解锁所选用户吗？',
  'usermanage.unlock.success.message': '解锁成功！',
  'usermanage.basicInformation': '基本信息',
  'usermanage.basicInformation.username': '登录用户名',
  'usermanage.basicInformation.username.required': '该字段为必填字段！',
  'usermanage.basicInformation.username.maxLength': '用户名不能大于20位字符！',
  'usermanage.basicInformation.username.placeholder': '请输入',
  'usermanage.basicInformation.password': '登录密码',
  'usermanage.basicInformation.password.tooltip':
    `（一）确保口令满足以下通用原则
    1、口令至少由10位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；
    2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；
    3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；
    4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；
    5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；
    6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。
    （二）避免以下易猜解口令规则
    1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；
    2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；
    3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址为************和************,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；
    4、两个及以上弱口令（常用单词）的组合规则，如admin+root，admin+888888、、sun+123456、console+888888等；
    5、密码为帐号的反序，比如root/toor等`,
  'usermanage.basicInformation.password.required': '该字段为必填字段！',
  'usermanage.basicInformation.password.minLength': '密码不能小于{minimumLength}位！',
  'usermanage.basicInformation.password.callback': '密码不符合规则！',
  'usermanage.basicInformation.password.placeholder': '请输入',
  'usermanage.basicInformation.confirm': '确认密码',
  'usermanage.basicInformation.confirm.required': '该字段为必填字段！',
  'usermanage.basicInformation.confirm.callback': '与上一次密码不符，请确认！',
  'usermanage.basicInformation.confirm.placeholder': '请输入',
  'usermanage.basicInformation.nickname': '昵称',
  'usermanage.basicInformation.nickname.tooltip': '最大20个中英文字符',
  'usermanage.basicInformation.nickname.required': '该字段为必填字段！',
  'usermanage.basicInformation.nickname.maxLength': '昵称不能大于20位字符！',
  'usermanage.basicInformation.nickname.pattern': '输入格式错误，只能输入中英文！',
  'usermanage.basicInformation.nickname.placeholder': '请输入',
  'usermanage.basicInformation.phonenum': '手机号码',
  'usermanage.basicInformation.phonenum.required': '该字段为必填项！',
  'usermanage.basicInformation.phonenum.pattern': '手机号格式不正确！',
  'usermanage.basicInformation.phonenum.placeholder': '请输入',
  'usermanage.basicInformation.email': '邮箱',
  'usermanage.basicInformation.email.required': '该字段为必填字段！',
  'usermanage.basicInformation.email.pattern': '邮箱格式不对！',
  'usermanage.basicInformation.email.placeholder': '请输入',
  'usermanage.basicInformation.enabled': '用户状态',
  'usermanage.basicInformation.enabled.success': '启用',
  'usermanage.basicInformation.enabled.default': '禁用',
  'usermanage.userGroup': '用户组',
  'usermanage.userGroup.callback': '需要选择一个用户组！',
  'usermanage.password.modal.modify': '修改',
  'usermanage.password.modal.title': '修改密码',
  'usermanage.password.modal.compareToFirstPassword': '与上一次密码不符，请确认！',
  'usermanage.password.modal.password.label': '输入新密码',
  'usermanage.password.modal.password.placeholder': '请输入新密码',
  'usermanage.password.modal.password.tooltip':
    `（一）确保口令满足以下通用原则
    1、口令至少由10位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；
    2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；
    3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；
    4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；
    5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；
    6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。
    （二）避免以下易猜解口令规则
    1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；
    2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；
    3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址为************和************,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；
    4、两个及以上弱口令（常用单词）的组合规则，如admin+root，admin+888888、、sun+123456、console+888888等；
    5、密码为帐号的反序，比如root/toor等`,
  'usermanage.password.modal.password.required': '该字段不能为空！',
  'usermanage.password.modal.password.minLength': '密码不能小于{minimumLength}位！',
  'usermanage.password.modal.password.pattern': '密码复杂度不符合要求！',
  'usermanage.password.modal.confirm.label': '再次输入新密码',
  'usermanage.password.modal.confirm.placeholder': '请再次输入新密码，确保2次新密码相同',
  'usermanage.password.modal.confirm.required': '该字段不能为空！',
  'usermanage.phonenum.description': '已绑定手机：',
  'usermanage.phonenum.modal.modify': '修改',
  'usermanage.phonenum.modal.title': '修改手机号',
  'usermanage.phonenum.modal.label': '输入新手机号',
  'usermanage.phonenum.modal.required': '该字段为必填项！',
  'usermanage.phonenum.modal.pattern': '手机号格式不正确！',
  'usermanage.phonenum.modal.placeholder': '请输入新手机号',
};
