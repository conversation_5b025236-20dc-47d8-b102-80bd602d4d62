import { But<PERSON>, Card, message, Modal } from 'antd';
import { withSilence } from 'demasia-pro-layout';
import ProTable from 'demasia-pro-table';
import { useAuth } from 'ponshine';
import React, { useState } from 'react';
import CreateModal from './components/CreateModal';
import { deleteUsers, getUsers, saveUser, unlockUsers } from './service';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { aesDecode } from '@/services/login';
import { exportFile } from '@/utils/utils';
// let tableColumn;

// try {
//   // eslint-disable-next-line global-require
//   tableColumn = require('./extra/tableColumn').default; // eslint-disable-next-line no-empty
// } catch (e) {}

const UserManage = () => {
  const { authState } = useAuth() || {};
  const passwordPolicy = authState?.passwordPolicy || {};
  const [selectedRowKeys, setSelectedKeys] = React.useState([]);
  const [opType, setOpType] = React.useState(null);
  const [editingUser, setEditingUser] = React.useState();
  const [lockedSelectedRowKeys, setLockedSelectedRowKeys] = React.useState([]);
  const actionRef = React.useRef();

  const [searchParams, setSearchParams] = useState({});

  const request = async (params, sort) => {
    const { current, pageSize, ...rest } = params;
    const sortKeys = Object.keys(sort);
    const response = await getUsers({
      ...rest,
      page: current - 1,
      size: pageSize,
      sort:
        sortKeys.length === 0
          ? params.sort
          : sortKeys.map((key) => `${key},${sort[key].replace('end', '')}`),
    });
    setSearchParams({
      ...rest,
      page: current - 1,
      size: pageSize,
      sort:
        sortKeys.length === 0
          ? params.sort
          : sortKeys.map((key) => `${key},${sort[key].replace('end', '')}`),
    });
    return Promise.resolve({
      data: response.items?.map((v) => {
        return {
          ...v,
          idCardNum: aesDecode(v.idCardNum),
          phonenum: aesDecode(v.phonenum),
        };
      }),
      success: true,
      total: response.totalNum,
    });
  };

  const handleAddUser = async (fieldsValues) => {
    try {
      const response = await saveUser(fieldsValues);

      if (response.state === 'SUCCESS') {
        message.success(
          formatMessage({
            id: 'usermanage.add.success.message',
          }),
        );
        actionRef.current?.reload();
        setOpType(null);
      } else {
        message.error(response.message);
      }
    } catch (e) {
      message.error(
        formatMessage({
          id: 'usermanage.add.error.message',
        }),
      );
    }
  };

  const handleUpdateUser = async (fieldsValues, doNotCloseModal) => {
    try {
      const response = await saveUser(fieldsValues);

      if (response.state === 'SUCCESS') {
        message.success(
          formatMessage({
            id: 'usermanage.edit.success.message',
          }),
        );
        actionRef.current?.reload();

        if (!doNotCloseModal) {
          setOpType(null);
        }

        return true;
      } else {
        message.error(response.message);
      }
    } catch (e) {
      message.error(
        formatMessage({
          id: 'usermanage.edit.error.message',
        }),
      );
    }

    return false;
  };

  const handleDelete = () => {
    Modal.confirm({
      maskClosable: false,
      title: formatMessage({
        id: 'usermanage.delete.title',
      }),
      content: formatMessage({
        id: 'usermanage.delete.confirm',
      }),
      onOk: async () => {
        const ids = selectedRowKeys;

        try {
          await deleteUsers({
            ids,
          });
          message.success(
            formatMessage({
              id: 'usermanage.delete.success.message',
            }),
          );
          actionRef.current?.reload();
          actionRef.current?.clearSelected();
        } catch (e) {
          // eslint-disable-next-line no-console
          console.error(e);
        }
      },
    });
  };
  const [exportLoading, setExportLoading] = useState(false);

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/user/exportUser',
      decode: true,
      params: searchParams,
      method: 'POST',
      requestType: 'form',
      stringifyOptions: {
        arrayFormat: 'indices', //数组传参时有下标
        allowDots: true, //代表点  list[0].id: 1
      },
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const handleDeleteSingle = (id) => {
    Modal.confirm({
      maskClosable: false,
      title: formatMessage({
        id: 'usermanage.delete.title',
      }),
      content: formatMessage({
        id: 'usermanage.delete.confirm',
      }),
      onOk: async () => {
        const ids = [id];

        try {
          await deleteUsers({
            ids,
          });
          message.success(
            formatMessage({
              id: 'usermanage.delete.success.message',
            }),
          );
          actionRef.current?.reload();
          actionRef.current?.clearSelected();
        } catch (e) {
          // eslint-disable-next-line no-console
          console.error(e);
        }
      },
    });
  };

  const handleUnlock = () => {
    Modal.confirm({
      maskClosable: false,
      title: formatMessage({
        id: 'usermanage.unlock.title',
      }),
      content: formatMessage({
        id: 'usermanage.unlock.confirm',
      }),
      onOk: async () => {
        const ids = lockedSelectedRowKeys;

        try {
          await unlockUsers({
            ids,
          });
          message.success(
            formatMessage({
              id: 'usermanage.unlock.success.message',
            }),
          );
          actionRef.current?.reload();
          actionRef.current?.clearSelected();
        } catch (e) {
          // eslint-disable-next-line no-console
          console.error(e);
        }
      },
    });
  };

  const handleUnlockSingle = (id) => {
    Modal.confirm({
      maskClosable: false,
      title: formatMessage({
        id: 'usermanage.unlock.title',
      }),
      content: formatMessage({
        id: 'usermanage.unlock.confirm',
      }),
      onOk: async () => {
        const ids = [id];

        try {
          await unlockUsers({
            ids,
          });
          message.success(
            formatMessage({
              id: 'usermanage.unlock.success.message',
            }),
          );
          actionRef.current?.reload();
          actionRef.current?.clearSelected();
        } catch (e) {
          // eslint-disable-next-line no-console
          console.error(e);
        }
      },
    });
  }; // @ts-ignore

  const columns = [
    {
      ellipsis: true,
      align: 'center',
      title: '员工工号',
      dataIndex: 'employeeId',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.username',
      }),
      dataIndex: 'username',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '姓名',
      dataIndex: 'realName',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.phonenum',
      }),
      dataIndex: 'phonenum',
      width: 120,
      hideInSearch: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.email',
      }),
      dataIndex: 'email',
      hideInSearch: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: '人力归属组织编码',
      dataIndex: 'sdeptcode',
      hideInSearch: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: '人力编码',
      dataIndex: 'ctHrCode',
      hideInSearch: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: '门户',
      dataIndex: 'userCode',
      hideInSearch: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.roleName',
      }),
      dataIndex: 'role.name',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.createTime',
      }),
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
      sorter: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.creator',
      }),
      dataIndex: 'creator',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.modifyTime',
      }),
      dataIndex: 'modifyTime',
      hideInSearch: true,
      valueType: 'dateTime',
      sorter: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.modifier',
      }),
      dataIndex: 'modifier',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.enabled',
      }),
      dataIndex: 'enabled',
      valueEnum: {
        false: {
          text: formatMessage({
            id: 'usermanage.columns.enabled.default',
          }),
          status: 'Default',
        },
        true: {
          text: formatMessage({
            id: 'usermanage.columns.enabled.success',
          }),
          status: 'Success',
        },
      },
      filters: undefined, // 取消筛选
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.locked',
      }),
      dataIndex: 'accountNonLocked',
      valueEnum: {
        false: {
          text: formatMessage({
            id: 'usermanage.columns.locked.error',
          }),
          status: 'Error',
        },
        true: {
          text: formatMessage({
            id: 'usermanage.columns.locked.success',
          }),
          status: 'Success',
        },
      },
      filters: undefined, // 取消筛选
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.attempts',
      }),
      dataIndex: 'attempts',
      hideInSearch: true,
      valueType: 'digit',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usermanage.columns.lockExpirationTime',
      }),
      dataIndex: 'lockExpirationTime',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '所属地市',
      dataIndex: 'company.name',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '所属部门',
      dataIndex: 'departmentName',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '是否脱敏',
      dataIndex: 'ifSensitive',
    },
    {
      ellipsis: true,
      align: 'center',
      title: '最近一次调用账号实名制风险审计接口时间',
      dataIndex: 'userAccountRiskAudit.callTime',
      hideInSearch: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: '账号实名制接口调用结果',
      dataIndex: 'userAccountRiskAudit.code',
      valueEnum: {
        0: { text: '请求成功，无异常研判结果' },
        503: { text: '服务器内部错误' },
        1000: { text: '请求成功，有异常研判结果' },
        1001: { text: '请求body为空参数错误' },
      },
      filters: undefined,
    },
    {
      ellipsis: true,
      align: 'center',
      title: '账号实名制接口调用返回信息',
      hideInSearch: true,
      dataIndex: 'userAccountRiskAudit.message',
    },
    {
      dataIndex: 'userMoreAccount.callTime',
      title: '最近一次调用一人多账号审计接口时间',
      ellipsis: true,
      align: 'center',
      hideInSearch: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: '一人多账号接口调用结果',
      dataIndex: 'userMoreAccount.code',
      valueEnum: {
        0: { text: '请求成功，无异常研判结果' },
        503: { text: '服务器内部错误' },
        1000: { text: '请求成功，有异常研判结果' },
        1001: { text: '请求body为空参数错误' },
      },
      filters: undefined,
    },
    {
      ellipsis: true,
      align: 'center',
      title: '一人多账号接口调用返回信息',
      hideInSearch: true,
      dataIndex: 'userMoreAccount.message',
    },
    {
      ellipsis: true,
      align: 'center',
      title: 'IT工单号',
      hideInSearch: true,
      dataIndex: 'itOrderNum',
    },
    {
      dataIndex: 'accountInvalidTime',
      key: 'accountInvalidTime',
      title: '账号冻结时间',
      ellipsis: true,
      align: 'center',
      hideInSearch: true,
    },
    {
      dataIndex: 'accountExpireTime',
      key: 'accountExpireTime',
      title: '账号失效时间',
      ellipsis: true,
      align: 'center',
      hideInSearch: true,
    },

    // ...tableColumn,
    {
      title: formatMessage({
        id: 'usermanage.columns.action',
      }),
      align: 'center',
      fixed: 'right',
      width: 120,
      render: (_, record) => {
        return [
          <Button
            title={formatMessage({
              id: 'usermanage.unlock',
            })}
            type="link"
            key="unlock"
            size="small"
            icon="unlock"
            disabled={record?.accountNonLocked !== false}
            onClick={() => {
              handleUnlockSingle(record?.id);
            }}
          />,
          <Button
            title={formatMessage({
              id: 'usermanage.edit',
            })}
            type="link"
            key="edit"
            icon="edit"
            size="small"
            onClick={() => {
              setEditingUser({ ...record, password: '********' });
              setOpType('edit');
            }}
          />,
          <Button
            title={formatMessage({
              id: 'usermanage.delete',
            })}
            type="link"
            key="delete"
            size="small"
            icon="delete"
            disabled={record?.username === 'admin'}
            onClick={() => {
              handleDeleteSingle(record?.id);
            }}
          />,
        ];
      },
    },
  ];
  return (
    <Card>
      <ProTable // options={{ density: false }}
        scroll={{
          x: 'max-content',
        }}
        actionRef={actionRef}
        columns={columns}
        request={request}
        rowKey="id"
        pagination={{
          defaultCurrent: 1,
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ['5', '10', '15', '20'],
          showTotal: (total, [start, end]) =>
            formatMessage(
              {
                id: 'usermanage.showTotal',
              },
              {
                start,
                end,
                total,
              },
            ),
        }}
        params={{
          sort: 'modifyTime,desc',
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => {
            setSelectedKeys(keys);
          },
          getCheckboxProps: (record) => {
            if (record.username === 'admin') {
              return {
                disabled: true,
              };
            }

            return {};
          },
        }}
        tableAlertRender={({
          selectedRowKeys: selectedRowKeysParam,
          selectedRows: selectedRowsParam,
        }) => {
          const newLockedSelectedRowKeys = selectedRowsParam.reduce((pre, item) => {
            if (item.accountNonLocked === false) {
              pre.push(item.id);
            }

            return pre;
          }, []);

          if (JSON.stringify(lockedSelectedRowKeys) !== JSON.stringify(newLockedSelectedRowKeys)) {
            setLockedSelectedRowKeys(newLockedSelectedRowKeys);
          }

          return formatMessage(
            {
              id: 'usermanage.selected',
            },
            {
              all: selectedRowKeysParam.length,
              locked: newLockedSelectedRowKeys.length,
            },
          );
        }}
        toolBarRender={() => [
          <Button
            key="unlock"
            size="default"
            type="primary"
            icon="unlock"
            disabled={lockedSelectedRowKeys.length === 0}
            onClick={handleUnlock}
          >
            {formatMessage({
              id: 'usermanage.unlock',
            })}
          </Button>,
          <Button
            key="add"
            size="default"
            type="primary"
            icon="plus"
            onClick={() => {
              setOpType('add');
            }}
          >
            {formatMessage({
              id: 'usermanage.add',
            })}
          </Button>,
          <Button
            key="delete"
            size="default"
            type="danger"
            icon="delete"
            disabled={selectedRowKeys.length === 0}
            onClick={handleDelete}
          >
            {formatMessage({
              id: 'usermanage.delete',
            })}
          </Button>,
          <Button
            key="export"
            size="default"
            type="primary"
            icon="export"
            loading={exportLoading}
            onClick={handleExport}
          >
            导出
          </Button>,
        ]}
      />
      <CreateModal
        passwordPolicy={passwordPolicy}
        visible={!!opType}
        opType={opType}
        user={editingUser}
        onAddUser={handleAddUser}
        onUpdateUser={handleUpdateUser}
        onClose={() => {
          setEditingUser(undefined);
          setOpType(null);
        }}
      />
    </Card>
  );
};

export default withSilence(UserManage);
