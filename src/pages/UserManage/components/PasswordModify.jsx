import { Button, Form, Icon, Input, Modal, Popover, Tooltip } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import React, { useState } from 'react';
import { validatePasswordStrength } from '../utils';
const FormItem = Form.Item;

const PasswordModify = (props) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmDirty, setConfirmDirty] = useState(false);
  const {
    form: {
      validateFieldsAndScroll,
      getFieldDecorator,
      resetFields,
      getFieldValue,
      validateFields,
    },
    username,
    onSave,
    passwordPolicy,
  } = props;

  const handleOk = () => {
    validateFieldsAndScroll(async (err, values) => {
      if (err) return;
      setLoading(true);
      const rs = await onSave?.(values, true);
      setLoading(false);

      if (rs) {
        setVisible(false);
      }
    });
  };

  const handleCancel = () => {
    resetFields();
    setVisible(false);
  };

  const compareToFirstPassword = (rule, value, callback) => {
    if (value && value !== getFieldValue('password')) {
      callback(
        formatMessage({
          id: 'usermanage.password.modal.compareToFirstPassword',
        }),
      );
    } else {
      callback();
    }
  };

  const validateToNextPassword = (rule, value, callback) => {
    if (!validatePasswordStrength(value, username)) {
      callback(
        formatMessage({
          id: 'usermanage.password.modal.password.pattern',
        }),
      );
    }

    if (value && confirmDirty) {
      validateFields(['confirm'], {
        force: true,
      });
    }

    callback();
  };

  const handleConfirmBlur = (e) => {
    const { value } = e.target;
    setConfirmDirty(confirmDirty || !!value);
  };

  const formItemLayout = {
    labelCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 7,
      },
    },
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 16,
      },
    },
  };
  return (
    <>
      <Button
        type="link"
        onClick={() => {
          setVisible(true);
        }}
      >
        {formatMessage({
          id: 'usermanage.password.modal.modify',
        })}
      </Button>
      <Modal
        maskClosable={false}
        title={formatMessage({
          id: 'usermanage.password.modal.title',
        })}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={520}
        getContainer={document.body}
        confirmLoading={loading}
        destroyOnClose={true}
      >
        <Form {...formItemLayout}>
          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'usermanage.password.modal.password.label',
                  })}
                </span>
                <Popover
                  content={<div style={{whiteSpace:'pre-wrap'}} dangerouslySetInnerHTML={{__html:formatMessage({
                    id: 'usermanage.password.modal.password.tooltip',
                  })}}></div>}
                >
                  <Icon type="info-circle" />
                </Popover>
              </span>
            }
            // hasFeedback
          >
            {getFieldDecorator('password', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'usermanage.password.modal.password.required',
                  }),
                },
                {
                  min: passwordPolicy?.minimumLengthEnable ? passwordPolicy?.minimumLength : -1,
                  message: formatMessage(
                    {
                      id: 'usermanage.password.modal.password.minLength',
                    },
                    {
                      minimumLength: passwordPolicy?.minimumLength,
                    },
                  ),
                },
                // {
                //   validator: validateToNextPassword,
                // },
              ],
            })(
              <Input.Password
                placeholder={formatMessage({
                  id: 'usermanage.password.modal.password.placeholder',
                })}
                suffix={null}
              />,
            )}
          </FormItem>
          <FormItem
            label={formatMessage({
              id: 'usermanage.password.modal.confirm.label',
            })}
          >
            {getFieldDecorator('confirm', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'usermanage.password.modal.confirm.required',
                  }),
                },
                {
                  validator: compareToFirstPassword,
                },
              ],
            })(
              <Input.Password
                onBlur={handleConfirmBlur}
                placeholder={formatMessage({
                  id: 'usermanage.password.modal.confirm.placeholder',
                })}
              />,
            )}
          </FormItem>
        </Form>
      </Modal>
    </>
  );
};

const PasswordModifyForm = Form.create()(PasswordModify);
export default PasswordModifyForm;
