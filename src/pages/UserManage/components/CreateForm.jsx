import {
  Card,
  Col,
  Form,
  Icon,
  Input,
  message,
  Popover,
  Radio,
  Row,
  Select,
  Spin,
  Tooltip,
} from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import React, { Fragment } from 'react';
import { desensitizationStringSerializer, validatePasswordStrength } from '../utils';
import { getAllGroups } from '../service';
import PasswordModify from './PasswordModify';
import PhonenumModify from './PhonenumModify';
import { getOrganization } from '../extra/service';
import request from '@/utils/request';
let Extra;

try {
  // eslint-disable-next-line global-require
  Extra = require('../extra').default; // eslint-disable-next-line no-empty
} catch (e) {}

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const CreateForm = Form.create()(
  class extends React.Component {
    state = {
      inited: false,
      groups: [],
      updatedPhonenum: null,
      companyOptions: [],
    };

    componentDidMount() {
      this.getCompany();
      this.loadGroups()
        .then(() => {
          this.setState({
            inited: true,
          });
        })
        .catch();
    }

    componentDidUpdate() {
      const { opType, initialValues } = this.props;
      const isEditing = opType === 'edit';
      if (
        isEditing &&
        initialValues.departmentName &&
        initialValues.departmentId &&
        !this.state.departmentOptions?.length
      ) {
        this.setState({
          departmentOptions: [
            {
              value: initialValues.departmentName + '^' + initialValues.departmentId,
              name: initialValues.departmentName,
            },
          ],
        });
      }
    }

    getCompany = async () => {
      const res = await getOrganization();
      if (res && res?.[0]?.value)
        this.setState({
          companyOptions: res || [],
        });
    };

    loadGroups = async () => {
      try {
        const response = await getAllGroups();

        if (response) {
          this.setState({
            groups: response,
          });
        }
      } catch (e) {
        message.warn(e.message);
      }
    };
    handleEditPwd = () => {
      const { form, opType, handleEditPwd, editPwd } = this.props;

      if (opType === 'edit' && !editPwd) {
        handleEditPwd?.();
        form.setFieldsValue({
          password: '',
          confirmPassword: '',
        });
      }
    };
    setUpdatedPhonenum = (newPhonenum) => {
      this.setState({
        ...this.state,
        updatedPhonenum: desensitizationStringSerializer(newPhonenum),
      });
    };

    onEmployeeIdChange = (e) => {
      if (!e?.target?.value) {
        this.props?.form?.setFieldsValue({
          departmentName: undefined,
          realName: undefined,
        });
        return;
      }
      const value = e.target.value;
      request('/api/hn/employee/getDetailByEmployeeId', {
        method: 'get',
        params: {
          employeeId: value,
        },
      }).then((res) => {
        if (res.code == 200 && res.data?.departmentName && res.data?.realName) {
          this.setState({
            details: res.data || {},
            departmentOptions: [
              {
                name: res.data?.departmentName,
                value: res.data?.departmentName + '^' + res.data?.departmentId,
              },
            ],
          });
          this.props?.form?.setFieldsValue({
            departmentName: res.data?.departmentName + '^' + res.data?.departmentId,
            realName: res.data?.realName,
          });
        } else {
          message.error('未查到该工号对应的信息，请重新输入！');
          this.setState({
            details: {},
          });
          this.props?.form?.setFieldsValue({
            departmentName: undefined,
            realName: undefined,
            employeeId: undefined,
          });
        }
      });
    };

    render() {
      const { companyOptions, departmentOptions } = this.state;
      const { form, initialValues, opType, editPwd, passwordPolicy, handleEdit } = this.props;
      const formItemLayout = {
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 8,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
      };
      const isEditing = opType === 'edit';

      if (!this.state.inited) {
        return (
          <div
            style={{
              marginTop: 40,
              textAlign: 'center',
            }}
          >
            <Spin />
          </div>
        );
      } // @ts-ignore

      return (
        <Form>
          <Row gutter={16}>
            <Col span={14}>
              <Card
                size="small"
                title={formatMessage({
                  id: 'usermanage.basicInformation',
                })}
                bodyStyle={{
                  height: 400,
                  overflowY: 'auto',
                }}
              >
                <FormItem {...formItemLayout} label="员工工号">
                  {form.getFieldDecorator('employeeId', {
                    initialValue: isEditing ? initialValues.employeeId : '',
                    rules: [
                      {
                        required: true,
                        whitespace: true,
                        message: '请输入员工工号',
                      },
                    ],
                  })(
                    <Input
                      placeholder="请输入员工工号"
                      disabled={isEditing}
                      onBlur={(e) => {
                        this.onEmployeeIdChange(e);
                      }}
                    />,
                  )}
                </FormItem>
                <FormItem
                  {...formItemLayout}
                  label={formatMessage({
                    id: 'usermanage.basicInformation.username',
                  })}
                >
                  {form.getFieldDecorator('username', {
                    initialValue: isEditing ? initialValues.username : '',
                    rules: [
                      {
                        required: true,
                        whitespace: true,
                        message: formatMessage({
                          id: 'usermanage.basicInformation.username.required',
                        }),
                      },
                      // {
                      //   max: 20,
                      //   message: formatMessage({
                      //     id: 'usermanage.basicInformation.username.maxLength',
                      //   }),
                      // },
                    ],
                  })(
                    <Input
                      autoComplete="new-password"
                      disabled={isEditing}
                      placeholder={formatMessage({
                        id: 'usermanage.basicInformation.username.placeholder',
                      })}
                      onChange={() => {
                        if (![null, undefined, ''].includes(form.getFieldValue('password'))) {
                          setTimeout(() => {
                            form.validateFields(['password']);
                          }, 0);
                        }
                      }}
                    />,
                  )}
                </FormItem>
                {isEditing ? (
                  <FormItem
                    {...formItemLayout}
                    label={formatMessage({
                      id: 'usermanage.basicInformation.password',
                    })}
                  >
                    ******
                    <PasswordModify
                      username={form.getFieldValue('username')}
                      onSave={handleEdit}
                      passwordPolicy={passwordPolicy}
                    />
                  </FormItem>
                ) : (
                  <>
                    <FormItem
                      {...formItemLayout}
                      label={
                        <span>
                          <span
                            style={{
                              marginRight: 5,
                            }}
                          >
                            {formatMessage({
                              id: 'usermanage.basicInformation.password',
                            })}
                          </span>
                          {/* <Tooltip
                            title={formatMessage({
                              id: 'usermanage.basicInformation.password.tooltip',
                            })}
                          > */}
                          <Popover
                            content={
                              <div
                                style={{ whiteSpace: 'pre-wrap' }}
                                dangerouslySetInnerHTML={{
                                  __html: formatMessage({
                                    id: 'usermanage.basicInformation.password.tooltip',
                                  }),
                                }}
                              ></div>
                            }
                          >
                            <Icon type="info-circle" />
                          </Popover>
                          {/* </Tooltip> */}
                        </span>
                      }
                    >
                      {form.getFieldDecorator('password', {
                        initialValue: isEditing ? initialValues.password : '',
                        validateFirst: true,
                        rules: [
                          {
                            required: true,
                            message: formatMessage({
                              id: 'usermanage.basicInformation.password.required',
                            }),
                          },
                          {
                            min: passwordPolicy?.minimumLengthEnable
                              ? passwordPolicy?.minimumLength
                              : -1,
                            message: formatMessage(
                              {
                                id: 'usermanage.basicInformation.password.minLength',
                              },
                              {
                                minimumLength: passwordPolicy?.minimumLength,
                              },
                            ),
                          },
                          {
                            validator: (rule, value, callback) => {
                              if (isEditing && !editPwd) {
                                callback();
                              } else {
                                const password = value;
                                const username = form.getFieldValue('username');

                                if (password) {
                                  if (validatePasswordStrength(password, username)) {
                                    callback();
                                  } else {
                                    callback(
                                      formatMessage({
                                        id: 'usermanage.basicInformation.password.callback',
                                      }),
                                    );
                                  }
                                } else {
                                  callback();
                                }
                              }
                            },
                          },
                        ],
                      })(
                        <Input
                          autoComplete="new-password"
                          type="password"
                          placeholder={formatMessage({
                            id: 'usermanage.basicInformation.password.placeholder',
                          })}
                          onFocus={this.handleEditPwd}
                          onChange={() => {
                            if (
                              ![null, undefined, ''].includes(form.getFieldValue('confirmPassword'))
                            ) {
                              setTimeout(() => {
                                form.validateFields(['confirmPassword']);
                              }, 0);
                            }
                          }}
                        />,
                      )}
                    </FormItem>
                    <FormItem
                      {...formItemLayout}
                      label={formatMessage({
                        id: 'usermanage.basicInformation.confirm',
                      })}
                    >
                      {form.getFieldDecorator('confirmPassword', {
                        initialValue: isEditing ? initialValues.password : '',
                        validateFirst: true,
                        rules: [
                          {
                            required: true,
                            message: formatMessage({
                              id: 'usermanage.basicInformation.confirm.required',
                            }),
                          },
                          {
                            validator: (rule, value, callback) => {
                              if (value && value !== form.getFieldValue('password')) {
                                callback(
                                  formatMessage({
                                    id: 'usermanage.basicInformation.confirm.callback',
                                  }),
                                );
                              } else {
                                callback();
                              }
                            },
                          },
                        ],
                      })(
                        <Input
                          type="password"
                          placeholder={formatMessage({
                            id: 'usermanage.basicInformation.confirm.placeholder',
                          })}
                        />,
                      )}
                    </FormItem>
                  </>
                )}

                <FormItem
                  {...formItemLayout}
                  label={
                    '姓名'
                    // <span>
                    //   <span
                    //     style={{
                    //       marginRight: 5,
                    //     }}
                    //   >
                    //     {formatMessage({
                    //       id: 'usermanage.basicInformation.nickname',
                    //     })}
                    //   </span>
                    //   <Tooltip
                    //     title={formatMessage({
                    //       id: 'usermanage.basicInformation.nickname.tooltip',
                    //     })}
                    //   >
                    //     <Icon type="info-circle" />
                    //   </Tooltip>
                    // </span>
                  }
                >
                  {form.getFieldDecorator('realName', {
                    initialValue: isEditing ? initialValues?.realName : '',
                    validateFirst: true,
                    rules: [
                      // {
                      //   required: true,
                      //   message: formatMessage({
                      //     id: 'usermanage.basicInformation.nickname.required',
                      //   }),
                      // },
                      // {
                      //   max: 20,
                      //   message: formatMessage({
                      //     id: 'usermanage.basicInformation.nickname.maxLength',
                      //   }),
                      // },
                      // {
                      //   pattern: /^[\u4e00-\u9fa5_a-zA-Z]{0,20}$/,
                      //   message: formatMessage({
                      //     id: 'usermanage.basicInformation.nickname.pattern',
                      //   }),
                      // },
                    ],
                  })(
                    <Input
                      disabled={true}
                      placeholder={
                        '请输入'
                        //   formatMessage({
                        //   id: 'usermanage.basicInformation.nickname.placeholder',
                        // })
                      }
                    />,
                  )}
                </FormItem>

                {isEditing ? (
                  <FormItem
                    {...formItemLayout}
                    label={formatMessage({
                      id: 'usermanage.basicInformation.phonenum',
                    })}
                  >
                    {typeof this.state.updatedPhonenum === 'string' && this.state.updatedPhonenum
                      ? this.state.updatedPhonenum
                      : initialValues?.phonenum}
                    <PhonenumModify
                      onSave={handleEdit}
                      setUpdatedPhonenum={this.setUpdatedPhonenum}
                    />
                  </FormItem>
                ) : (
                  <FormItem
                    {...formItemLayout}
                    label={formatMessage({
                      id: 'usermanage.basicInformation.phonenum',
                    })}
                  >
                    {form.getFieldDecorator('phonenum', {
                      initialValue: isEditing ? initialValues?.phonenum : '',
                      validateFirst: true,
                      rules: [
                        {
                          required: true,
                          message: formatMessage({
                            id: 'usermanage.basicInformation.phonenum.required',
                          }),
                        },
                        {
                          pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                          message: formatMessage({
                            id: 'usermanage.basicInformation.phonenum.pattern',
                          }),
                        },
                      ],
                    })(
                      <Input
                        placeholder={formatMessage({
                          id: 'usermanage.basicInformation.phonenum.placeholder',
                        })}
                      />,
                    )}
                  </FormItem>
                )}

                <FormItem {...formItemLayout} label="身份证">
                  {form.getFieldDecorator('idCardNum', {
                    initialValue: isEditing ? initialValues.idCardNum : '',
                    rules: [
                      // {
                      //   required: true,
                      //   whitespace: true,
                      //   message: '请输入员工工号',
                      // },
                    ],
                  })(<Input placeholder="请输入身份证" />)}
                </FormItem>

                {/* <FormItem
                  {...formItemLayout}
                  label={formatMessage({
                    id: 'usermanage.basicInformation.email',
                  })}
                >
                  {form.getFieldDecorator('email', {
                    initialValue: isEditing ? initialValues?.email : '',
                    validateFirst: true,
                    rules: [
                      {
                        required: true,
                        message: formatMessage({
                          id: 'usermanage.basicInformation.email.required',
                        }),
                      },
                      {
                        type: 'email',
                        message: formatMessage({
                          id: 'usermanage.basicInformation.email.pattern',
                        }),
                      },
                    ],
                  })(
                    <Input
                      placeholder={formatMessage({
                        id: 'usermanage.basicInformation.email.placeholder',
                      })}
                    />,
                  )}
                </FormItem> */}
                <FormItem {...formItemLayout} label="IT工单号">
                  {form.getFieldDecorator('itOrderNum', {
                    initialValue: isEditing ? initialValues.itOrderNum : '',
                    rules: [
                      {
                        required: true,
                        message: '请输入IT工单号',
                      },
                      {
                        max: 50,
                        message: '最多输入50个字符',
                      },
                    ],
                  })(<Input placeholder="请输入" disabled={isEditing} />)}
                </FormItem>
                <FormItem
                  {...formItemLayout}
                  label={formatMessage({
                    id: 'usermanage.basicInformation.enabled',
                  })}
                >
                  {form.getFieldDecorator('enabled', {
                    initialValue: isEditing ? `${initialValues?.enabled}` : 'true',
                  })(
                    <RadioGroup>
                      <Radio value="true">
                        {formatMessage({
                          id: 'usermanage.basicInformation.enabled.success',
                        })}
                      </Radio>
                      <Radio value="false">
                        {formatMessage({
                          id: 'usermanage.basicInformation.enabled.default',
                        })}
                      </Radio>
                    </RadioGroup>,
                  )}
                </FormItem>

                <FormItem {...formItemLayout} label="所属地市">
                  {form.getFieldDecorator('company.id', {
                    initialValue: isEditing ? initialValues.company.id : '',
                    rules: [
                      {
                        required: true,
                        // whitespace: true,
                        message: '请选择所属地市',
                      },
                    ],
                  })(
                    <Select placeholder="请选择所属地市">
                      {companyOptions.map((v) => (
                        <Option key={v.id} value={v.id}>
                          {v.name}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </FormItem>
                <FormItem {...formItemLayout} label="所属部门">
                  {form.getFieldDecorator('departmentName', {
                    initialValue: isEditing
                      ? initialValues.departmentName && initialValues.departmentId
                        ? initialValues.departmentName + '^' + initialValues.departmentId
                        : ''
                      : '',
                    rules: [
                      {
                        required: true,
                        // whitespace: true,
                        message: '请选择所属部门',
                      },
                    ],
                  })(
                    <Select placeholder="请选择所属部门" disabled={true}>
                      {departmentOptions?.map((v) => (
                        <Option key={v.value} value={v.value}>
                          {v.name}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </FormItem>
                <FormItem {...formItemLayout} label="是否脱敏">
                  {form.getFieldDecorator('ifSensitive', {
                    initialValue: isEditing ? initialValues.ifSensitive || '否' : '否',
                    rules: [
                      {
                        required: true,
                        // whitespace: true,
                        message: '请选择是否脱敏',
                      },
                    ],
                  })(
                    <RadioGroup>
                      <Radio value="是">是</Radio>
                      <Radio value="否">否</Radio>
                    </RadioGroup>,
                  )}
                </FormItem>
                <FormItem {...formItemLayout} label="是否4方人员">
                  {form.getFieldDecorator('ifFourthEmp', {
                    initialValue: isEditing ? initialValues.ifFourthEmp : '0',
                    rules: [
                      {
                        required: true,
                        message: '请选择是否4方人员',
                      },
                    ],
                  })(
                    <Select placeholder="请选择所属部门" disabled={isEditing}>
                      {['是', '否']?.map((v) => (
                        <Option key={v === '是' ? '1' : '0'} value={v === '是' ? '1' : '0'}>
                          {v}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </FormItem>
                {form.getFieldValue('ifFourthEmp') === '1' && (
                  <Fragment>
                    <FormItem {...formItemLayout} label="所在单位">
                      {form.getFieldDecorator('oriCompany', {
                        initialValue: isEditing ? initialValues.oriCompany : '',
                        rules: [
                          {
                            required: true,
                            message: '请输入所在单位',
                          },
                        ],
                      })(<Input placeholder="请输入" disabled={isEditing} />)}
                    </FormItem>
                    <FormItem {...formItemLayout} label="电信方负责人">
                      {form.getFieldDecorator('personInCharge', {
                        initialValue: isEditing ? initialValues.personInCharge : '',
                        rules: [
                          {
                            required: true,
                            message: '请输入电信方负责人',
                          },
                        ],
                      })(<Input placeholder="请输入" disabled={isEditing} />)}
                    </FormItem>
                    <FormItem {...formItemLayout} label="电信方负责人联系方式">
                      {form.getFieldDecorator('chargePersonPhone', {
                        initialValue: isEditing ? initialValues.chargePersonPhone : '',
                        rules: [
                          {
                            required: true,
                            message: '请输入电信方负责人联系方式',
                          },
                          {
                            pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                            message: '请输入正确的联系方式',
                          },
                        ],
                      })(<Input placeholder="请输入" disabled={isEditing} />)}
                    </FormItem>
                  </Fragment>
                )}

                {/* {Extra && (
                  <Extra
                    form={form}
                    formValues={initialValues}
                    formItemProps={{
                      labelCol: {
                        xs: {
                          span: 24,
                        },
                        sm: {
                          span: 6,
                        },
                      },
                      wrapperCol: {
                        xs: {
                          span: 24,
                        },
                        sm: {
                          span: 18,
                        },
                      },
                    }}
                  />
                )} */}
              </Card>
            </Col>
            <Col span={10}>
              <Card
                size="small"
                title={formatMessage({
                  id: 'usermanage.userGroup',
                })}
                bodyStyle={{
                  height: 400,
                  overflowY: 'auto',
                }}
              >
                <FormItem>
                  {form.getFieldDecorator('role.id', {
                    initialValue: isEditing ? initialValues?.role?.id : '',
                    rules: [
                      {
                        validator: (_, value, callback) => {
                          if (!value) {
                            callback(
                              formatMessage({
                                id: 'usermanage.userGroup.callback',
                              }),
                            );
                          }

                          callback();
                        },
                      },
                    ],
                  })(
                    <RadioGroup>
                      {this.state.groups.map((x) => (
                        <div key={x.id}>
                          <Radio value={x.id}>{x.name}</Radio>
                        </div>
                      ))}
                    </RadioGroup>,
                  )}
                </FormItem>
              </Card>
            </Col>
          </Row>
        </Form>
      );
    }
  },
);
export default CreateForm;
