import React from 'react';
import { Modal } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import CreateForm from './CreateForm';

const mapTitle = (opType) => {
  return {
    add: formatMessage({
      id: 'usermanage.add.title',
    }),
    edit: formatMessage({
      id: 'usermanage.edit.title',
    }),
  }[opType];
};

export default class extends React.Component {
  formRef = null;
  state = {
    editPwd: false,
  };

  static getDerivedStateFromProps(nextProps, prevState) {
    const { editPwd } = prevState;
    const { opType } = nextProps;

    if (editPwd && opType === null) {
      return {
        editPwd: false,
      };
    }

    return null;
  }

  handleEditPwd = () => {
    this.setState({
      editPwd: true,
    });
  };
  handleOk = () => {
    if (!this.formRef) {
      return;
    }

    const { editPwd } = this.state;
    const { opType, onAddUser, onUpdateUser, user, onClose } = this.props;
    const { form } = this.formRef.props;
    form.validateFieldsAndScroll((error, values) => {
      if (error) {
        return;
      }

      if (opType === 'add') {
        const newValues = {
          ...values,
          departmentName: values.departmentName?.split('^')?.[0],
          departmentId: values.departmentName?.split('^')?.[1],
        };
        onAddUser(newValues);
      } else if (form.isFieldsTouched()) {
        const newValues = {
          ...values,
          departmentName: values.departmentName?.split('^')?.[0],
          departmentId: values.departmentName?.split('^')?.[1],
        };

        if (!editPwd) {
          newValues.password = '';
          newValues.confirmPassword = '';
        }

        onUpdateUser({
          id: user?.id,
          ...newValues,
        });
      } else {
        onClose?.();
      }
    });
  };
  handleEdit = async (params, doNotCloseModal) => {
    const { onUpdateUser, user } = this.props;
    const rs = await onUpdateUser(
      {
        id: user?.id,
        ...params,
      },
      doNotCloseModal,
    );
    return rs;
  };

  render() {
    const { editPwd } = this.state;
    const { visible, opType, user, onClose, afterClose, passwordPolicy } = this.props;
    return (
      <Modal
        maskClosable={false}
        destroyOnClose
        title={mapTitle(opType)}
        visible={visible}
        width={1000}
        onOk={this.handleOk}
        onCancel={onClose}
        afterClose={afterClose}
      >
        <CreateForm
          passwordPolicy={passwordPolicy}
          editPwd={editPwd}
          initialValues={user}
          opType={opType}
          wrappedComponentRef={(form) => {
            this.formRef = form;
          }}
          handleEditPwd={this.handleEditPwd}
          handleEdit={this.handleEdit}
        />
      </Modal>
    );
  }
}
