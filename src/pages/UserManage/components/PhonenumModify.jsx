import { Button, Form, Input, Modal } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import React, { useState } from 'react';
const FormItem = Form.Item;

const PhonenumModify = (props) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const {
    form: { validateFieldsAndScroll, getFieldDecorator, resetFields, getFieldValue },
    onSave,
    setUpdatedPhonenum,
  } = props;

  const handleOk = () => {
    validateFieldsAndScroll(async (err, values) => {
      if (err) return;
      const newPhonenum = getFieldValue('phonenum');
      setLoading(true);
      const rs = await onSave?.(values, true);
      setLoading(false);

      if (rs) {
        setUpdatedPhonenum(newPhonenum);
        setVisible(false);
      }
    });
  };

  const handleCancel = () => {
    resetFields();
    setVisible(false);
  };

  const formItemLayout = {
    labelCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 7,
      },
    },
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 16,
      },
    },
  };
  return (
    <>
      <Button
        type="link"
        onClick={() => {
          setVisible(true);
        }}
      >
        {formatMessage({
          id: 'usermanage.phonenum.modal.modify',
        })}
      </Button>
      <Modal
        maskClosable={false}
        title={formatMessage({
          id: 'usermanage.phonenum.modal.title',
        })}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={520}
        getContainer={document.body}
        confirmLoading={loading}
        destroyOnClose={true}
      >
        <Form {...formItemLayout}>
          <FormItem
            label={formatMessage({
              id: 'usermanage.phonenum.modal.label',
            })}
          >
            {getFieldDecorator('phonenum', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'usermanage.phonenum.modal.required',
                  }),
                },
                {
                  pattern: /^1[3|4|5|7|8][0-9]{9}$/,
                  message: formatMessage({
                    id: 'usermanage.phonenum.modal.pattern',
                  }),
                },
              ],
            })(
              <Input
                placeholder={formatMessage({
                  id: 'usermanage.phonenum.modal.placeholder',
                })}
              />,
            )}
          </FormItem>
        </Form>
      </Modal>
    </>
  );
};

const PhonenumModifyForm = Form.create()(PhonenumModify);
export default PhonenumModifyForm;
