/* eslint-disable func-names */
let globalMock = null;

try {
  delete require.cache[require.resolve('../../mock')]; // eslint-disable-next-line global-require

  globalMock = require('../../mock'); // eslint-disable-next-line no-empty
} catch (e) {}

export default {
  ...globalMock,
  'POST /api/user/delUsers': function (req, res) {
    res.json({
      data: null,
      ext: null,
      message: '删除成功',
      state: 'SUCCESS',
      success: true,
    });
  },
  'POST /api/user/unlockUsers': function (req, res) {
    res.json({
      data: null,
      ext: null,
      message: '解锁成功',
      state: 'SUCCESS',
    });
  },
  'POST /api/user/saveUser': function (req, res) {
    res.json({
      state: 'SUCCESS',
    });
  },
  'POST /api/user/findUserPager': {
    content: [
      {
        id: 1,
        username: 'admin',
        nickname: '斗鱼超管',
        phonenum: '187*****203',
        email: '<EMAIL>',
        createTime: null,
        creator: null,
        modifyTime: null,
        modifier: null,
        lastLoginTime: null,
        lastLoginIp: '127.0.0.1',
        attempts: 1,
        lockExpirationTime: '2022-06-14 10:00:13',
        role: {
          id: 1,
          name: '超级管理员',
          remark: null,
          modifyTime: null,
          modifier: null,
          createTime: null,
          creator: null,
        },
        accountNonExpired: null,
        accountNonLocked: true,
        credentialsNonExpired: null,
        enabled: true,
        company: {
          id: 1,
          name: '单位1',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        department: {
          id: 3,
          name: '部门1',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        section: {
          id: 5,
          name: '科室1',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
      },
      {
        id: 2,
        username: 'test001',
        nickname: '测试001',
        phonenum: '123*****001',
        email: '<EMAIL>',
        createTime: null,
        creator: null,
        modifyTime: null,
        modifier: null,
        lastLoginTime: null,
        lastLoginIp: '127.0.0.1',
        attempts: 10,
        lockExpirationTime: '2023-06-14 10:00:13',
        role: {
          id: 2,
          name: '测试组',
          remark: null,
          modifyTime: null,
          modifier: null,
          createTime: null,
          creator: null,
        },
        accountNonExpired: null,
        accountNonLocked: false,
        credentialsNonExpired: null,
        enabled: true,
        company: {
          id: 2,
          name: '单位2',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        department: {
          id: 4,
          name: '部门4',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        section: {
          id: 6,
          name: '科室6',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
      },
      {
        id: 3,
        username: 'test002',
        nickname: '测试002',
        phonenum: '123*****002',
        email: '<EMAIL>',
        createTime: null,
        creator: null,
        modifyTime: null,
        modifier: null,
        lastLoginTime: null,
        lastLoginIp: '127.0.0.1',
        attempts: 10,
        lockExpirationTime: '2023-06-15 10:00:13',
        role: {
          id: 2,
          name: '测试组',
          remark: null,
          modifyTime: null,
          modifier: null,
          createTime: null,
          creator: null,
        },
        accountNonExpired: null,
        accountNonLocked: false,
        credentialsNonExpired: null,
        enabled: true,
        company: {
          id: 2,
          name: '单位2',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        department: {
          id: 4,
          name: '部门4',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        section: {
          id: 6,
          name: '科室6',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
      },
      {
        id: 4,
        username: 'test003',
        nickname: '测试003',
        phonenum: '123*****003',
        email: '<EMAIL>',
        createTime: '2021-03-13 12:12:12',
        creator: 'admin',
        modifyTime: '2021-03-13 14:14:14',
        modifier: 'some one',
        lastLoginTime: null,
        lastLoginIp: '127.0.0.1',
        attempts: 0,
        lockExpirationTime: null,
        role: {
          id: 2,
          name: '测试组',
          remark: null,
          modifyTime: null,
          modifier: null,
          createTime: null,
          creator: null,
        },
        accountNonExpired: null,
        accountNonLocked: true,
        credentialsNonExpired: null,
        enabled: true,
        company: {
          id: 2,
          name: '单位2',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        department: {
          id: 4,
          name: '部门4',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
        section: {
          id: 6,
          name: '科室6',
          value: null,
          pid: null,
          type: null,
          sort: null,
        },
      },
    ],
    pageable: {
      sort: {
        unsorted: true,
        sorted: false,
        empty: true,
      },
      offset: 0,
      pageSize: 1,
      pageNumber: 0,
      paged: true,
      unpaged: false,
    },
    totalElements: 10,
    last: false,
    totalPages: 10,
    number: 0,
    size: 1,
    sort: {
      unsorted: true,
      sorted: false,
      empty: true,
    },
    first: true,
    numberOfElements: 1,
    empty: false,
  },
  '/api/role/list': [
    {
      id: 5,
      name: 'lzytest',
      remark: null,
      modifyTime: '2021-03-11 15:20:26',
      modifier: 'admin',
      createTime: '2021-03-11 15:20:26',
      creator: 'admin',
    },
    {
      id: 4,
      name: 'test11’and(select+1)&gt;0waitfor/**/delay’0:0:2',
      remark: 'test group 4',
      modifyTime: '2021-03-11 15:20:26',
      modifier: 'admin',
      createTime: '2021-03-11 15:20:26',
      creator: 'admin',
    },
    {
      id: 3,
      name: 'test group 3',
      remark: 'test group 3',
      modifyTime: null,
      modifier: null,
      createTime: '2021-03-11 15:20:26',
      creator: 'admin',
    },
    {
      id: 2,
      name: '测试组',
      remark: '测试组',
      modifyTime: null,
      modifier: null,
      createTime: '2021-03-11 15:20:26',
      creator: 'admin',
    },
    {
      id: 1,
      name: '超级管理员',
      remark: null,
      modifyTime: null,
      modifier: null,
      createTime: '2021-03-11 15:20:26',
      creator: null,
    },
  ],
};
