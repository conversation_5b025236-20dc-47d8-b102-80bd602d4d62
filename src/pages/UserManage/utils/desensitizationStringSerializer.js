// 脱敏逻辑参考后端，如后端有所改动则前端也必须相应的改动

/**
 * 用于数据脱敏， 数据长度大于10时保留首尾各三个字符串 数据长度小于10且大于4时保留首尾各两个字符 数据长度小于等于4时都转为*
 */
export default (value) => {
  let str = value; // @ts-ignore

  if (
    value === null ||
    value === undefined ||
    Number.isNaN(value) ||
    value === Infinity ||
    value === -Infinity
  ) {
    str = '';
  } else {
    str = `${value}`;
  }

  const DES_THREE_MINIMUM_LENGTH = 2;
  const DES_THREE = 3;
  const DES_TWO_MINIMUM_LENGTH = 4;
  const DES_TWO = 2; // 这种方法的好处在于：可以正确处理各种Unicode字符，不会将大于\uFFFF的Unicode字符算作2个字符。

  const strArr = [...str];
  const length = strArr.length;
  let rs = '';

  if (length > DES_THREE_MINIMUM_LENGTH) {
    const partStrArr = strArr.slice(0, DES_THREE);

    while (partStrArr.length < length - DES_THREE) {
      partStrArr.push('*');
    }

    rs = partStrArr.concat(strArr.slice(length - DES_THREE)).join('');
  } else if (length > DES_TWO_MINIMUM_LENGTH) {
    const partStrArr = strArr.slice(0, DES_TWO);

    while (partStrArr.length < length - DES_TWO) {
      partStrArr.push('*');
    }

    rs = partStrArr.concat(strArr.slice(length - DES_TWO)).join('');
  } else {
    const partStrArr = [];

    while (partStrArr.length < length) {
      partStrArr.push('*');
    }

    rs = partStrArr.join('');
  }

  return rs;
};
