import React from 'react';
import { FormattedMessage } from 'ponshine-plugin-react/locale';
export default [
  {
    ellipsis: true,
    align: 'center',
    title: <FormattedMessage id="usermanage.columns.company" />,
    dataIndex: 'company.name',
    width: 120,
    hideInSearch: true,
  },
  {
    ellipsis: true,
    align: 'center',
    title: <FormattedMessage id="usermanage.columns.department" />,
    dataIndex: 'department.name',
    hideInSearch: true,
  },
  {
    ellipsis: true,
    align: 'center',
    title: <FormattedMessage id="usermanage.columns.section" />,
    dataIndex: 'section.name',
    hideInSearch: true,
  },
];
