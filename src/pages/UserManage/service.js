import Jsencrypt from 'jsencryptNew';
import request from '@/utils/request';
const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzaCrNH49tWuCmTxnVivfIgg8OSljmE3Lx9+KZIBQxfXZhLsj7uLdXRB3Q9MQ5sUhS7dQ+XfUaczNizMv+gQ8AYsPW9P9VTYUNRug47KhPgU28oOlkI5qm5RPQg06B5S7SGYpihhmTjzFWNTlshFjVZTV4QyfuX5hNrigJ4ddFmwIDAQAB'; // 分页查询

export function getUsers(params) {
  // const name = params.role.name
  // delete params.role.name;
  // params['role.name'] = name
  return request('/api/user/findUserPager', {
    method: 'POST',
    data: {...params},
    requestType: 'form',
    stringifyOptions: {
      allowDots: true,
    }
  });
} // 新增/编辑用户

export function saveUser(params) {
  const encrypt = new Jsencrypt();
  encrypt.setPublicKey(publicKey);
  const password = params.password ? encrypt.encryptLong(params.password) : '';
  const confirmPassword = params.confirmPassword ? encrypt.encryptLong(params.confirmPassword) : '';
  return request('/api/user/saveUser', {
    method: 'POST',
    data: { ...params, password, confirmPassword },
    requestType: 'form',
    qsPkg: 'qs',
    stringifyOptions: {
      arrayFormat: 'repeat',
      strictNullHandling: true,
      allowDots: true,
    },
  });
} // 删除用户

export function deleteUsers(params) {
  return request('/api/user/delUsers', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
} // 解锁用户

export function unlockUsers(params) {
  return request('/api/user/unlockUsers', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
} // 获取所有用户组信息

export function getAllGroups() {
  return request('/api/role/list', {
    method: 'GET',
  });
}
