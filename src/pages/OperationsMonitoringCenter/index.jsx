import React, { useEffect, useState, useCallback } from 'react';
import {
  FullScreenContainer,
  BorderBox7,
  Loading,
  Decoration5,
  BorderBox11,
  Decoration11,
  ScrollBoard,
} from '@jiaminghi/data-view-react';
import RollView from './components/RollView/index';
import LineEcharts from './components/lineEcharts';
import BarEcharts from './components/barEcharts';
import ConfigModal from './components/configModal';
import OptionModal from './components/optionModal';
import FlowPath from './components/flowPath';
import { exportFile } from '@/utils/utils';
import {
  Icon,
  Tabs,
  Radio,
  Tooltip,
  Form,
  Row,
  Col,
  Select,
  Input,
  DatePicker,
  Tag,
  Table,
  Pagination,
} from 'antd';

import request from '@/utils/request';
const { RangePicker } = DatePicker;
import styles from './index.less';
import Title from './components/Title';
import moment from 'moment';
const { TabPane } = Tabs;
const { Option } = Select;
const formItemLayout = {
  labelCol: {
    span: 5,
  },
};
function index(props) {
  const {
    form: { getFieldDecorator, getFieldValue, setFieldsValue, validateFields },
  } = props;
  const [isFullScreen, setisFullScreen] = useState(false);
  const [MonitorBlack, setMonitorBlack] = useState([]);
  const [configvisible, setconfigvisible] = useState(false);
  const [row, setrow] = useState({});
  useEffect(() => {
    if (!configvisible) {
      setrow({});
    }
  }, [configvisible]);
  const toFullScreen = () => {
    setisFullScreen(true);

    const elem = document.getElementById('indexWrapper');
    if (elem.webkitRequestFullScreen) {
      elem.webkitRequestFullScreen();
    } else if (elem.mozRequestFullScreen) {
      elem.mozRequestFullScreen();
    } else if (elem.requestFullScreen) {
      elem.requestFullscreen();
    } else {
      // 浏览器不支持全屏API或已被禁用
    }
  };
  useEffect(() => {
    getMonitorBlack();
    handleSearchTop();
    handleSearchRight();
  }, []);
  useEffect(() => {
    function onKeyup(e) {
      if (e.key == 'Escape') {
        exitFullscreen();
      }
    }
    window.addEventListener('keyup', onKeyup);
    return () => window.removeEventListener('keyup', onKeyup);
  }, [isFullScreen]);
  const exitFullscreen = () => {
    setisFullScreen(false);
    const elem = document;
    if (elem.webkitCancelFullScreen) {
      elem.webkitCancelFullScreen();
    } else if (elem.mozCancelFullScreen) {
      elem.mozCancelFullScreen();
    } else if (elem.cancelFullScreen) {
      elem.cancelFullScreen();
    } else if (elem.exitFullscreen) {
      elem.exitFullscreen();
    } else {
      // 浏览器不支持全屏API或已被禁用
    }
  };
  const [TabsNum, setTabsNum] = useState('黑名单同步');
  const [visible, setVisible] = useState(false);
  const callback = (e) => {
    setTabsNum(e.target.value);
    setFieldsValue({
      letfTime: moment().subtract(1, 'days'),
    });
  };
  useEffect(() => {}, []);

  const config = {
    header: ['监控指标', '平台变化两', '比对量', '检测时间', '指标情况'],
    data: [
      {
        attackType: '监控指标',
        subType: '平台变化两',
        level: '比对量',
        srcIp: '检测时间',
        region: '指标情况',
      },
      {
        attackType: '监控指标',
        subType: '平台变化两',
        level: '比对量',
        srcIp: '检测时间',
        region: '指标情况',
      },
      {
        attackType: '监控指标',
        subType: '平台变化两',
        level: '比对量',
        srcIp: '检测时间',
        region: '指标情况',
      },
      {
        attackType: '监控指标',
        subType: '平台变化两',
        level: '比对量',
        srcIp: '检测时间',
        region: '指标情况',
      },
      {
        attackType: '监控指标',
        subType: '平台变化两',
        level: '比对量',
        srcIp: '检测时间',
        region: '指标情况',
      },
    ],
    align: 'center',
    headerBGC: 'rgb(53,61,77)',
    oddRowBGC: 'rgb(53,61,77)',
    evenRowBGC: 'rgb(4,25,54)',
  };
  const getMonitorBlack = () => {
    request(`/api/hn/monitor/getMonitorBlack`, {
      method: 'POST',
      params: {},
      requestType: 'json',
    }).then((res) => {
      if (res.code == 200) {
        setMonitorBlack(res?.data || []);
      } else {
        setMonitorBlack([]);
      }
    });
  };
  const dataList = [
    // {
    //   attackType: '监控指标0',
    //   subType: '平台变化两',
    //   level: '比对量',
    //   srcIp: '检测时间',
    //   region: '指标情况',
    // },
  ];
  const abnormalColumn = [
    {
      title: '监控指标',
      dataIndex: 'blackType',
      key: 'blackType',
      align: 'center',
      colNum: 5,
      render: (val) =>
        (
          <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
            <div className={styles.handleColumnVal}>{val}</div>
          </Tooltip>
        ) || '-',
    },
    {
      title: '平台变化量',
      dataIndex: 'platformChange',
      key: 'platformChange',
      align: 'center',
      colNum: 5,
      render: (val) =>
        (
          <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
            <div className={styles.handleColumnVal}>{val}</div>
          </Tooltip>
        ) || '-',
    },
    {
      title: '比对量',
      dataIndex: 'contrastChange',
      key: 'contrastChange',
      align: 'center',
      colNum: 4,
      render: (val) =>
        (
          <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
            <div className={styles.handleColumnVal}>{val}</div>
          </Tooltip>
        ) || '-',
    },
    {
      title: '检测时间',
      dataIndex: 'monitorDate',
      key: 'monitorDate',
      align: 'center',
      colNum: 5,
      render: (val) =>
        (
          <Tooltip getPopupContainer={() => document.getElementById('indexWrapper')} title={val}>
            <div className={styles.handleColumnVal}>{val}</div>
          </Tooltip>
        ) || '-',
    },
    {
      title: '指标情况',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      colNum: 5,
      render: (val) =>
        // 状态 0异常 1正常
        {
          const type = {
            0: '异常',
            1: '正常',
          };
          return (
            (
              <Tooltip
                getPopupContainer={() => document.getElementById('indexWrapper')}
                title={type[val]}
              >
                <div
                  style={type[val] == '异常' ? { color: 'red' } : {}}
                  className={styles.handleColumnVal}
                >
                  {type[val]}
                </div>
              </Tooltip>
            ) || '-'
          );
        },
    },
  ];
  const columns = [
    {
      title: '级别',
      dataIndex: 'monitorLevel',
      key: 'monitorLevel',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={styles.tableCol}
              style={{
                background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
              }}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '监控分类',
      dataIndex: 'monitorType',
      key: 'monitorType',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={styles.tableCol}
              style={{
                background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
              }}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '告警时间',
      dataIndex: 'alarmDate',
      key: 'alarmDate',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={styles.tableCol}
              style={{
                background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
              }}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '告警描述',
      dataIndex: 'alarmDescribe',
      key: 'alarmDescribe',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={styles.tableCol}
              style={{
                background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
              }}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '负责人',
      dataIndex: 'headPeople',
      key: 'headPeople',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={styles.tableCol}
              style={{
                background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
              }}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'alarmState',
      key: 'alarmState',
      align: 'center',
      render: (value, row) => {
        // 处理状态 0待处理 1已处理
        const type = {
          0: '待处理',
          1: '已处理',
        };
        return (
          <Tooltip
            title={type[value]}
            getPopupContainer={() => document.getElementById('indexWrapper')}
          >
            <div
              className={styles.tableCol}
              style={{
                background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
              }}
            >
              {type[value] || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '处置结果',
      dataIndex: 'alarmResult',
      key: 'alarmResult',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div
              className={styles.tableCol}
              style={{
                background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
              }}
            >
              {value || '--'}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'address',
      key: 'address',
      align: 'center',
      render: (value, row) => {
        return (
          <div
            className={styles.tableCol}
            style={{
              background: row.monitorLevel == '严重' ? 'rgb(246,85,41)' : 'rgb(16,154,226)',
            }}
          >
            <Tag
              onClick={() => {
                setconfigvisible(true);
                setrow(row);
              }}
              style={{ height: '20px', fontSize: '12px' }}
              color="rgb(0,153,255)"
            >
              操作
            </Tag>
          </div>
        );
      },
    },
  ];
  const bottmColumns = [
    {
      title: '监控模型',
      dataIndex: 'monitorModel',
      key: 'level',
      align: 'center',
    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      key: 'threshold',
      align: 'center',
    },
    {
      title: '号码量',
      dataIndex: 'NumberQuantity',
      key: 'NumberQuantity',
      align: 'center',
    },
    {
      title: '检测时间',
      dataIndex: 'Time',
      key: 'Time',
      align: 'center',
    },
    {
      title: '是否正常',
      dataIndex: 'isnormal',
      key: 'normal',
      align: 'center',
    },
  ];
  const rightColumns = [
    {
      title: (
        <Tooltip
          title={'接口名称'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div className={styles.tableCol}>接口名称</div>
        </Tooltip>
      ),
      dataIndex: 'interfaceName',
      key: 'interfaceName',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'接口频率(天)'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div className={styles.tableCol}>{'接口频率(天)'}</div>
        </Tooltip>
      ),
      dataIndex: 'interfaceRate',
      key: 'interfaceRate',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{Number(value) || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip title={'数据量'} getPopupContainer={() => document.getElementById('indexWrapper')}>
          <div className={styles.tableCol}>{'数据量'}</div>
        </Tooltip>
      ),
      dataIndex: 'dataCount',
      key: 'dataCount',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip
            title={(value && value == 0 && String(value)) || '--'}
            getPopupContainer={() => document.getElementById('indexWrapper')}
          >
            <div className={styles.tableCol}>{(value && value == 0 && String(value)) || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'是否上传文件'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div className={styles.tableCol}>{'是否上传文件'}</div>
        </Tooltip>
      ),
      dataIndex: 'upSuccess',
      key: 'upSuccess',
      align: 'center',
      render: (value, row) => {
        const type = {
          0: '否',
          1: '是',
        };
        return (
          <Tooltip
            title={type[value]}
            getPopupContainer={() => document.getElementById('indexWrapper')}
          >
            <div className={styles.tableCol}>{type[value] || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'检测时间'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div className={styles.tableCol}>{'检测时间'}</div>
        </Tooltip>
      ),
      dataIndex: 'monitorDate',
      key: 'monitorDate',
      align: 'center',
      render: (value, row) => {
        return (
          <Tooltip title={value} getPopupContainer={() => document.getElementById('indexWrapper')}>
            <div className={styles.tableCol}>{value || '--'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: (
        <Tooltip
          title={'是否正常'}
          getPopupContainer={() => document.getElementById('indexWrapper')}
        >
          <div className={styles.tableCol}>{'是否正常'}</div>
        </Tooltip>
      ),
      dataIndex: 'upSuccess',
      key: 'upSuccess',
      align: 'center',
      render: (value, row) => {
        const type = {
          0: '异常',
          1: '正常',
        };
        return (
          <Tooltip
            title={type[value]}
            getPopupContainer={() => document.getElementById('indexWrapper')}
          >
            <div style={type[value] == '异常' ? { color: 'red' } : {}} className={styles.tableCol}>
              {type[value] || '--'}
            </div>
          </Tooltip>
        );
      },
    },
  ];
  const [TopData, setTopData] = useState({});
  const [RightData, setRightData] = useState({});
  const [TopShow, setTopShow] = useState({});
  const handleSearchTop = (pagination) => {
    validateFields((error, value) => {
      if (error) return;
      let params = {
        monitorType: value?.monitorType,
        alarmState: value?.alarmState,
        alarmDateStart: value?.alarmDate
          ? moment(value?.alarmDate[0]).format('YYYY-MM-DD 00:00:00')
          : '',
        alarmDateEnd: value?.alarmDate
          ? moment(value?.alarmDate[1]).format('YYYY-MM-DD 23:59:59')
          : '',
        pageSize: pagination?.pageSize || 10,
        currentPage: pagination?.current || 1,
      };
      request(`api/hn/monitor/getMonitorAlarmPage`, {
        //top 表格
        method: 'POST',
        data: params,
        requestType: 'json',
      }).then((res) => {
        if (res?.code == 200) {
          setTopData({
            list:
              res?.data?.items.map((item) => {
                let obj = {};
                for (let key in item) {
                  if (item[key] == 0) {
                    obj[key] = '0';
                  } else {
                    obj[key] = item[key];
                  }
                }
                return obj;
              }) || [],
            pagination: {
              pageSize: pagination?.pageSize || 10,
              current: pagination?.current || 1,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setTopData({
            list: [],
            pagination: {
              pageSize: 10,
              current: 1,
              total: 0,
            },
          });
        }
      });
      request(`api/hn/monitor/getMonitorAlarmTotal`, {
        //top 表格
        method: 'POST',
        data: {
          monitorType: params?.monitorType,
          alarmDateStart: params?.alarmDateStart,
          alarmDateEnd: params?.alarmDateEnd,
        },
        requestType: 'json',
      }).then((res) => {
        if (res?.code == 200) {
          setTopShow(res?.data || {});
        } else {
          setTopShow({});
        }
      });
    });
  };
  const RightTableChange = (pagination) => {
    handleSearchRight(pagination);
  };
  const handleSearchRight = (pagination) => {
    validateFields((error, value) => {
      if (error) return;
      let params = {
        interfaceName: value?.interfaceName,
        monitorDateStart: value?.monitorDate
          ? moment(value?.monitorDate[0]).format('YYYY-MM-DD 00:00:00')
          : '',
        monitorDateEnd: value?.monitorDate
          ? moment(value?.monitorDate[1]).format('YYYY-MM-DD 23:59:59')
          : '',
        pageSize: pagination?.pageSize || 10,
        currentPage: pagination?.current || 1,
      };
      request(`/api/hn/monitor/getMonitorInterfaceData`, {
        //top 表格
        method: 'POST',
        data: params,
        requestType: 'json',
      }).then((res) => {
        if (res?.code == 200) {
          setRightData({
            list:
              res?.data?.items.map((item) => {
                let obj = {};
                for (let key in item) {
                  if (item[key] == 0) {
                    obj[key] = '0';
                  } else {
                    obj[key] = item[key];
                  }
                }
                return obj;
              }) || [],
            pagination: {
              pageSize: pagination?.pageSize || 10,
              current: pagination?.current || 1,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setRightData({
            list: [],
            pagination: {
              pageSize: 10,
              current: 1,
              total: 0,
            },
          });
        }
      });
    });
  };
  const TopTableChange = (pagination) => {
    handleSearchTop(pagination);
  };
  // useEffect(() => {
  //   // Handler to call on window resize
  //   function handleResize() {
  //     document.getElementById('indexWrapper').style.zoom =
  //       document.documentElement.clientWidth / 1920;
  //   }
  //   // Add event listener
  //   window.addEventListener('resize', handleResize);

  //   // Call handler right away so state gets updated with initial window size
  //   handleResize();

  //   // Remove event listener on cleanup
  //   return () => window.removeEventListener('resize', handleResize);
  // }, []);
  return (
    <div id="indexWrapper" className={styles.container}>
      <div className={styles.main}>
        <div className={styles.left}>
          <div
            className={styles.leftTop}
            style={isFullScreen ? { width: '95%', height: '31%', marginBottom: '3%' } : {}}
          >
            <BorderBox7>
              <Title title="关停数据同步监控"></Title>
              <LineEcharts isFullScreen={isFullScreen} time={2000}></LineEcharts>
            </BorderBox7>
          </div>
          <div
            className={styles.leftCenter}
            style={isFullScreen ? { width: '95%', height: '31%', marginBottom: '3%' } : {}}
          >
            <BorderBox7>
              <Title title="用户信息同步监控"></Title>
              <BarEcharts isFullScreen={isFullScreen} time={1000}></BarEcharts>
            </BorderBox7>
          </div>
          <div
            className={styles.leftBottom}
            style={isFullScreen ? { width: '95%', height: '33%' } : {}}
          >
            <BorderBox7>
              <div className={styles.GJ}>
                <Title style={{ width: '60%' }} title="平台综合告警"></Title>
                <div>
                  <Radio.Group value={TabsNum} onChange={callback} style={{ marginBottom: 16 }}>
                    <Radio.Button value="黑名单同步">黑名单同步</Radio.Button>
                    <Radio.Button value="集团渠道关停号码同步">集团渠道关停号码同步</Radio.Button>
                    <Radio.Button value="集团白名单流程">集团白名单流程</Radio.Button>
                  </Radio.Group>
                </div>
              </div>
              {TabsNum == '黑名单同步' && (
                <div
                  className={styles.ScrollBoard}
                  style={{ width: '100%', height: 'calc(100% - 34px)' }}
                  id="ScrollBoard"
                >
                  <RollView
                    listData={MonitorBlack || []}
                    columnConfig={abnormalColumn}
                    rowKey={'id'}
                    showCount={isFullScreen ? 6 : 4}
                    listItemHeight={22}
                  ></RollView>
                </div>
              )}
              {TabsNum != '黑名单同步' && (
                <>
                  <div className={styles.titleTime}>
                    <Form>
                      <Form.Item wrapperCol={{ span: isFullScreen ? 7 : 10 }}>
                        {getFieldDecorator('letfTime', {
                          initialValue: moment().subtract(1, 'days'),
                        })(
                          <DatePicker
                            getCalendarContainer={() => document.getElementById('indexWrapper')}
                            placeholder=""
                            allowClear={false}
                          />,
                        )}
                      </Form.Item>
                    </Form>
                  </div>
                  <FlowPath
                    letfTime={getFieldValue('letfTime')}
                    isFullScreen={isFullScreen}
                    type={TabsNum}
                  ></FlowPath>
                </>
              )}
            </BorderBox7>
          </div>
        </div>
        <div className={styles.center}>
          <div className={styles.centerTop}>
            <div className={styles.centerTopTitle}>
              <div className={styles.centerTopTitleContent}>反诈管理平台运营监控中心</div>
              <Decoration5
                style={{
                  width: '100%',
                  height: '40px',
                  paddingTop: '16px',
                }}
                dur={1}
              ></Decoration5>
            </div>
          </div>
          <div className={styles.centerGJNumber}>
            <Decoration11
              color={['rgb(105,85,233)', 'rgb(105,85,233)']}
              style={{ width: '200px', height: '60px' }}
            >
              <div className={styles.centerGJNumberContent}>
                <div style={{ color: 'rgb(105,85,233)' }} className={styles.centerGJNumberTitle}>
                  告警总数
                </div>
                <div
                  title={TopShow?.alarmTotal}
                  style={{ color: 'rgb(105,85,233)' }}
                  className={styles.centerGJNumberData}
                >
                  {TopShow?.alarmTotal || '--'}
                </div>
              </div>
            </Decoration11>
            <Decoration11
              color={['rgb(226,80,42)', 'rgb(226,80,42)']}
              style={{ width: '200px', height: '60px' }}
            >
              <div className={styles.centerGJNumberContent}>
                <div style={{ color: 'rgb(226,80,42)' }} className={styles.centerGJNumberTitle}>
                  严重告警
                </div>
                <div
                  title={TopShow?.seriousAlarm}
                  style={{ color: 'rgb(226,80,42)' }}
                  className={styles.centerGJNumberData}
                >
                  {TopShow?.seriousAlarm || '--'}
                </div>
              </div>
            </Decoration11>
            <Decoration11
              color={['rgb(26,154,202)', 'rgb(26,154,202)']}
              style={{ width: '200px', height: '60px' }}
            >
              <div className={styles.centerGJNumberContent}>
                <div style={{ color: 'rgb(26,154,202)' }} className={styles.centerGJNumberTitle}>
                  普通告警
                </div>
                <div
                  title={TopShow?.ordinaryAlarm}
                  style={{ color: 'rgb(26,154,202)' }}
                  className={styles.centerGJNumberData}
                >
                  {TopShow?.ordinaryAlarm || '--'}
                </div>
              </div>
            </Decoration11>
          </div>
          <div className={isFullScreen ? styles.centerCenterIsFullScreen : styles.centerCenter}>
            <Form>
              <Row>
                <Col span={6}>
                  <Form.Item labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="监控分类">
                    {getFieldDecorator('monitorType')(
                      <Select
                        placeholder="请选择"
                        allowClear
                        getPopupContainer={() => document.getElementById('indexWrapper')}
                      >
                        <Option value="模型监控">模型监控</Option>
                        <Option value="平台监控">平台监控</Option>
                        <Option value="接口监控">接口监控</Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} label="告警时间">
                    {getFieldDecorator('alarmDate', {
                      initialValue: [moment(), moment()],
                    })(
                      <RangePicker
                        format={'YYYY-MM-DD'}
                        getCalendarContainer={() => document.getElementById('indexWrapper')}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item labelCol={{ span: 7 }} wrapperCol={{ span: 17 }} label={'状态'}>
                    {getFieldDecorator('alarmState', {
                      initialValue: '0',
                    })(
                      <Select
                        placeholder="请选择"
                        allowClear
                        getPopupContainer={() => document.getElementById('indexWrapper')}
                      >
                        <Option value="0">待处理</Option>
                        <Option value="1">已处理</Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Tag
                    style={{ margin: '10px 4px 0 4px' }}
                    onClick={() => {
                      handleSearchTop();
                    }}
                    color="rgb(0,153,255)"
                  >
                    查询
                  </Tag>
                  <Tag
                    color="rgb(0,153,255)"
                    onClick={() => {
                      validateFields((error, value) => {
                        let params = {
                          monitorType: value?.monitorType,
                          alarmState: value?.alarmState,
                          alarmDateStart: value?.alarmDate
                            ? moment(value?.alarmDate[0]).format('YYYY-MM-DD 00:00:00')
                            : '',
                          alarmDateEnd: value?.alarmDate
                            ? moment(value?.alarmDate[1]).format('YYYY-MM-DD 23:59:59')
                            : '',
                        };
                        exportFile({
                          urlAPi: '/api/hn/monitor/exportMonitorAlarm',
                          decode: true,
                          params: params,
                          method: 'POST',
                        });
                      });
                    }}
                  >
                    导出
                  </Tag>
                </Col>
                <Col span={24} style={{ height: isFullScreen ? '362px' : '151px' }}>
                  <Table
                    bordered={true}
                    pagination={TopData?.pagination}
                    onChange={TopTableChange}
                    columns={columns}
                    size="small"
                    dataSource={TopData?.list || []}
                  ></Table>
                </Col>
              </Row>
            </Form>
          </div>
          <div className={isFullScreen ? styles.centerBottomIsFullScreen : styles.centerBottom}>
            <BorderBox7>
              <Form>
                <Row>
                  <Col span={6}>
                    <Form.Item labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="监控模型">
                      {getFieldDecorator('JKMX')(<Input></Input>)}
                    </Form.Item>
                  </Col>
                  <Col span={isFullScreen ? 10 : 8}>
                    <Form.Item
                      labelCol={{ span: isFullScreen ? 6 : 8 }}
                      wrapperCol={{ span: isFullScreen ? 18 : 16 }}
                      label="检测时间"
                    >
                      {getFieldDecorator('JKMXTime')(
                        <RangePicker
                          format={'YYYY-MM-DD'}
                          getCalendarContainer={() => document.getElementById('indexWrapper')}
                        />,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={isFullScreen ? 8 : 10}>
                    <Tag style={{ margin: '10px 4px 0 4px' }} color="rgb(0,153,255)">
                      查询
                    </Tag>
                    <Tag style={{ margin: '10px 4px 0 4px' }} color="rgb(0,153,255)">
                      导出
                    </Tag>
                    <Tag
                      color="rgb(0,153,255)"
                      onClick={() => {
                        setVisible(true);
                      }}
                    >
                      模型监控配置
                    </Tag>
                  </Col>
                  <Col span={24} style={{ height: isFullScreen ? '205px' : '130px' }}>
                    <Table
                      bordered={true}
                      pagination={false}
                      dataSource={[
                        {
                          monitorModel: '话单数据',
                          threshold: '》=10000',
                          NumberQuantity: '200000',
                          Time: '2023/5/31 10:00:00',
                          isnormal: '正常',
                        },
                        {
                          monitorModel: '手机终端变化',
                          threshold: '100-2000',
                          NumberQuantity: '780',
                          Time: '2023/5/31 10:00:00',
                          isnormal: '正常',
                        },
                        {
                          monitorModel: 'SP短信异常',
                          threshold: '1-2000',
                          NumberQuantity: '200',
                          Time: '2023/5/31 10:00:00',
                          isnormal: '正常',
                        },
                        {
                          monitorModel: '高风险异常通话',
                          threshold: '1-500',
                          NumberQuantity: '88',
                          Time: '2023/5/31 10:00:00',
                          isnormal: '正常',
                        },
                        {
                          monitorModel: '全网黑终端用户',
                          threshold: '1-500',
                          NumberQuantity: '210',
                          Time: '2023/5/31 10:00:00',
                          isnormal: '正常',
                        },
                        {
                          monitorModel: '次级高危地',
                          threshold: '1-500',
                          NumberQuantity: '100',
                          Time: '2023/5/31 10:00:00',
                          isnormal: '正常',
                        },
                      ]}
                      columns={bottmColumns}
                      size="small"
                    ></Table>
                  </Col>
                </Row>
              </Form>
            </BorderBox7>
            <Pagination
              style={{ float: 'right', margin: '4px 0' }}
              size="small"
              pageSize={10}
              current={1}
              total={6}
            ></Pagination>
          </div>
        </div>
        <div className={styles.right}>
          <div
            className={isFullScreen ? styles.rightTopIsFullScreen : styles.rightTop}
            style={isFullScreen ? { width: '95%', height: '49%' } : {}}
          >
            <BorderBox7>
              <div style={{ width: '100%', textAlign: 'right', display: 'flex' }}>
                <Title
                  style={{ width: 'calc(100% - 14px)', textAlign: 'left' }}
                  title="接口监控"
                ></Title>
                {isFullScreen ? (
                  <Icon
                    type="shrink"
                    onClick={() => {
                      exitFullscreen();
                    }}
                  />
                ) : (
                  <Icon
                    type="arrows-alt"
                    onClick={() => {
                      toFullScreen();
                    }}
                  />
                )}
              </div>
              <Form>
                <Row>
                  <Col span={9}>
                    <Form.Item labelCol={{ span: 14 }} wrapperCol={{ span: 10 }} label="接口名称">
                      {getFieldDecorator('interfaceName')(<Input placeholder=""></Input>)}
                    </Form.Item>
                  </Col>
                  <Col span={isFullScreen ? 9 : 15}>
                    <Form.Item
                      labelCol={{ span: isFullScreen ? 8 : 7 }}
                      wrapperCol={{ span: isFullScreen ? 16 : 17 }}
                      label="检测时间"
                    >
                      {getFieldDecorator('monitorDate', {
                        initialValue: [moment(), moment()],
                      })(
                        <RangePicker
                          format={'YYYY-MM-DD'}
                          getCalendarContainer={() => document.getElementById('indexWrapper')}
                        />,
                      )}
                    </Form.Item>
                  </Col>
                  <Col align="right" span={isFullScreen ? 6 : 24}>
                    <Tag
                      style={{ margin: '10px 4px 0 4px' }}
                      onClick={() => {
                        handleSearchRight();
                      }}
                      color="rgb(0,153,255)"
                    >
                      查询
                    </Tag>
                    <Tag
                      onClick={() => {
                        validateFields((error, value) => {
                          let params = {
                            interfaceName: value?.interfaceName,
                            monitorDateStart: value?.monitorDate
                              ? moment(value?.monitorDate[0]).format('YYYY-MM-DD 00:00:00')
                              : '',
                            monitorDateEnd: value?.monitorDate
                              ? moment(value?.monitorDate[1]).format('YYYY-MM-DD 23:59:59')
                              : '',
                          };
                          exportFile({
                            urlAPi: '/api/hn/monitor/exportMonitorInterfaceData',
                            decode: true,
                            params: params,
                            method: 'POST',
                          });
                        });
                      }}
                      color="rgb(0,153,255)"
                    >
                      导出
                    </Tag>
                  </Col>
                  <Col span={24} style={{ height: isFullScreen ? '205px' : '130px' }}>
                    <Table
                      bordered={true}
                      pagination={{
                        pageSize: RightData?.pagination?.pageSize || 10,
                        current: RightData?.pagination?.current || 1,
                        total: RightData?.pagination?.total || 0,
                      }}
                      onChange={RightTableChange}
                      dataSource={RightData?.list || []}
                      columns={rightColumns}
                      size="small"
                    ></Table>
                  </Col>
                </Row>
              </Form>
            </BorderBox7>
          </div>
          <div
            className={styles.rightBottom}
            style={isFullScreen ? { width: '95%', height: '48%' } : {}}
          >
            <BorderBox7>
              <div style={{ display: 'flex' }}>
                <Title
                  title="断卡流程"
                  style={{
                    float: 'left',
                    width: isFullScreen ? '14%' : '22%',
                    lineHeight: isFullScreen ? '30px' : '26px',
                  }}
                ></Title>
                <div className={styles.titleTime}>
                  <Form>
                    <Form.Item wrapperCol={{ span: 15 }}>
                      {getFieldDecorator('RightTime', {
                        initialValue: moment().subtract(1, 'days'),
                      })(
                        <DatePicker
                          placeholder=""
                          getCalendarContainer={() => document.getElementById('indexWrapper')}
                          allowClear={false}
                        />,
                      )}
                    </Form.Item>
                  </Form>
                </div>
              </div>
              <FlowPath
                RightTime={moment(getFieldValue('RightTime')).format('YYYY-MM-DD 00:00:00')}
                isFullScreen={isFullScreen}
                type="duanka"
              ></FlowPath>
            </BorderBox7>
          </div>
        </div>
        <ConfigModal
          getContainer={() => {
            document.getElementById('indexWrapper');
          }}
          visible={visible}
          setVisible={setVisible}
        ></ConfigModal>
        <OptionModal
          configvisible={configvisible}
          row={row}
          setconfigvisible={setconfigvisible}
          handleSearchTop={handleSearchTop}
        ></OptionModal>
      </div>
    </div>
  );
}
export default Form.create()(index);
