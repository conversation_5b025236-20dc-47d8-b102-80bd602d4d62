.container::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.tableCol {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.ScrollBoard {}

.container {
  width: 100%;
  height: 100%;
  overflow: scroll;
  color: #FFF;

  :global {
    .ant-modal {
      height: initial !important;
      padding-bottom: 0px;
    }

    .ant-modal-content {
      background-color: rgb(46, 54, 68);
    }


    .ant-modal-header {
      background-color: rgb(46, 54, 68);
    }

    .ant-modal-title {
      color: #FFF;
    }

    .ant-modal-close-x {
      color: #FFF;
    }
  }

  * {
    box-sizing: border-box;
  }

  h1,
  h2,
  h3,
  h4,
  p {
    margin: 0;
    padding: 0;
  }

  background: rgb(35, 40, 51);
  background-size: 100% 100%;
  padding:10px;
  position: relative;
  // height: 1700px;
}

.handleColumnVal {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100px;
  text-align: center;
}

.main {
  display: flex;
  height: 100%;

  .left {
    flex: 3;
  }

  .center {
    flex: 4;

    .centerTopTitle {
      position: relative;
      font-size: 40px;
      font-family: PangMenZhengDao;
      font-weight: 550;
      color: #fff;
      text-align: center;

      .centerTopTitleContent {
        position: absolute;
        font-size: 25px;
        width: 100%;
        text-align: center;
      }
    }

    .centerGJNumber {
      display: flex;
      margin-top: 30px;

      .centerGJNumberContent {
        display: flex;
        height: 43px;
        line-height: 43px;

        .centerGJNumberTitle {
          margin-right: 22px;
        }

        .centerGJNumberData {
          font-size: 29px;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 62.5px;
          text-align: center;
          overflow: hidden;
        }
      }
    }
  }
}

.right {
  flex: 3;

  .rightTop {
    width: 290px;
    height: 273px;
    float: right;
    margin-bottom: 10px;

    :global {
      .ant-form-item label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
      }

      .ant-form-item {
        margin-bottom: 0px;
      }

      .ant-table-tbody>tr.ant-table-row:hover>td {
        background: none !important;
      }

      .ant-empty-normal {
        display: none;
      }

      .ant-select-selection {
        font-size: 12px;
        height: 20px;
        margin-top: 11px;
      }

      .ant-select-selection__rendered {
        line-height: 20px;
      }

      .ant-input {
        margin-top: 11px;
        height: 20px;
        font-size: 12px;
        line-height: 12px;
      }

      .ant-table-column-title {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        display: inline-block;
      }

      .ant-table-placeholder {
        background: rgb(35, 40, 51);
        border: 1px solid rgb(121, 121, 121);
        display: none;
      }

      .ant-table-body {
        background: rgb(53, 61, 77);
        margin: 0px !important;
      }

      .ant-empty-description {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-table-bordered .ant-table-tbody>tr>td {
        border-right: 1px solid rgb(121, 121, 121);
      }

      .ant-table-small {
        border-radius: 0;
        border: 1px solid rgb(121, 121, 121);
      }

      .ant-table-bordered .ant-table-thead>tr>th {
        border-right: 1px solid rgb(121, 121, 121);
      }

      .ant-table-align-center {
        border-bottom: 1px solid rgb(121, 121, 121) !important;
      }

      .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

        color: rgba(255, 255, 255, 0.8);
      }

      .ant-table-tbody {
        display: block;
        color: #fff;
        text-align: center;
        height: 54px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
      }

      .ant-table-thead>tr,
      .ant-table-tbody>tr {
        display: table;
        width: 100%;
        table-layout: fixed;
      }

      .ant-table-thead>tr {
        border: none;
      }

      table tbody::-webkit-scrollbar {
        width: 0px;
      }

      .ant-table-tbody>tr>td {
        border-bottom: 1px solid rgb(121, 121, 121) !important;
      }

      .ant-table-thead>tr:first-child>th:first-child {
        border-radius: 0px;
      }

      .ant-table-small.ant-table-bordered .ant-table-content {
        border-right: 1px solid rgb(121, 121, 121);
      }

      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-active,
      .ant-pagination-item-active:hover {
        border-color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item {
        a {
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .ant-pagination-item-container {
        .anticon {
          color: rgba(255, 255, 255, 0.8);
        }

        .ant-pagination-item-ellipsis {
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .ant-pagination-item-active {
        background: rgb(35, 40, 51);
      }
    }
  }

  .rightTopIsFullScreen {
    width: 290px;
    height: 238px;
    float: right;
    margin-bottom: 10px;

    :global {
      .ant-form-item label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
      }

      .ant-form-item {
        margin-bottom: 0px;
      }

      .ant-table-tbody>tr.ant-table-row:hover>td {
        background: none !important;
      }

      .ant-empty-normal {
        display: none;
      }

      .ant-select-selection {
        font-size: 12px;
        height: 20px;
        margin-top: 11px;
      }

      .ant-select-selection__rendered {
        line-height: 20px;
      }

      .ant-input {
        margin-top: 11px;
        height: 20px;
        font-size: 12px;
        line-height: 12px;
      }

      .ant-table-column-title {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        display: inline-block;
      }

      .ant-table-placeholder {
        background: rgb(35, 40, 51);
        border: 1px solid rgb(121, 121, 121);
        display: none;
      }

      .ant-table-body {
        background: rgb(53, 61, 77);
        margin: 0px !important;
      }

      .ant-empty-description {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-table-bordered .ant-table-tbody>tr>td {
        border-right: 1px solid rgb(121, 121, 121);
      }

      .ant-table-small {
        border-radius: 0;
        border: 1px solid rgb(121, 121, 121);
      }

      .ant-table-bordered .ant-table-thead>tr>th {
        border-right: 1px solid rgb(121, 121, 121);
      }

      .ant-table-align-center {
        border-bottom: 1px solid rgb(121, 121, 121) !important;
      }

      .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

        color: rgba(255, 255, 255, 0.8);
      }

      .ant-table-tbody {
        display: block;
        color: #fff;
        text-align: center;
        height: 247px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
      }

      .ant-table-thead>tr,
      .ant-table-tbody>tr {
        display: table;
        width: 100%;
        table-layout: fixed;
      }

      .ant-table-thead>tr {
        border: none;
      }

      table tbody::-webkit-scrollbar {
        width: 0px;
      }

      .ant-table-tbody>tr>td {
        border-bottom: 1px solid rgb(121, 121, 121) !important;
      }

      .ant-table-thead>tr:first-child>th:first-child {
        border-radius: 0px;
      }

      .ant-table-small.ant-table-bordered .ant-table-content {
        border-right: 1px solid rgb(121, 121, 121);
      }

      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-active,
      .ant-pagination-item-active:hover {
        border-color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item {
        a {
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .ant-pagination-item-container {
        .anticon {
          color: rgba(255, 255, 255, 0.8);
        }

        .ant-pagination-item-ellipsis {
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .ant-pagination-item-active {
        background: rgb(35, 40, 51);
      }
    }
  }

  .rightBottom {
    width: 290px;
    height: 264px;
    float: right;
  }
}

#ScrollBoard {
  :global {
    .ant-col :nth-child(5) div {
      border-right: 0px;
    }
  }
}

.leftTop {
  width: 290px;
  height: 160px;
  margin-bottom: 10px;
}

.leftCenter {
  width: 290px;
  height: 160px;
  margin-bottom: 10px;
}

.leftBottom {
  width: 290px;
  height: 206px;
}

.GJ {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);

  :global {
    .ant-radio-group {
      margin: 0 !important;
      width: 100%;
      position: relative;
      top: 3px;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      box-shadow: none;
      border-top: 1px solid rgba(255, 255, 255, 0.3);
      border-left: 1px solid rgba(255, 255, 255, 0.3);
      border-right: 1px solid rgba(255, 255, 255, 0.3);
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
      color: rgba(255, 255, 255, 0.7);
      border-color: rgba(255, 255, 255, 0.3);
      box-shadow: none;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
      color: rgba(255, 255, 255, 0.7);
      border-color: rgba(255, 255, 255, 0.3);
      box-shadow: none;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      display: none;
    }

    .ant-radio-button-wrapper {
      width: 37%;
      font-size: 10px;
      padding: 1px;
      display: flex;
      align-items: center;
      line-height: 13px;
      justify-content: center;
      background: rgba(0, 0, 0, 0);
      color: rgba(255, 255, 255, 0.7);
      border: 0px;

      span:nth-child(2) {
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        -webkit-line-clamp: 2;
        overflow: hidden;
      }


    }

    .ant-radio-group-outline {
      display: flex;
      margin: 0px;
    }
  }
}

.centerCenter {
  width: 100%;
  margin-bottom: 3%;

  :global {
    .ant-form-item label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-table-tbody>tr.ant-table-row:hover>td {
      background: none !important;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection {
      font-size: 12px;
      height: 20px;
      margin-top: 11px;
    }

    .ant-select-selection__rendered {
      line-height: 20px;
    }

    .ant-input {
      margin-top: 11px;
      height: 20px;
      font-size: 12px;
      line-height: 12px;
    }

    .ant-table-column-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-table-placeholder {
      background: rgb(35, 40, 51);
      border: 1px solid rgb(121, 121, 121);
      display: none;
    }

    .ant-table-body {
      background: rgb(53, 61, 77);
      margin: 0px !important;
    }

    .ant-empty-description {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-bordered .ant-table-tbody>tr>td {
      border-right: 1px solid rgb(121, 121, 121);
      padding: 0px !important;
    }

    .ant-table-small {
      border-radius: 0;
      border: 1px solid rgb(121, 121, 121);
    }

    .ant-table-bordered .ant-table-thead>tr>th {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-align-center {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-thead>tr>th {
      background: rgb(5, 27, 100);
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-tbody {
      display: block;
      color: #fff;
      text-align: center;
      height: 76px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .ant-table-thead>tr,
    .ant-table-tbody>tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    table tbody::-webkit-scrollbar {
      width: 0px;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-thead>tr:first-child>th:first-child {
      border-radius: 0px;
    }

    .ant-table-small.ant-table-bordered .ant-table-content {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .anticon {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item-active,
    .ant-pagination-item-active:hover {
      border-color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item {
      a {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-container {
      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-ellipsis {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-active {
      background: rgb(35, 40, 51);
    }
  }
}

.centerCenterIsFullScreen {
  width: 100%;
  margin-bottom: 3%;

  :global {
    .ant-form-item label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-table-tbody>tr.ant-table-row:hover>td {
      background: none !important;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection {
      font-size: 12px;
      height: 20px;
      margin-top: 11px;
    }

    .ant-select-selection__rendered {
      line-height: 20px;
    }

    .ant-input {
      margin-top: 11px;
      height: 20px;
      font-size: 12px;
      line-height: 12px;
    }

    .ant-table-column-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-table-placeholder {
      background: rgb(35, 40, 51);
      border: 1px solid rgb(121, 121, 121);
      display: none;
    }

    .ant-table-body {
      background: rgb(53, 61, 77);
      margin: 0px !important;
    }

    .ant-empty-description {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-bordered .ant-table-tbody>tr>td {
      border-right: 1px solid rgb(121, 121, 121);
      padding: 0px !important;
    }

    .ant-table-small {
      border-radius: 0;
      border: 1px solid rgb(121, 121, 121);
    }

    .ant-table-bordered .ant-table-thead>tr>th {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-align-center {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-thead>tr>th {
      background: rgb(5, 27, 100);
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-tbody {
      display: block;
      color: #fff;
      text-align: center;
      height: 282px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .ant-table-thead>tr,
    .ant-table-tbody>tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    table tbody::-webkit-scrollbar {
      width: 0px;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-thead>tr:first-child>th:first-child {
      border-radius: 0px;
    }

    .ant-table-small.ant-table-bordered .ant-table-content {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .anticon {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item-active,
    .ant-pagination-item-active:hover {
      border-color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item {
      a {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-container {
      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-ellipsis {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-active {
      background: rgb(35, 40, 51);
    }
  }
}

.centerBottom {
  width: 100%;

  :global {
    .ant-form-item label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-table-tbody>tr.ant-table-row:hover>td {
      background: none !important;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection {
      font-size: 12px;
      height: 20px;
      margin-top: 11px;
    }

    .ant-select-selection__rendered {
      line-height: 20px;
    }

    .ant-input {
      margin-top: 11px;
      height: 20px;
      font-size: 12px;
      line-height: 12px;
    }

    .ant-table-column-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-table-placeholder {
      background: rgb(35, 40, 51);
      border: 1px solid rgb(121, 121, 121);
      display: none;
    }

    .ant-table-body {
      background: rgb(53, 61, 77);
      margin: 0px !important;
    }

    .ant-empty-description {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-bordered .ant-table-tbody>tr>td {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-small {
      border-radius: 0;
      border: 1px solid rgb(121, 121, 121);
    }

    .ant-table-bordered .ant-table-thead>tr>th {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-align-center {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-tbody {
      display: block;
      color: #fff;
      text-align: center;
      height: 90px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .ant-table-thead>tr,
    .ant-table-tbody>tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .ant-table-thead>tr {
      border: none;
    }

    table tbody::-webkit-scrollbar {
      width: 0px;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-thead>tr:first-child>th:first-child {
      border-radius: 0px;
    }

    .ant-table-small.ant-table-bordered .ant-table-content {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .anticon {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item-active,
    .ant-pagination-item-active:hover {
      border-color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item {
      a {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-container {
      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-ellipsis {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-active {
      background: rgb(35, 40, 51);
    }
  }
}

.centerBottomIsFullScreen {
  width: 100%;

  :global {
    .ant-form-item label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-table-tbody>tr.ant-table-row:hover>td {
      background: none !important;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection {
      font-size: 12px;
      height: 20px;
      margin-top: 11px;
    }

    .ant-select-selection__rendered {
      line-height: 20px;
    }

    .ant-input {
      margin-top: 11px;
      height: 20px;
      font-size: 12px;
      line-height: 12px;
    }

    .ant-table-column-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-table-placeholder {
      background: rgb(35, 40, 51);
      border: 1px solid rgb(121, 121, 121);
      display: none;
    }

    .ant-table-body {
      background: rgb(53, 61, 77);
      margin: 0px !important;
    }

    .ant-empty-description {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-bordered .ant-table-tbody>tr>td {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-small {
      border-radius: 0;
      border: 1px solid rgb(121, 121, 121);
    }

    .ant-table-bordered .ant-table-thead>tr>th {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-align-center {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-tbody {
      display: block;
      color: #fff;
      text-align: center;
      height: 163px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .ant-table-thead>tr,
    .ant-table-tbody>tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .ant-table-thead>tr {
      border: none;
    }

    table tbody::-webkit-scrollbar {
      width: 0px;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-thead>tr:first-child>th:first-child {
      border-radius: 0px;
    }

    .ant-table-small.ant-table-bordered .ant-table-content {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .anticon {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item-active,
    .ant-pagination-item-active:hover {
      border-color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item {
      a {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-container {
      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-ellipsis {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-active {
      background: rgb(35, 40, 51);
    }
  }
}

.centerBottom {
  width: 100%;

  :global {
    .ant-form-item label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-form-item {
      margin-bottom: 0px;
    }

    .ant-table-tbody>tr.ant-table-row:hover>td {
      background: none !important;
    }

    .ant-empty-normal {
      display: none;
    }

    .ant-select-selection {
      font-size: 12px;
      height: 20px;
      margin-top: 11px;
    }

    .ant-select-selection__rendered {
      line-height: 20px;
    }

    .ant-input {
      margin-top: 11px;
      height: 20px;
      font-size: 12px;
      line-height: 12px;
    }

    .ant-table-column-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .ant-table-placeholder {
      background: rgb(35, 40, 51);
      border: 1px solid rgb(121, 121, 121);
      display: none;
    }

    .ant-table-body {
      background: rgb(53, 61, 77);
      margin: 0px !important;
    }

    .ant-empty-description {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-bordered .ant-table-tbody>tr>td {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-small {
      border-radius: 0;
      border: 1px solid rgb(121, 121, 121);
    }

    .ant-table-bordered .ant-table-thead>tr>th {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .ant-table-align-center {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-small>.ant-table-content>.ant-table-body>table>.ant-table-tbody>tr>td {

      color: rgba(255, 255, 255, 0.8);
    }

    .ant-table-tbody {
      display: block;
      color: #fff;
      text-align: center;
      height: 90px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .ant-table-thead>tr,
    .ant-table-tbody>tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .ant-table-thead>tr {
      border: none;
    }

    table tbody::-webkit-scrollbar {
      width: 0px;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid rgb(121, 121, 121) !important;
    }

    .ant-table-thead>tr:first-child>th:first-child {
      border-radius: 0px;
    }

    .ant-table-small.ant-table-bordered .ant-table-content {
      border-right: 1px solid rgb(121, 121, 121);
    }

    .anticon {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item-active,
    .ant-pagination-item-active:hover {
      border-color: rgba(255, 255, 255, 0.8);
    }

    .ant-pagination-item {
      a {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-container {
      .anticon {
        color: rgba(255, 255, 255, 0.8);
      }

      .ant-pagination-item-ellipsis {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-pagination-item-active {
      background: rgb(35, 40, 51);
    }
  }
}

.MoadlTable {
  :global {
    .ant-table-body {

      colgroup {
        display: flex;
        width: 949px;
      }
    }

    .ant-table-tbody {
      height: 300px;

    }
  }

}

.Form {
  width: 100%;

  :global {
    .ant-form-item-label {
      label {
        color: #FFF
      }
    }
  }
}

.titleTime {
  :global {
    .ant-calendar-picker-input {
      height: 20px;
      background-color: rgb(16, 154, 226);
      color: #FFF;
    }

    .ant-form-item {
      margin-bottom: 0;
    }
  }
}

.flowPath {
  width: 30%;
  height: 40px;
  background: rgb(4, 25, 54);
  line-height: 40px;
  text-align: center;
  border: 1px solid rgb(255, 255, 255, 0.7);
  font-size: 12px;
}

.TooltipClass {
  :global {
    .ant-tooltip-arrow::before {
      background: rgb(204, 204, 204);
    }

    .ant-tooltip-inner {
      color: #000;
      font-size: 12px;
      background: rgb(204, 204, 204);
    }
  }
}
