import React, { useEffect, useState } from 'react';
import {
  Input,
  TextArea,
  Select,
  Modal,
  Row,
  Form,
  Col,
  Upload,
  Button,
  Icon,
  message,
  Table,
  Tag,
} from 'antd';
import FileViewer from 'react-file-viewer';
import request from 'ponshine-request';
import styles from '../index.less';
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const Index = (props) => {
  const {
    configvisible,
    setconfigvisible,
    row,
    form: { getFieldDecorator, getFieldsValue },
    handleSearchTop,
  } = props;
  const submit = () => {
    if (!getFieldsValue().alarmResult) {
      message.error('处置结果为必填！');
      return;
    }
    request('api/hn/monitor/updateMonitorAlarm', {
      method: 'POST',
      data: {
        id: row?.id,
        alarmResult: getFieldsValue().alarmResult,
      },
      requestType: 'json',
    }).then((res) => {
      if (res?.code == 200) {
        message.success(res?.message);
        handleSearchTop();
        setconfigvisible(false);
      } else {
        message.error(res?.message);
      }
    });
  };
  return (
    <>
      {configvisible && (
        <Modal
          title={'监控告警处置'}
          visible={configvisible}
          width={800}
          style={{ background: 'rgb(46,54,68)' }}
          bodyStyle={{ content: '', display: '-webkit-box' }}
          footer={
            <div>
              <Button
                type="primary"
                onClick={() => {
                  submit();
                }}
              >
                确认
              </Button>
              <Button
                onClick={() => {
                  setconfigvisible();
                }}
              >
                取消
              </Button>
            </div>
          }
          height={300}
          onCancel={() => {
            setconfigvisible();
          }}
          centered={true}
          getContainer={document.getElementById('indexWrapper')}
        >
          <div className={styles.Form}>
            <Form style={{ width: '100%' }} layout={'horizontal'}>
              <Col span={24}>
                <Form.Item label={'处置结果'} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
                  {getFieldDecorator('alarmResult', {
                    initialValue: row?.alarmResult,
                  })(<Input.TextArea height={230} allowClear placeholder="请填写" />)}
                </Form.Item>
              </Col>
            </Form>
          </div>
        </Modal>
      )}
    </>
  );
};
export default Form.create()(Index);
