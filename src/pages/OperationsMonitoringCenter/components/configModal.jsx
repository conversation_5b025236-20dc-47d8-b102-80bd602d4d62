import React, { useEffect, useState } from 'react';
import {
  Input,
  TextArea,
  Select,
  Modal,
  Row,
  Form,
  Col,
  Upload,
  Button,
  Icon,
  message,
  Table,
  Tag,
} from 'antd';
import FileViewer from 'react-file-viewer';
import request from 'ponshine-request';
import styles from '../index.less';
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const Index = (props) => {
  const {
    visible,
    modalValues,
    setVisible,
    form,
    uploadTitle,
    record,
    getListDatas,
    getContainer,
  } = props;
  const { getFieldDecorator, getFieldsValue } = form;
  const [fileSearchVisible, setfileSearchVisible] = useState(false);
  const [fileInfo, setfileInfo] = useState({});
  const [txtData, settxtData] = useState('');
  const [RemoveList, setRemoveList] = useState([]);
  useEffect(() => {
    setfileList(
      (record?.fileList || []).map((item) => {
        return {
          uid: item?.id,
          name: item?.fileName,
          status: 'done',
          url: item?.filePath,
          id: item?.id,
        };
      }),
    );
  }, [record]);
  useEffect(() => {
    getLevelByUser();
  }, []);
  const [fileList, setfileList] = useState([]);

  const handleSubmit = () => {
    form.validateFields((err, values) => {
      if (err) return;
      request('/api/hn/blackTerminal/singleAddBlackTerminal', {
        method: 'POST',
        data: { ...values },
        requestType: 'json',
      }).then((res) => {
        if (res?.code == 200) {
          setVisible();
          getListDatas();
          message.success(res?.message);
        } else {
          message.error(res?.message);
        }
      });
    });
  };
  const [LevelByUser, setLevelByUser] = useState([]);
  const getLevelByUser = () => {
    request('/api/hn/document/getLevelByUser', {
      method: 'get',
    }).then((res) => {
      if (res?.code == 200) {
        setLevelByUser(res?.data || []);
      } else {
        setLevelByUser([]);
      }
    });
  };
  const columns = [
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      width: 100,
      render: (value, row) => {
        return (
          <>
            <Button type="link" icon="edit"></Button>
            <Button type="link" icon="delete"></Button>
          </>
        );
      },
    },
    {
      title: '监控模型',
      dataIndex: 'jkmx',
      key: 'jkmx',
      align: 'center',
      width: 150,
    },
    {
      title: '告警级别',
      dataIndex: 'level',
      key: 'level',
      align: 'center',
      width: 80,
    },
    {
      title: '检测时间（1-24）',
      dataIndex: 'tiem',
      key: 'tiem',
      align: 'center',
      width: 120,
    },
    {
      title: '阈值下限',
      dataIndex: 'thresholdXX',
      key: 'thresholdXX',
      align: 'center',
      width: 100,
    },
    {
      title: '阈值上限',
      dataIndex: 'thresholdSX',
      key: 'thresholdSX',
      align: 'center',
      width: 100,
    },
    {
      title: '是否连续三次告警升级提醒',
      dataIndex: 'isTrue',
      key: 'isTrue',
      align: 'center',
      width: 100,
    },
    {
      title: '责任人',
      dataIndex: 'man',
      key: 'man',
      align: 'center',
      width: 100,
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
      key: 'phone',
      align: 'center',
      width: 120,
    },
    {
      title: '配置人',
      dataIndex: 'configman',
      key: 'configman',
      align: 'center',
      width: 120,
    },
    {
      title: '配置时间',
      dataIndex: 'configTime',
      key: 'configTime',
      align: 'center',
      width: 120,
    },
  ];
  const [configvisible, setconfigvisible] = useState(false);
  const Timevalidator = (rule, value, callback) => {
    var retNum = /^\d*$/; //判断是否为数字
    const valueArr = value.split(',');
    if (
      valueArr.some((item) => {
        return !retNum.test(item) || !(item >= 1 && item <= 24);
      })
    ) {
      callback('仅能输入1-24数字多个可用英文逗号隔开');
    } else {
      callback();
    }
  };

  return (
    <>
      {visible && (
        <Modal
          title={''}
          visible={visible}
          width={1000}
          style={{ background: 'rgb(46,54,68)' }}
          footer={null}
          height={500}
          onCancel={() => {
            setVisible();
          }}
          centered={true}
          getContainer={document.getElementById('indexWrapper')}
        >
          <div
            style={{ display: 'flex', color: '#FFF', alignItems: 'center', marginBottom: '10px' }}
          >
            <div> 模型监控配置</div>
            <div style={{ fontSize: '10px', marginLeft: '10px' }}>ps：当日配置后次日生效</div>
            <Tag
              style={{ marginLeft: '10px' }}
              color="rgb(0,153,255)"
              onClick={() => {
                setconfigvisible(true);
              }}
            >
              新增
            </Tag>
            <Tag style={{ marginLeft: '10px' }} color="rgb(0,153,255)">
              导出
            </Tag>
          </div>
          <div className={styles.centerBottom}>
            <Table
              className={styles.MoadlTable}
              bordered={true}
              pagination={{ pageSize: 10, current: 1, total: 6 }}
              columns={columns}
              size="small"
              dataSource={[
                {
                  jkmx: '话单数据',
                  level: '严重',
                  tiem: '6,7,8,9,10,11,12,13，14,15,16,17,18,19',
                  thresholdXX: '10000',
                  thresholdSX: '--',
                  isTrue: '是',
                  man: '张三',
                  phone: '12345678901',
                  configman: '胡玮',
                  configTime: '2023-06-08 12:00:00',
                },
                {
                  jkmx: '手机终端变化',
                  level: '严重',
                  tiem: '9,10,11,12,13，14,15,16,17,18,19，20,21',
                  thresholdXX: '100',
                  thresholdSX: '2000',
                  isTrue: '是',
                  man: '张三',
                  phone: '12345678901',
                  configman: '胡玮',
                  configTime: '2023-06-08 12:00:00',
                },
                {
                  jkmx: 'SP短信异常',
                  level: '严重',
                  tiem: '9,10,11,12,13，14,15,16,17,18,19，20,21',
                  thresholdXX: '1',
                  thresholdSX: '500',
                  isTrue: '是',
                  man: '张三',
                  phone: '12345678901',
                  configman: '胡玮',
                  configTime: '2023-06-08 12:00:00',
                },
                {
                  jkmx: '高风险异常通话',
                  level: '严重',
                  tiem: '9,10,11,12,13，14,15,16,17,18,19，20,21',
                  thresholdXX: '1',
                  thresholdSX: '500',
                  isTrue: '是',
                  man: '张三',
                  phone: '12345678901',
                  configman: '胡玮',
                  configTime: '2023-06-08 12:00:00',
                },
                {
                  jkmx: '全网黑终端用户',
                  level: '严重',
                  tiem: '9,10,11,12,13，14,15,16,17,18,19，20,21',
                  thresholdXX: '1',
                  thresholdSX: '500',
                  isTrue: '是',
                  man: '张三',
                  phone: '12345678901',
                  configman: '胡玮',
                  configTime: '2023-06-08 12:00:00',
                },
                {
                  jkmx: '次级高危地',
                  level: '严重',
                  tiem: '9,10,11,12,13，14,15,16,17,18,19，20,21',
                  thresholdXX: '1',
                  thresholdSX: '500',
                  isTrue: '是',
                  man: '张三',
                  phone: '12345678901',
                  configman: '胡玮',
                  configTime: '2023-06-08 12:00:00',
                },
              ]}
            ></Table>
          </div>
        </Modal>
      )}
      {configvisible && (
        <Modal
          title={'模拟监控配置'}
          visible={configvisible}
          width={1000}
          style={{ background: 'rgb(46,54,68)' }}
          bodyStyle={{ height: '300px' }}
          footer={
            <div>
              <Button type="primary">确认</Button>
              <Button
                onClick={() => {
                  setconfigvisible();
                }}
              >
                取消
              </Button>
            </div>
          }
          height={300}
          onCancel={() => {
            setconfigvisible();
          }}
          centered={true}
          getContainer={document.getElementById('indexWrapper')}
        >
          <div className={styles.Form}>
            <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }} layout={'horizontal'}>
              <Col span={12}>
                <Form.Item label={'监控模型'} labelCol={{ span: 9 }} wrapperCol={{ span: 13 }}>
                  {getFieldDecorator('mei', {
                    rules: [
                      {
                        required: true,
                        message: '监控模型为必填',
                      },
                      {
                        pattern: /^[^\u4e00-\u9fa5]{14,15}$/,
                        message: '终端串号格式不正确，请重新录入',
                      },
                    ],
                  })(<Input allowClear placeholder="请填写" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={'告警级别'}
                  labelCol={{ span: 9 }}
                  wrapperCol={{ span: 13 }}
                  allowClear
                  placeholder="请选择"
                >
                  {getFieldDecorator('terminalTag', {
                    rules: [
                      {
                        required: true,
                        message: '告警级别为必选',
                      },
                    ],
                  })(
                    <Select allowClear placeholder="请选择">
                      {['严重', '普通'].map((item) => {
                        return (
                          <Select.Option value={item} key={item}>
                            {item}
                          </Select.Option>
                        );
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={'检测时间'} labelCol={{ span: 9 }} wrapperCol={{ span: 13 }}>
                  {getFieldDecorator('Time', {
                    validateFirst: true,
                    rules: [
                      {
                        required: true,
                        message: '检测时间为必填',
                      },
                      { validator: Timevalidator },
                    ],
                  })(<Input placeholder="请填写"></Input>)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={'阈值上限'} labelCol={{ span: 9 }} wrapperCol={{ span: 13 }}>
                  {getFieldDecorator('thresholdSX', {
                    validateFirst: true,
                    rules: [
                      {
                        required: !getFieldsValue()?.thresholdXX,
                        message: '阈值上限下限两个至少填写一个',
                      },
                      {
                        validator: (rule, value, callback) => {
                          var retNum = /^\d*$/;
                          if (getFieldsValue()?.thresholdXX) {
                            if (value >= getFieldsValue()?.thresholdXX) {
                              callback('阈值上限不得大于下限');
                            }
                          }

                          if (!retNum.test(value) || value < 0) {
                            callback('非负整数');
                            return;
                          } else {
                            callback();
                            return;
                          }
                        },
                      },
                    ],
                  })(<Input placeholder="请填写"></Input>)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={'阈值下限'} labelCol={{ span: 9 }} wrapperCol={{ span: 13 }}>
                  {getFieldDecorator('thresholdXX', {
                    validateFirst: true,
                    rules: [
                      {
                        required: !getFieldsValue()?.thresholdSX,
                        message: '阈值上限下限两个至少填写一个',
                      },
                      {
                        validator: (rule, value, callback) => {
                          var retNum = /^\d*$/;
                          if (
                            !retNum.test(value) ||
                            value <= 0 ||
                            value <= getFieldsValue()?.thresholdSX
                          ) {
                            callback('正整数，且要求大于下限');
                            return;
                          } else {
                            callback();
                            return;
                          }
                        },
                      },
                    ],
                  })(<Input placeholder="请填写"></Input>)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={'责任人'} labelCol={{ span: 9 }} wrapperCol={{ span: 13 }}>
                  {getFieldDecorator('man', {
                    rules: [
                      {
                        required: true,
                        message: '责任人为必填',
                      },
                    ],
                  })(<Input placeholder="请填写"></Input>)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={'联系方式'} labelCol={{ span: 9 }} wrapperCol={{ span: 13 }}>
                  {getFieldDecorator('phone', {
                    validateFirst: true,
                    rules: [
                      {
                        required: true,
                        message: '联系方式为必填',
                      },
                      {
                        validator: (rule, value, callback) => {
                          var retNum = /^1(3|4|5|6|7|8|9)d{9}$/;
                          const valueArr = value.split(',');
                          if (
                            valueArr.some((item) => {
                              return !retNum.test(item) || item?.length != 11;
                            })
                          ) {
                            callback('多个时用英文逗号隔开，请输入正确联系方式');
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })(<Input placeholder="请填写"></Input>)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={'是否连续三次告警升级提醒'}
                  labelCol={{ span: 9 }}
                  wrapperCol={{ span: 13 }}
                >
                  {getFieldDecorator('isTrue', {
                    rules: [
                      {
                        required: true,
                        message: '是否连续三次告警升级提醒为必选',
                      },
                    ],
                  })(
                    <Select allowClear placeholder="请选择">
                      {['是', '否'].map((item) => {
                        return (
                          <Select.Option value={item} key={item}>
                            {item}
                          </Select.Option>
                        );
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Form>
          </div>
        </Modal>
      )}
    </>
  );
};
export default Form.create()(Index);
