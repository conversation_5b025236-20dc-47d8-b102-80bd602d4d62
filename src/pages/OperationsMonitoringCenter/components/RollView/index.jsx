import React, { Fragment, useEffect, useState } from 'react';
import { Row, Col, Carousel } from 'antd';
import styles from './index.less';
import classNames from 'classnames';
const defaultSettings = {
  slidesToShow: 5,
  slidesToScroll: 1,
  autoplaySpeed: 1000, //播放时间
  // centerPadding: 0, // 间隔填充
  // rtl: true,//方向？
  // pauseOnFocus:true, //
  // vertical: true,
  // verticalSwiping: true,
  // swipeToSlide: true,
};
export default function index(props) {
  const {
    listData,
    closable,
    columnConfig,
    showCount,
    rowKey,
    listItemHeight,
    titleStyle = {},
  } = props;
  const [columnData, setcolumnData] = useState(null);
  const [settings, setSettings] = useState(null);
  const [keyStr, setkeyStr] = useState('id');
  useEffect(() => {
    if (listData) {
      let set = { ...defaultSettings };
      if (showCount) {
        set.slidesToShow = showCount;
      }
      if (listData.length < set.slidesToShow) {
        set.slidesToShow = listData.length;
      }
      setSettings(set);
    }
  }, [listData, showCount]);
  useEffect(() => {
    if (rowKey) {
      setkeyStr(rowKey);
    }
  }, [rowKey]);
  useEffect(() => {
    let column = [];
    if (columnConfig) {
      const { count, num } = getColnum();
      let spanNum = parseInt(count / (columnConfig.length - num));
      let lastSpan = () => {
        if (spanNum * (columnConfig.length - num) < count) {
          return spanNum + (count - spanNum * (columnConfig.length - num));
        } else {
          return spanNum;
        }
      };
      columnConfig.forEach((item, index) => {
        column.push({
          label: item.title || '-',
          value: item.dataIndex || 'a',
          colNum: item.colNum || spanNum,
          render: item.render || '',
        });
      });
      let colIndex = [];
      column.forEach((element, index) => {
        if (element.colNum == spanNum) {
          colIndex.push(index);
        }
      });
      setcolumnData(column);
    }
  }, [columnConfig]);
  function getColnum() {
    let count = 24;
    let num = 0;
    columnConfig.forEach((item) => {
      if (item.colNum) {
        count = count - item.colNum;
        num++;
      }
    });
    return { count, num };
  }
  return (
    <Fragment>
      <Row style={{ width: '100%' }}>
        {columnData &&
          columnData.map((item, index) => {
            return (
              <Col span={columnData[index].colNum || false} key={item.value}>
                <div
                  className={styles.spanTitle}
                  style={{
                    height: listItemHeight,
                    ...titleStyle,
                    marginBottom: '2px',
                    padding: '0 2px',
                    borderRight:
                      index + 1 != columnData?.length
                        ? '1px solid rgba(255, 255, 255, 0.3)'
                        : '0px',
                  }}
                >
                  {item.label || '-'}
                </div>
              </Col>
            );
          })}
      </Row>
      {columnData && listData && listData.length > 0 && settings && (
        <div style={{ height: '100%', width: '100%' }} className={styles.carouselBox}>
          <Carousel
            autoplay={true}
            dotPosition={'left'}
            dots={false}
            style={{ height: '100%', width: '100%' }}
            {...settings}
          >
            {listData.map((element, ind) => {
              return (
                <Row
                  key={element[keyStr] || '0'}
                  style={{ background: (ind % 2 == 0 && '') || '' }}
                >
                  {columnData.map((itemSon, indexSon) => {
                    return (
                      <Col
                        span={columnData[indexSon].colNum || false}
                        key={`${element[keyStr] || '0'}-${indexSon}`}
                      >
                        {/* <div className={classNames(styles.spanItem)} style={{height:listItemHeight}}>
                                                    {itemSon.render&&itemSon.render(element[itemSon.value],element)||element[itemSon.value]||"-"}
                                                </div> */}
                        {/* <div className={ind % 2 == 0 ? styles.nobackground : styles.spanTitle}> */}
                        <div className={styles.nobackground}>
                          {(itemSon.render && itemSon.render(element[itemSon.value], element)) ||
                            element[itemSon.value] ||
                            '-'}
                        </div>
                      </Col>
                    );
                  })}
                </Row>
              );
            })}
          </Carousel>
        </div>
      )}
    </Fragment>
  );
}
