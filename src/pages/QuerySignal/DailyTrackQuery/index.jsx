import React, { useState, useEffect, useMemo } from 'react';
import { Button, Card, Form, Input, message, Modal, Row, Col, Select, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import ExportApprove from '@/components/ExportApprove';
import request from '@/utils/request';
import styles from './index.less';
import { exportFile } from '@/utils/utils';
import moment from 'moment';
import { Licensee, useLicensee } from 'ponshine';

const mockData = Array.from({ length: 100 }, (_, index) => ({
  id: index + 1,
  msisdn: `**********${index}`,
  imei: `12345678901234${index}`,
  signalType: '呼叫',
  startTime: `${moment().format('YYYY-MM-DD')} ${Math.floor(Math.random() * 24)
    .toString()
    .padStart(2, '0')}:${Math.floor(Math.random() * 60)
    .toString()
    .padStart(2, '0')}:${Math.floor(Math.random() * 60)
    .toString()
    .padStart(2, '0')}-${Math.floor(Math.random() * 24)
    .toString()
    .padStart(2, '0')}:${Math.floor(Math.random() * 60)
    .toString()
    .padStart(2, '0')}:${Math.floor(Math.random() * 60)
    .toString()
    .padStart(2, '0')}`,
  cellId: Math.floor(Math.random() * 1000000) + '-' + Math.floor(Math.random() * 10),
  cellName: '长沙北站-1',
  city: '长沙市',
  county: '芙蓉区',
  longitude: (112 + Math.random()).toFixed(4),
  latitude: (28 + Math.random()).toFixed(4),
}));

const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator },
  } = props;
  // 列表加载状态
  const [tableLoading, setTableLoading] = useState(false);
  // 列表数据
  const [tableData, setTableData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  // 列表选中项
  const [selectedRows, setSelectedRows] = useState([]);
  // 查询条件
  const [searchParams, setSearchParams] = useState({});
  // 添加排序状态
  const [sortedInfo, setSortedInfo] = useState({});

  // 表格配置项
  const columns = [
    {
      title: '号码',
      dataIndex: 'msisdn',
      align: 'center',
      ellipsis: true,
      width: 140,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'msisdn' && sortedInfo.order,
    },
    {
      title: 'IMEI',
      dataIndex: 'imei',
      align: 'center',
      ellipsis: true,
      width: 140,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'imei' && sortedInfo.order,
    },
    {
      title: '信令类型',
      dataIndex: 'signalType',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: '时间',
      dataIndex: 'startTime',
      align: 'center',
      ellipsis: true,
      width: 160,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'startTime' && sortedInfo.order,
    },
    // {
    //     title: '基站id',
    //     dataIndex: 'stationId',
    //     align: 'center',
    //     ellipsis: true,
    //     width: 140,
    // },
    {
      title: '小区id',
      dataIndex: 'cellId',
      align: 'center',
      ellipsis: true,
      width: 130,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'cellId' && sortedInfo.order,
    },
    {
      title: '小区名称',
      dataIndex: 'cellName',
      align: 'center',
      ellipsis: true,
      width: 120,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'cellName' && sortedInfo.order,
    },
    // {
    //     title: '基站名称',
    //     dataIndex: 'stationName',
    //     align: 'center',
    //     ellipsis: true,
    //     width: 140,
    // },
    {
      title: '地市',
      dataIndex: 'city',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: '区县',
      dataIndex: 'county',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: '经度',
      dataIndex: 'longitude',
      align: 'center',
      ellipsis: true,
      width: 120,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'longitude' && sortedInfo.order,
    },
    {
      title: '纬度',
      dataIndex: 'latitude',
      align: 'center',
      ellipsis: true,
      width: 120,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'latitude' && sortedInfo.order,
    },
  ];

  // 添加获取当前页数据的方法
  const getCurrentPageData = useMemo(() => {
    let currentData = [...tableData.list];

    // 修改时间格式处理函数
    const parseCustomDateTime = (timeStr) => {
      if (!timeStr) return 0;
      // 处理格式如 "2025-05-15 00:00:00-04:00:00"
      const datePart = timeStr.split(' ')[0]; // 获取日期部分

      // 提取时间段的开始时间
      const timeRange = timeStr.substring(timeStr.indexOf(' ') + 1);
      const startTimeStr = timeRange.split('-')[0]; // 获取"-"前的开始时间

      // 构建完整的时间字符串并转换为毫秒时间戳
      return moment(`${datePart} ${startTimeStr}`).valueOf();
    };

    // 根据排序信息对数据进行排序
    if (sortedInfo.columnKey && sortedInfo.order) {
      currentData.sort((a, b) => {
        const order = sortedInfo.order === 'ascend' ? 1 : -1;

        switch (sortedInfo.columnKey) {
          case 'msisdn':
            // 号码是纯数字，使用数值比较
            return order * ((Number(a.msisdn) || 0) - (Number(b.msisdn) || 0));
          case 'imei':
            // IMEI是纯数字，使用数值比较
            return order * ((Number(a.imei) || 0) - (Number(b.imei) || 0));
          case 'startTime':
            // 使用自定义时间解析函数进行比较
            return order * (parseCustomDateTime(a.startTime) - parseCustomDateTime(b.startTime));
          case 'cellId':
            // 小区id可能是数字或字符串混合，先尝试数值比较，不行则用字符串比较
            const aCellId = Number(a.cellId);
            const bCellId = Number(b.cellId);
            if (!isNaN(aCellId) && !isNaN(bCellId)) {
              return order * (aCellId - bCellId);
            }
            return order * (a.cellId || '').localeCompare(b.cellId || '');
          case 'cellName':
            // 小区名称使用字符串比较
            return order * (a.cellName || '').localeCompare(b.cellName || '');
          case 'longitude':
            // 经度使用数值比较
            return order * ((Number(a.longitude) || 0) - (Number(b.longitude) || 0));
          case 'latitude':
            // 纬度使用数值比较
            return order * ((Number(a.latitude) || 0) - (Number(b.latitude) || 0));
          default:
            return 0;
        }
      });
    }

    // 分页
    const { current, pageSize } = pagination;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return currentData.slice(start, end);
  }, [tableData.list, pagination, sortedInfo]);

  // 列表查询
  const handleSearch = (current = 1, pageSize = 10) => {
    form.validateFieldsAndScroll((errors, formData) => {
      if (errors) return;
      const params = {
        ...formData,
        queryDate: formData.queryDate?.format('YYYYMMDD'),
        current: current,
        pageSize: pageSize,
      };
      setTableLoading(true);
      request('/api/hn/signalDailyLocus/querySignalDailyLocus', {
        method: 'POST',
        data: {
          ...params,
        },
        requestType: 'json',
      })
        .then((res) => {
          if (res.code == 200) {
            setTableData({
              list: res.data || [],
              total: res.data?.length || 0,
            });
            setPagination({
              current: current,
              pageSize: pageSize,
            });
            setSearchParams(params);
            // 清空排序状态
            setSortedInfo({});
          } else {
            message.error(res.message);
            setTableData({
              list: [],
              total: 0,
            });
            setPagination({
              current: 1,
              pageSize: 10,
            });
          }
        })
        .finally(() => {
          setTableLoading(false);
        });
    });
  };
  // 导出
  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/signalDailyLocus/exportSignalDailyLocusQueryResult',
      params: { dataList: tableData.list || [] },
      method: 'POST',
      decode: true,
    });
  };
  // 列表改变分页
  const handleTableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 分析时间限制
  const disabledDate = (current) => {
    return current > moment() || current < moment().subtract(30, 'day');
  };

  return (
    <Card>
      <Form className={styles.myStylesForm}>
        <Row gutter={24}>
          <Licensee license="signalDailyLocus_querySignalDailyLocus">
            <Col span={5}>
              <Form.Item label="查询类型">
                {getFieldDecorator('conditionType', {
                  initialValue: 'phoneNum',
                  rules: [
                    {
                      required: true,
                      message: `请选择查询类型`,
                    },
                  ],
                })(
                  <Select placeholder="请输入" allowClear style={{ width: '100%' }}>
                    <Select.Option value={'phoneNum'} key={1}>
                      号码
                    </Select.Option>
                    <Select.Option value={'imei'} key={2}>
                      IMEI
                    </Select.Option>
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item label="" wrapperCol={{ span: 24 }}>
                {getFieldDecorator('conditionValue', {
                  initialValue: undefined,
                  rules: [
                    {
                      required: true,
                      message: `请输入${
                        form.getFieldValue('conditionType')
                          ? form.getFieldValue('conditionType') == 'phoneNum'
                            ? '号码'
                            : 'IMEI'
                          : ''
                      }`,
                    },
                    {
                      validator: (rules, value, callback) => {
                        if (form.getFieldValue('conditionType') == 'phoneNum') {
                          const phoneReg = /^\d+$/;
                          if (!phoneReg.test(value)) callback('号码必须为纯数字');
                        }
                        if (form.getFieldValue('conditionType') == 'imei') {
                          const IMEIReg = /^[0-9]{14}$/;
                          if (!IMEIReg.test(value)) callback('IMEI必须为14位纯数字');
                        }
                        callback();
                      },
                    },
                  ],
                  validateFirst: true,
                })(<Input placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item label="分析时间">
                {getFieldDecorator('queryDate', {
                  initialValue: undefined,
                  rules: [
                    {
                      required: true,
                      message: `请选择分析时间`,
                    },
                  ],
                })(
                  <DatePicker
                    allowClear
                    disabledDate={disabledDate}
                    format={'YYYY-MM-DD'}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
          </Licensee>
          <Col span={7} style={{ textAlign: 'right' }}>
            <Licensee license="signalDailyLocus_querySignalDailyLocus">
              <Button
                type="primary"
                onClick={() => {
                  handleSearch();
                }}
              >
                查询
              </Button>
            </Licensee>
            <Licensee license="signalDailyLocus_exportSignalDailyLocusQueryResult">
              <ExportApprove
                buttonStyle={{ marginLeft: 12 }}
                exportParams={{
                  urlAPi: '/api/hn/signalDailyLocus/exportSignalDailyLocusQueryResult',
                  params: { dataList: tableData.list || [], ...searchParams },
                  method: 'POST',
                  decode: true,
                }}
                moduleTile="轨迹管理"
              />
              {/* <Button type='primary' onClick={() => { handleExport() }} style={{ marginLeft: 12 }}>数据导出</Button> */}
            </Licensee>
          </Col>
        </Row>
      </Form>
      <div>
        <StandardTable
          selectedRows={selectedRows}
          data={{
            list: getCurrentPageData,
            pagination: {
              total: tableData.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          }}
          columns={columns}
          scroll={{ x: 'max-content' }}
          onChange={handleTableChange}
          rowSelectionProps={false}
          showSelectCount={false}
          rowkey="id"
          loading={tableLoading}
        />
      </div>
    </Card>
  );
};

export default Form.create()(Index);
