import React, { useState, useEffect, useMemo } from 'react';
import { Button, Card, Form, Input, message, Row, Col, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import styles from './index.less';
import moment from 'moment';

const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator },
  } = props;
  // 列表加载状态
  const [tableLoading, setTableLoading] = useState(false);
  // 列表数据
  const [tableData, setTableData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  // 列表选中项
  const [selectedRows, setSelectedRows] = useState([]);
  // 查询条件
  const [searchParams, setSearchParams] = useState({});
  // 添加排序状态
  const [sortedInfo, setSortedInfo] = useState({});

  // 表格配置项
  const columns = [
    {
      title: '主叫号码',
      dataIndex: 'caller',
      align: 'center',
      ellipsis: true,
      width: 160,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'caller' && sortedInfo.order,
    },
    {
      title: '被叫号码',
      dataIndex: 'called',
      align: 'center',
      ellipsis: true,
      width: 160,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'called' && sortedInfo.order,
    },
    {
      title: '呼叫时间',
      dataIndex: 'startTime',
      align: 'center',
      ellipsis: true,
      width: 200,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'startTime' && sortedInfo.order,
    },
    {
      title: '源IP/源点码',
      dataIndex: 'srcIp',
      align: 'center',
      ellipsis: true,
      width: 180,
      // sorter: true,
      // sortOrder: sortedInfo.columnKey === 'srcIp' && sortedInfo.order,
    },
    {
      title: '网元名称',
      dataIndex: 'netName',
      align: 'center',
      ellipsis: true,
      width: 140,
    },
    {
      title: '中继群号',
      dataIndex: 'relayNumber',
      align: 'center',
      ellipsis: true,
      width: 140,
    },
    {
      title: '中继群名',
      dataIndex: 'relayName',
      align: 'center',
      ellipsis: true,
      width: 140,
    },
  ];

  useEffect(() => {}, []);

  // 添加获取当前页数据的方法
  const getCurrentPageData = useMemo(() => {
    let currentData = [...tableData.list];

    // IP地址排序的辅助函数
    const compareIP = (ip1, ip2) => {
      const ip1Parts = (ip1 || '').split('.').map(Number);
      const ip2Parts = (ip2 || '').split('.').map(Number);

      for (let i = 0; i < 4; i++) {
        if (ip1Parts[i] !== ip2Parts[i]) {
          return ip1Parts[i] - ip2Parts[i];
        }
      }
      return 0;
    };

    // 根据排序信息对数据进行排序
    if (sortedInfo.columnKey && sortedInfo.order) {
      currentData.sort((a, b) => {
        const order = sortedInfo.order === 'ascend' ? 1 : -1;

        switch (sortedInfo.columnKey) {
          case 'caller':
            return order * (Number(a.caller || 0) - Number(b.caller || 0));
          case 'called':
            return order * (Number(a.called || 0) - Number(b.called || 0));
          case 'startTime':
            return order * (moment(a.startTime).valueOf() - moment(b.startTime).valueOf());
          // case 'srcIp':
          //   return order * compareIP(a.srcIp, b.srcIp);
          default:
            return 0;
        }
      });
    }

    // 分页
    const { current, pageSize } = pagination;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return currentData.slice(start, end);
  }, [tableData.list, pagination, sortedInfo]);

  // 修改表格改变事件处理函数
  const handleTableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 修改查询方法，添加清空排序
  const handleSearch = (current = 1, pageSize = 10) => {
    form.validateFields((errors, formData) => {
      if (errors) return;
      const params = {
        ...formData,
        startDate: formData?.date?.[0]?.format('YYYYMMDD'),
        endDate: formData?.date?.[1]?.format('YYYYMMDD'),
      };
      delete params.date;
      setTableLoading(true);

      // 清空排序状态
      setSortedInfo({});

      request('/api/hn/callSignalling/getCallSignalling', {
        method: 'POST',
        data: {
          ...params,
        },
        requestType: 'json',
      })
        .then((res) => {
          if (res.code == 200) {
            setTableData({
              list: res.data || [],
              total: res.data?.length || 0,
            });
            setPagination({
              current: current,
              pageSize: pageSize,
            });
            setSearchParams(formData);
          } else {
            message.error(res.message);
            setTableData({
              list: [],
              total: 0,
            });
            setPagination({
              current: 1,
              pageSize: 10,
            });
          }
        })
        .finally(() => {
          setTableLoading(false);
        });
    });
  };

  const [startTime, setStartTime] = useState(undefined);

  // 分析时间限制
  const disabledDate = (current) => {
    if (startTime) {
      return (
        current < moment(startTime).startOf('month') || current > moment(startTime).endOf('month')
      );
    } else {
      return false;
    }
  };

  return (
    <Card>
      <Form className={styles.myStylesForm}>
        <Row gutter={24}>
          {/* <Licensee license="signalLocation_querySignalLocation"> */}
          <Col span={6}>
            <Form.Item label="主叫">
              {getFieldDecorator('caller', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: `请输入主叫`,
                  },
                  {
                    validator: (rules, value, callback) => {
                      const phoneReg = /^\d+$/;
                      if (!phoneReg.test(value)) callback('主叫必须为纯数字');
                      callback();
                    },
                  },
                ],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="被叫">
              {getFieldDecorator('called', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: `请输入被叫`,
                  },
                  {
                    validator: (rules, value, callback) => {
                      const phoneReg = /^\d+$/;
                      if (!phoneReg.test(value)) callback('被叫必须为纯数字');
                      callback();
                    },
                  },
                ],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="查询时间">
              {getFieldDecorator('date', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: `请选择查询时间`,
                  },
                ],
              })(
                <DatePicker.RangePicker
                  allowClear
                  disabledDate={disabledDate}
                  format={'YYYY-MM-DD'}
                  onCalendarChange={(dates) => {
                    if (dates[1]) {
                      setStartTime();
                    } else {
                      setStartTime(dates[0]);
                    }
                  }}
                  style={{ width: '100%' }}
                />,
              )}
            </Form.Item>
          </Col>
          {/* </Licensee> */}
          <Col span={4} style={{ textAlign: 'right' }}>
            {/* <Licensee license="signalLocation_querySignalLocation"> */}
            <Button
              type="primary"
              onClick={() => {
                handleSearch();
              }}
            >
              查询
            </Button>
            {/* </Licensee> */}
          </Col>
        </Row>
      </Form>
      <div>
        <StandardTable
          selectedRows={selectedRows}
          data={{
            list: getCurrentPageData, // 使用处理后的当前页数据
            pagination: {
              total: tableData.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          }}
          columns={columns}
          scroll={{ x: 'max-content' }}
          onChange={handleTableChange}
          rowSelectionProps={false}
          showSelectCount={false}
          rowkey="id"
          loading={tableLoading}
        />
      </div>
    </Card>
  );
};

export default Form.create()(Index);
