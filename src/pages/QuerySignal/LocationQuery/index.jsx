import React, { useState, useEffect, useMemo } from 'react';
import { Button, Card, Form, Input, message, Modal, Row, Col, Select, DatePicker } from 'antd';
import StandardTable from '@/components/StandardTable';
import ExportApprove from '@/components/ExportApprove';
import request from '@/utils/request';
import styles from './index.less';
import { exportFile } from '@/utils/utils';
import moment from 'moment';
import { Licensee, useLicensee } from 'ponshine';
import { aesDecode } from '@/services/login';

const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator },
  } = props;
  // 列表加载状态
  const [tableLoading, setTableLoading] = useState(false);
  // 列表数据
  const [tableData, setTableData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  // 列表选中项
  const [selectedRows, setSelectedRows] = useState([]);
  // 查询条件
  const [searchParams, setSearchParams] = useState({});
  // 添加排序状态
  const [sortedInfo, setSortedInfo] = useState({});

  // 表格配置项
  const columns = [
    {
      title: '号码',
      dataIndex: 'msisdn',
      align: 'center',
      ellipsis: true,
      width: 160,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'msisdn' && sortedInfo.order,
    },
    {
      title: 'IMEI',
      dataIndex: 'imei',
      align: 'center',
      ellipsis: true,
      width: 160,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'imei' && sortedInfo.order,
    },
    {
      title: '信令类型',
      dataIndex: 'signalType',
      align: 'center',
      ellipsis: true,
      width: 160,
    },
    {
      title: '时间',
      dataIndex: 'time',
      align: 'center',
      ellipsis: true,
      width: 180,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'time' && sortedInfo.order,
    },
    {
      title: '省份',
      dataIndex: 'province',
      align: 'center',
      ellipsis: true,
      width: 140,
    },
    {
      title: '地市',
      dataIndex: 'city',
      align: 'center',
      ellipsis: true,
      width: 140,
    },
  ];

  // 列表查询
  const handleSearch = (current = 1, pageSize = 10) => {
    form.validateFields((errors, formData) => {
      if (errors) return;
      const params = {
        ...formData,
        startDate: formData?.startDate?.[0]?.format('YYYYMMDD'),
        endDate: formData?.startDate?.[1]?.format('YYYYMMDD'),
        current: current,
        pageSize: pageSize,
      };
      setTableLoading(true);

      // 清空排序状态
      setSortedInfo({});

      request('/api/hn/signalLocation/querySignalLocation', {
        method: 'POST',
        data: {
          ...params,
        },
        requestType: 'json',
      })
        .then((res) => {
          if (res.code == 200) {
            setTableData({
              list: res.data || [],
              total: res.data?.length || 0,
            });
            setPagination({
              current: current,
              pageSize: pageSize,
            });
            setSearchParams(params);
          } else {
            message.error(res.message);
            setTableData({
              list: [],
              total: 0,
            });
            setPagination({
              current: 1,
              pageSize: 10,
            });
          }
        })
        .finally(() => {
          setTableLoading(false);
        });
    });
  };
  // 导出
  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/signalLocation/exportSignalLocationQueryResult',
      params: { dataList: tableData.list || [] },
      method: 'POST',
      decode: true,
    });
  };

  // 获取当前页数据
  const getCurrentPageData = useMemo(() => {
    let currentData = [...tableData.list];

    // 根据排序信息对数据进行排序
    if (sortedInfo.columnKey && sortedInfo.order) {
      currentData.sort((a, b) => {
        const order = sortedInfo.order === 'ascend' ? 1 : -1;

        switch (sortedInfo.columnKey) {
          case 'msisdn':
            return order * (Number(a?.msisdn || 0) - Number(b?.msisdn || 0));
          case 'imei':
            return order * (Number(a?.imei || 0) - Number(b?.imei || 0));
          case 'time':
            return order * (moment(a?.time).valueOf() - moment(b?.time).valueOf());
          default:
            return 0;
        }
      });
    }

    // 分页
    const { current, pageSize } = pagination;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return currentData.slice(start, end);
  }, [tableData.list, pagination, sortedInfo]);

  // 修改表格改变事件处理函数
  const handleTableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const [startTime, setStartTime] = useState(undefined);

  // 分析时间限制
  const disabledDate = (current) => {
    if (startTime) {
      return (
        current < moment(startTime).startOf('month') ||
        current > moment(startTime).endOf('month') ||
        current > moment().endOf('day') ||
        current < moment().subtract(3, 'months').startOf('month')
      );
    } else {
      return (
        current > moment().endOf('day') || current < moment().subtract(3, 'months').startOf('month')
      );
    }
  };

  return (
    <Card>
      <Form className={styles.myStylesForm}>
        <Row gutter={24}>
          <Licensee license="signalLocation_querySignalLocation">
            <Col span={5}>
              <Form.Item label="查询类型">
                {getFieldDecorator('conditionType', {
                  initialValue: 'phoneNum',
                  rules: [
                    {
                      required: true,
                      message: `请选择查询类型`,
                    },
                  ],
                })(
                  <Select placeholder="请输入" allowClear style={{ width: '100%' }}>
                    <Select.Option value={'phoneNum'} key={1}>
                      号码
                    </Select.Option>
                    <Select.Option value={'imei'} key={2}>
                      IMEI
                    </Select.Option>
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item label="" wrapperCol={{ span: 24 }}>
                {getFieldDecorator('conditionValue', {
                  initialValue: undefined,
                  rules: [
                    {
                      required: true,
                      message: `请输入${
                        form.getFieldValue('conditionType')
                          ? form.getFieldValue('conditionType') == 'phoneNum'
                            ? '号码'
                            : 'IMEI'
                          : ''
                      }`,
                    },
                    {
                      validator: (rules, value, callback) => {
                        if (form.getFieldValue('conditionType') == 'phoneNum') {
                          const phoneReg = /^\d+$/;
                          if (!phoneReg.test(value)) callback('号码必须为纯数字');
                        }
                        if (form.getFieldValue('conditionType') == 'imei') {
                          const IMEIReg = /^[0-9]{14}$/;
                          if (!IMEIReg.test(value)) callback('IMEI必须为14位纯数字');
                        }
                        callback();
                      },
                    },
                  ],
                  validateFirst: true,
                })(<Input placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item label="分析时间">
                {getFieldDecorator('startDate', {
                  initialValue: undefined,
                  rules: [
                    {
                      required: true,
                      message: `请选择分析时间`,
                    },
                  ],
                })(
                  <DatePicker.RangePicker
                    allowClear
                    disabledDate={disabledDate}
                    format={'YYYY-MM-DD'}
                    onCalendarChange={(dates) => {
                      if (dates[1]) {
                        setStartTime();
                      } else {
                        setStartTime(dates[0]);
                      }
                    }}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
          </Licensee>
          <Col span={5} style={{ textAlign: 'right' }}>
            <Licensee license="signalLocation_querySignalLocation">
              <Button
                type="primary"
                onClick={() => {
                  handleSearch();
                }}
              >
                查询
              </Button>
            </Licensee>
            <Licensee license="signalLocation_exportSignalLocationQueryResult">
              <ExportApprove
                buttonStyle={{ marginLeft: 12 }}
                exportParams={{
                  urlAPi: '/api/hn/signalLocation/exportSignalLocationQueryResult',
                  params: { dataList: tableData.list || [], ...searchParams },
                  method: 'POST',
                  decode: true,
                }}
                moduleTile="轨迹管理"
              />
              {/* <Button type='primary' onClick={() => { handleExport() }} style={{ marginLeft: 12 }}>数据导出</Button> */}
            </Licensee>
          </Col>
        </Row>
      </Form>
      <div>
        <StandardTable
          selectedRows={selectedRows}
          data={{
            list: getCurrentPageData, // 使用处理后的当前页数据
            pagination: {
              total: tableData.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          }}
          columns={columns}
          scroll={{ x: 'max-content' }}
          onChange={handleTableChange}
          rowSelectionProps={false}
          showSelectCount={false}
          rowkey="id"
          loading={tableLoading}
        />
      </div>
    </Card>
  );
};

export default Form.create()(Index);
