import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Table,
  DatePicker,
  Input,
  Button,
  Modal,
  Tooltip,
  message,
} from 'antd';
import { Licensee, useLicensee } from 'ponshine';
import 'antd/dist/antd.css';
import { exportFile } from '@/utils/utils';

// import StandardTable from "./StandardTable";
import StandardTable from '@/components/StandardTable';
import BtnCallbackComponentknVmkzhLMf from './components/upModal';
import moment from 'moment';
const { RangePicker } = DatePicker;
import styles from './index.less';
// import request from "umi-request";
import request from 'ponshine-request';

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator } = form;

  const formItemLayout = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 18,
    },
  };
  useEffect(() => {
    getListDatas();
  }, []);
  const [selectedRows, setSelectedRows] = useState([]);
  const onSelectRow = (selectedRows) => {
    setSelectedRows(selectedRows);
  };
  const [data, setdata] = useState({});
  const [loading, setloading] = useState(false);
  const getListDatas = (
    pagination = {
      pageNum: 1,
      pageSize: 10,
    },
  ) => {
    setloading(true);
    const values = form.getFieldsValue();
    let data = {
      ...values,
      startDate: values?.time && values?.time[0] ? values?.time[0].format('YYYY-MM-DD') : undefined,
      endDate: values?.time && values?.time[1] ? values?.time[1].format('YYYY-MM-DD') : undefined,
      ...pagination,
    };
    delete data.time;
    request('/api/hn/document/pageDocument', {
      method: 'POST',
      data,
      requestType: 'json',
    })
      .then((res) => {
        if (res?.code == 200) {
          setdata({
            list: res?.data?.items || [],
            pagination: {
              ...pagination,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setdata({
            list: [],
            pagination: {
              ...pagination,
              total: 0,
            },
          });
        }
      })
      .finally(() => {
        setloading(false);
      });
  };
  const handleDelete = (row) => {
    Modal.confirm({
      title: '删除',
      content: row ? '是否删除该条数据？' : '是否删除选中数据？',
      onOk: () => {
        request('/api/hn/document/deleteDocumentById', {
          method: 'POST',
          data: {
            idList: row
              ? [row?.id]
              : (selectedRows || []).map((item) => {
                  return item?.id;
                }),
          },
          requestType: 'form',
        }).then((res) => {
          if (res?.code == 200) {
            getListDatas();
            setSelectedRows([]);
            message.success(res?.message);
          } else {
            message.error(res?.message);
          }
        });
      },
    });
  };
  const handleTableChange = (pagination) => {
    getListDatas({
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };
  const [VisibleknVmkzhLMf, setVisibleknVmkzhLMf] = useState(false);
  const relationClickknVmkzhLMf = () => {
    setVisibleknVmkzhLMf(true);
    setuploadTitle('信息上传');
  };
  let columns = [
    {
      title: '标题',
      width: 270,
      dataIndex: 'title',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '查阅等级',
      width: 80,
      dataIndex: 'level',
      render: (v) => {
        const type = {
          1: '一级',
          2: '二级',
          3: '三级',
        };
        const t = type[v] || '--';
        return (
          <Tooltip title={t} placement="topLeft">
            {t}
          </Tooltip>
        );
      },
      align: 'center',
      ellipsis: true,
    },
    {
      title: '上传人',
      width: 200,
      dataIndex: 'uploaderName',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '上传时间',
      width: 200,
      dataIndex: 'gmtCreate',
      align: 'center',
    },
  ];
  columns = [
    ...columns,
    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      fixed: 'right',
      align: 'center',
      width: 250,
      render: (value, row, index) => {
        return (
          <>
            <Licensee license="document_update">
              <Button
                type={'link'}
                style={{ marginRight: 10 }}
                onClick={(e) => {
                  e.stopPropagation();
                  setuploadTitle('编辑');
                  getInfo(row);
                }}
              >
                编辑
              </Button>
            </Licensee>
            <Licensee license="document_delete">
              <Button
                type={'link'}
                style={{ marginRight: 10 }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(row);
                }}
              >
                删除
              </Button>
            </Licensee>
            <Button
              type={'link'}
              style={{ marginRight: 10 }}
              onClick={(e) => {
                e.stopPropagation();
                setuploadTitle('详情');
                getInfo(row);
              }}
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];
  const [record, setRecord] = useState({});
  const [uploadTitle, setuploadTitle] = useState('');
  useEffect(() => {
    if (!VisibleknVmkzhLMf) {
      setRecord({});
    }
  }, [VisibleknVmkzhLMf]);
  // const lists = [{"id":1,"name":"关键词","type":"类型","time":"2022-01-11","title":"XXXXXXX","level":"一级","upLoadMan":"IKUN","upLoadTime":"2020-1-1"}];
  const handleExport = () => {
    const values = form.getFieldsValue();
    let data = {
      ...values,
      startDate: values?.time && values?.time[0] ? values?.time[0].format('YYYY-MM-DD') : undefined,
      endDate: values?.time && values?.time[1] ? values?.time[1].format('YYYY-MM-DD') : undefined,
    };
    delete data.time;
    exportFile({
      urlAPi: '/api/hn/document/exportDocument',
      decode: true,
      params: data,
      method: 'POST',
    });
  };
  const getInfo = (row) => {
    request('/api/hn/document/getDetailById', {
      method: 'POST',
      data: {
        id: row.id,
      },
      requestType: 'form',
    }).then((res) => {
      if (res?.code == 200) {
        setRecord({ ...row, ...res.data });
        setVisibleknVmkzhLMf(true);
      } else {
        setRecord({});
        message.error(res?.message);
      }
    });
  };
  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
      <Card>
        <Row>
          <Col>
            <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} layout={'horizontal'}>
              <Col span={8}>
                <Form.Item label={'标题'} >
                  {getFieldDecorator('title')(<Input allowClear />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label={'上传人'} >
                  {getFieldDecorator('uploaderName')(<Input allowClear />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label={'操作时间'}>
                  {getFieldDecorator('time', {
                    // initialValue: [
                    //   moment(moment(new Date()).startOf('day'), 'YYYY-MM-DD'),
                    //   moment(moment(new Date()), 'YYYY-MM-DD'),
                    // ],
                  })(
                    <RangePicker
                      style={{
                        width: '100%',
                      }}
                      allowClear
                      getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Form>
          </Col>
          <Col align="right" span={24} style={{marginBottom: 16}}>
            <Licensee license="document_query">
              <Button
                type={'primary'}
                style={{ marginRight: 10 }}
                onClick={() => {
                  getListDatas();
                }}
              >
                查询
              </Button>
              <Button
                style={{ marginRight: 10 }}
                onClick={() => {
                  form.resetFields();
                  getListDatas();
                }}
              >
                重置
              </Button>
            </Licensee>
            <Licensee license="document_export">
              <Button
                type={'primary'}
                style={{ marginRight: 10 }}
                onClick={() => {
                  handleExport();
                }}
              >
                数据导出
              </Button>
            </Licensee>
            <Licensee license="document_add">
              <Button
                type={'primary'}
                style={{ marginRight: 10 }}
                onClick={() => {
                  relationClickknVmkzhLMf();
                }}
              >
                + 新建
              </Button>
            </Licensee>
            <Licensee license="document_delete">
              <Button
                disabled={selectedRows?.length == 0}
                type={'primary'}
                onClick={() => {
                  handleDelete();
                }}
              >
                删除
              </Button>
            </Licensee>
          </Col>
        </Row>
        
        <div className={styles.standarTable}>
          <StandardTable
            columns={columns}
            data={{
              list: data?.list || [],
              pagination: {
                ...data?.pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                defaultPageSize: 20,
                showTotal: (total, range) => {
                  return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
                },
              },
            }}
            onChange={handleTableChange}
            multiple={true}
            loading={loading}
            showSelectCount={true}
            rowSelectionProps={true}
            selectedRows={selectedRows}
            rowKey="id"
            onSelectRow={onSelectRow}
          />
        </div>
      </Card>
      {VisibleknVmkzhLMf && (
        <BtnCallbackComponentknVmkzhLMf
          uploadTitle={uploadTitle}
          record={record}
          visible={VisibleknVmkzhLMf}
          setVisible={setVisibleknVmkzhLMf}
          getListDatas={getListDatas}
        />
      )}
      </div>
    </div>
  );
};
export default Form.create({})(Index);
