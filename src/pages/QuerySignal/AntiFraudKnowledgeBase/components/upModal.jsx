import React, { useEffect, useState } from 'react';
import {
  Input,
  TextArea,
  Select,
  Modal,
  Row,
  Form,
  Col,
  Upload,
  Button,
  Icon,
  message,
} from 'antd';
import FileViewer from 'react-file-viewer';
import request from 'ponshine-request';
import styles from '../index.less';
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const acceptTypes = [
  '.csv',
  '.xls',
  '.xlsx',
  '.jpg',
  '.png',
  '.bmp',
  '.jpeg',
  '.rar',
  '.zip',
  '.7z',
  '.doc',
  '.docx',
  '.txt',
  '.pdf',
  '.ppt',
  '.pptx',
];
const type = {
  1: '一级',
  2: '二级',
  3: '三级',
};
const Index = (props) => {
  const { visible, modalValues, setVisible, form, uploadTitle, record, getListDatas } = props;
  const { getFieldDecorator } = form;
  const [fileSearchVisible, setfileSearchVisible] = useState(false);
  const [fileInfo, setfileInfo] = useState({});
  const [txtData, settxtData] = useState('');
  const [RemoveList, setRemoveList] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [uploadFileLoading, setUploadFileLoading] = useState(false);

  const upLoadProps = {
    accept: '.csv,.xls,.xlsx,.jpg,.png,.bmp,.jpeg,.rar,.zip,.7z,.doc,.docx,.txt,.pdf,.ppt,.pptx,',
    multiple: true,
    beforeUpload: (file, list) => {
      if (fileList?.length >= 50) {
        message.warning('累计文件个数不能超过50个，请知悉');
        return;
      }
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      const size = file.size / 1024 / 1024 / 2;
      if (
        acceptTypes.some((item) => {
          return item == tag;
        })
      ) {
        if (size >= 10) {
          message.warning('单个文件不能超过20M，请压缩后上传');
          return false;
        }
        const formData = new FormData();
        formData.append('file', file);
        setUploadFileLoading(true);
        request('/api/hn/document/uploadSingleFile', {
          method: 'POST',
          data: formData,
          requestType: 'form',
        })
          .then((res) => {
            if (res.code == 200) {
              file.id = res?.data;
              setfileList([...fileList, file]);
              message.success(res?.message);
            } else {
              message.error(res?.message);
            }
          })
          .finally(() => {
            setUploadFileLoading(false);
          });

        return false;
      }
      message.warning(
        '因安全管理要求，仅能上传以下类型文件，表格：csv、xls、xlsx；图片:jpg、png、bmp、jpeg；压缩包：rar、zip、7z；文字：doc、docx、txt、pdf、ppt、pptx。',
      );
      return false;
    },
    onRemove: (file) => {
      Modal.confirm({
        title: '提示',
        content: '请确认是否删除该文件',
        onOk: () => {
          const newFileList = [...fileList];
          setfileList(newFileList.filter((item) => item.id != file.id));
          setRemoveList([...RemoveList, file.id]);
        },
      });
    },

    onPreview: (file, list) => {
      const tag = file.name?.substring(file?.name.lastIndexOf('.'));
      if (
        ['.txt', '.jpg', '.png', '.bmp', '.jpeg'].some((item) => {
          return item == tag;
        })
      ) {
        if (tag == '.txt') {
          request('/api/hn/document/downloadFileById', {
            method: 'get',
            params: { fileId: file?.id },
            // requestType: 'form',
            // responseType: 'blob',
            useCSRFToken: false,
            getResponse: true,
          }).then((res) => {
            setfileInfo({
              unit: tag?.split('.')[1],
            });
            settxtData(res?.data || '');
            setfileSearchVisible(true);
          });
        } else {
          setfileInfo({
            unit: tag?.split('.')[1],
            file: `/api/hn/document/downloadFileById?fileId=${file?.id}`,
          });
          setfileSearchVisible(true);
        }
      } else {
        message.warning('仅txt jpg png bmp jpeg格式支持预览');
        return;
      }
    },
    // onDownload: (file) => {
    //   Modal.confirm({
    //     title: '提示',
    //     content: '请确认是否下载该文件',
    //     onOk: () => {
    //       window.open(`/api/hn/document/downloadFileById?fileId=${file?.id}`);
    //     },
    //   });
    // },
    showUploadList: {
      showRemoveIcon: false,
      // showDownloadIcon: uploadTitle == '详情',
    },
    fileList,
  };
  useEffect(() => {
    setfileList(
      (record?.fileList || []).map((item) => {
        return {
          uid: item?.id,
          name: item?.fileName,
          status: 'done',
          url: item?.filePath,
          id: item?.id,
        };
      }),
    );
  }, [record]);
  useEffect(() => {
    getLevelByUser();
  }, []);
  const [fileList, setfileList] = useState([]);

  const handleSubmit = () => {
    form.validateFields((err, values) => {
      if (err) return;
      const newData = record?.id
        ? {
            fileIdList: fileList.map((item) => item.id),
            ...values,
            id: record?.id,
            deletedFileIdList: RemoveList,
          }
        : { fileIdList: fileList.map((item) => item.id), ...values };
      delete newData.file;
      setConfirmLoading(true);

      request(
        uploadTitle == '编辑' ? '/api/hn/document/updateDocumentById' : '/api/hn/document/addFile',
        {
          method: 'POST',
          data: newData,
          requestType: 'json',
        },
      )
        .then((res) => {
          if (res?.code == 200) {
            setVisible();
            getListDatas();
            message.success(res?.message);
          } else {
            message.error(res?.message);
          }
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };
  const [LevelByUser, setLevelByUser] = useState([]);
  const getLevelByUser = () => {
    request('/api/hn/document/getLevelByUser', {
      method: 'get',
    }).then((res) => {
      if (res?.code == 200) {
        setLevelByUser(res?.data || []);
      } else {
        setLevelByUser([]);
      }
    });
  };
  return (
    <>
      {visible && (
        <Modal
          title={uploadTitle}
          visible={visible}
          width={800}
          onOk={() => {
            handleSubmit();
          }}
          onCancel={() => {
            setVisible();
            setRemoveList([]);
          }}
          okButtonProps={uploadTitle == '详情' ? { style: { display: 'none' } } : {}}
          cancelButtonProps={uploadTitle == '详情' ? { style: { display: 'none' } } : {}}
          okText={uploadTitle == '编辑' ? '确认编辑' : '确认添加'}
          cancelText="取消"
          confirmLoading={confirmLoading}
        >
          <div className={styles.formStyles}>
            <Form {...formItemLayout}>
              <Row gutter={[24, 8]}>
                <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }} layout={'horizontal'}>
                  <Col span={24}>
                    <Form.Item label={'标题'} labelCol={{ span: 4 }} wrapperCol={{ span: 14 }}>
                      {getFieldDecorator('title', {
                        initialValue: record?.title,
                        rules: [
                          { required: true, message: '请输入标题' },
                          { max: 30, message: '不超过30字' },
                        ],
                      })(
                        <Input
                          disabled={uploadTitle == '详情'}
                          allowClear
                          placeholder="请填写，不超过30字"
                        />,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <div className={styles.TextArea}>
                      <Form.Item
                        label={'内容描述'}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 14 }}
                      >
                        {getFieldDecorator('content', {
                          initialValue: record?.content,
                          rules: [{ max: 500, message: '不超过500字' }],
                        })(
                          <Input.TextArea
                            disabled={uploadTitle == '详情'}
                            placeholder="请填写，不超过500字"
                          />,
                        )}
                      </Form.Item>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div className={styles.level}>
                      <Form.Item
                        label={'查阅等级'}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 14 }}
                      >
                        {getFieldDecorator('level', {
                          initialValue: record?.level,
                          rules: [{ required: true, message: '请选择查阅等级' }],
                        })(
                          <Select
                            disabled={uploadTitle == '详情'}
                            mode=""
                            size="default"
                            allowClear={true}
                            autoFocus={false}
                            placeholder="请选择"
                            showArrow={true}
                            showSearch={false}
                            getPopupContainer={(triggerNode) => triggerNode.parentElement}
                          >
                            {(LevelByUser || []).map((item) => {
                              return (
                                <Select.Option key={type[item]} value={item}>
                                  {type[item]}
                                </Select.Option>
                              );
                            })}
                          </Select>,
                        )}
                        <a
                          onClick={() => {
                            Modal.confirm({
                              cancelButtonProps: {
                                style: {
                                  display: 'none',
                                },
                              },
                              width: 600,
                              title: '提示',
                              content: (
                                <>
                                  <div>【一级】：最低保密等级，任何角色都可以查阅</div>
                                  <div>
                                    【二级】：中级保密等级，地市一级管理员及省公司一级管理员无法查阅，其他角色可查阅
                                  </div>
                                  <div>
                                    【三级】：最高保密等级，仅地市三级管理员、省公司三级管理员（复盘）、省公司三级管理员（支撑）及省公司总管理员可查阅
                                  </div>
                                </>
                              ),
                            });
                          }}
                        >
                          等级说明？
                        </a>
                      </Form.Item>
                    </div>
                  </Col>
                  <Col span={24}>
                    <Form.Item label={'附件上传'} labelCol={{ span: 4 }} wrapperCol={{ span: 14 }}>
                      {getFieldDecorator('file')(
                        <Upload
                          disabled={uploadTitle == '详情' || uploadFileLoading}
                          {...upLoadProps}
                          fileList={fileList}
                        >
                          <Button disabled={uploadTitle == '详情'}>
                            <Icon type={`${uploadFileLoading ? 'loading' : 'upload'}`} /> 选择文件
                          </Button>
                        </Upload>,
                      )}
                    </Form.Item>
                    <div className={styles.downloadIcon}>
                      {fileList.map((item, index) => {
                        return (
                          <>
                            <a
                              onClick={() => {
                                uploadTitle == '详情'
                                  ? Modal.confirm({
                                      title: '提示',
                                      content: '请确认是否下载该文件',
                                      onOk: () => {
                                        window.open(
                                          `/api/hn/document/downloadFileById?fileId=${fileList[index]?.id}`,
                                        );
                                      },
                                    })
                                  : Modal.confirm({
                                      title: '提示',
                                      content: '请确认是否删除该文件',
                                      onOk: () => {
                                        const newFileList = [...fileList];
                                        setfileList(
                                          newFileList.filter(
                                            (item) => item.id != fileList[index]?.id,
                                          ),
                                        );
                                        setRemoveList([...RemoveList, fileList[index]?.id]);
                                      },
                                    });
                              }}
                            >
                              {uploadTitle == '详情' ? (
                                <Icon type="download" />
                              ) : (
                                <Icon type="delete" />
                              )}
                            </a>
                          </>
                        );
                      })}
                    </div>
                  </Col>
                </Form>
              </Row>
            </Form>
          </div>
        </Modal>
      )}

      <Modal
        title={'文件预览'}
        visible={fileSearchVisible}
        width={1000}
        // onOk={() => {
        //   handleSubmit();
        // }}
        onCancel={() => {
          setfileSearchVisible();
          settxtData('');
          setfileInfo({});
        }}
        footer={null}
      >
        <div className={styles.flieContent}>
          {/* 展示txt文件 */}
          {fileInfo?.unit == 'txt' && (
            <div style={{ whiteSpace: 'pre-line', overflowY: 'scroll', height: '100%' }}>
              {txtData}
            </div>
          )}
          {fileInfo?.unit != 'txt' && fileInfo?.unit && (
            <FileViewer
              fileType={fileInfo?.unit} //文件类型
              filePath={fileInfo?.file} //文件地址
            />
          )}
          {/* <iframe src={Pptx} width="100%" height="100%" frameborder="1"></iframe> */}
        </div>
      </Modal>
    </>
  );
};
export default Form.create()(Index);
