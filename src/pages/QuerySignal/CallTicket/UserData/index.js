import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Button, Card, Form, Input, message, Row, Col, DatePicker, Popover } from 'antd';
import StandardTable from '@/components/StandardTable';
import AddModal from '@/pages/DataCenter/BlackBaseStationManage/AddModal';
import request from '@/utils/request';
import moment from 'moment';
import styles from './index.less';
import { useLicensee } from 'ponshine';
const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator },
  } = props;
  // 列表加载状态
  const [tableLoading, setTableLoading] = useState(false);
  const [addVisible, setAddVisible] = useState(false);
  // 列表数据
  const [tableData, setTableData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  // 列表选中项
  const [selectedRows, setSelectedRows] = useState([]);
  const [editRow, setEditRow] = useState({});
  // 查询条件
  const searchParams = useRef({});
  // 添加排序状态
  const [sortedInfo, setSortedInfo] = useState({});
  // 表格配置项
  const columns = [
    {
      title: '主叫号码',
      dataIndex: 'callingNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'callingNbr' && sortedInfo.order,
    },
    {
      title: '被叫号码',
      dataIndex: 'calledNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'calledNbr' && sortedInfo.order,
    },
    {
      title: '主叫号码归属地',
      dataIndex: 'callingBelongingPlace',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '被叫号码归属地',
      dataIndex: 'calledBelongingPlace',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '通话时间',
      dataIndex: 'callingTime',
      align: 'center',
      ellipsis: true,
      width: 180,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'callingTime' && sortedInfo.order,
    },
    {
      title: '时长',
      dataIndex: 'callingDur',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'callingDur' && sortedInfo.order,
    },
    {
      title: '通话地区',
      dataIndex: 'callingArea',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '呼叫类型',
      dataIndex: 'callType',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '基站ID',
      dataIndex: 'cellId',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'cellId' && sortedInfo.order,
      render: (text, record) => {
        return (
          <Popover
            content={
              <Button
                type="link"
                onClick={() => {
                  setEditRow({ ...record, relatedEnbId: record.cellId, title: '转为黑基站' });
                  setAddVisible(true);
                }}
                disabled={!isShowJH}
              >
                转为黑基站
              </Button>
            }
          >
            <a>{text}</a>
          </Popover>
        );
      },
    },
    {
      title: '基站名称',
      dataIndex: 'cellName',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'cellName' && sortedInfo.order,
    },
  ];
  const isShowJH = useLicensee('queryUserBill_add_black_base');
  // 列表查询
  const getTableData = ({ current = 1, pageSize = 10, ...props }) => {
    setTableLoading(true);
    setSortedInfo({});

    request('/api/hn/billQuery/queryUserBill', {
      method: 'POST',
      data: props,
      requestType: 'json',
    })
      .then((res) => {
        if (res.code == 200) {
          setTableData({
            list: res.data || [],
            total: res.data?.length || 0,
          });
          setPagination({
            current: current,
            pageSize: pageSize,
          });
          searchParams.current = props;
        } else {
          message.error(res.message);
          setTableData({
            list: [],
            total: 0,
          });
          setPagination({
            current: 1,
            pageSize: 10,
          });
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  };

  const handleSearch = () => {
    form.validateFields((errors, formData) => {
      if (errors) return;
      const params = {
        ...formData,
        queryDateStart: formData?.date?.[0]?.format('YYYY-MM-DD'),
        queryDateEnd: formData?.date?.[1]?.format('YYYY-MM-DD'),
        // current: current,
        // pageSize: pageSize,
      };
      delete params.date;
      getTableData(params);
    });
  };

  // 列表改变分页
  const handleTableChange = (p, filters, sorter) => {
    setSortedInfo(sorter);
    setPagination({ current: p.current, pageSize: p.pageSize });
  };

  // 添加排序逻辑
  const getCurrentPageData = useMemo(() => {
    let currentData = [...(tableData.list || [])];

    if (sortedInfo.columnKey && sortedInfo.order) {
      currentData.sort((a, b) => {
        const order = sortedInfo.order === 'ascend' ? 1 : -1;

        switch (sortedInfo.columnKey) {
          case 'callingNbr':
            return order * (Number(a.callingNbr || '0') - Number(b.callingNbr || '0'));
          case 'calledNbr':
            return order * (Number(a.calledNbr || '0') - Number(b.calledNbr || '0'));
          case 'callingTime':
            return order * (moment(a.callingTime).valueOf() - moment(b.callingTime).valueOf());
          case 'callingDur':
            return order * (Number(a.callSeconds || '0') - Number(b.callSeconds || '0'));
          case 'cellId':
            return order * (Number(a.cellId || '0') - Number(b.cellId || '0'));
          case 'cellName':
            return order * (a.cellName || '').localeCompare(b.cellName || '', 'zh-CN');
          default:
            return 0;
        }
      });
    }

    const { current = 1, pageSize = 10 } = pagination || {};
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return currentData.slice(start, end);
  }, [tableData.list, pagination, sortedInfo]);

  // 分析时间限制
  const disabledDate = (current) => {
    // 计算6个月前的日期
    const sixMonthsAgo = moment().subtract(6, 'months');

    // 禁用所有6个月前的日期之前的日期
    return current && (current < sixMonthsAgo || current > moment());
  };

  const handleReload = () => {
    getTableData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams.current,
    });
  };

  return (
    <Card bordered={false}>
      <Row>
        <Form className={styles.myStylesForm}>
          {/* <Licensee license="signalLocation_querySignalLocation"> */}
          <Col span={8}>
            <Form.Item label="查询号码">
              {getFieldDecorator('queryNum', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: `请输入查询号码`,
                  },
                  // {
                  //   validator: (rules, value, callback) => {
                  //     const phoneReg = /^\d+$/;
                  //     if (value &&!phoneReg.test(value)) callback('查询号码必须为纯数字');
                  //     callback();
                  //   },
                  // },
                ],
              })(<Input placeholder="如查询号码为固话，格式为：0731-10000" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="主叫号码">
              {getFieldDecorator('callingNum', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  //   {
                  //     required: true,
                  //     message: `请输入主叫`,
                  //   },
                  {
                    validator: (rules, value, callback) => {
                      const phoneReg = /^\d+$/;
                      if (value && !phoneReg.test(value)) callback('主叫必须为纯数字');
                      callback();
                    },
                  },
                ],
              })(<Input placeholder="如主叫号码为固话，格式为：073110000" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="被叫号码">
              {getFieldDecorator('calledNum', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  //   {
                  //     required: true,
                  //     message: `请输入被叫`,
                  //   },
                  {
                    validator: (rules, value, callback) => {
                      const phoneReg = /^\d+$/;
                      if (value && !phoneReg.test(value)) callback('被叫必须为纯数字');
                      callback();
                    },
                  },
                ],
              })(<Input placeholder="如被叫号码为固话，格式为：073110000" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="查询日期">
              {getFieldDecorator('date', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: `请选择查询时间`,
                  },
                ],
              })(
                <DatePicker.RangePicker
                  allowClear
                  disabledDate={disabledDate}
                  format={'YYYY-MM-DD'}
                  //   onCalendarChange={onCalendarChange}
                  style={{ width: '100%' }}
                />,
              )}
            </Form.Item>
          </Col>
          {/* </Licensee> */}
          <Col span={16} align="right">
            <Button
              type="primary"
              onClick={() => {
                handleSearch();
              }}
            >
              查询
            </Button>
            {/* <Licensee license="signalLocation_querySignalLocation"> */}

            {/* </Licensee> */}
          </Col>
        </Form>
      </Row>
      <div>
        <StandardTable
          selectedRows={selectedRows}
          data={{
            list: getCurrentPageData,
            pagination: {
              total: tableData.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          }}
          columns={columns}
          // scroll={{ x: 'max-content' }}
          onChange={handleTableChange}
          rowSelectionProps={false}
          showSelectCount={false}
          rowkey="eventTypeId"
          loading={tableLoading}
        />
      </div>
      {addVisible && (
        <AddModal
          visible={addVisible}
          
          cancel={() => {
            setAddVisible(false);
          }}
          editRow={editRow}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
