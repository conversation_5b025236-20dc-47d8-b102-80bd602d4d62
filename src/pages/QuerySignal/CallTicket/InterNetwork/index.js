import React, { useState, useEffect, useMemo } from 'react';
import { Button, Card, Form, Input, message, Row, Col, DatePicker, Select } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import styles from './index.less';
import moment from 'moment';
import { Licensee, useLicensee } from 'ponshine';
const { Option } = Select;
const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator },
  } = props;
  // 列表加载状态
  const [tableLoading, setTableLoading] = useState(false);
  // 列表数据
  const [tableData, setTableData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  // 列表选中项
  const [selectedRows, setSelectedRows] = useState([]);
  // 查询条件
  const [searchParams, setSearchParams] = useState({});
  // 添加排序状态
  const [sortedInfo, setSortedInfo] = useState({});
  const [opponentOperator, setOpponentOperator] = useState([
    {
      value: 'B',
      label: '中国移动手机',
    },
    {
      value: 'F',
      label: '中国移动固网(原铁通)',
    },
    {
      value: 'G',
      label: '中国联通手机',
    },

    {
      value: 'H',
      label: '中国联通固网(原网通)',
    },
    {
      value: 'E',
      label: '广电移网',
    },
    {
      value: 'K',
      label: '广电固网',
    },
  ]);
  // 表格配置项
  const columns = [
    {
      title: '主叫号码',
      dataIndex: 'CallingNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'CallingNbr' && sortedInfo.order,
    },
    {
      title: '被叫号码',
      dataIndex: 'CalledNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'CalledNbr' && sortedInfo.order,
    },
    {
      title: '主叫号码归属地',
      dataIndex: 'callingBelongingPlace',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '被叫号码归属地',
      dataIndex: 'calledBelongingPlace',
      align: 'center',
      ellipsis: true,
    },

    {
      title: '通话时间',
      dataIndex: 'CallingTime',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'CallingTime' && sortedInfo.order,
    },
    {
      title: '时长',
      dataIndex: 'CallingDur',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'CallingDur' && sortedInfo.order,
    },
    {
      title: '过网结算地区',
      dataIndex: 'SettleArea',
      align: 'center',
      ellipsis: true,
      //width: 160,
    },
    {
      title: '关口网',
      dataIndex: 'SwitchName',
      align: 'center',
      ellipsis: true,
      // width: 180,
    },
    {
      title: '入中继',
      dataIndex: 'TrunkIn',
      align: 'center',
      ellipsis: true,
      // width: 140,
    },
    {
      title: '出中继',
      dataIndex: 'TrunkOut',
      align: 'center',
      ellipsis: true,
      // width: 140,
    },
    {
      title: '对端信息',
      dataIndex: 'oppositeInformation',
      align: 'center',
      ellipsis: true,
      // width: 140,
    },
  ];

  useEffect(() => {}, []);
  // 列表查询
  const handleSearch = (current = 1, pageSize = 10) => {
    form.validateFields((errors, formData) => {
      if (errors) return;
      if (!formData.callingNum && !formData.calledNum) {
        return message.error('请输入主叫号码或者被叫号码');
      } else {
        const params = {
          ...formData,
          queryDate: formData?.date?.format('YYYY-MM-DD'),
          // current: current,
          // pageSize: pageSize,
        };
        delete params.date;
        setTableLoading(true);
      setSortedInfo({});

        request('/api/hn/billQuery/queryInternetBill', {
          method: 'POST',
          data: {
            ...params,
          },
          requestType: 'json',
        })
          .then((res) => {
            if (res.code == 200) {
              setTableData({
                list: res.data || [],
                total: res.data?.length || 0,
              });
              setPagination({
                current: current,
                pageSize: pageSize,
              });
              setSearchParams(formData);
            } else {
              message.error(res.message);
              setTableData({
                list: [],
                total: 0,
              });
              setPagination({
                current: 1,
                pageSize: 10,
              });
            }
          })
          .finally(() => {
            setTableLoading(false);
          });
      }
    });
  };
  // 添加排序逻辑
  const getCurrentPageData = useMemo(() => {
    let currentData = [...(tableData.list || [])];

    if (sortedInfo.columnKey && sortedInfo.order) {
      currentData.sort((a, b) => {
        const order = sortedInfo.order === 'ascend' ? 1 : -1;

        switch (sortedInfo.columnKey) {
          case 'CallingNbr':
            return order * (Number(a?.CallingNbr || '0') - Number(b?.CallingNbr || '0'));
          case 'CalledNbr':
            return order * (Number(a?.CalledNbr || '0') - Number(b?.CalledNbr || '0'));
          case 'CallingTime':
            return order * (moment(a?.CallingTime).valueOf() - moment(b?.CallingTime).valueOf());
          case 'CallingDur':
            return order * (Number(a?.callSeconds || '0') - Number(b?.callSeconds || '0'));
          default:
            return 0;
        }
      });
    }

    const { current = 1, pageSize = 10 } = pagination || {};
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return currentData.slice(start, end);
  }, [tableData.list, pagination, sortedInfo]);

  // 修改 handleTableChange 处理排序
  const handleTableChange = (p, filters, sorter) => {
    setSortedInfo(sorter);
    setPagination({ current: p.current, pageSize: p.pageSize });
  };

  const [startTime, setStartTime] = useState(undefined);

  // 分析时间限制
  const disabledDate = (current) => {
    // 计算6个月前的日期
    const sixMonthsAgo = moment().subtract(6, 'months');

    // 禁用所有6个月前的日期之前的日期
    return current && (current < sixMonthsAgo || current > moment());
  };

  return (
    <Card bordered={false}>
      <Form className={styles.myStylesForm}>
        <Row gutter={24}>
          {/* <Licensee license="signalLocation_querySignalLocation"> */}

          <Col span={8}>
            <Form.Item label="主叫号码">
              {getFieldDecorator('callingNum', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  //   {
                  //     required: true,
                  //     message: `请输入主叫`,
                  //   },
                  {
                    validator: (rules, value, callback) => {
                      const phoneReg = /^\d+$/;
                      if (value && !phoneReg.test(value)) callback('主叫必须为纯数字');
                      callback();
                    },
                  },
                ],
              })(<Input placeholder="如主叫号码为固话，格式为：073110000" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="被叫号码">
              {getFieldDecorator('calledNum', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  //   {
                  //     required: true,
                  //     message: `请输入被叫`,
                  //   },
                  {
                    validator: (rules, value, callback) => {
                      const phoneReg = /^\d+$/;
                      if (value && !phoneReg.test(value)) callback('被叫必须为纯数字');
                      callback();
                    },
                  },
                ],
              })(<Input placeholder="如被叫号码为固话，格式为：073110000" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="对端运营商">
              {getFieldDecorator('partnerId', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  //   {
                  //     required: true,
                  //     message: `请输入被叫`,
                  //   },
                  //   {
                  //     validator: (rules, value, callback) => {
                  //       const phoneReg = /^\d+$/;
                  //       if (value &&!phoneReg.test(value)) callback('被叫必须为纯数字');
                  //       callback();
                  //     },
                  //   },
                ],
              })(
                <Select placeholder="请选择" allowClear>
                  {opponentOperator.length &&
                    opponentOperator.map((v, i) => {
                      return (
                        <Option key={i} value={v.value}>
                          {v.label}
                        </Option>
                      );
                    })}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="查询日期">
              {getFieldDecorator('date', {
                initialValue: undefined,
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: `请选择查询日期`,
                  },
                ],
              })(
                <DatePicker
                  allowClear
                  // disabledDate={disabledDate}
                  format={'YYYY-MM-DD'}
                  //   onCalendarChange={onCalendarChange}
                  style={{ width: '100%' }}
                />,
              )}
            </Form.Item>
          </Col>
          {/* </Licensee> */}
          <Col span={16} align="right">
            {/* <Licensee license="signalLocation_querySignalLocation"> */}
            <Button
              type="primary"
              onClick={() => {
                handleSearch();
              }}
            >
              查询
            </Button>
            {/* </Licensee> */}
          </Col>
        </Row>
      </Form>
      <div>
        <StandardTable
          selectedRows={selectedRows}
          data={{
            list: getCurrentPageData,
            pagination: {
              total: tableData.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          }}
          columns={columns}
          // scroll={{ x: 'max-content' }}
          onChange={handleTableChange}
          rowSelectionProps={false}
          showSelectCount={false}
          rowkey="eventTypeId"
          loading={tableLoading}
        />
      </div>
    </Card>
  );
};

export default Form.create()(Index);
