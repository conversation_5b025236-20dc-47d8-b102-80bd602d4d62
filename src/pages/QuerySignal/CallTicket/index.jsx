/*
 * @Author: ss <EMAIL>
 * @Date: 2024-07-09 10:15:16
 * @LastEditors: ss <EMAIL>
 * @LastEditTime: 2024-07-18 15:13:00
 * @FilePath: /hunanfanzha/src/pages/QuerySignal/CallTicket/index.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, message, Modal, Row, Col, Select, Tabs } from 'antd';

import { Licensee, useLicensee } from 'ponshine';
import UserData from './UserData';
import InterNetwork from './InterNetwork';
import ConnectedCall from './ConnectedCall';
import NoAuth from '@/pages/NoAuth';

const { TabPane } = Tabs;

const Index = (props) => {
    const [key, setKey] = useState("1")
    const callback = (key) => {
        setKey(key)
    }

    return (
        <Card bordered={false}>
            <Tabs defaultActiveKey={key} activeKey={key} onChange={callback}>
                <TabPane tab="用户话单查询" key="1">
                    <Licensee license='queryUserBill' fallback={NoAuth}>
                        {
                            key == '1' && <UserData />
                        }
                    </Licensee>
                </TabPane>
                <TabPane tab="网间话单查询" key="2">
                    <Licensee license='queryInternetBill' fallback={NoAuth}>
                        {
                            key == '2' && <InterNetwork />
                        }
                    </Licensee>
                </TabPane>
                <TabPane tab="通联话单查询" key="3">
                    <Licensee license='queryRelatedBill' fallback={NoAuth}>
                        {
                            key == '3' && <ConnectedCall />
                        }
                    </Licensee>
                </TabPane>
            </Tabs>,

        </Card>
    )

};

export default Form.create()(Index);