import request from '@/utils/request';
import Jsencrypt from 'jsencryptNew';
const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzaCrNH49tWuCmTxnVivfIgg8OSljmE3Lx9+KZIBQxfXZhLsj7uLdXRB3Q9MQ5sUhS7dQ+XfUaczNizMv+gQ8AYsPW9P9VTYUNRug47KhPgU28oOlkI5qm5RPQg06B5S7SGYpihhmTjzFWNTlshFjVZTV4QyfuX5hNrigJ4ddFmwIDAQAB'; // 密码策略配置

export async function saveMergeSelf(params) {
  const encrypt = new Jsencrypt();
  encrypt.setPublicKey(publicKey);
  const { password, ...rest } = params;
  return request('/api/user/mergeSelf', {
    method: 'POST',
    data: { ...rest, password: password ? encrypt.encryptLong(password) : '' },
    requestType: 'form',
    qsPkg: 'qs',
    stringifyOptions: {
      arrayFormat: 'repeat',
      strictNullHandling: true,
      allowDots: true,
    },
  });
} // 查询头像

export async function getAvatar() {
  return request('/api/user/avatar', {
    method: 'GET',
  });
} // 设置头像

export async function saveAvatar(params) {
  const formData = new FormData();
  formData.append('file', params?.file);
  return request('/api/user/avatar', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}
