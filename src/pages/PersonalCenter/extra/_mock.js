export default {
  'GET /api/dict/organization': (req, res) => {
    const data = [
      {
        id: '1',
        name: '单位1',
        pid: '',
      },
      {
        id: '2',
        name: '单位2',
        pid: '',
      },
      {
        id: '3',
        name: '部门1',
        pid: '1',
      },
      {
        id: '4',
        name: '部门2',
        pid: '2',
      },
      {
        id: '5',
        name: '科室1',
        pid: '3',
      },
      {
        id: '6',
        name: '科室2',
        pid: '4',
      },
    ];
    const pid = req.query.pid;
    setTimeout(() => {
      res.json(data.filter((v) => `${v.pid}` === `${pid}`));
    }, 2000);
  },
};
