import React, { useLayoutEffect, useState, useRef, useCallback } from 'react';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { Col, Form, Row, Select } from 'antd';
import { getOrganization } from './service';
const FormItem = Form.Item;
const { Option } = Select;

const isValidId = (id) => ['number', 'string'].includes(typeof id) && !!`${id}`.trim();

const formatId = (id) => (isValidId(id) ? `${id}`.trim() : undefined);

const filterObjHaveInvalidIdInArr = (arr) => {
  const rArr = [];

  if (Array.isArray(arr)) {
    arr.forEach((item) => {
      if (isValidId(item?.id)) {
        rArr.push({ ...item, id: formatId(item?.id) });
      }
    });
  }

  return rArr;
};

const Extra = (props) => {
  const [companyOptions, setCompanyOptions] = useState([]);
  const [departmentOptions, setDepartmentOptions] = useState([]);
  const [sectionOptions, setSectionOptions] = useState([]);
  const { form, formValues } = props;
  const { getFieldDecorator, setFieldsValue } = form;
  const [companyLoading, setCompanyLoading] = useState(false);
  const [departmentLoading, setDepartmentLoading] = useState(false);
  const [sectionLoading, setSectionLoading] = useState(false);
  const {
    company: lastCompany,
    department: lastDepartment,
    section: lastSection,
  } = formValues || {};
  const lastCompanyId = formatId(lastCompany?.id);
  const lastDepartmentId = formatId(lastDepartment?.id);
  const lastSectionId = formatId(lastSection?.id);
  const lastIdsRef = useRef();
  const getLastIdInRef = useCallback(function (key) {
    return lastIdsRef.current[key];
  }, []);
  const setLastIdsInRef = useCallback(function (ids) {
    lastIdsRef.current = { ...lastIdsRef.current, ...ids };
  }, []);
  const queryOrganization = useCallback(
    async (type, pvalue) => {
      if (type === 'company.id') {
        setCompanyLoading(true);
        setCompanyOptions([]);
        setDepartmentOptions([]);
        setSectionOptions([]);
      } else if (type === 'department.id' && pvalue === getLastIdInRef('company.id')) {
        setDepartmentLoading(true);
        setDepartmentOptions([]);
        setSectionOptions([]);
      } else if (type === 'section.id' && pvalue === getLastIdInRef('department.id')) {
        setSectionLoading(true);
        setSectionOptions([]);
      }

      try {
        const response = await getOrganization({
          pid: pvalue,
        });

        if (type === 'company.id') {
          setCompanyOptions(filterObjHaveInvalidIdInArr(response));
        } else if (type === 'department.id' && pvalue === getLastIdInRef('company.id')) {
          setDepartmentOptions(filterObjHaveInvalidIdInArr(response));
        } else if (type === 'section.id' && pvalue === getLastIdInRef('department.id')) {
          setSectionOptions(filterObjHaveInvalidIdInArr(response));
        }
      } finally {
        if (type === 'company.id') {
          setCompanyLoading(false);
        } else if (type === 'department.id' && pvalue === getLastIdInRef('company.id')) {
          setDepartmentLoading(false);
        } else if (type === 'section.id' && pvalue === getLastIdInRef('department.id')) {
          setSectionLoading(false);
        }
      }
    },
    [getLastIdInRef],
  );
  const handleChangeCompanyId = useCallback(
    (companyId) => {
      setLastIdsInRef({
        'company.id': companyId,
        'department.id': undefined,
        'section.id': undefined,
      });
      setFieldsValue({
        'department.id': undefined,
        'section.id': undefined,
      });
      queryOrganization('department.id', companyId);
    },
    [queryOrganization, setFieldsValue, setLastIdsInRef],
  );
  const handleChangeDepartmentId = useCallback(
    (departmentId) => {
      setLastIdsInRef({
        'department.id': departmentId,
        'section.id': undefined,
      });
      setFieldsValue({
        'section.id': undefined,
      });
      queryOrganization('section.id', departmentId);
    },
    [queryOrganization, setFieldsValue, setLastIdsInRef],
  );
  const ref = useRef();
  ref.current = {
    'company.id': {
      onChange: handleChangeCompanyId,
      loading: companyLoading,
      options: companyOptions,
    },
    'department.id': {
      onChange: handleChangeDepartmentId,
      loading: departmentLoading,
      options: departmentOptions,
    },
    'section.id': {
      onChange: null,
      loading: sectionLoading,
      options: sectionOptions,
    },
  };
  useLayoutEffect(() => {
    const fetchData = async () => {
      setLastIdsInRef({
        'company.id': lastCompanyId,
        'department.id': lastDepartmentId,
        'section.id': lastSectionId,
      });
      await Promise.all([
        queryOrganization('company.id', ''),
        isValidId(lastCompanyId)
          ? queryOrganization('department.id', lastCompanyId)
          : Promise.resolve(),
        isValidId(lastDepartmentId)
          ? queryOrganization('section.id', lastDepartmentId)
          : Promise.resolve(),
      ]);

      if (
        lastCompanyId === getLastIdInRef('company.id') &&
        lastDepartmentId === getLastIdInRef('department.id') &&
        lastSectionId === getLastIdInRef('section.id')
      ) {
        setFieldsValue({
          'company.id': lastCompanyId,
          'department.id': lastDepartmentId,
          'section.id': lastSectionId,
        });
      }
    };

    fetchData();
  }, [
    getLastIdInRef,
    lastCompanyId,
    lastDepartmentId,
    lastSectionId,
    queryOrganization,
    setFieldsValue,
    setLastIdsInRef,
  ]);

  const renderFormItem = (formItemKey) => {
    return (
      <FormItem
        {...props.formItemProps}
        label={formatMessage({
          id: `personalcenter.${formItemKey}`,
        })}
      >
        {getFieldDecorator(`${formItemKey}.id`, {
          rules: [
            {
              required: true,
              message: formatMessage({
                id: `personalcenter.${formItemKey}.required`,
              }),
            },
          ],
        })(
          <Select
            loading={ref.current[`${formItemKey}.id`]?.loading}
            placeholder={formatMessage({
              id: `personalcenter.${formItemKey}.placeholder`,
            })}
            onChange={ref.current[`${formItemKey}.id`]?.onChange}
          >
            {ref.current[`${formItemKey}.id`]?.options?.map((v) => (
              <Option key={v.id} value={v.id}>
                {v.name}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>
    );
  };

  const renderCols = () => {
    return ['company', 'department', 'section'].map((key) => {
      if (props.colProps) {
        return <Col {...props.colProps}>{renderFormItem(key)}</Col>;
      }

      return renderFormItem(key);
    });
  };

  const renderRow = () => {
    if (props.rowProps) {
      return <Row {...props.rowProps}>{renderCols()}</Row>;
    }

    return renderCols();
  };

  return renderRow();
};

export default Extra;
