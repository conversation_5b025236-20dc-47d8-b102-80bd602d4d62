export default {
  'personalcenter.basicSetting': 'Basic Settings',
  'personalcenter.securitySetting': 'Security Settings',
  'personalcenter.username': '<PERSON><PERSON><PERSON>',
  'personalcenter.nickname': 'Nickname',
  'personalcenter.nickname.tooltip': 'Maximum 20 Chinese and English characters',
  'personalcenter.nickname.required': 'This field is required!',
  'personalcenter.nickname.maxLength': 'The nickname cannot be greater than 20 characters!',
  'personalcenter.nickname.pattern':
    'The input format is wrong, only Chinese and English can be input!',
  'personalcenter.nickname.placeholder': 'Please enter a nickname',
  'personalcenter.email': 'Email',
  'personalcenter.email.required': 'This field is required!',
  'personalcenter.email.pattern': 'The email format is wrong!',
  'personalcenter.email.placeholder': 'Please enter email',
  'personalcenter.avatar': 'Change avatar',
  'personalcenter.avatar.isLt1M': 'The maximum avatar is 1M!',
  'personalcenter.submit': 'Update information',
  'personalcenter.submit.success.message': 'Modified successfully!',
  'personalcenter.submit.error.message': 'Modified failed!',
  'personalcenter.password': 'Account password',
  'personalcenter.password.modal.modify': 'Modify',
  'personalcenter.password.modal.title': 'Modify password',
  'personalcenter.password.modal.compareToFirstPassword':
    'Does not match the last password, please confirm!',
  'personalcenter.password.modal.password.label': 'Password',
  'personalcenter.password.modal.password.placeholder': 'Please enter a new password',
  'personalcenter.password.modal.password.tooltip':
    'The password must meet the following requirements: It cannot contain the username, and at least contains three of the following four types of characters: English uppercase letters (A-Z) English lowercase letters (a-z) Numbers (0-9) Special characters (such as: @,#,￥,%)',
  'personalcenter.password.modal.password.required': 'This field cannot be empty!',
  'personalcenter.password.modal.password.minLength':
    'The length of the password cannot be less than {minimumLength}！',
  'personalcenter.password.modal.password.pattern':
    'Password complexity does not meet the requirements!',
  'personalcenter.password.modal.confirm.label': 'Confirm password',
  'personalcenter.password.modal.confirm.placeholder':
    'Please enter the new password again, make sure the new password is the same two times',
  'personalcenter.password.modal.confirm.required': 'This field cannot be empty!',
  'personalcenter.phonenum': 'Mobile phone number',
  'personalcenter.phonenum.description': 'Mobile phone has been bound:',
  'personalcenter.phonenum.none-description': 'Unbound Mobile phone',
  'personalcenter.phonenum.modal.modify': 'Modify',
  'personalcenter.phonenum.modal.title': 'Modify phone number',
  'personalcenter.phonenum.modal.label': 'Mobile phone',
  'personalcenter.phonenum.modal.required': 'This field is required!',
  'personalcenter.phonenum.modal.pattern': 'Mobile phone number format is incorrect!',
  'personalcenter.phonenum.modal.placeholder': 'Please enter a new phone number',
  'personalcenter.getAvatar.failed': 'The avatar query failed!',
  'personalcenter.password.submit.success.message': 'Password modified successfully!',
  'personalcenter.password.submit.success.description':
    'In order to avoid your account cannot be used normally, please use the new password to log in to the platform.',
  'personalcenter.password.submit.success.goToLogin': 'Return to login page',
};
