import React, { useState } from 'react';
import { Form, Button, Modal, Input } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
const FormItem = Form.Item;

const VersionSelect = (props) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const {
    form: { validateFieldsAndScroll, getFieldDecorator, resetFields },
    onSave,
  } = props;

  const handleOk = () => {
    validateFieldsAndScroll(async (err, values) => {
      if (err) return;
      setLoading(true);
      const isFlag = await onSave?.(values);
      setLoading(false);

      if (isFlag) {
        setVisible(false);
      }
    });
  };

  const handleCancel = () => {
    resetFields();
    setVisible(false);
  };

  const formItemLayout = {
    labelCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 7,
      },
    },
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 16,
      },
    },
  };
  return (
    <>
      <Button
        type="link"
        onClick={() => {
          setVisible(true);
        }}
      >
        {formatMessage({
          id: 'personalcenter.phonenum.modal.modify',
        })}
      </Button>
      <Modal
        maskClosable={false}
        title={formatMessage({
          id: 'personalcenter.phonenum.modal.title',
        })}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={520}
        getContainer={document.body}
        confirmLoading={loading}
        destroyOnClose={true}
      >
        <Form {...formItemLayout}>
          <FormItem
            label={formatMessage({
              id: 'personalcenter.phonenum.modal.label',
            })}
          >
            {getFieldDecorator('phonenum', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'personalcenter.phonenum.modal.required',
                  }),
                },
                {
                  pattern: /^1[3|4|5|7|8][0-9]{9}$/,
                  message: formatMessage({
                    id: 'personalcenter.phonenum.modal.pattern',
                  }),
                },
              ],
            })(
              <Input
                placeholder={formatMessage({
                  id: 'personalcenter.phonenum.modal.placeholder',
                })}
              />,
            )}
          </FormItem>
        </Form>
      </Modal>
    </>
  );
};

const VersionSelectForm = Form.create()(VersionSelect);
export default VersionSelectForm;
