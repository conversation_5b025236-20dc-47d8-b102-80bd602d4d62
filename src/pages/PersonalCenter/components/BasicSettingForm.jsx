import React from 'react';
import { Form, Input, Row, Col, Upload, Icon, Button, Avatar, message, Tooltip, Select } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { getOrganization } from '../extra/service';
let Extra;

try {
  // eslint-disable-next-line global-require
  Extra = require('../extra').default; // eslint-disable-next-line no-empty
} catch (e) {}

const FormItem = Form.Item;
const BasicSettingForm = Form.create()(
  class extends React.Component {
    state={};
    componentDidMount() {
      const { form, formValues } = this.props;
      this.getCompany();
      if (formValues) {
        form.setFieldsValue({
          username: formValues?.username,
          realName: formValues?.realName,
          email: formValues?.email,
          ['company.id']:formValues?.company?.id,
          departmentName:formValues.departmentName
        });
      }
    }

    getCompany = async () => {
      const res = await getOrganization()
      if (res && res?.[0]?.value)
        this.setState({
          companyOptions: res || []
        })
    }

    componentDidUpdate(prevProps) {
      if (JSON.stringify(prevProps.formValues) !== JSON.stringify(this.props.formValues)) {
        const { form, formValues } = this.props;
        form.setFieldsValue({
          username: formValues?.username,
          realName: formValues?.realName,
          email: formValues?.email,
         
        });
      }
    }


    handleSubmit = (e) => {
      e.preventDefault();
      const { form, onSave } = this.props;
      form.validateFieldsAndScroll((err, values) => {
        if (err) {
          return;
        }

        if (onSave) onSave(values);
      });
    };

    render() {

      const { companyOptions } = this.state;
      const { form, file, onSaveAvatar, formValues } = this.props;
      const { getFieldDecorator } = form;
      const props = {
        name: 'avatar',
        showUploadList: false,
        accept: '.png, .jpg, .jpeg, .bmp',
        // eslint-disable-next-line @typescript-eslint/no-shadow
        beforeUpload: (file) => {
          const isLt1M = file.size / 1024 / 1024 < 1;

          if (!isLt1M) {
            message.error(
              formatMessage({
                id: 'personalcenter.avatar.isLt1M',
              }),
            );
          }

          return isLt1M;
        },
        customRequest: (info) => {
          // 手动上传
          onSaveAvatar({
            file: info.file,
          });
        },
      };
      return (
        <Form layout="vertical">
          <Row gutter={16}>
            <Col span={14}>
              <FormItem
                label={formatMessage({
                  id: 'personalcenter.username',
                })}
              >
                {getFieldDecorator('username', {})(<Input disabled />)}
              </FormItem>
              <FormItem
                label={
                 '姓名'
                }
              >
                {getFieldDecorator('realName', {
                  validateFirst: true,
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'personalcenter.realName.required',
                      }),
                    },
                    // {
                    //   max: 20,
                    //   message: formatMessage({
                    //     id: 'personalcenter.realName.maxLength',
                    //   }),
                    // },
                    // {
                    //   pattern: /^[\u4e00-\u9fa5_a-zA-Z]{0,20}$/,
                    //   message: formatMessage({
                    //     id: 'personalcenter.realName.pattern',
                    //   }),
                    // },
                  ],
                })(
                  <Input
                    placeholder={'请输入'}
                    disabled={true}
                  />,
                )}
              </FormItem>
              <FormItem
                label={formatMessage({
                  id: 'personalcenter.email',
                })}
              >
                {getFieldDecorator('email', {
                  validateFirst: true,
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'personalcenter.email.required',
                      }),
                    },
                    {
                      pattern: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/,
                      message: formatMessage({
                        id: 'personalcenter.email.pattern',
                      }),
                    },
                  ],
                })(
                  <Input
                    placeholder={formatMessage({
                      id: 'personalcenter.email.placeholder',
                    })}
                  />,
                )}
              </FormItem>
            </Col>
            <Col
              span={10}
              style={{
                display: 'flex',
                flexFlow: 'column',
                alignItems: 'center',
              }}
            >
              <Avatar
                size={128}
                icon="user"
                src={file}
                style={{
                  margin: '40px 0 20px 0',
                }}
              />
              <Upload {...props}>
                <Button>
                  <Icon type="upload" />
                  {formatMessage({
                    id: 'personalcenter.avatar',
                  })}
                </Button>
              </Upload>
            </Col>

            <Col span={24}>
              {/* {Extra && (
                <Extra
                  form={form}
                  formValues={formValues}
                  formItemProps={{
                    wrapperCol: {
                      span: 16,
                    },
                  }}
                  rowProps={{
                    gutter: 16,
                  }}
                  colProps={{
                    span: 8,
                  }}
                />
              )} */}
              <Row gutter={16}>
                <Col span={8}>
               <FormItem  label="所属地市">
                  {form.getFieldDecorator('company.id', {
                    // initialValue: isEditing ? initialValues.company.id : '',
                    rules: [
                      {
                        required: true,
                        // whitespace: true,
                        message: '请选择所属地市',
                      },
                    ],
                  })(
                    <Select placeholder='请选择所属地市'>
                      {
                        companyOptions?.map(v => (
                          <Select.Option key={v.id} value={v.id}>
                            {v.name}
                          </Select.Option>
                        ))
                      }
                    </Select>
                  )}
                </FormItem>
                </Col>
                <Col span={8}>
                <FormItem  label="所属部门">
                  {form.getFieldDecorator('departmentName', {
                    // initialValue: isEditing ? (initialValues.departmentName&&initialValues.departmentId)? (initialValues.departmentName+'^'+initialValues.departmentId):'': '',
                    rules: [
                      {
                        required: true,
                        // whitespace: true,
                        message: '请选择所属部门',
                      },
                    ],
                  })(
                    <Select placeholder='请选择所属部门' disabled={true}>
                   
                  </Select>
                  )}
                </FormItem>
                </Col>
                </Row>
              <Button type="primary" htmlType="submit" onClick={this.handleSubmit}>
                {formatMessage({
                  id: 'personalcenter.submit',
                })}
              </Button>
            </Col>
          </Row>
        </Form>
      );
    }
  },
);
export default BasicSettingForm;
