import React, { useRef, useState } from 'react';
import { Form, Button, Modal, Input, Tooltip, Icon, notification } from 'antd';
import { useInterval } from 'phooks';
import { router, refreshAuth } from 'ponshine';
import { formatMessage } from 'ponshine-plugin-react/locale';
import { validatePasswordStrength } from '../utils';
const FormItem = Form.Item;

const CountDown = () => {
  const [count, setCount] = useState(5);
  useInterval(
    () => {
      setCount(count - 1);
    },
    count <= 0 ? null : 1000,
  );
  return <span>{count}</span>;
};

const PasswordModify = (props) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmDirty, setConfirmDirty] = useState(false);
  const myRef = useRef();
  const {
    form: {
      validateFieldsAndScroll,
      getFieldDecorator,
      resetFields,
      getFieldValue,
      validateFields,
    },
    username,
    onSave,
    passwordPolicy,
  } = props;

  const handleToLogin = async () => {
    if (myRef.current) clearInterval(myRef.current);
    await refreshAuth(); // TODO 下方跳转路由在非单点登录时只是一个双保险（因为 refreshAuth() 后在 auth.[j|t]sx? 文件中有对回包头中的 session-status 进行特殊处理），而如果是单点登录，则不需要跳转路由，完全由 refreshAuth() 后通过 auth.[j|t]sx? 文件中的逻辑去控制

    router.push({
      pathname: '/user/login',
    });
    notification.close('personalCenter');
  };

  const handleOk = () => {
    validateFieldsAndScroll(async (err, values) => {
      if (err) return;
      setLoading(true);
      const isFlag = await onSave?.(values, false);
      setLoading(false);

      if (isFlag) {
        setVisible(false);
        if (myRef.current) clearInterval(myRef.current);
        myRef.current = setTimeout(() => {
          handleToLogin();
        }, 1000 * 5);
        notification.success({
          key: 'personalCenter',
          message: formatMessage({
            id: 'personalcenter.password.submit.success.message',
          }),
          duration: 5,
          description: (
            <div>
              <div>
                {formatMessage({
                  id: 'personalcenter.password.submit.success.description',
                })}
              </div>
              <a onClick={handleToLogin}>
                {formatMessage({
                  id: 'personalcenter.password.submit.success.goToLogin',
                })}
                <CountDown />
              </a>
            </div>
          ),
        });
      }
    });
  };

  const handleCancel = () => {
    resetFields();
    setVisible(false);
  };

  const compareToFirstPassword = (rule, value, callback) => {
    if (value && value !== getFieldValue('password')) {
      callback(
        formatMessage({
          id: 'personalcenter.password.modal.compareToFirstPassword',
        }),
      );
    } else {
      callback();
    }
  };

  const validateToNextPassword = (rule, value, callback) => {
    if (!validatePasswordStrength(value, username)) {
      callback(
        formatMessage({
          id: 'personalcenter.password.modal.password.pattern',
        }),
      );
    }

    if (value && confirmDirty) {
      validateFields(['confirm'], {
        force: true,
      });
    }

    callback();
  };

  const handleConfirmBlur = (e) => {
    const { value } = e.target;
    setConfirmDirty(confirmDirty || !!value);
  };

  const formItemLayout = {
    labelCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 7,
      },
    },
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 16,
      },
    },
  };
  return (
    <>
      <Button
        type="link"
        onClick={() => {
          setVisible(true);
        }}
      >
        {formatMessage({
          id: 'personalcenter.password.modal.modify',
        })}
      </Button>
      <Modal
        maskClosable={false}
        title={formatMessage({
          id: 'personalcenter.password.modal.title',
        })}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={520}
        getContainer={document.body}
        confirmLoading={loading}
        destroyOnClose={true}
      >
        <Form {...formItemLayout}>
          <FormItem
            label={
              <span>
                <span
                  style={{
                    marginRight: 5,
                  }}
                >
                  {formatMessage({
                    id: 'personalcenter.password.modal.password.label',
                  })}
                </span>
                <Tooltip
                  title={formatMessage({
                    id: 'personalcenter.password.modal.password.tooltip',
                  })}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
            hasFeedback
          >
            {getFieldDecorator('password', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'personalcenter.password.modal.password.required',
                  }),
                },
                {
                  min: passwordPolicy?.minimumLengthEnable ? passwordPolicy?.minimumLength : -1,
                  message: formatMessage(
                    {
                      id: 'personalcenter.password.modal.password.minLength',
                    },
                    {
                      minimumLength: passwordPolicy?.minimumLength,
                    },
                  ),
                },
                {
                  validator: validateToNextPassword,
                },
              ],
            })(
              <Input.Password
                placeholder={formatMessage({
                  id: 'personalcenter.password.modal.password.placeholder',
                })}
              />,
            )}
          </FormItem>
          <FormItem
            label={formatMessage({
              id: 'personalcenter.password.modal.confirm.label',
            })}
          >
            {getFieldDecorator('confirm', {
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'personalcenter.password.modal.confirm.required',
                  }),
                },
                {
                  validator: compareToFirstPassword,
                },
              ],
            })(
              <Input.Password
                onBlur={handleConfirmBlur}
                placeholder={formatMessage({
                  id: 'personalcenter.password.modal.confirm.placeholder',
                })}
              />,
            )}
          </FormItem>
        </Form>
      </Modal>
    </>
  );
};

const PasswordModifyForm = Form.create()(PasswordModify);
export default PasswordModifyForm;
