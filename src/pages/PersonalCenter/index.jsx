import React, { useEffect, useState } from 'react';
import { Card, message, List, Spin } from 'antd';
import { withSilence } from 'demasia-pro-layout';
import { useAuth, refreshAuth } from 'ponshine';
import BasicSettingForm from './components/BasicSettingForm';
import PasswordModify from './components/PasswordModify';
import PhonenumModify from './components/PhonenumModify';
import { saveMergeSelf, getAvatar, saveAvatar } from './service';
import { formatMessage } from 'ponshine-plugin-react/locale';
import styles from './index.less';

const PersonalCenter = () => {
  const [file, setFile] = useState('');
  const [loading, setLoading] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const { authState } = useAuth() || {};
  const { user = {}, passwordPolicy = {} } = authState || {};
  /**
   * 配置密码策略
   */

  const handleSaveMergeSelf = async (values, showMessage = true) => {
    setLoading(true);
    const response = await saveMergeSelf(values);
    setLoading(false);

    if (response?.state === 'SUCCESS') {
      await refreshAuth();

      if (showMessage) {
        message.success(
          response.message ||
            formatMessage({
              id: 'personalcenter.submit.success.message',
            }),
        );
      }

      return true;
    }

    message.error(
      response.message ||
        formatMessage({
          id: 'personalcenter.submit.error.message',
        }),
    );
    return false;
  };
  /**
   * 查询头像
   */

  const handleQueryAvatar = async () => {
    setAvatarLoading(true);
    let realAvatar = '';

    try {
      realAvatar = await getAvatar();
    } catch (e) {
      message.error(
        formatMessage({
          id: 'personalcenter.getAvatar.failed',
        }),
      );
    }

    setAvatarLoading(false);
    setFile(realAvatar);
  };
  /**
   * 配置头像
   */

  const handleSaveAvatar = async (values) => {
    const response = await saveAvatar(values);

    if (response?.state === 'SUCCESS') {
      handleQueryAvatar();
      message.success(
        response.message ||
          formatMessage({
            id: 'personalcenter.submit.success.message',
          }),
      );
    } else {
      message.error(
        response.message ||
          formatMessage({
            id: 'personalcenter.submit.error.message',
          }),
      );
    }
  };

  useEffect(() => {
    handleQueryAvatar();
  }, []);
  const data = [
    {
      title: formatMessage({
        id: 'personalcenter.password',
      }),
      description: '',
      action: (
        <PasswordModify
          username={user?.username}
          onSave={handleSaveMergeSelf}
          passwordPolicy={passwordPolicy}
        />
      ),
    },
    {
      title: formatMessage({
        id: 'personalcenter.phonenum',
      }),
      description: ['number', 'string'].includes(typeof user?.phonenum)
        ? formatMessage({
            id: 'personalcenter.phonenum.description',
          }) + user?.phonenum
        : formatMessage({
            id: 'personalcenter.phonenum.none-description',
          }),
      action: <PhonenumModify onSave={handleSaveMergeSelf} />,
    },
  ];

  return (
    <Spin spinning={loading || avatarLoading}>
      <Card
        title={formatMessage({
          id: 'personalcenter.basicSetting',
        })}
      >
        <BasicSettingForm
          formValues={user}
          onSave={handleSaveMergeSelf}
          file={file}
          onSaveAvatar={handleSaveAvatar}
        />
      </Card>
      <Card
        className={styles.cardTopMargin}
        title={formatMessage({
          id: 'personalcenter.securitySetting',
        })}
      >
        <List
          itemLayout="horizontal"
          dataSource={data}
          renderItem={(item) => (
            <List.Item actions={[item.action]}>
              <List.Item.Meta title={item.title} description={item.description} />
            </List.Item>
          )}
        />
      </Card>
    </Spin>
  );
};

export default withSilence(PersonalCenter);
