import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/groupInterfaceShare/queryGroupInterfaceShare`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 反馈上报
export async function updateGroupInterfaceShare(params) {
  return request(`/api/hn/groupInterfaceShare/updateGroupInterfaceShare`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 获取资源各级下拉
export async function getSystemConfigParentListByConfigType(params) {
  return request(`/api/hn/systemConfig/getSystemConfigParentListByConfigType`, {
    method: 'GET',
    params,
  });
}
