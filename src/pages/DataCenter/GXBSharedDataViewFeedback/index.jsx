import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Modal,
} from 'antd';

import moment from 'moment';

import StandardTable from '@/components/StandardTable';
import FeedBackModal from './FeedBackModal';
import { selectPage, getSystemConfigParentListByConfigType } from './services';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [feedBackVisible, setFeedBackVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [currentRow, setCurrentRow] = useState({});
  const handleStateList = ['待填写', '已填写', '已上报'];
  const [firstTypeList, setFirstTypeList] = useState([]);

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const getTypeList = async () => {
    const response = await getSystemConfigParentListByConfigType({
      configType: 'relateContentType',
    });
    if (response && response.code === 200) {
      setFirstTypeList(response.data || []);
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas();
    getTypeList();
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    const { createTime } = values;
    getListDatas({
      ...values,
      createTime: undefined,
      beginTime: createTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endTime: createTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas();
  };

  // 编辑
  const handleEdit = (r, type) => {
    setCurrentRow({ ...r, modalType: type });
    setFeedBackVisible(true);
  };

  let columns = [
    {
      title: '工单ID',
      width: 170,
      dataIndex: 'commandId',
      ellipsis: true,
    },
    {
      title: '共享ID',
      dataIndex: 'businessId',
      width: 240,
      ellipsis: true,
    },
    {
      title: '处理单位',
      width: 100,
      dataIndex: 'sourceCode',
      ellipsis: true,
    },
    {
      title: '资源类型',
      width: 100,
      dataIndex: 'type',
      ellipsis: true,
    },
    {
      title: '涉诈号码',
      width: 100,
      dataIndex: 'phoneNumber',
      ellipsis: true,
    },
    {
      title: '信息二级分类',
      width: 120,
      dataIndex: 'infoType',
      ellipsis: true,
    },
    {
      title: '信息三级分类',
      width: 120,
      dataIndex: 'fakeCategory',
      ellipsis: true,
    },
    {
      title: '信息四级分类',
      width: 120,
      dataIndex: 'fakeTarget',
      ellipsis: true,
    },
    {
      title: '处理状态',
      width: 100,
      dataIndex: 'state',
      ellipsis: true,
      render: (v) => {
        return handleStateList[v - 1];
      },
    },
    {
      title: '处理时间',
      width: 100,
      dataIndex: 'dateTime',
      ellipsis: true,
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'opt',
      ellipsis: true,
      fixed: 'right',
      render: (v, r) => {
        return (
          <Fragment>
            <Icon
              type="search"
              onClick={() => handleEdit(r, '查看')}
              style={{ color: '#1890ff', marginRight: 8 }}
            />
            {r.state !== 3 && (
              <Icon
                type="edit"
                style={{ color: '#1890ff' }}
                onClick={() => handleEdit(r, '编辑')}
              />
            )}
          </Fragment>
        );
      },
    },
  ];

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="工单ID">
              {getFieldDecorator('commandId')(<Input allowClear={true} placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="处理时间">
              {getFieldDecorator('createTime')(
                <RangePicker
                  format={'YYYY-MM-DD'}
                  // disabledDate={disabledDate}
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="处理状态">
              {getFieldDecorator('state')(
                <Select placeholder="请选择" allowClear={true}>
                  {handleStateList?.map((ele, index) => (
                    <Select.Option value={index + 1} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col align="right" span={6} style={{ textAlign: 'right' }}>
            <Form.Item wrapperCol={{ span: 24 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={onReset}>重置</Button>
            </Form.Item>
          </Col>
        </Form>
      </Row>

      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
      {/* 反馈上报 */}
      <FeedBackModal
        visible={feedBackVisible}
        cancel={() => {
          setFeedBackVisible(false);
          currentRow?.id && setCurrentRow({});
        }}
        reload={() => onReset()}
        currentRow={currentRow}
        firstTypeList={firstTypeList}
      />
    </Card>
  );
};
export default Form.create({})(Index);
