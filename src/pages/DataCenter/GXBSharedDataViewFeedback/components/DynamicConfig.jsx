import React, {
  Fragment,
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useMemo,
  memo,
  useRef,
} from 'react';
import { Form, Row, Col, Input, Icon, message, Spin } from 'antd';
import CascadeTypeSelect from './CascadeTypeSelect';
import { v4 as uuidv4 } from 'uuid';

const DynamicConfig = forwardRef((props, ref) => {
  const {
    form,
    form: {
      getFieldValue,
      getFieldDecorator,
      setFieldsValue,
      getFieldsValue,
      validateFields,
      resetFields,
    },
    currentRow = {},
    visible,
    disabled,
    firstTypeList,
  } = props;

  const defaultList = useRef();

  const relateContentInfo = JSON.parse(currentRow?.relateContentInfo ?? '[]');
  const relateContentType = JSON.parse(currentRow?.relateContentType ?? '[]');
  const relateContentType2 = JSON.parse(currentRow?.relateContentType2 ?? '[]');

  defaultList.current = relateContentInfo?.map((ele, index) => {
    return {
      relateContentInfo: ele,
      relateContentType: relateContentType[index],
      relateContentType2: relateContentType2[index],
      key: uuidv4(),
    };
  });

  const [formList, setFormList] = useState(defaultList.current.length ? defaultList.current : [{}]);

  const handleAdd = (index) => {
    const list = getFieldsValue().list;
    if (list.length >= 10) {
      return message.info('最多新增10条');
    }
    const newFormList = [...formList];
    resetFields();
    list.splice(index + 1, 0, {
      relateContentInfo: '',
      relateContentType: undefined,
      relateContentType2: undefined,
      key: uuidv4(),
      typeList2: [],
    });
    let filedFormList = list.map((ele, index) => {
      return {
        ...ele,
        typeList2: newFormList?.[index]?.typeList2 || [],
      };
    });
    setFieldsValue({ list: list });
    setFormList(filedFormList);
  };

  const handleDelete = (index) => {
    const list = getFieldsValue().list;
    const newFormList = [...formList];
    list.splice(index, 1);
    newFormList.splice(index, 1);
    let filedFormList = list.map((ele, index) => {
      return {
        ...ele,
        typeList2: newFormList?.[index]?.typeList2,
      };
    });
    resetFields();
    setFieldsValue({ list: list });
    setFormList(filedFormList);
  };

  useEffect(() => {
    if (!visible) {
      setFormList([]);
    } else {
      getRowsTypes(firstTypeList);
    }
  }, [visible]);

  const getRowsTypes = (data) => {
    const list = defaultList.current.length ? [...defaultList.current] : [{}];
    list?.map((item) => {
      item.typeList2 = data?.find((ele) => ele.value === item?.relateContentType)?.childList || [];
    }),
      setFormList(list);
  };

  const changeFormType2List = (index, list) => {
    const newList = [...formList];
    newList[index].typeList2 = list;
    setFormList(newList);
  };

  useImperativeHandle(ref, () => ({
    getData: () => {
      return new Promise((resolve, reject) => {
        validateFields((err, values) => {
          if (err) {
            reject(err);
            return;
          } else {
            const { list } = values;
            const obj = {
              relateContentInfo: JSON.stringify(list.map((ele) => ele.relateContentInfo)),
              relateContentType: JSON.stringify(list.map((ele) => ele.relateContentType)),
              relateContentType2: JSON.stringify(list.map((ele) => ele.relateContentType2)),
            };
            resolve(obj);
          }
        });
      });
    },
  }));

  return (
    <Fragment>
      <Row>
        {formList?.map((ele, index) => (
          <div key={ele.key}>
            <Col span={8}>
              <Form.Item label="关联涉诈内容">
                {getFieldDecorator(`list.${index}.relateContentInfo`, {
                  initialValue: ele.relateContentInfo,
                  rules: [
                    {
                      required: true,
                      message: '请输入关联涉诈内容',
                    },
                    {
                      max: 200,
                      message: '最多输入200个字符',
                    },
                  ],
                })(<Input placeholder="请输入" allowClear disabled={disabled} />)}
              </Form.Item>
            </Col>

            <CascadeTypeSelect
              firstTypeList={firstTypeList}
              index={index}
              form={form}
              currentEle={ele}
              visible={visible}
              formList={formList}
              changeFormType2List={changeFormType2List}
              disabled={disabled}
            />
            {!disabled && (
              <Col span={1}>
                <Form.Item wrapperCol={{ span: 24 }}>
                  {formList.length - 1 === index && (
                    <Icon
                      type="plus-circle"
                      onClick={() => handleAdd(index)}
                      style={{ marginLeft: 8, marginRight: 8 }}
                      disabled={disabled}
                    />
                  )}

                  {formList.length !== 1 && (
                    <Icon
                      type="minus-circle"
                      onClick={() => handleDelete(index)}
                      disabled={disabled}
                    />
                  )}
                </Form.Item>
              </Col>
            )}
          </div>
        ))}
      </Row>
    </Fragment>
  );
});
export default memo(Form.create()(DynamicConfig));
