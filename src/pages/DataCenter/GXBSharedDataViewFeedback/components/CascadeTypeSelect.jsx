import React, { Fragment, memo, useEffect, useMemo, useState } from 'react';
import { Form, Col, Select } from 'antd';

const CascadeTypeSelect = ({
  index,
  form: { getFieldDecorator, setFieldsValue },
  firstTypeList = [],
  currentEle,
  changeFormType2List,
  disabled,
}) => {
  const handleChange = async (v, isClear = true) => {
    isClear && setFieldsValue({ [`list.${index}.relateContentType2`]: undefined });
    let list = [];
    if (v) {
      list = firstTypeList.find((ele) => ele.value === v)?.childList;
    }
    changeFormType2List(index, list);
  };

  return (
    <Fragment>
      <Col span={8}>
        <Form.Item label="关联涉诈内容资源一级分类">
          {getFieldDecorator(`list.${index}.relateContentType`, {
            initialValue: currentEle.relateContentType,
            rules: [
              {
                required: true,
                message: '请选择关联涉诈内容资源一级分类',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              onChange={handleChange}
              disabled={disabled}
            >
              {firstTypeList?.map((item) => (
                <Select.Option value={item.value} key={item.value}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </Col>
      <Col span={7}>
        <Form.Item
          label="请选择关联涉诈内容资源二级分类"
          wrapperCol={{ span: 15 }}
          labelCol={{ span: 9 }}
        >
          {getFieldDecorator(`list.${index}.relateContentType2`, {
            initialValue: currentEle.relateContentType2,
            rules: [
              {
                required: true,
                message: '请选择关联涉诈内容资源二级分类',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              disabled={disabled}
            >
              {currentEle?.typeList2?.map((item) => (
                <Select.Option value={item.value} key={item.value}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </Col>
    </Fragment>
  );
};

export default memo(CascadeTypeSelect);
