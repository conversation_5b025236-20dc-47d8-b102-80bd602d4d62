import React, { Fragment, useEffect, useRef, useState, memo } from 'react';
import {
  Form,
  Modal,
  Select,
  Row,
  Col,
  message,
  DatePicker,
  Input,
  Radio,
  Button,
  Spin,
} from 'antd';
import DynamicConfig from './components/DynamicConfig';
import { updateGroupInterfaceShare } from './services';
import moment from 'moment';
import styles from './index.less';

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const ConfigEdit = ({
  visible,
  form,
  form: { getFieldValue, getFieldDecorator, validateFields, resetFields },
  cancel,
  reload,
  currentRow,
  firstTypeList,
}) => {
  const [submitLoading, setSubmitLoading] = useState(false);

  const dynamicConfigRef = useRef();
  const disabled = currentRow?.modalType === '查看';
  // 保存
  const handleOk = async () => {
    validateFields(async (err, values) => {
      if (err) return;
      const { disposeTime, dateTime } = values;
      const extraListObj = await dynamicConfigRef?.current?.getData();
      const params = {
        id: currentRow?.id,
        ...values,
        disposeTime: disposeTime?.format('YYYY-MM-DD HH:mm:ss'),
        dateTime: dateTime?.format('YYYY-MM-DD'),
        ...extraListObj,
      };
      setSubmitLoading(true);
      const response = await updateGroupInterfaceShare(params);
      setSubmitLoading(false);

      if (response && response.code === 200) {
        message.success(response.message);
        cancel();
        reload();
      } else {
        message.error(response.message);
      }
    });
  };

  return (
    <Fragment>
      <Modal
        visible={visible}
        title={`${currentRow?.modalType ?? ''}反馈数据录入`}
        footer={
          currentRow.modalType === '查看' ? null : (
            <div>
              <Button onClick={cancel}>取消</Button>
              <Button onClick={handleOk} loading={submitLoading} type="primary">
                确认上报
              </Button>
            </div>
          )
        }
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        width={'85%'}
      >
        <div className={styles.labelWrapperForm}>
          <Form {...formItemLayout}>
            <Row>
              <Col span={8}>
                <Form.Item label="工单ID">{currentRow?.commandId || '--'}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="共享ID">{currentRow?.businessId || '--'}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="涉诈号码">{currentRow?.phoneNumber || '--'}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="处理时间">{currentRow?.dateTime || '--'}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="是否涉诈">
                  {getFieldDecorator('fraudResult', {
                    initialValue: currentRow?.fraudResult ?? undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择是否涉诈',
                      },
                    ],
                  })(
                    <Select
                      placeholder="请选择"
                      getPopupContainer={(triggerNode) => triggerNode.parentElement}
                      disabled={disabled}
                    >
                      {[
                        {
                          key: 1,
                          label: '是',
                        },
                        {
                          key: 0,
                          label: '否',
                        },
                        {
                          key: 99,
                          label: '无法认定',
                        },
                      ]?.map((ele) => (
                        <Select.Option value={ele.key} key={ele.key}>
                          {ele.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="涉诈认定描述">
                  {getFieldDecorator('judgeExtend', {
                    initialValue: currentRow?.judgeExtend,
                    rules: [
                      {
                        required: true,
                        message: '请输入涉诈认定描述',
                      },
                      {
                        max: 200,
                        message: '最多输入200个字符',
                      },
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={disabled} />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="是否处置">
                  {getFieldDecorator('disposeResult', {
                    initialValue: currentRow?.disposeResult ?? undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择是否处置',
                      },
                    ],
                  })(
                    <Select
                      placeholder="请选择"
                      getPopupContainer={(triggerNode) => triggerNode.parentElement}
                      disabled={disabled}
                    >
                      {[
                        {
                          key: 1,
                          label: '是',
                        },
                        {
                          key: 0,
                          label: '否',
                        },
                      ]?.map((ele) => (
                        <Select.Option value={ele.key} key={ele.key}>
                          {ele.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="处置时间">
                  {getFieldDecorator('disposeTime', {
                    initialValue: currentRow?.disposeTime
                      ? moment(currentRow?.disposeTime)
                      : undefined,
                    rules: [
                      {
                        required: getFieldValue('disposeResult') === 1,
                        message: '请选择处置时间',
                      },
                    ],
                  })(
                    <DatePicker
                      format={'YYYY-MM-DD HH:mm:ss'}
                      showTime={true}
                      allowClear
                      placeholder="请选择"
                      style={{ width: '100%' }}
                      disabled={disabled}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="处置方式">
                  {getFieldDecorator('disposeInfo', {
                    initialValue: currentRow?.disposeInfo,
                    rules: [
                      {
                        required: getFieldValue('disposeResult') === 1,
                        message: '请输入处置方式',
                      },
                      {
                        max: 100,
                        message: '最多输入100个字符',
                      },
                    ],
                  })(<Input placeholder="请输入" allowClear disabled={disabled} />)}
                </Form.Item>
              </Col>
            </Row>

            <DynamicConfig
              currentRow={currentRow}
              wrappedComponentRef={dynamicConfigRef}
              visible={visible}
              disabled={disabled}
              firstTypeList={firstTypeList}
            />
          </Form>
        </div>
      </Modal>
    </Fragment>
  );
};
export default memo(Form.create()(ConfigEdit));
