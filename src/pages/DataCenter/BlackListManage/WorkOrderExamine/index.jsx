import React, { Fragment, useEffect, useState, useRef } from 'react';
import { Button, Card, Table, message } from 'antd';
import { connect } from 'dryad';

import { VTComponents } from 'virtualizedtableforantd';

import StandardTable from '@/components/VirtualStandardTable';
import ExportApprove from '@/components/ExportApprove';
import FileContent from '@/components/FileContent';
import SearchFrom from './SearchFrom';
import Approve from './Approve';

import { Licensee } from 'ponshine';

import { renderToolTip, getReloadTableWithFilterPageNum } from '@/utils/utils';

const listType = [
  { label: '黑名单', value: 2 },
  { label: '灰名单', value: 3 },
];

import { getCustomerInfoList, getSearchItemList } from '../../common';

const index = (props) => {
  const {
    blackWorkOrderExamine: { tableData, searchParams },
    tableLoading,
    dispatch,
  } = props;

  const formRef = useRef(null);
  // const [searchParams, setSearchParams] = useState({});
  const [selectedRows, setSelectedRows] = useState([]);
  const [changeTypeList, setChangeTypeList] = useState([]);
  const [approveVisible, setApproveVisible] = useState(false);

  const columns = [
    {
      title: '证件号',
      dataIndex: 'idCardNum',
      width: 170,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatus',

      // width: 80,
      ellipsis: true,
    },
    // {
    //   title: '客户名称',
    //   dataIndex: 'userName',

    //   // width: 60,
    //   ellipsis: true,
    // },
    {
      title: '变更类型',
      dataIndex: 'operateType',

      // width: 60,
      ellipsis: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireTime',

      width: 160,
      ellipsis: true,
    },
    {
      title: '名单类型',
      dataIndex: 'listType',

      // width: 60,
      ellipsis: true,
      render: (v) => {
        const str = listType?.find((ele) => ele.value == v)?.label;
        return renderToolTip(str);
      },
    },

    {
      title: '客户标签',
      dataIndex: 'userTag',

      // width: 60,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'userSubTag',

      // width: 70,
      ellipsis: true,
    },

    {
      title: '原因',
      dataIndex: 'blackReason',

      // width: 100,
      ellipsis: true,
    },
    {
      title: '附件',
      dataIndex: 'attachments',

      // width: 70,
      render: (v, r) => (
        <FileContent data={r.fileList} downLoadApi="/api/hn/blackAndGrayList/downDeleteBlackFile" />
      ),
    },

    {
      title: '录入人',
      dataIndex: 'creator',

      // width: 60,
      ellipsis: true,
    },

    {
      title: '操作时间',
      dataIndex: 'gmtCreate',

      width: 160,
      ellipsis: true,
    },

    {
      title: '工单号',
      dataIndex: 'recordNum',

      width: 150,
      ellipsis: true,
    },
  ];

  const findTableDataPager = ({ currentPage = 1, pageSize = 200, ...props } = {}) => {
    dispatch({
      type: 'blackWorkOrderExamine/findTableData',
      payload: {
        ...props,
        currentPage,
        pageSize,
      },
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
  };
  useEffect(() => {
    getSearchItemList('changeType', (data) => {
      setChangeTypeList(data);
    });
    findTableDataPager();
  }, []);

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({
      ...searchParams,
      currentPage: current,
      pageSize,
    });
  };
  const handleSearch = () => {
    const {
      form: { getFieldsValue },
    } = formRef.current;
    const formValue = getFieldsValue();
    const { applyTime } = formValue;
    findTableDataPager({
      ...formValue,
      gmtOperateStart: applyTime?.[0]?.format('YYYYMMDD000000'),
      gmtOperateEnd: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
  };

  const handleClearSearch = () => {
    const {
      clearCustomerSubTagList,
      form: { resetFields, getFieldsValue },
    } = formRef.current;
    resetFields();
    findTableDataPager();
    clearCustomerSubTagList();
    setSelectedRows([]);
  };

  const handleExamine = () => {
    setApproveVisible(true);
  };

  // 携带上次条件刷新
  const reloadTableWithFilter = () => {
    const {
      list,
      pagination: { total, pageSize, current },
    } = tableData;

    findTableDataPager({
      ...searchParams,
      // 如果是最后一页，且只有一条数据，未避免查出来总数和当前请求页对不上，改为请求当前页的上一页
      // 如果是第一页，则还是请求第一页
      currentPage: getReloadTableWithFilterPageNum({
        total,
        pageSize,
        current,
        listLength: list.length,
        selectedRowsLength: selectedRows?.length,
      }),
    });
    setSelectedRows([]);
  };

  const confirmApprove = (formValue, callback) => {
    dispatch({
      type: 'blackWorkOrderExamine/blackListApprove',
      payload: {
        ...formValue,
        idList: selectedRows?.map((ele) => ele.id),
      },
      callback: (res) => {
        callback && callback();
        if (res.code === 200) {
          message.success(res.message);
          setApproveVisible(false);
          reloadTableWithFilter();
        } else {
          message.error(res.message);
        }
      },
    });
  };
  const onSelectChange = (_selectRows) => {
    setSelectedRows(_selectRows);
  };

  return (
    <div>
      <Card>
        <SearchFrom
          wrappedComponentRef={formRef}
          change={getCustomerInfoList}
          changeTypeList={changeTypeList}
          listType={listType}
        >
          <div style={{ display: 'flex', justifyContent: 'end', marginBottom: 24 }}>
            <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
              查询
            </Button>
            <Button style={{ marginRight: 16 }} onClick={handleClearSearch}>
              重置
            </Button>
            <Licensee license="blackApproval_export">
              <ExportApprove
                exportParams={{
                  urlAPi: '/api/hn/blackAndGrayList/exportBlackApproval',
                  title: '黑灰名单工单审批',
                  //接口文档：入参和分页查询一样，一定要传分页参数，传了就行，数值随便
                  params: { currentPage: 1, pageSize: 99999, ...searchParams },
                  method: 'POST',
                  mime: 'xlsx',
                  isDate: true,
                  currentDateFormate: 'YYYYMMDD',
                }}
                moduleTile="黑灰名单管理"
                // 是否校验商业秘密电子文件相关
                isVerifyhEncryption={true}
                disabledExport={!tableData?.list?.length}
              />
            </Licensee>
            <Licensee license="blackApproval_approve">
              <Button
                style={{ marginLeft: 16 }}
                onClick={handleExamine}
                type="primary"
                disabled={!selectedRows?.length}
              >
                审批
              </Button>
            </Licensee>
          </div>
        </SearchFrom>
        <StandardTable
          // showSelectCount={false}
          // rowSelectionProps={false}
          columns={columns}
          data={tableData}
          onChange={handleTableChange}
          rowKey="id"
          loading={tableLoading}
          onSelectRow={onSelectChange}
          selectedRows={selectedRows}
          scroll={{ y: 600 }}
          components={VTComponents({
            id: 1000,
            /* 可能的配置选项 */
            // 使用虚拟化组件
          })}

          // isNeedAutoWidth={true}
        />
        {approveVisible && (
          <Approve
            visible={approveVisible}
            confirm={confirmApprove}
            cancel={() => {
              setApproveVisible(false);
            }}
          />
        )}
      </Card>
    </div>
  );
};

export default connect(({ blackWorkOrderExamine, loading }) => ({
  blackWorkOrderExamine,
  tableLoading: loading.effects['blackWorkOrderExamine/findTableData'],
}))(index);
