/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-22 13:57:59
 * @LastEditors: zhao<PERSON><PERSON>en
 * @LastEditTime: 2022-09-23 15:01:21
 * @FilePath: \hunanfanzha\src\pages\DataCenter\WhiteListManage\WorkOrderExamine\Approve.jsx
 * @Description:
 */
import { Modal, Form, Select, Input } from 'antd';
import React, { useState } from 'react';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const approveType = [
  { value: 1, label: '审批通过' },
  { value: 2, label: '审批驳回' },
];

const Approve = Form.create()(
  ({
    visible,
    cancel,
    confirm,
    form: { validateFields, getFieldDecorator, resetFields, setFieldsValue },
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const handleOk = () => {
      validateFields((err, fieldsValue) => {
        if (!err) {
          setConfirmLoading(true);
          confirm({ ...fieldsValue }, () => {
            setConfirmLoading(false);
          });
        }
      });
    };

    const handleChange = (value) => {
      if (!value) return;
      setFieldsValue({ approvalRemark: value });
    };

    return (
      <Modal
        title="黑灰名单审批"
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
      >
        <Form {...formItemLayout}>
          <Form.Item label="审批结果">
            {getFieldDecorator('approvalResult', {
              rules: [
                {
                  required: true,
                  message: '请选择审批结果',
                },
              ],
            })(
              <Select placeholder="请选择" onChange={handleChange}>
                {['审批通过', '审批驳回'].map((ele, index) => (
                  <Select.Option value={ele} key={index}>
                    {ele}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="审批意见">
            {getFieldDecorator('approvalRemark', {
              rules: [
                {
                  required: true,
                  message: '请输入审批意见',
                },
                {
                  max: 200,
                  message: '最多输入200个字符',
                },
              ],
            })(<TextArea placeholder="请输入" allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default Approve;
