/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-16 15:08:38
 * @LastEditors: zxw
 * @LastEditTime: 2023-07-04 14:35:14
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\BlackListManage\WorkOrderManage\SearchFrom.jsx
 * @Description:
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Form, Row, Col, Input, Select, Button, DatePicker } from 'antd';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const SearchFrom = Form.create()(
  forwardRef((props, ref) => {
    const {
      form,
      form: { getFieldDecorator, setFieldsValue, getFieldValue },
      change,
      listType,
      changeTypeList,
      children,
      approveStateList,
    } = props;

    const [customerTagList, setCustomerTagList] = useState([]);

    const [customerSubTagList, setCustomerSubTagList] = useState([]);

    useImperativeHandle(ref, () => ({
      form: form,
      clearCustomerSubTagList: () => {
        setCustomerSubTagList([]);
        setCustomerTagList([]);
      },
    }));

    const handleChangeType = (value) => {
      setFieldsValue({ userTag: undefined, userSubTag: undefined });

      if (!value) {
        setCustomerSubTagList([]);
        setCustomerTagList([]);
        return;
      }
      change({
        rosterType: value,
        type: 2,
        callback: (data) => {
          setCustomerTagList(data);
        },
      });
    };

    const handleChange = (value) => {
      setFieldsValue({ userSubTag: undefined });

      if (!value) {
        return setCustomerSubTagList([]);
      }
      change({
        rosterType: getFieldValue('listType'),
        rosterTge: value,
        type: 1,
        callback: (data) => {
          setCustomerSubTagList(data);
        },
      });
    };
    return (
      <Form {...formItemLayout} autoComplete="off">
        <Row gutter={[24]}>
          <Col span={6}>
            <FormItem label="证件号">
              {getFieldDecorator('idCardNum')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态">
              {getFieldDecorator('approvalStatus')(
                <Select placeholder="请选择" allowClear>
                  {approveStateList?.map((ele) => (
                    <Select.Option value={ele} key={ele}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="名单类型">
              {getFieldDecorator('listType')(
                <Select placeholder="请选择" onChange={handleChangeType} allowClear>
                  {listType?.map((ele) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="客户标签">
              {getFieldDecorator('userTag')(
                <Select placeholder="请选择" onChange={handleChange} allowClear>
                  {customerTagList?.map((ele) => (
                    <Select.Option value={ele}>{ele}</Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="客户子标签">
              {getFieldDecorator('userSubTag')(
                <Select placeholder="请选择" allowClear>
                  {customerSubTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="工单号">
              {getFieldDecorator('recordNum')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="操作时间">
              {getFieldDecorator('applyTime')(
                <RangePicker
                  allowClear
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="变更类型">
              {getFieldDecorator('operateType')(
                <Select placeholder="请选择" allowClear>
                  {changeTypeList?.map((ele, index) => (
                    <Select.Option value={ele.name} key={index}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="客户名称">
              {getFieldDecorator('userName')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="录入人">
              {getFieldDecorator('operator')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={12}>{children}</Col>
        </Row>
      </Form>
    );
  }),
);

export default SearchFrom;
