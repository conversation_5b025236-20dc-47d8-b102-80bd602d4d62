/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:26:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-10-18 14:46:55
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\BlackListManage\WorkOrderManage\index.jsx
 * @Description:
 */
import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import SearchFrom from './SearchFrom';

import { connect } from 'dryad';
import { Card, Button, message, Modal, Tooltip } from 'antd';
import { exportFile } from '@/utils/utils';
import {
  getCustomerInfoList,
  getCorrelationModelList,
  getNumberAttributionList,
  getSearchItemList,
} from '../../common';
import { Licensee, useLicensee } from 'ponshine';
import ExportApprove from '@/components/ExportApprove';
import FileContent from '@/components/FileContent';

const listType = [
  { label: '黑名单', value: 2 },
  { label: '灰名单', value: 3 },
];

const approveStateList = ['待审批', '审批通过', '审批驳回', '无需审批'];

const WorkOrderManage = (props) => {
  const {
    blackListWorkOrderManage: { tableData },
    tableLoading,
    dispatch,
  } = props;
  const formRef = useRef(null);

  const columns = [
    {
      title: '证件号',
      dataIndex: 'idCardNum',
      align: 'center',

      width: 160,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatus',
      align: 'center',
      width: 70,
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'userName',
      align: 'center',

      width: 80,
      ellipsis: true,
    },
    {
      title: '变更类型',
      dataIndex: 'operateType',
      align: 'center',

      width: 90,
      ellipsis: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireTime',
      align: 'center',
      width: 160,
      ellipsis: true,
    },
    {
      title: '名单类型',
      dataIndex: 'listType',
      align: 'center',
      width: 90,
      ellipsis: true,
    },

    {
      title: '客户标签',
      dataIndex: 'userTag',
      align: 'center',

      width: 100,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'userSubTag',
      align: 'center',

      width: 100,
      ellipsis: true,
    },

    {
      title: '原因',
      dataIndex: 'blackReason',
      align: 'center',

      width: 110,
      ellipsis: true,
    },
    {
      title: '附件',
      dataIndex: 'attachments',
      align: 'center',
      width: 70,
      render: (v, r) => (
        <FileContent data={r.fileList} downLoadApi="/api/hn/blackAndGrayList/downDeleteBlackFile" />
      ),
    },

    {
      title: '录入人',
      dataIndex: 'operator',
      align: 'center',

      width: 80,
      ellipsis: true,
    },

    {
      title: '操作时间',
      dataIndex: 'opreateTime',
      align: 'center',
      width: 160,
      ellipsis: true,
    },
    {
      title: '审批人',
      dataIndex: 'approverName',
      align: 'center',
      width: 80,
      ellipsis: true,
    },
    {
      title: '审批时间',
      dataIndex: 'approvalTime',
      align: 'center',
      width: 160,
      ellipsis: true,
    },
    {
      title: '审批意见',
      dataIndex: 'approvalRemark',
      align: 'center',
      width: 120,
      ellipsis: true,
    },

    {
      title: '工单号',
      dataIndex: 'recordNum',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
  ];

  const [searchParams, setSearchParams] = useState({});
  const [changeTypeList, setChangeTypeList] = useState([]);

  useEffect(() => {
    getSearchItemList('changeType', (data) => {
      setChangeTypeList(data);
    });

    // findTableDataPager();
  }, []);

  const findTableDataPager = ({ currentPage = 1, pageSize = 10, ...props } = {}) => {
    dispatch({
      type: 'blackListWorkOrderManage/findTableData',
      payload: {
        ...props,
        currentPage,
        pageSize,
      },
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleSearch = () => {
    const {
      form: { getFieldsValue },
    } = formRef.current;
    const formValue = getFieldsValue();
    const { applyTime } = formValue;
    findTableDataPager({
      ...formValue,
      gmtOperateStart: applyTime?.[0]?.format('YYYYMMDD000000'),
      gmtOperateEnd: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
    setSearchParams({
      ...formValue,
      gmtOperateStart: applyTime?.[0]?.format('YYYYMMDD000000'),
      gmtOperateEnd: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({
      currentPage: current,
      pageSize,
      ...searchParams,
    });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/blackAndGrayList/exportOperateRecord',
      title: '黑灰名单操作记录管理',
      //接口文档：入参和分页查询一样，一定要传分页参数，传了就行，数值随便
      params: { currentPage: 1, pageSize: 99999, ...searchParams },
      method: 'POST',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };

  const handleClearSearch = () => {
    const {
      clearCustomerSubTagList,
      form: { resetFields, getFieldsValue },
    } = formRef.current;
    resetFields();
    const formValue = getFieldsValue();
    const { applyTime } = formValue;
    setSearchParams({
      ...formValue,
      gmtOperateStart: applyTime?.[0]?.format('YYYYMMDD000000'),
      gmtOperateEnd: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
    findTableDataPager();
    clearCustomerSubTagList();
  };

  return (
    <Card>
      <Licensee license="blackListWorkOrderManage_pageBlackRecord">
        <SearchFrom
          wrappedComponentRef={formRef}
          change={getCustomerInfoList}
          changeTypeList={changeTypeList}
          listType={listType}
          approveStateList={approveStateList}
        >
          <div style={{ display: 'flex', justifyContent: 'end', marginBottom: 24 }}>
            <Licensee license="blackListWorkOrderManage_pageBlackRecord">
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                查询
              </Button>
              <Button style={{ marginRight: 16 }} onClick={handleClearSearch}>
                重置
              </Button>
            </Licensee>
            <Licensee license="blackListWorkOrderManage_exportOperateRecord">
              {/* <Button type="primary" style={{ marginRight: 16 }} onClick={handleExport}>
            批量导出
          </Button> */}
              <ExportApprove
                exportParams={{
                  urlAPi: '/api/hn/blackAndGrayList/exportOperateRecord',
                  title: '黑灰名单操作记录管理',
                  //接口文档：入参和分页查询一样，一定要传分页参数，传了就行，数值随便
                  params: { currentPage: 1, pageSize: 99999, ...searchParams },
                  method: 'POST',
                  mime: 'xlsx',
                  isDate: true,
                  currentDateFormate: 'YYYYMMDD',
                }}
                moduleTile="黑灰名单管理"
                // 是否校验商业秘密电子文件相关
                isVerifyhEncryption={true}
                disabledExport={!tableData?.list?.length}
              />
            </Licensee>
          </div>
        </SearchFrom>
      </Licensee>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        loading={tableLoading}
        // isNeedAutoWidth={true}
      />
    </Card>
  );
};

export default connect(({ blackListWorkOrderManage, loading }) => ({
  blackListWorkOrderManage,
  tableLoading: loading.effects['blackListWorkOrderManage/findTableData'],
}))(WorkOrderManage);
