/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:26:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-10-18 14:32:31
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\BlackListManage\InfoQuery\index.jsx
 * @Description:
 */
import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import SearchFrom from './SearchFrom';
import AddOrEdit from './AddOrEdit';
import Delete from './Delete';
import { connect } from 'dryad';
import { Card, Button, message, Modal, Tooltip } from 'antd';
import { exportFile } from '@/utils/utils';
import { getCustomerInfoList, getSearchItemList, getCustomerInfoList2 } from '../../common';
import { Licensee, useLicensee } from 'ponshine';

import ExportApprove from '@/components/ExportApprove';

const listType = [
  { label: '黑名单', value: 2 },
  { label: '灰名单', value: 3 },
];

const InfoQuery = (props) => {
  const {
    blackListInfoQuery: { tableData },
    tableLoading,
    dispatch,
  } = props;
  const formRef = useRef(null);

  const columns = [
    {
      title: '证件号',
      dataIndex: 'idCardNum',
      align: 'center',

      width: 170,
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'userName',
      align: 'center',

      width: 80,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'blackStatus',
      align: 'center',

      width: 80,
      ellipsis: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireTime',
      align: 'center',

      width: 180,
      ellipsis: true,
    },
    {
      title: '名单类型',
      dataIndex: 'listType',
      align: 'center',
      width: 80,
      ellipsis: true,
    },
    {
      title: '客户标签',
      dataIndex: 'userTag',
      align: 'center',

      width: 100,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'userSubTag',
      align: 'center',

      width: 100,
      ellipsis: true,
    },

    {
      title: '原因',
      dataIndex: 'blackReason',
      align: 'center',

      width: 100,
      ellipsis: true,
    },
    {
      title: '开始时间',
      dataIndex: 'gmtCreate',
      align: 'center',

      width: 180,
      ellipsis: true,
    },
  ];

  const [selectedRows, setSelectedRows] = useState([]);
  const [visible, setVisible] = useState(false);
  const [deleteVisible, setDeleteVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [statusList, setStatusList] = useState([]);

  useEffect(() => {
    getSearchItemList('state_white', (data) => {
      setStatusList(data);
    });
    // findTableDataPager();
  }, []);

  const findTableDataPager = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    dispatch({
      type: 'blackListInfoQuery/findTableData',
      payload: {
        ...props,
        pageNum,
        pageSize,
      },
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleSearch = () => {
    const {
      form: { getFieldsValue },
    } = formRef.current;
    const formValue = getFieldsValue();
    const { startTime, expireTime } = formValue;
    findTableDataPager({
      ...formValue,
      createTimeBegin: startTime?.[0]?.format('YYYYMMDD000000'),
      createTimeEnd: startTime?.[1]?.format('YYYYMMDD235959'),
      expireTimeBegin: expireTime?.[0]?.format('YYYYMMDD000000'),
      expireTimeEnd: expireTime?.[1]?.format('YYYYMMDD235959'),
      expireTime: undefined,
      startTime: undefined,
    });
    setSelectedRows([]);
    setSearchParams({
      ...formValue,
      createTimeBegin: startTime?.[0]?.format('YYYYMMDD000000'),
      createTimeEnd: startTime?.[1]?.format('YYYYMMDD235959'),
      expireTimeBegin: expireTime?.[0]?.format('YYYYMMDD000000'),
      expireTimeEnd: expireTime?.[1]?.format('YYYYMMDD235959'),
      expireTime: undefined,
      startTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({
      pageNum: current,
      pageSize,
      ...searchParams,
    });
    setSelectedRows([]);
  };

  const handleSelectRows = (selectedRows) => {
    setSelectedRows(selectedRows);
  };

  const handleConfirm = (params, callback) => {
    dispatch({
      type: `blackListInfoQuery/${params?.id ? 'updateBlackInfo' : 'addBlackInfo'}`,
      payload: { ...params, expireTime: params?.time?.format('YYYYMMDDHHmmss'), time: undefined },
      callback: (res) => {
        callback();
        if (res.code === 200) {
          message.success(res.message);
          reload();
          setVisible(false);
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const reload = () => {
    const {
      form: { resetFields },
      clearCustomerSubTagList,
    } = formRef.current;
    resetFields();
    setSearchParams({});
    findTableDataPager();
    setSelectedRows([]);
    clearCustomerSubTagList();
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/blackAndGrayList/exportBlackAngGrayList',
      title: '黑灰名单信息导出',
      params: { pageNum: 1, pageSize: 9999, ...searchParams },
      method: 'POST',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };

  const handleDelete = (formValue, callback) => {
    selectedRows?.map((ele) => {
      formValue.append('idList', ele.id);
    });
    // formValue.append('idList', JSON.stringify(selectedRows?.map((ele) => ele.id)));
    dispatch({
      type: 'blackListInfoQuery/deleteBlackInfo',
      payload: formValue,
      // payload: {
      //   ...formValue,
      //   idList: selectedRows?.map((ele) => ele.id),
      // },
      callback: (res) => {
        callback && callback();
        if (res.code === 200) {
          message.success(res.message);
          setDeleteVisible(false);
          reload();
        } else {
          message.error(res.message);
        }
      },
    });
  };

  return (
    <Card>
      <Licensee license="blackListInfoQuery_pageBlackAndGrayList">
        <SearchFrom
          wrappedComponentRef={formRef}
          change={getCustomerInfoList}
          statusList={statusList}
          listType={listType}
        />
      </Licensee>
      <div style={{ display: 'flex', justifyContent: 'end', marginBottom: 24 }}>
        <Licensee license="blackListInfoQuery_pageBlackAndGrayList">
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
            查询
          </Button>
          <Button style={{ marginRight: 16 }} onClick={reload}>
            重置
          </Button>
        </Licensee>

        <Licensee license="blackListInfoQuery_exportBlackAngGrayList">
          {/* <Button type="primary" style={{ marginRight: 16 }} onClick={handleExport}>
            数据导出
          </Button> */}
          <ExportApprove
            buttonStyle={{ marginRight: 10 }}
            exportParams={{
              urlAPi: '/api/hn/blackAndGrayList/exportBlackAngGrayList',
              title: '黑灰名单信息导出',
              params: { pageNum: 1, pageSize: 9999, ...searchParams },
              method: 'POST',
              mime: 'xlsx',
              isDate: true,
              currentDateFormate: 'YYYYMMDD',
            }}
            moduleTile="黑灰名单管理"
            // 是否校验商业秘密电子文件相关
            isVerifyhEncryption={true}
            disabledExport={!tableData?.list?.length}
          />
        </Licensee>
        <Licensee license="blackListInfoQuery_insertIntoBlackAndGrayList">
          <Button
            type="primary"
            icon="plus"
            style={{ marginRight: 16 }}
            onClick={() => {
              setSelectedRows([]);
              setVisible(true);
            }}
          >
            新建
          </Button>
        </Licensee>
        {/* <Button
          type="primary"
          icon="edit"
          disabled={selectedRows?.length !== 1}
          style={{ marginRight: 16 }}
          onClick={() => {
            setVisible(true);
          }}
        >
          编辑
        </Button> */}
        <Licensee license="blackListInfoQuery_deleteFromBlackAndGrayList">
          <Button
            type="danger"
            icon="delete"
            disabled={selectedRows?.length < 1}
            onClick={() => {
              setDeleteVisible(true);
            }}
          >
            删除
          </Button>
        </Licensee>
      </div>
      <StandardTable
        // showSelectCount={false}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        onSelectRow={handleSelectRows}
        selectedRows={selectedRows}
        loading={tableLoading}
      />
      <AddOrEdit
        visible={visible}
        confimAdd={handleConfirm}
        cancel={() => {
          setVisible(false);
          selectedRows.length && setSelectedRows([]);
        }}
        currentRow={selectedRows?.[0] || {}}
        listType={listType}
        change={getCustomerInfoList2}
      />
      <Delete
        visible={deleteVisible}
        confirm={handleDelete}
        cancel={() => {
          setDeleteVisible(false);
        }}
      />
    </Card>
  );
};

export default connect(({ blackListInfoQuery, loading }) => ({
  blackListInfoQuery,
  tableLoading: loading.effects['blackListInfoQuery/findTableData'],
}))(InfoQuery);
