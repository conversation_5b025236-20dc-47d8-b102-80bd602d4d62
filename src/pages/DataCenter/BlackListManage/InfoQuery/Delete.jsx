/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 17:20:57
 * @LastEditors: zhaoxiaowen
 * @LastEditTime: 2022-09-30 15:22:40
 * @FilePath: \hunanfanzha\src\pages\DataCenter\BlackListManage\InfoQuery\DeleteModal.jsx
 * @Description:
 */
import React, { useState } from 'react';
import { Modal, Form, Input, Upload, Button, Icon, message } from 'antd';
import { connect } from 'dryad';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const normFile = (e) => {
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
};

const DeleteModal = Form.create()(
  ({
    visible,
    form: { validateFields, getFieldDecorator, resetFields, getFieldValue },
    confirm,
    cancel,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const fileType =
      '.csv,.xls,.xlsx,.jpg,.png,.bmp,.jpeg,.rar,.zip,.7z,.doc,.docx,.txt,.pdf,.ppt,.pptx';
    const uploadProps = {
      accept: fileType,
      beforeUpload: (file) => {
        const fileTypeStr = file?.name.substr(file?.name?.lastIndexOf('.'));
        const isTrueType = fileType.includes(fileTypeStr);

        if (!isTrueType) {
          message.error(`请上传${fileType}格式的文件`);
          return Promise.reject();
        }
        const isLt50M = file.size / 1024 / 1024 <= 50;
        if (!isLt50M) {
          message.error('文件大小不能超过50M');
          return Promise.reject();
        }

        if (getFieldValue('uploadFile')?.length >= 1) {
          message.error('最多上传1个文件');
          return Promise.reject();
        }

        return false;
      },
    };
    const handleOk = () => {
      validateFields((err, fieldsValue) => {
        if (!err) {
          const { uploadFile } = fieldsValue;
          const formData = new FormData();
          Object.keys(fieldsValue).forEach((ele) => {
            if (ele !== 'uploadFile') {
              formData.append(`${ele}`, fieldsValue[ele]);
            }
          });
          formData.append('fileArray', uploadFile?.[0]?.originFileObj);
          setConfirmLoading(true);
          confirm(formData, () => {
            setConfirmLoading(false);
          });
        }
      });
    };

    return (
      <Modal
        title="黑灰名单删除"
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        confirmLoading={confirmLoading}
        maskClosable={false}
      >
        <Form {...formItemLayout}>
          <Form.Item label="删除原因">
            {getFieldDecorator('deleteReason', {
              rules: [
                {
                  required: true,
                  message: '请输入原因',
                },
                {
                  max: 100,
                  message: '最多输入100个字符',
                },
              ],
            })(<TextArea placeholder="请输入" />)}
          </Form.Item>
          <Form.Item label="附件上传">
            {getFieldDecorator('uploadFile', {
              valuePropName: 'fileList',
              getValueFromEvent: normFile,
              rules: [
                {
                  required: true,
                  message: '请上传附件',
                },
              ],
            })(
              <Upload {...uploadProps}>
                <Button>
                  <Icon type="upload" /> 上传文件
                </Button>
              </Upload>,
            )}
          </Form.Item>
          <p style={{ color: 'red' }}>
            注:申请黑名单解黑原因请简要阐述清楚(200字以内)同时上传相关附件解除管控函件，提交后自动进入审批流程，审批通过后解除黑名单管控
          </p>
        </Form>
      </Modal>
    );
  },
);

export default DeleteModal;
