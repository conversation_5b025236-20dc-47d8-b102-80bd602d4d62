import { Modal, Form, Input, Select, DatePicker, Row, Col, message } from 'antd';
import React, { useEffect, useState } from 'react';
import moment from 'moment';

import { getCustomerInfoList } from '../../common';

const { TextArea } = Input;

const formItemLayout = { labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const enumRoster = { 白名单: 1, 黑名单: 2, 灰名单: 3 };

const FormItem = Form.Item;
export default Form.create()((props) => {
  const {
    visible,
    form: { getFieldDecorator, validateFields, resetFields, getFieldValue, setFieldsValue },
    currentRow = {},
    confimAdd,
    cancel,
    listType,
    change,
  } = props;

  const [customerTagList, setCustomerTagList] = useState([]);

  const [customerSubTagList, setCustomerSubTagList] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (currentRow?.id) {
        getCustomerInfoList({
          rosterType: enumRoster[currentRow?.listType],
          type: 2,
          callback: (data) => {
            setCustomerTagList(data);
          },
        });
        getCustomerInfoList({
          rosterType: enumRoster[currentRow?.listType],
          rosterTge: currentRow?.userTag,
          type: 2,
          callback: (data) => {
            setCustomerSubTagList(data);
          },
        });
      }
    } else {
      setCustomerTagList([]);
      setCustomerSubTagList([]);
    }
  }, [visible]);

  const handleChangeType = (value) => {
    setFieldsValue({ userTag: undefined, userSubTag: undefined });

    if (!value) {
      setCustomerSubTagList([]);
      setCustomerTagList([]);
      return;
    }
    change({
      rosterType: value,
      type: 2,
      callback: (data) => {
        setCustomerTagList(data);
      },
    });
  };

  const handleChange = (value) => {
    setFieldsValue({ userSubTag: undefined });

    if (!value) {
      return setCustomerSubTagList([]);
    }
    change({
      rosterType: getFieldValue('listType'),
      rosterTge: value,
      type: 2,
      callback: (data) => {
        setCustomerSubTagList(data);
      },
    });
  };

  const handleOk = () => {
    validateFields((err, fieldsValue) => {
      if (!err) {
        setConfirmLoading(true);
        confimAdd({ ...fieldsValue, id: currentRow?.id || undefined }, () => {
          setConfirmLoading(false);
        });
      }
    });
  };

  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };

  const isConstant = (str) => {
    if (str.length === 1) {
      return false;
    }
    const firstChar = str[0];
    for (let i = 1; i < str.length; i++) {
      if (str[i] !== firstChar) {
        return false; // 如果有字符与第一个不同，则不是由相同字符构成的
      }
    }

    return true; // 所有字符都相同
  };

  const validatorIdCardNum = (rule, value, callback) => {
    if (value && isConstant(value)) {
      callback('证件号不能全部重复');
    } else {
      callback();
    }
  };

  return (
    <Modal
      title={`黑灰名单单个${currentRow?.id ? '修改' : '新增'}`}
      width={700}
      visible={visible}
      onOk={handleOk}
      okText={`确认${currentRow?.id ? '修改' : '添加'}`}
      afterClose={() => {
        resetFields();
      }}
      onCancel={cancel}
      confirmLoading={confirmLoading}
      maskClosable={false}
    >
      <Form {...formItemLayout}>
        <Row>
          <Col span={12}>
            <FormItem label="证件号">
              {getFieldDecorator('idCardNum', {
                initialValue: currentRow?.idCardNum,
                rules: [
                  { required: true, message: '请输入证件号' },
                  {
                    pattern: /^[A-Za-z0-9]+$/,
                    message: '证件号不能存在数字、字母之外的字符',
                  },
                  {
                    min: 18,
                    message: '证件号18位',
                  },
                  {
                    max: 18,
                    message: '证件号18位',
                  },
                  {
                    validator: validatorIdCardNum,
                  },
                ],
              })(<Input placeholder="请输入" allowClear disabled={currentRow?.id} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="客户名称">
              {getFieldDecorator('userName', {
                initialValue: currentRow?.userName,
                rules: [{ max: 30, message: '最多输入30个字符' }],
              })(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label="到期时间">
              {getFieldDecorator('time', {
                initialValue: currentRow?.expireTime
                  ? moment(currentRow?.expireTime, 'YYYY-MM-DD')
                  : undefined,
                rules: [{ required: true, message: '请选择到期时间' }],
              })(
                <DatePicker
                  style={{ width: '100%' }}
                  getCalendarContainer={(trigger) => trigger.parentNode}
                  disabledDate={disabledDate}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="名单类型">
              {getFieldDecorator('listType', {
                initialValue: enumRoster[currentRow?.listType],
                rules: [{ required: true, message: '请选择名单类型' }],
              })(
                <Select
                  placeholder="请选择"
                  onChange={handleChangeType}
                  allowClear
                  getPopupContainer={(trigger) => trigger.parentNode}
                >
                  {listType?.map((ele) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="客户标签">
              {getFieldDecorator('userTag', {
                initialValue: currentRow?.userTag,
                rules: [{ required: true, message: '请选择客户标签' }],
              })(
                <Select
                  placeholder="请选择"
                  onChange={handleChange}
                  allowClear
                  getPopupContainer={(trigger) => trigger.parentNode}
                >
                  {customerTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="客户子标签">
              {getFieldDecorator('userSubTag', {
                initialValue: currentRow?.userSubTag,
                // rules: [
                //   {
                //     required: true,
                //     message: '请选择客户子标签',
                //   },
                // ],
              })(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(trigger) => trigger.parentNode}
                >
                  {customerSubTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={24}>
            <FormItem label="原因" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
              {getFieldDecorator('blackReason', {
                initialValue: currentRow?.blackReason,
                rules: [
                  { required: true, message: '请输入原因' },
                  { max: 100, message: '最多输入100个字符' },
                ],
              })(<TextArea placeholder="请输入" />)}
            </FormItem>
          </Col>
        </Row>
        <p style={{ color: 'red' }}>
          注：录入原因必须包含时间、分类（如：友商号码涉案/银行卡涉案等）、案件编码、描述，缺乏关键信息将不予审批通过，样例如:
          20240510友商涉案关联号码155****0408案件编号A50011***********050002
        </p>
      </Form>
    </Modal>
  );
});
