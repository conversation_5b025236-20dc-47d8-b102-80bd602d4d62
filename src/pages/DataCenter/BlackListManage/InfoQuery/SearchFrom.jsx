/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-16 15:08:38
 * @LastEditors: zhaoxiaowen
 * @LastEditTime: 2022-09-30 15:58:32
 * @FilePath: \hunanfanzha\src\pages\DataCenter\BlackListManage\InfoQuery\SearchFrom.jsx
 * @Description:
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Form, Row, Col, Input, Select, Button, DatePicker } from 'antd';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const SearchFrom = Form.create()(
  forwardRef((props, ref) => {
    const {
      form,
      form: { getFieldDecorator, getFieldValue, setFieldsValue },
      change,
      statusList,
      listType,
    } = props;

    const [numberTypeList, setNumberTypeList] = useState([]);
    const [customerTagList, setCustomerTagList] = useState([]);
    const [customerSubTagList, setCustomerSubTagList] = useState([]);

    useImperativeHandle(ref, () => ({
      form: form,
      clearCustomerSubTagList: () => {
        setCustomerSubTagList([]);
        setCustomerTagList([]);
      },
    }));

    const handleChangeType = (value) => {
      setFieldsValue({ userTag: undefined, userSubTag: undefined });
      if (!value) {
        setCustomerSubTagList([]);
        setCustomerTagList([]);
        return;
      }
      change({
        rosterType: value,
        type: 2,
        callback: (data) => {
          setCustomerTagList(data);
        },
      });
    };

    const handleChange = (value) => {
      setFieldsValue({ userSubTag: undefined });

      if (!value) {
        return setCustomerSubTagList([]);
      }
      change({
        rosterType: getFieldValue('listType'),
        rosterTge: value,
        type: 2,
        callback: (data) => {
          setCustomerSubTagList(data);
        },
      });
    };
    return (
      <Form {...formItemLayout} autoComplete="off">
        <Row gutter={[24]}>
          <Col span={6}>
            <FormItem label="证件号">
              {getFieldDecorator('idCardNum')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态">
              {getFieldDecorator('blackStatus')(
                <Select placeholder="请选择" allowClear>
                  {statusList.map((ele) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="名单类型">
              {getFieldDecorator('listType')(
                <Select placeholder="请选择" onChange={handleChangeType} allowClear>
                  {listType?.map((ele) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="客户标签">
              {getFieldDecorator('userTag')(
                <Select placeholder="请选择" onChange={handleChange} allowClear>
                  {customerTagList?.map((ele) => (
                    <Select.Option value={ele}>{ele}</Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="客户子标签">
              {getFieldDecorator('userSubTag')(
                <Select placeholder="请选择" allowClear>
                  {customerSubTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="开始时间">
              {getFieldDecorator('startTime')(
                <RangePicker
                  allowClear
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="到期时间">
              {getFieldDecorator('expireTime')(
                <RangePicker
                  allowClear
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="客户名称">
              {getFieldDecorator('userName')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }),
);

export default SearchFrom;
