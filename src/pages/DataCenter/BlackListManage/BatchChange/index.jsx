/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:26:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-07-04 15:41:00
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\BlackListManage\BatchChange\index.jsx
 * @Description:
 */
import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import { withContext } from 'demasia-pro-layout';

import { connect } from 'dryad';
import { Card, Button, message, Modal, Upload, Icon, Tooltip } from 'antd';
import { exportFile } from '@/utils/utils';

import { batchUploadFile } from '@/services/DataCenter/BlackListManage/BatchChange';

import request from '@/utils/request';
import { Licensee, useLicensee } from 'ponshine';

const changeTypeList = [
  { value: 1, label: '新增' },
  { value: 2, label: '删除' },
];

const BatchChange = (props) => {
  const {
    blackListBatchChange: { tableData },
    tableLoading,
    dispatch,
    submitLoading,
  } = props;

  const formRef = useRef(null);

  const columns = [
    {
      title: '证件号',
      dataIndex: 'idCardNum',
      align: 'center',

      width: 130,
      ellipsis: true,
     
    },
    {
      title: '客户名称',
      dataIndex: 'userName',
      align: 'center',

      width: 70,
      ellipsis: true,
     
    },
    {
      title: '变更类型',
      dataIndex: 'operateType',
      align: 'center',

      width: 70,
      ellipsis: true,
     
    },
    {
      title: '到期时间',
      dataIndex: 'expireTime',
      align: 'center',

      width: 120,
      ellipsis: true,
     
    },

    {
      title: '名单类型',
      dataIndex: 'listType',
      align: 'center',
     

      width: 70,
      ellipsis: true,
    },

    {
      title: '客户标签',
      dataIndex: 'userTag',
      align: 'center',
     
      width: 70,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'userSubTag',
      align: 'center',
     
      width: 80,
      ellipsis: true,
    },

    {
      title: '原因',
      dataIndex: 'blackReason',
      align: 'center',
     
      width: 70,
      ellipsis: true,
    },

    {
      title: '申请人',
      dataIndex: 'creatorName',
      align: 'center',
     
      width: 70,
      ellipsis: true,
    },

    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      align: 'center',
     
      width: 120,
      ellipsis: true,
    },
  ];

  const [errorfileVisible, setErrorfileVisible] = useState(false);
  const [fileName, setFileName] = useState('');
  const [errorTip, setErrorTip] = useState('');

  useEffect(() => {
    setFileName('');
    dispatch({
      type: 'blackListBatchChange/clearTableData',
    });
  }, []);

  const findTableDataPager = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    dispatch({
      type: 'blackListBatchChange/findTableData',
      payload: {
        ...props,
        pageNum,
        pageSize,
      },
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({ pageNum: current, pageSize });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/template/getTemplate',
      title: '黑灰名单导入模板',
      params: { templateCode: 'blackAndGrayList' },
      method: 'GET',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };

  // 导出错误报告
  const handleDownloadErr = () => {
    setErrorfileVisible(false);
    exportFile({
      urlAPi: '/api/hn/blackAndGrayList/exportBatchBlackErrorFile',
      title: '黑灰名单批量变更导入错误原因',
      // params: { filePath: errFilePath },
      method: 'GET',
      mime: 'xlsx',
    });
  };

  const handleSubmit = () => {
    dispatch({
      type: 'blackListBatchChange/submitFileList',
      callback: (res) => {
        if (res.code === 200) {
          message.success(res.message);
          setFileName('');
          dispatch({
            type: 'blackListBatchChange/clearTableData',
          });
        } else {
          message.error(res.message);
        }
      },
    });
  };

  // 上传文件
  const upLoadFile = async (formData, fileName) => {
    await batchUploadFile(formData).then((res) => {
      if (res.code === 401) {
        setErrorfileVisible(true);
        setErrorTip(res.message);
        setFileName(fileName);
        findTableDataPager();
      } else if (res.code === 200) {
        message.success(res.message);
        findTableDataPager();
        setFileName(fileName);
      } else {
        message.error(res.message);
      }
    });
  };

  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Licensee license="blackListBatchChange_parseBlackAndGrayExcelData">
            <Upload
              accept=".xlsx"
              showUploadList={false}
              customRequest={(info) => {
                const formData = new FormData();
                formData.append('file', info.file);
                // 手动上传
                upLoadFile(formData, info.file?.name);
              }}
            >
              <Button type="primary" style={{ marginRight: 16 }}>
                模板导入
              </Button>
              {fileName || '  未选择任何文件'}
            </Upload>
          </Licensee>
          {/* <Licensee license="whiteListWorkOrderManage_exportWhiteWorkOrderManage"> */}

          <a style={{ marginLeft: 24 }} onClick={handleExport}>
            黑灰名单导入模板下载
          </a>
          {/* </Licensee> */}
        </div>
        <Licensee license="blackListBatchChange_submitBlackAndGrayList">
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={submitLoading}
            disabled={!tableData?.list?.length}
          >
            提交
          </Button>
        </Licensee>
      </div>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        loading={!!tableLoading}
      />
      {/* 下载错误报告 errorfilepath */}
      <Modal
        title="异常提示"
        visible={errorfileVisible}
        width={800}
        maskClosable={false}
        onCancel={() => {
          // setErrorfilepathVisible(false);
          setErrorfileVisible(false);
        }}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        afterClose={() => {
          // setErrFile('');
        }}
        onOk={handleDownloadErr}
      >
        <span>
          <Icon
            type="exclamation-circle"
            style={{ marginRight: 5, color: 'orange', fontWeight: 'bolder' }}
          />
          {errorTip}
        </span>
      </Modal>
    </Card>
  );
};

export default connect(({ blackListBatchChange, loading }) => ({
  blackListBatchChange,
  tableLoading: loading.effects['blackListBatchChange/findTableData'],
  submitLoading: loading.effects['blackListBatchChange/submitFileList'],
}))(BatchChange);
