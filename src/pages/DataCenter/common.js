/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 18:07:30
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-15 14:27:32
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\common.js
 * @Description:
 */

import {
  getCustomerTags,
  getNumberAttribution,
  getCorrelationModel,
  getSearchList,
  getGrayBlackTagByUser,
} from '@/services/DataCenter/common';
import { message } from 'antd';

/**
 *
 * @param rosterType 名单类型
 * @param rosterTge 客户标签
 * @param rosterSubTab 客户子标签
 * @param type 标签使用场景
 */
export const getCustomerInfoList = ({
  rosterType = undefined,
  rosterTge = undefined,
  type,
  callback,
}) => {
  getCustomerTags({
    rosterType,
    rosterTge,
    type,
  })
    .then((res) => {
      if (res.code === 200) {
        callback && callback(res.data || []);
      } else {
        callback([]);
        message.error(res.message);
      }
    })
    .catch((err) => {});
};
export const getCustomerInfoList2 = ({
  rosterType = undefined,
  rosterTge = undefined,
  type,
  callback,
}) => {
  getGrayBlackTagByUser({
    rosterType,
    rosterTge,
    type,
  })
    .then((res) => {
      if (res.code === 200) {
        callback && callback(res.data || []);
      } else {
        callback([]);
        message.error(res.message);
      }
    })
    .catch((err) => {});
};
export const getCorrelationModelList = (callback) => {
  getCorrelationModel()
    .then((res) => {
      if (res.code === 200) {
        callback(res.data);
      } else {
        callback([]);
        message.error(res.message);
      }
    })
    .catch((err) => {});
};

export const getNumberAttributionList = (callback) => {
  getNumberAttribution()
    .then((res) => {
      if (res.code === 200) {
        callback(res.data);
      } else {
        callback([]);
        message.error(res.message);
      }
    })
    .catch((err) => {});
};

export const getSearchItemList = (type, callback) => {
  getSearchList({ configType: type })
    .then((res) => {
      if (res.code === 200) {
        callback(res.data);
      } else {
        callback([]);
        message.error(res.message);
      }
    })
    .catch((err) => {});
};
