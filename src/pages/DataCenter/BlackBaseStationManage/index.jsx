import React, { Fragment, useEffect, useState, useRef } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col } from 'antd';

import StandardTable from '@/components/StandardTable';
import AddModal from './AddModal';

const { RangePicker } = DatePicker;

import { Licensee } from 'ponshine';
import request from 'ponshine-request';
import { exportFile } from '@/utils/utils';

const Index = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator },
  } = props;

  const [selectedRows, setSelectedRows] = useState([]);
  const [addVisible, setAddVisible] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});

  const columns = [
    {
      title: '基站ID',
      dataIndex: 'relatedEnbId',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '所属省',
      dataIndex: 'prov',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '地市',
      dataIndex: 'regionName',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '区县',
      dataIndex: 'cityName',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
    {
      title: '基站名',
      dataIndex: 'relatedEnbUserlabel',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '加黑原因',
      dataIndex: 'blackReason',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '加黑时间',
      dataIndex: 'blackTime',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '累计诈骗号码数（个）',
      dataIndex: 'totalFraudPhoneCount',
      align: 'center',
      width: 170,
      ellipsis: true,
    },
    {
      title: '近30天内诈骗号码数（个）',
      dataIndex: 'thirdDaysFraudPhoneCount',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '录入来源',
      dataIndex: 'creator',
      align: 'center',
      width: 100,
      ellipsis: true,
    },
  ];

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/blackStation/pageBlackStation', {
      data: { pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };
  useEffect(() => {
    findTableDataPager();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { optTime } = formValue;
    findTableDataPager({
      ...formValue,
      startDate: optTime?.[0]?.format('YYYY-MM-DD'),
      endDate: optTime?.[1]?.format('YYYY-MM-DD'),
      optTime: undefined,
    });
    setSelectedRows([]);
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
    setSelectedRows([]);
  };

  const handleAdd = () => {
    setAddVisible(true);
  };

  const onSelectChange = (_selectRows) => {
    setSelectedRows(_selectRows);
  };

  const handleDelete = () => {
    if (!selectedRows?.length) {
      return message.warning('请选择数据后删除');
    }
    if (selectedRows?.length > 1) {
      return message.warning('一次只能删除一个基站');
    }

    Modal.confirm({
      title: '提示',
      content: '是否删除该基站',
      onOk: async () => {
        const response = await request('/api/hn/blackStation/delBlackStation', {
          data: { id: selectedRows?.map((ele) => ele.id)?.join() },
          method: 'POST',
          requestType: 'json',
        });
        if (response.code === 200) {
          message.success(response.message);
          handleReset();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/blackStation/exportBlackStation',
      decode: true,
      params: serachParams,
      method: 'POST',
    });
  };

  return (
    <Card>
      <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
        <Row gutter={[24]}>
          <Col span={8}>
            <Form.Item label="基站ID">
              {getFieldDecorator('relatedEnbId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="基站名称">
              {getFieldDecorator('relatedEnbUserlabel')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="日期">
              {getFieldDecorator('optTime')(
                <RangePicker allowClear format="YYYY-MM-DD" style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'right' }}>
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                查询
              </Button>
              <Button style={{ marginRight: 16 }} onClick={handleReset}>
                重置
              </Button>
              <Licensee license="black_base_export">
                <Button type="primary" style={{ marginRight: 16 }} onClick={handleExport}>
                  导出
                </Button>
              </Licensee>

              <Licensee license="black_base_add">
                <Button type="primary" style={{ marginRight: 16 }} onClick={handleAdd}>
                  新建
                </Button>
              </Licensee>
              <Licensee license="black_base_del">
                <Button type="danger" onClick={handleDelete} disabled={!selectedRows?.length}>
                  删除
                </Button>
              </Licensee>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <StandardTable
        // showSelectCount={false}
        // rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
        onSelectRow={onSelectChange}
        selectedRows={selectedRows}
        // otherRowSelectionProps={{
        //   columnWidth: '5%',
        // }}
        scroll={{ x: 1000 }}
      />
      {addVisible && (
        <AddModal
          visible={addVisible}
          onReload={handleReset}
          cancel={() => {
            setAddVisible(false);
          }}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
