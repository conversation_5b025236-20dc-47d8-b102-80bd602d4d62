import { Modal, Form, Input, message } from 'antd';
import React, { useState } from 'react';
import request from 'ponshine-request';

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};

const AddModal = Form.create()(
  ({
    visible,
    cancel,
    form: { validateFields, getFieldDecorator, resetFields },
    onReload,
    editRow,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const handleOk = () => {
      validateFields(async (err, fieldsValue) => {
        if (err) return;
        setConfirmLoading(true);
        const response = await request('/api/hn/blackStation/addBlackStation', {
          data: fieldsValue,
          method: 'POST',
          requestType: 'json',
        });
        setConfirmLoading(false);
        if (response.code === 200) {
          message.success(response.message);
          cancel();
          onReload && onReload();
        } else {
          message.error(response.message);
        }
      });
    };

    return (
      <Modal
        title={editRow?.title || '新增黑基站'}
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
      >
        <Form {...formItemLayout}>
          <Form.Item label="基站ID">
            {getFieldDecorator('relatedEnbId', {
              rules: [
                {
                  required: true,
                  message: '请输入基站ID',
                },
              ],
              initialValue: editRow?.relatedEnbId,
            })(<Input placeholder="请输入" allowClear disabled={!!editRow?.relatedEnbId} />)}
          </Form.Item>
          <Form.Item label="加黑原因">
            {getFieldDecorator('blackReason', {
              rules: [
                {
                  required: true,
                  message: '请输入加黑原因',
                },
                {
                  max: 200,
                  message: '最多输入200个字符',
                },
              ],
            })(<Input placeholder="请输入" allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default AddModal;
