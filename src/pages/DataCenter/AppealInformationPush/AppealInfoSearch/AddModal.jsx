import React, { Fragment, useEffect, useRef, useState, memo } from 'react';
import { Form, Modal, Select, Row, Col, message, Input, Button, Upload, Icon } from 'antd';
import { addAppealInfo, updateAppealInfo } from './services';
import { difference } from 'lodash';
import { transforObj2Array, exportFile } from '@/utils/utils';
import styles from './index.less';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 14,
  },
};

const ConfigEdit = ({
  visible,
  form,
  form: {
    getFieldValue,
    getFieldDecorator,
    setFieldsValue,
    getFieldsValue,
    validateFields,
    resetFields,
  },
  cancel,
  reload,
  currentRow,
  appealTypeList,
  allAppealKeyWordsTypeList,
  appealSourceList,
}) => {
  const selectProps = {
    placeholder: '请选择',
    getPopupContainer: (triggerNode) => triggerNode.parentElement,
    disabled: currentRow.modalType === '查看',
  };

  const [appealKeyWordsTypeList, setAppealKeyWordsTypeList] = useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);

  const defaultList = currentRow?.fileList?.map((ele) => {
    return {
      uid: ele.id,
      id: ele.id,
      name: ele.fileName,
      status: 'done',
      // url: ele.filePath,
    };
  });

  useEffect(() => {
    if (visible && currentRow?.id) {
      const currentObj = allAppealKeyWordsTypeList[currentRow.appealType];
      setAppealKeyWordsTypeList(transforObj2Array(currentObj));
    }
  }, [visible]);

  const uploadProps = {
    disabled: currentRow?.modalType === '查看',
    accept: '.zip',
    beforeUpload: (file) => {
      const fileType = file.name.split('.')[1];
      const isTrueFileType = ['zip'].includes(fileType);
      if (!isTrueFileType) {
        message.error('请上传zip格式的文件');
        return Promise.reject();
      }
      const isLt50M = file.size / 1024 / 1024 <= 50;
      if (!isLt50M) {
        message.error('文件大小不能超过50M');
        return Promise.reject();
      }

      if (getFieldValue('uploadFile')?.length >= 1) {
        message.error('最多上传1个文件');
        return Promise.reject();
      }

      return false;
    },
    showUploadList: {
      showRemoveIcon: currentRow?.modalType !== '查看',
      showDownloadIcon: currentRow?.modalType === '查看',
    },
    onDownload: (file) => {
      exportFile({
        urlAPi: '/api/hn/appealInfo/downloadAppealFile',
        params: {
          fileId: file.id,
        },
        decode: true,
        method: 'GET',
      });
    },
  };
  // 保存
  const handleOk = async () => {
    validateFields(async (err, values) => {
      if (err) return;
      const { uploadFile } = values;

      const formData = new FormData();
      Object.keys(values).forEach((ele) => {
        if (ele !== 'uploadFile') {
          formData.append(`${ele}`, values[ele]);
        }
      });
      // 筛选新增的文件
      uploadFile
        ?.filter((ele) => ele.originFileObj)
        ?.forEach((ele) => {
          formData.append('fileArray', ele.originFileObj);
        });

      // 如果是编辑
      if (currentRow?.id) {
        formData.append('id', currentRow?.id);
        filterDeleteId(uploadFile).forEach((ele) => {
          formData.append('deletedFileId', ele);
        });
      }
      setSubmitLoading(true);
      let response;
      if (currentRow?.id) {
        response = await updateAppealInfo(formData);
      } else {
        response = await addAppealInfo(formData);
      }
      setSubmitLoading(false);

      if (response && response.code === 200) {
        message.success(response.message);
        cancel();
        reload();
      } else {
        message.error(response.message);
      }
    });
  };

  const filterDeleteId = () => {
    const fileList = getFieldValue('uploadFile');
    const deleteId = difference(
      defaultList?.map((ele) => ele.id),
      fileList?.filter((ele) => ele.id)?.map((ele) => ele.id),
    );
    return deleteId;
  };

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const handleChangeType = (v) => {
    setFieldsValue({
      appealKeyWordsType: undefined,
    });
    if (v) {
      const currentObj = allAppealKeyWordsTypeList[v];

      setAppealKeyWordsTypeList(transforObj2Array(currentObj));
    }
  };

  return (
    <Fragment>
      <Modal
        visible={visible}
        title={`${currentRow?.modalType ?? ''}上报`}
        footer={
          currentRow.modalType === '查看' ? null : (
            <div>
              <Button onClick={cancel}>取消</Button>
              <Button onClick={handleOk} loading={submitLoading} type="primary">
                确认上报
              </Button>
            </div>
          )
        }
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        width={'60%'}
      >
        <div>
          <Form {...formItemLayout} className={styles.myForm}>
            <Row>
              <Col span={12}>
                <Form.Item label="申诉来源">
                  {getFieldDecorator('appealSource', {
                    initialValue: currentRow?.appealSource,
                    rules: [
                      {
                        required: true,
                        message: '请选择申诉来源',
                      },
                    ],
                  })(
                    <Select {...selectProps}>
                      {transforObj2Array(appealSourceList)?.map((ele) => (
                        <Select.Option value={ele.value} key={ele.value}>
                          {ele.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="申诉问题类型">
                  {getFieldDecorator('appealType', {
                    initialValue: currentRow?.appealType,
                    rules: [
                      {
                        required: true,
                        message: '请选择申诉问题类型',
                      },
                    ],
                  })(
                    <Select {...selectProps} onChange={handleChangeType}>
                      {appealTypeList?.map((ele) => (
                        <Select.Option value={ele.value} key={ele.value}>
                          {ele.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="申诉信息类型">
                  {getFieldDecorator('appealKeyWordsType', {
                    initialValue: currentRow?.appealKeyWordsType,
                    rules: [
                      {
                        required: true,
                        message: '请选择申诉信息类型',
                      },
                    ],
                  })(
                    <Select {...selectProps}>
                      {appealKeyWordsTypeList?.map((ele) => (
                        <Select.Option value={ele.value} key={ele.value}>
                          {ele.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="关键信息">
                  {getFieldDecorator('appealKeyWords', {
                    initialValue: currentRow?.appealKeyWords,
                    rules: [
                      {
                        required: true,
                        message: '请输入关键信息',
                      },
                      {
                        max: 200,
                        message: '最多输入200个字符',
                      },
                    ],
                  })(
                    <Input
                      placeholder="请输入"
                      allowClear
                      disabled={currentRow.modalType === '查看'}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="申诉人姓名">
                  {getFieldDecorator('questionerName', {
                    initialValue: currentRow?.questionerName,
                    rules: [
                      {
                        required: true,
                        message: '请输入申诉人姓名',
                      },
                      {
                        max: 20,
                        message: '最多输入20个字符',
                      },
                    ],
                  })(
                    <Input
                      placeholder="请输入"
                      allowClear
                      disabled={currentRow.modalType === '查看'}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="联系电话">
                  {getFieldDecorator('questionerPhone', {
                    initialValue: currentRow?.questionerPhone,
                    rules: [
                      {
                        required: true,
                        message: '请输入联系电话',
                      },
                      {
                        pattern: /^[0-9]+$/,
                        message: '只能输入数字',
                      },
                      {
                        max: 20,
                        message: '最多输入20个字符',
                      },
                    ],
                  })(
                    <Input
                      placeholder="请输入"
                      allowClear
                      disabled={currentRow.modalType === '查看'}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="问题描述" wrapperCol={{ span: 19 }} labelCol={{ span: 4 }}>
                  {getFieldDecorator('content', {
                    initialValue: currentRow?.content,
                    rules: [
                      {
                        required: true,
                        message: '请输入问题描述',
                      },
                      {
                        max: 255,
                        message: '最多输入255个字符',
                      },
                    ],
                  })(
                    <TextArea
                      rows={2}
                      placeholder="请输入"
                      allowClear
                      disabled={currentRow.modalType === '查看'}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="证明材料上传">
                  {getFieldDecorator('uploadFile', {
                    valuePropName: 'fileList',
                    getValueFromEvent: normFile,
                    initialValue: defaultList,
                  })(
                    <Upload {...uploadProps}>
                      <Button disabled={currentRow?.modalType === '查看'}>
                        <Icon type="upload" /> 上传文件
                      </Button>
                    </Upload>,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </Fragment>
  );
};
export default memo(Form.create()(ConfigEdit));
