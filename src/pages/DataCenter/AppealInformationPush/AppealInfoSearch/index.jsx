import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Modal,
} from 'antd';

import moment from 'moment';

import StandardTable from '@/components/StandardTable';
import AddModal from './AddModal';
import { selectPage, getDetailById } from './services';
import { transforObj2Array, renderToolTip } from '@/utils/utils';
import {
  getAppealTypeObj,
  getAppealKeyWordsTypeObj,
  orderStateList,
  appealSourceList,
} from '../utils';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [addVisible, setAddVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [currentRow, setCurrentRow] = useState({});
  const [appealTypeObj, setAppealTypeObj] = useState({});
  const [allAppealKeyWordsTypeList, setAllAppealKeyWordsTypeList] = useState({});

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const getAppealTypeList = async () => {
    setAppealTypeObj(await getAppealTypeObj());
  };

  // 获取关键信息类型下拉
  const getAppealKeyWordsTypeList = async () => {
    setAllAppealKeyWordsTypeList(await getAppealKeyWordsTypeObj());
  };

  useEffect(() => {
    getListDatas();
    getAppealTypeList();
    getAppealKeyWordsTypeList();
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    const { appealTime } = values;
    getListDatas({
      ...values,
      appealTime: undefined,
      appealTimeStart: appealTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      appealTimeEnd: appealTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas();
  };

  // 编辑
  const handleEdit = async (r = {}, type) => {
    if (r?.id) {
      const response = await getDetailById({ id: r?.id });
      if (response && response.code === 200) {
        setCurrentRow({
          ...(response?.data || {}),
          modalType: type,
        });
      } else {
        message.error(response.data);
      }
    } else {
      setCurrentRow({
        ...r,
        modalType: type,
      });
    }

    setAddVisible(true);
  };

  let columns = [
    {
      title: '工单ID',
      width: 200,
      dataIndex: 'commandId',
      ellipsis: true,
    },
    {
      title: '顺序号',
      dataIndex: 'seqNumber',
      width: 120,
      ellipsis: true,
    },
    {
      title: '工单状态',
      width: 100,
      dataIndex: 'orderStatus',
      ellipsis: true,
      render: (v) => {
        return renderToolTip(orderStateList[Number(v) - 1]);
      },
    },
    {
      title: '申诉登记时间',
      width: 140,
      dataIndex: 'appealTime',
      ellipsis: true,
    },
    {
      title: '申诉来源',
      width: 100,
      dataIndex: 'appealSource',
      ellipsis: true,
      render: (v) => {
        return renderToolTip(appealSourceList?.[v]);
      },
    },
    {
      title: '申诉问题类型',
      width: 100,
      dataIndex: 'appealType',
      ellipsis: true,
      render: (v) => {
        return renderToolTip(appealTypeObj[v]);
      },
    },
    {
      title: '关键信息类型',
      width: 100,
      dataIndex: 'appealKeyWordsType',
      ellipsis: true,
      render: (v, r) => {
        return renderToolTip(allAppealKeyWordsTypeList?.[r.appealType]?.[v]);
      },
    },
    {
      title: '关键信息',
      width: 120,
      dataIndex: 'appealKeyWords',
      ellipsis: true,
    },
    {
      title: '申诉人姓名',
      width: 120,
      dataIndex: 'questionerName',
      ellipsis: true,
    },
    {
      title: '联系电话',
      width: 120,
      dataIndex: 'questionerPhone',
      ellipsis: true,
    },

    {
      title: '问题描述',
      width: 100,
      dataIndex: 'content',
      ellipsis: true,
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'opt',
      ellipsis: true,
      fixed: 'right',
      render: (v, r) => {
        return (
          <Fragment>
            <Icon
              type="search"
              style={{ color: '#1890ff', marginRight: 8 }}
              onClick={() => handleEdit(r, '查看')}
            />
            {['1', '3']?.includes(r.orderStatus) && (
              <Icon
                type="edit"
                style={{ color: '#1890ff' }}
                onClick={() => handleEdit(r, '编辑')}
              />
            )}
          </Fragment>
        );
      },
    },
  ];

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={7}>
            <Form.Item label="申诉登记时间">
              {getFieldDecorator('appealTime')(
                <RangePicker
                  format={'YYYY-MM-DD'}
                  // disabledDate={disabledDate}
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="申诉问题类型">
              {getFieldDecorator('appealType')(
                <Select placeholder="请选择" allowClear={true}>
                  {transforObj2Array(appealTypeObj)?.map((ele, index) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={5}>
            <Form.Item label="工单状态">
              {getFieldDecorator('orderStatus')(
                <Select placeholder="请选择" allowClear={true}>
                  {orderStateList?.map((ele, index) => (
                    <Select.Option value={String(index + 1)} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col align="right" span={6} style={{ textAlign: 'right' }}>
            <Form.Item wrapperCol={{ span: 24 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={onReset} style={{ marginRight: 10 }}>
                重置
              </Button>
              <Button type="primary" onClick={() => handleEdit({}, '新增')}>
                新增上报
              </Button>
            </Form.Item>
          </Col>
        </Form>
      </Row>

      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
      {/* 反馈上报 */}
      <AddModal
        visible={addVisible}
        cancel={() => {
          setAddVisible(false);
        }}
        afterClose={() => {
          setCurrentRow({});
        }}
        reload={() => onReset()}
        currentRow={currentRow}
        appealTypeList={transforObj2Array(appealTypeObj)}
        allAppealKeyWordsTypeList={allAppealKeyWordsTypeList}
        appealSourceList={appealSourceList}
      />
    </Card>
  );
};
export default Form.create({})(Index);
