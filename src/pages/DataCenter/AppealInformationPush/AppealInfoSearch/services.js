import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/appealInfo/pageAppealInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 新增上报
export async function addAppealInfo(params) {
  return request(`/api/hn/appealInfo/addAppealInfo`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}


// 查看详情
export async function getDetailById(params) {
  return request(`/api/hn/appealInfo/getDetailById`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}


// 编辑上报
export async function updateAppealInfo(params) {
  return request(`/api/hn/appealInfo/updateAppealInfo`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 查询申诉问题类型
export async function getAppealType(params) {
  return request(`/api/hn/systemConfig/getConfigValueByConfigType`, {
    method: 'GET',
    params,
  });
}

// 查询关键信息类型
export async function getAppealKeyWordsType(params) {
  return request(`/api/hn/groupInterfaceShare/updateGroupInterfaceShare`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}
