import { getAppealType, getAppealKeyWordsType } from './commonServices';

// 获取申诉问题类型
export const getAppealTypeObj = async () => {
  const response = await getAppealType({ configType: 'appealType' });
  if (response.code === 200) {
    return response?.data || {};
  } else {
    message.error(response.message);
  }
};

// 获取申诉关键信息类型
export const getAppealKeyWordsTypeObj = async () => {
  const response = await getAppealType({ configType: 'appealKeyWordsType' });
  if (response.code === 200) {
    return response?.data || {};
  } else {
    message.error(response.message);
  }
};

// 工单状态
export const orderStateList = ['待审批', '审批通过', '审批驳回', '已上报'];

// 来源

export const appealSourceList = {
  118: '湖南电信',
  99: '其他',
};
