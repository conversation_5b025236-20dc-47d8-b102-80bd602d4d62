import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import { Card, Form, Row, Col, DatePicker, Select, Input, Button, message } from 'antd';

import moment from 'moment';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState({});

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas();
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    const { feedbackTime } = values;
    getListDatas({
      ...values,
      feedbackTime: undefined,
      appealTimeStart: feedbackTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      appealTimeEnd: feedbackTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas();
  };

  let columns = [
    {
      title: '工单ID',
      width: 170,
      dataIndex: 'commandId',
      ellipsis: true,
    },
    {
      title: '申诉反馈时间',
      dataIndex: 'feedbackTime',
      width: 140,
      ellipsis: true,
    },
    {
      title: '申诉来源',
      width: 100,
      dataIndex: 'company',
      ellipsis: true,
    },
    {
      title: '申诉问题类型',
      width: 100,
      dataIndex: 'appealType',
      ellipsis: true,
    },
    {
      title: '关键信息类型',
      width: 100,
      dataIndex: 'appealKeyWordsType',
      ellipsis: true,
    },
    {
      title: '关键信息',
      width: 100,
      dataIndex: 'appealKeyWords',
      ellipsis: true,
    },
    {
      title: '认定结果',
      width: 100,
      dataIndex: 'confirmation',
      ellipsis: true,
    },
    {
      title: '认定时间',
      width: 140,
      dataIndex: 'confirmedTime',
      ellipsis: true,
    },
    {
      title: '认定情况',
      width: 120,
      dataIndex: 'content',
      ellipsis: true,
    },
    {
      title: '联系电话',
      width: 120,
      dataIndex: 'contact',
      ellipsis: true,
    },

    {
      title: '认定单位',
      width: 100,
      dataIndex: 'confirmedUnit',
      ellipsis: true,
    },
    {
      title: '关联案件编号',
      width: 120,
      dataIndex: 'relateCase',
      ellipsis: true,
    },
  ];

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={6}>
            <Form.Item label="工单ID">
              {getFieldDecorator('commandId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="申诉反馈时间">
              {getFieldDecorator('feedbackTime')(
                <RangePicker format={'YYYY-MM-DD'} disabledDate={disabledDate} />,
              )}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="认定结果">
              {getFieldDecorator('confirmation')(
                <Select placeholder="请选择" allowClear={true}>
                  {[
                    {
                      value: 0,
                      label: '不解封',
                    },
                    {
                      value: 1,
                      label: '解封',
                    },
                    {
                      value: 99,
                      label: '不处理',
                    },
                  ]?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col align="right" span={4} style={{ textAlign: 'right' }}>
            <Form.Item wrapperCol={{ span: 24 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={onReset}>重置</Button>
            </Form.Item>
          </Col>
        </Form>
      </Row>

      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </Card>
  );
};
export default Form.create({})(Index);
