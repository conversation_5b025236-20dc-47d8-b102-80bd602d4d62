import React, { Fragment, useEffect, useRef, useState, memo } from 'react';
import { Form, Modal, Row, Col, Upload } from 'antd';
import { getAppealKeyWordsTypeObj, appealSourceList } from '../utils';
import { exportFile } from '@/utils/utils';

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 14,
  },
};

const DetailModal = ({
  visible,
  form: { getFieldDecorator, resetFields },
  cancel,
  currentRow,
  appealTypeObj,
}) => {
  const [allAppealKeyWordsTypeList, setAllAppealKeyWordsTypeList] = useState({});

  const getAppealKeyWordsTypeList = async () => {
    setAllAppealKeyWordsTypeList(await getAppealKeyWordsTypeObj());
  };

  useEffect(() => {
    if (visible) {
      // 获取关键信息类型下拉
      getAppealKeyWordsTypeList();
    }
  }, [visible]);

  const defaultList = currentRow?.fileList?.map((ele) => {
    return {
      uid: ele.id,
      id: ele.id,
      name: ele.fileName,
      status: 'done',
      // url: ele.filePath,
    };
  });

  const uploadProps = {
    showUploadList: {
      showDownloadIcon: true,
      showRemoveIcon: false,
    },
    onDownload: (file) => {
      exportFile({
        urlAPi: '/api/hn/appealInfo/downloadAppealFile',
        params: {
          fileId: file.id,
        },
        decode: true,
        method: 'GET',
      });
    },
  };

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  return (
    <Fragment>
      <Modal
        visible={visible}
        title={`审批信息查看`}
        footer={null}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        width={'60%'}
      >
        <div>
          <Form {...formItemLayout}>
            <Row>
              <Col span={12}>
                <Form.Item label="申诉来源">
                  {appealSourceList?.[currentRow?.appealSource] || '--'}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="申诉问题类型">
                  {appealTypeObj?.[currentRow?.appealType] || '--'}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="关键信息类型">
                  {allAppealKeyWordsTypeList?.[currentRow?.appealType]?.[
                    currentRow?.appealKeyWordsType
                  ] || '--'}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="关键信息">{currentRow?.appealKeyWords || '--'}</Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="申诉人姓名">{currentRow?.questionerName || '--'}</Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="联系电话">{currentRow?.questionerPhone || '--'}</Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="问题描述" wrapperCol={{ span: 19 }} labelCol={{ span: 4 }}>
                  {currentRow?.content || '--'}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="证明材料上传">
                  {getFieldDecorator('uploadFile', {
                    valuePropName: 'fileList',
                    getValueFromEvent: normFile,
                    initialValue: defaultList,
                  })(<Upload {...uploadProps}></Upload>)}
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </Fragment>
  );
};
export default memo(Form.create()(DetailModal));
