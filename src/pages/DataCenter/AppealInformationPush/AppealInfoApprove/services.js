import request from 'ponshine-request';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/appealInfo/pageAppealInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 审批
export async function approveAppealInfo(params) {
  return request(`/api/hn/appealInfo/approveAppealInfo`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 查看详情
export async function getDetailById(params) {
  return request(`/api/hn/appealInfo/getDetailById`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}



