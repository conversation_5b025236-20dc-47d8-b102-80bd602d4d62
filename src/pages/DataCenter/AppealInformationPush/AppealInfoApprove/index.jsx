import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Modal,
} from 'antd';

import moment from 'moment';

import StandardTable from '@/components/StandardTable';
import DetailModal from './DetailModal';
import ApproveModal from './ApproveModal';
import { orderStateList, getAppealTypeObj, getAppealKeyWordsTypeObj } from '../utils';
import { renderToolTip, transforObj2Array } from '@/utils/utils';

import { selectPage, getDetailById } from './services';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [addVisible, setAddVisible] = useState(false);
  const [approveVisible, setApproveVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [currentRow, setCurrentRow] = useState({});
  const [appealTypeObj, setAppealTypeObj] = useState({});

  const getAppealTypeList = async () => {
    setAppealTypeObj(await getAppealTypeObj());
  };

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    // 查询接口与申诉信息查询接口一致， 订单状态默认查询待审批
    const response = await selectPage({
      pageNum,
      pageSize,
      ...props,
      // orderStatus: props.orderStatus || 1,
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getAppealTypeList();
    getListDatas();
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    const { createTime } = values;
    getListDatas({
      ...values,
      createTime: undefined,
      appealTimeStart: createTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      appealTimeEnd: createTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas({
      orderStatus: 1,
    });
  };

  const handleEdit = async (r) => {
    const response = await getDetailById({ id: r?.id });
    if (response && response.code === 200) {
      setCurrentRow({
        ...(response?.data || {}),
      });
    } else {
      message.error(response.data);
    }
    setAddVisible(true);
  };

  const handleAudit = (r) => {
    setCurrentRow(r);
    setApproveVisible(true);
  };

  let columns = [
    {
      title: '申诉登记时间',
      width: 140,
      dataIndex: 'appealTime',
      ellipsis: true,
    },
    {
      title: '工单状态',
      width: 100,
      dataIndex: 'orderStatus',
      ellipsis: true,
      render: (v) => {
        return renderToolTip(orderStateList[Number(v) - 1]);
      },
    },
    {
      title: '申诉人姓名',
      width: 120,
      dataIndex: 'questionerName',
      ellipsis: true,
    },
    {
      title: '审批人',
      width: 100,
      dataIndex: 'approverName',
      ellipsis: true,
    },
    {
      title: '审批时间',
      width: 100,
      dataIndex: 'approvalTime',
      ellipsis: true,
    },
    {
      title: '审批结果',
      width: 100,
      dataIndex: 'approvalResult',
      ellipsis: true,
    },
    {
      title: '审批意见',
      width: 100,
      dataIndex: 'approvalOpinion',
      ellipsis: true,
    },

    {
      title: '操作',
      width: 80,
      dataIndex: 'opt',
      ellipsis: true,
      fixed: 'right',
      render: (v, r) => {
        return (
          <Fragment>
            <Icon
              type="search"
              style={{ color: '#1890ff', marginRight: 8 }}
              onClick={() => handleEdit(r)}
            />
            {['1']?.includes(r.orderStatus) && (
              <Icon type="audit" style={{ color: '#1890ff' }} onClick={() => handleAudit(r)} />
            )}
          </Fragment>
        );
      },
    },
  ];

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  return (
    <Card>
      <Row>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
          <Col span={8}>
            <Form.Item label="申诉登记时间">
              {getFieldDecorator('createTime')(
                <RangePicker
                  format={'YYYY-MM-DD'}
                  // disabledDate={disabledDate}
                />,
              )}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="工单状态">
              {getFieldDecorator('orderStatus', {
                initialValue: 1,
              })(
                <Select placeholder="请选择" allowClear>
                  {orderStateList?.map((ele, index) => (
                    <Select.Option value={index + 1} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col align="right" span={10} style={{ textAlign: 'right' }}>
            <Form.Item wrapperCol={{ span: 24 }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={onReset}>重置</Button>
            </Form.Item>
          </Col>
        </Form>
      </Row>

      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
      {/* 查看上报 */}
      <DetailModal
        visible={addVisible}
        cancel={() => {
          setAddVisible(false);
          setCurrentRow({});
        }}
        currentRow={currentRow}
        appealTypeObj={appealTypeObj}
      />
      {/* 反馈上报 */}
      <ApproveModal
        visible={approveVisible}
        cancel={() => {
          setApproveVisible(false);
          setCurrentRow({});
        }}
        reload={onReset}
        currentRow={currentRow}
      />
    </Card>
  );
};
export default Form.create({})(Index);
