import React, { Fragment, useEffect, useRef, useState, memo } from 'react';
import {
  Form,
  Modal,
  Select,
  Row,
  Col,
  message,
  DatePicker,
  Input,
  Radio,
  Button,
  Upload,
  Icon,
} from 'antd';
import { approveAppealInfo } from './services';
import moment from 'moment';

const { TextArea } = Input;
const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 14,
  },
};

const ConfigEdit = ({
  visible,
  form,
  form: {
    getFieldValue,
    getFieldDecorator,
    setFieldsValue,
    getFieldsValue,
    validateFields,
    resetFields,
  },
  cancel,
  reload,
  currentRow,
}) => {
  const [submitLoading, setSubmitLoading] = useState(false);

  // 保存
  const handleOk = async () => {
    validateFields(async (err, values) => {
      if (err) return;
      setSubmitLoading(true);
      const response = await approveAppealInfo({ ...values, id: currentRow?.id });
      setSubmitLoading(false);

      if (response && response.code === 200) {
        message.success(response.message);
        cancel();
        reload();
      } else {
        message.error(response.message);
      }
    });
  };

  const handleChange = (v) => {
    setFieldsValue({
      approvalOpinion: v,
    });
  };

  return (
    <Fragment>
      <Modal
        visible={visible}
        title={'申诉信息审批'}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        width={'40%'}
        confirmLoading={submitLoading}
      >
        <div>
          <Form {...formItemLayout}>
            <Form.Item label="审批结果">
              {getFieldDecorator('approvalResult', {
                rules: [
                  {
                    required: true,
                    message: '请选择审批结果',
                  },
                ],
              })(
                <Select
                  placeholder="请选择"
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                  onChange={handleChange}
                >
                  {['审批通过', '审批驳回']?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item label="审批意见">
              {getFieldDecorator('approvalOpinion', {
                rules: [
                  {
                    required: true,
                    message: '请输入审批意见',
                  },
                  {
                    max: 200,
                    message: '最多输入200个字符',
                  },
                ],
              })(<TextArea rows={2} placeholder="请输入" allowClear />)}
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </Fragment>
  );
};
export default memo(Form.create()(ConfigEdit));
