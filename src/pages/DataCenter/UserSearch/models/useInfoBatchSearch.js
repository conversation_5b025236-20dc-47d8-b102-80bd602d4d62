/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-28 09:28:59
 * @LastEditors: z<PERSON><PERSON><PERSON>en
 * @LastEditTime: 2022-09-28 15:16:05
 * @FilePath: \hunanfanzha\src\pages\DisposalCenter\models\useInfoBatchSearch.js
 * @Description:
 */
import {
  findTableData,
  addEditTag,
  deleteTag,
  getAllFraudNatureTypeAndRule,
  getCRMInterfaceType,
  getCRMTag,
} from '@/services/DisposalCenter/useInfoBatchSearch';
import { message } from 'antd';
const defaultState = {};

export default {
  namespace: 'useInfoBatchSearch',
  state: defaultState,
  effects: {
    *findTableData({ payload, callback }, { call, put }) {
      const response = yield call(findTableData, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *addEditTag({ payload, callback }, { call, put }) {
      const response = yield call(addEditTag, payload);
      if (callback) callback(response);
    },
    *deleteTag({ payload, callback }, { call, put }) {
      const response = yield call(deleteTag, payload);
      if (callback) callback(response);
    },
    *getAllFraudNatureTypeAndRule({ payload, callback }, { call, put }) {
      const response = yield call(getAllFraudNatureTypeAndRule, payload);
      if (!response) return;
      if (response) if (callback) callback(response);
    },
    *getCRMInterfaceType({ payload, callback }, { call, put }) {
      const response = yield call(getCRMInterfaceType, payload);
      if (!response) return;
      if (callback) callback(response);
    },
    *getCRMTag({ payload, callback }, { call, put }) {
      const response = yield call(getCRMTag, payload);
      if (!response) return;
      if (callback) callback(response);
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
