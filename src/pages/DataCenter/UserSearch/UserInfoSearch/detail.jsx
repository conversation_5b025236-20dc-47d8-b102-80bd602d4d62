import React from "react";
import { Modal, Form, Tooltip, Row, Col } from "antd";
import _ from 'lodash';
import styles from './index.less';


const Details = ({ details, visible, onClose, columns }) => {

    return (
        <Modal
            title={'用户信息详情'}
            visible={visible}
            footer={null}
            maskClosable={false}
            onCancel={() => { onClose() }}
            destroyOnClose
            width={1080}
            bodyStyle={{maxHeight:520,overflow:'auto'}}
        >
            <Form wrapperCol={{span:16}} labelCol={{span:8}}>
                {
                    _.chunk(columns.filter(v => v.title !== '操作'), 2)?.map(group => {
                        return <Row gutter={24}>
                            {
                                group.map(item => (
                                    <Col span={12}>
                                        <Form.Item label={item.title} className={styles.myStyleForm}>
                                            {details[item.key] ? <Tooltip title={details[item.key]}><span>{details[item.key]}</span></Tooltip> : '--'}
                                        </Form.Item>
                                    </Col>
                                ))
                            }
                        </Row>
                    })
                }
            </Form>
        </Modal>
    )
};

export default Details;