import React, { useReducer, useEffect, Fragment, useState } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Input,
  Button,
  InputNumber,
  Modal,
  Icon,
  Upload,
  message,
  DatePicker,
  Select,
} from 'antd';
import { connect } from 'dryad';

import { exportFile, exportFileTip } from '@/utils/utils';

import { onEnterPage } from '@/utils/openTab';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import Details from './detail';
import { InputSizes } from 'antd/lib/input/Input';
import { Licensee, useLicensee } from 'ponshine';
import ExportApprove from '@/components/ExportApprove';
import moment from 'moment';

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const initialState = {};
const FraudPreventionLableManage = (props) => {
  const {
    form: { getFieldDecorator, getFieldsValue, resetFields, getFieldValue },
    dispatch,
    tableLoading,
  } = props;
  const [state, setState] = useReducer(reducer, initialState);
  const {
    searchValue = {},
    pagination = {},
    dataSource = [],
    total = 0,
    selectedRows = [],
    batchState = false,
    batchVisible = false,
    batchFile = [],
    startTime = undefined,
    loading = false,
  } = state;

  useEffect(() => {
    getAllFraudType();
    // initTable();
    // const unlistenHistory = onEnterPage(props, () => {
    //   initTable();
    // });
    // return () => {
    //   if (unlistenHistory) unlistenHistory();
    // };
  }, []);

  const specialColumns = [
    {
      dataIndex: 'userScore',
      key: 'userScore',
      title: '集团用户评分',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      title: '风险等级',
      ellipsis: true,
      align: 'center',
      render: (v, r, i) => {
        return <span style={{ color: r.riskLevelColor }}>{v}</span>;
      },
    },
  ];

  const columns = [
    {
      dataIndex: 'phoneNum',
      key: 'phoneNum',
      title: '号码',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'localNetwork',
      key: 'localNetwork',
      title: '本地网',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'businessArea',
      key: 'businessArea',
      title: '营业区',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'internetAccessTime',
      key: 'internetAccessTime',
      title: '入网时间',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'channelType',
      key: 'channelType',
      title: '渠道类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'acceptancePoint',
      key: 'acceptancePoint',
      title: '受理网点',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'phoneNumState',
      key: 'phoneNumState',
      title: '号码状态',
      ellipsis: true,
      align: 'center',
    },

    {
      dataIndex: 'saleCategory',
      key: 'saleCategory',
      title: '销售品',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'salePrice',
      key: 'salePrice',
      title: '销售品价格',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'whetherCampus',
      key: 'whetherCampus',
      title: '是否校园卡',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'whetherOpenChineseAnime',
      key: 'whetherOpenChineseAnime',
      title: '是否开通国漫',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'whetherOneDualTerminal',
      key: 'whetherOneDualTerminal',
      title: '是否一号双终端',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'masterCard',
      key: 'masterCard',
      title: '是否主副卡',
      ellipsis: true,
      align: 'center',
    },
    ...specialColumns,

    {
      dataIndex: 'usingCustomer',
      key: 'usingCustomer',
      title: '使用客户',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'certificatesNumber',
      key: 'certificatesNumber',
      title: '使用客户证件号码',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'certificatesType',
      key: 'certificatesType',
      title: '使用人证件类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'usingCustomerAddress',
      key: 'usingCustomerAddress',
      title: '使用客户地址',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'propertyCustomer',
      key: 'propertyCustomer',
      title: '产权客户',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'propertyCertificatesNumber',
      key: 'propertyCertificatesNumber',
      title: '产权证件号码',
      ellipsis: true,
      align: 'center',
      width: 140,
      forceShow: true,
    },
    {
      dataIndex: 'propertyCertificatesType',
      key: 'propertyCertificatesType',
      title: '产权证件类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'propertyCustomerAddress',
      key: 'propertyCustomerAddress',
      title: '产权客户地址',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'customerType',
      key: 'customerType',
      title: '客户类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'productType',
      key: 'productType',
      title: '产品类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'phoneNumTag',
      key: 'phoneNumTag',
      title: '号码标识',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'userInstallAddress',
      key: 'userInstallAddress',
      title: '用户装机地址',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'resumeNumbers',
      key: 'resumeNumbers',
      title: '复机单号',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'resumeTime',
      key: 'resumeTime',
      title: '复机时间',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'resumeWorkId',
      key: 'resumeWorkId',
      title: '复机工号',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'resumeOperator',
      key: 'resumeOperator',
      title: '复机人',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'acceptStaff',
      key: 'acceptStaff',
      title: '受理员工',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'acceptanceWorkId',
      key: 'acceptanceWorkId',
      title: '受理工号',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'grabMachineWorkId',
      key: 'grabMachineWorkId',
      title: '揽机人工号',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'grabMachineName',
      key: 'grabMachineName',
      title: '揽机人名称',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'grabMachineOrganize',
      key: 'grabMachineOrganize',
      title: '揽机人组织',
      ellipsis: true,

      align: 'center',
    },
    {
      dataIndex: 'grabMachineOrganizeChannelType',
      key: 'grabMachineOrganizeChannelType',
      title: '揽机人组织渠道类型',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'throwOrderTime',
      key: 'throwOrderTime',
      title: '甩单时间',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'throwOrderWorkId',
      key: 'throwOrderWorkId',
      title: '甩单工号',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'throwOrderOperator',
      key: 'throwOrderOperator',
      title: '甩单人',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'inputOrderTime',
      key: 'inputOrderTime',
      title: '输单时间',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'inputOrderWorkId',
      key: 'inputOrderWorkId',
      title: '输单工号',
      ellipsis: true,
      align: 'center',
    },
    {
      dataIndex: 'inputOrderOperator',
      key: 'inputOrderOperator',
      title: '输单人',
      ellipsis: true,
      align: 'center',
    },

    {
      dataIndex: 'action',
      key: 'action',
      title: '操作',
      width: 80,
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Button
            type="link"
            onClick={() => {
              handleInfo(record);
            }}
          >
            <Icon type="copy" title="详情" />
          </Button>
        );
      },
    },
  ];

  const defaultColunmsArr = [
    '号码',
    '本地网',
    '营业区',
    '入网时间',
    '渠道类型',
    '受理网点',
    '号码状态',
    '销售品',
    '销售品价格',
    '是否校园卡',
    '是否开通国漫',
    '是否一号双终端',
    '是否主副卡',
    '使用客户',
    '使用客户证件号码',
    '使用人证件类型',
    '使用客户地址',
    '产权客户',
    '产权证件号码',
    '产权证件类型',
    '产权客户地址',
    '客户类型',
    '产品类型',
    '操作',
  ];
  const defaultColunmsValue = columns.filter((ele) =>
    defaultColunmsArr.some((item) => item === ele.title),
  );

  const [defaultColunms, setDefaultColunms] = useState(defaultColunmsValue);

  const [infoVisible, setInfoVisible] = useState(false);
  const [info, setInfo] = useState({});
  const [expand, setExpand] = useState(false);

  const handleInfo = (record) => {
    setInfoVisible(true);
    setInfo(record);
  };

  // 切换展开收起
  const handleToggle = () => {
    setExpand(!expand);
  };

  const initTable = ({ pageNum = 1, pageSize = 10, ...props } = {}, isSearch = true) => {
    setState({ pagination: { current: pageNum, pageSize: pageSize, loading: true } });
    if (props.phoneNum || props.certificatesNumber) {
      const newColumns = [...defaultColunms];
      const index = newColumns.findIndex((ele) => ele.title === '是否主副卡');
      newColumns.splice(index + 1, 0, ...specialColumns);
      setDefaultColunms(newColumns);
    } else {
      setDefaultColunms(defaultColunmsValue);
    }

    dispatch({
      type: 'userInfoSearch/findTableData',
      payload: {
        pageSize,
        pageNum,
        ...(props || {}),
      },
      callback: (res) => {
        setState({ loading: false });
        // 熔断需求增加提示
        if (res?.code === 200) {
          res.message !== '操作成功' && message.info(res.message);
        }
        if (res && res.data) {
          setState({
            batchState: false,
            dataSource: res.data.items || [],
            total: res.data.totalNum || 0,
          });
        } else {
          if (isSearch) {
            message.error(res.message);
          }
          setState({ dataSource: [], total: 0 });
        }
      },
    });
  };

  const reloadTable = () => {
    // initTable({}, false);
    setState({
      searchValue: {},
      selectedRows: [],
      shutdownSubTagList: [],
      shutdownTagList: [],
      dataSource: [],
      pagination: {},
      total: 0,
    });
    resetFields();
  };

  const handleSearch = () => {
    const values = getFieldsValue();
    const {
      time,
      phoneNum, //号码
      certificatesNumber, //客户证件号码
      propertyCertificatesNumber, //产权证件号码
      acceptancePoint, //受理网点
      userInstallAddress, //用户装机地址
    } = values;

    if (
      !phoneNum &&
      !certificatesNumber &&
      !propertyCertificatesNumber &&
      !acceptancePoint &&
      !userInstallAddress
    ) {
      return message.info(
        '号码、客户证件号码、产权证件号码、受理网点、用户装机地址中至少填写一个才能进行查询',
      );
    }

    const timeValues = {
      internetAccessStartTime: time?.[0]?.format('YYYY-MM-DD 00:00:00'),
      internetAccessEndTime: time?.[1]?.format('YYYY-MM-DD 23:59:59'),
      time: undefined,
    };

    setState({
      searchValue: {
        ...values,
        ...timeValues,
      },
    });

    initTable({ ...values, ...timeValues });
  };

  //批量查询
  const handleBatchSearch = () => {
    setState({ batchVisible: true });
  };

  // 批量查询提交
  const handleBatchSearchSubmit = () => {
    if (!batchFile?.length) {
      return message.error('请选择上传文件');
    }
    const params = new FormData();
    params.append('file', batchFile[0]);
    request('/api/userInfo/queryBatchUserInformation', {
      method: 'POST',
      data: params,
    }).then((res) => {
      if (res.code == 200) {
        res.message !== '操作成功' && message.info(res.message);
        if (res?.data?.hasFail) {
          setState({
            batchFile: [],
            batchState: true,
            batchVisible: false,
            dataSource: res.data?.list || [],
            total: res.data?.list?.length || 0,
          });
          Modal.confirm({
            title: '导入文件存在错误，是否下载错误文件？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              let url = '/api/userInfo/queryBatchUserInformationError',
                filename = '批量用户信息错误数据导出.xlsx';
              // if (type === 'stop') {
              //   filename = '停机批量导入模板.xlsx';
              //   url = '/api/hn/shutdown/downLoadTemplate';
              //   data = {};
              // }
              return new Promise((resolve, reject) => {
                request
                  .post(url, {
                    responseType: 'blob',
                    useCSRFToken: false,
                    getResponse: true,
                    // params: data,
                  })
                  .then(({ data, response = {} }) => {
                    let fileName =
                      response?.headers?.get('content-disposition')?.split('filename=')[1] || '';
                    const name = fileName || filename;
                    if (data) {
                      if ('download' in document.createElement('a')) {
                        // 非IE下载
                        const elink = document.createElement('a');
                        elink.download = name;
                        elink.style.display = 'none';
                        elink.href = URL.createObjectURL(data);
                        document.body.appendChild(elink);
                        elink.click();
                        URL.revokeObjectURL(elink.href); // 释放URL 对象
                        document.body.removeChild(elink);
                      } else {
                        // IE10+下载
                        navigator.msSaveBlob(data, name);
                      }
                    } else {
                      reject();
                      return;
                    }
                    resolve();
                  })
                  .catch((err) => {
                    reject(err);
                  });
              });
            },
          });
        } else {
          setState({
            batchFile: [],
            batchState: true,
            batchVisible: false,
            dataSource: res.data?.list || [],
            total: res.data?.list?.length || 0,
          });
        }
      } else {
        message.error(res.message);
        setState({ dataSource: [], total: 0 });
      }
    });
  };

  const handleSelectRows = (rows) => {
    setState({
      selectedRows: rows,
    });
  };

  const getAllFraudType = () => {
    dispatch({
      type: 'fraudPreventionManage/getAllFraudType',
      callback: (res) => {
        if (res && res.code == 200) {
          setState({ fraudTypeList: res.data || [] });
        } else {
          setState({ fraudTypeList: [] });
        }
      },
    });
  };
  const handlePaginationTable = (pagination) => {
    const { current, pageSize } = pagination;
    setState({ selectedRows: [] });
    if (!batchState) {
      initTable({ pageNum: current, pageSize, ...searchValue });
    } else {
      setState({
        pagination: pagination,
      });
    }
  };
  const handleExport = () => {
    if (!batchState && !searchValue.phoneNum && !searchValue.identityCard) {
      message.info('号码或身份证必填一项');
      return false;
    }
    return true;

    // exportFile({
    //   urlAPi: batchState
    //     ? '/api/userInfo/exportBatchUserInformation'
    //     : '/api/userInfo/exportUserInformation',
    //   title: '用户信息查询数据下载',
    //   params: batchState ? dataSource : searchValue,
    //   method: 'POST',
    //   mime: 'xlsx',
    //   isDate: false,
    //   currentDateFormate: 'YYYYMMDD',
    // });
  };

  const handleTempDown = () => {
    let url = '/api/template/getTemplate',
      filename = '用户信息查询模板.xlsx',
      data = { templateCode: 'batchUserInormation' };
    // if (type === 'stop') {
    //   filename = '停机批量导入模板.xlsx';
    //   url = '/api/hn/shutdown/downLoadTemplate';
    //   data = {};
    // }
    return new Promise((resolve, reject) => {
      request
        .get(url, {
          responseType: 'blob',
          useCSRFToken: false,
          getResponse: true,
          params: data,
        })
        .then(({ data, response = {} }) => {
          const name = filename;
          if (data) {
            if ('download' in document.createElement('a')) {
              // 非IE下载
              const elink = document.createElement('a');
              elink.download = name;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(data);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(data, name);
            }
          } else {
            reject();
            return;
          }
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  };
  let x = 0;
  columns.forEach((item) => {
    x += item.width;
  });

  // const extraColunmsStr =
  //   getFieldValue('certificatesNumber') || getFieldValue('phoneNum')
  //     ? '集团用户评分,风险等级,'
  //     : '';

  // const filterListStr = ['号码','本地网','营业区','入网时间','渠道类型','受理网点','号码状态','销售品','销售品价格','是否校园卡','是否开通国漫','是否一号双终端','是否主副卡','使用客户','使用客户证件号码','使用人证件类型','使用客户地址','产权客户','产权证件号码','产权证件类型','产权客户地址','客户类型','产品类型','操作'];
  // const filterList = filterListStr.split(',');
  // let newfilterList = [];
  // columns.forEach((item) => {
  //   if (
  //     filterList.some((items) => {
  //       return items == item.title;
  //     })
  //   ) {
  //     newfilterList.push({ ...item });
  //   }
  // });
  // setNewColumns(newfilterList);

  const disabledDate = (current) => {
    if (startTime) {
      return (
        current > moment(startTime).add(3, 'months') ||
        current < moment(startTime).subtract(3, 'months') ||
        current > moment().subtract(0, 'day')
      );
    } else {
      return current > moment().subtract(0, 'day');
    }
  };

  return (
    <Fragment>
      <Card style={{ width: '100%' }}>
        <Form {...formItemLayout}>
          <Row>
            <Licensee license="useInfoSearch_getUserInformation">
              <Col span={6}>
                <Form.Item label="号码">
                  {getFieldDecorator('phoneNum', {
                    getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                  })(<Input style={{ width: '100%' }} placeholder="请输入" allowClear />)}
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item label="客户证件号码">
                  {getFieldDecorator('certificatesNumber', {
                    getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                  })(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="使用客户">
                  {getFieldDecorator('usingCustomer', {
                    getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                  })(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="产权证件号码">
                  {getFieldDecorator('propertyCertificatesNumber', {
                    getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                  })(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <div style={{ display: expand ? 'block' : 'none' }}>
                <Col span={6}>
                  <Form.Item label="产权客户">
                    {getFieldDecorator('propertyCustomer', {
                      getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                    })(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="入网时间">
                    {getFieldDecorator('time')(
                      <RangePicker
                        placeholder={'请选择'}
                        allowClear
                        format={'YYYY-MM-DD'}
                        onCalendarChange={(dates) => {
                          if (dates[1]) {
                            setState({ startTime: undefined });
                          } else {
                            setState({ startTime: dates[0] });
                          }
                        }}
                        disabledDate={disabledDate}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="受理网点">
                    {getFieldDecorator('acceptancePoint', {
                      getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                    })(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="使用客户地址">
                    {getFieldDecorator('usingCustomerAddress', {
                      getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                    })(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="产权客户地址">
                    {getFieldDecorator('propertyCustomerAddress', {
                      getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                    })(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="用户装机地址">
                    {getFieldDecorator('userInstallAddress', {
                      getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                    })(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="销售品">
                    {getFieldDecorator('saleCategory', {
                      getValueFromEvent: (e) => e.target.value.replace(/(^\s*)|(\s*$)/g, ''),
                    })(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="渠道类型">
                    {getFieldDecorator('channelType')(
                      <Select allowClear placeholder="请选择">
                        {['电子渠道', '实体渠道']?.map((ele, index) => (
                          <Select.Option value={ele} key={index}>
                            {ele}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </div>
            </Licensee>

            <Col span={24} style={{ textAlign: 'right' }}>
              <Form.Item wrapperCol={{ span: 24 }}>
                <Licensee license="useInfoSearch_getUserInformation">
                  <Button type="primary" onClick={handleSearch} style={{ margin: '0 16px' }}>
                    查询
                  </Button>
                  <Button style={{ marginRight: 16 }} onClick={reloadTable}>
                    重置
                  </Button>
                </Licensee>
                <Licensee license="useInfoSearch_queryBatchUserInformation">
                  <Button type="primary" onClick={handleBatchSearch} style={{ marginRight: 16 }}>
                    批量查询
                  </Button>
                </Licensee>
                <Licensee license="useInfoSearch_exportUserInformation">
                  {/* <Button type="primary" onClick={handleExport} style={{ marginRight: 16 }}>
                    数据导出
                  </Button> */}
                  <ExportApprove
                    buttonStyle={{ marginRight: 10 }}
                    exportParams={{
                      urlAPi: batchState
                        ? '/api/userInfo/exportBatchUserInformation'
                        : '/api/userInfo/exportUserInformation',
                      title: '用户信息查询数据下载',
                      params: batchState
                        ? dataSource
                        : { ...searchValue, pageNum: 1, pageSize: 999 },
                      method: 'POST',
                      mime: 'xlsx',
                      isDate: false,
                      currentDateFormate: 'YYYYMMDD',
                    }}
                    // beforeExport={handleExport}
                    moduleTile="用户信息管理"
                    // 是否校验商业秘密电子文件相关
                    isVerifyhEncryption={true}
                    disabledExport={!dataSource.length}
                  />
                </Licensee>
                <a style={{ marginLeft: 8, fontSize: 12 }} onClick={handleToggle}>
                  {expand ? '收起' : '展开'} <Icon type={expand ? 'up' : 'down'} />
                </a>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <StandardTable
          // filterList={filterList}
          detailColumns={columns.map((item) => {
            return { width: 100, ...item, key: item?.dataIndex, ellipsis: true, align: 'center' };
          })}
          tools={true}
          rowKey="id"
          columns={defaultColunms}
          showSelectCount={false}
          rowSelectionProps={false}
          isNeedAutoWidth={true}
          // onSelectRow={handleSelectRows}
          // selectedRows={selectedRows}
          data={{
            list: dataSource || [],
            pagination: {
              total: total | 0,
              showSizeChanger: true,
              showQuickJumper: true,
              ...pagination,
            },
          }}
          onChange={handlePaginationTable}
          loading={tableLoading}
        />

        {batchVisible && (
          <Modal
            title="批量查询导入"
            okText="查询"
            cancelText="取消"
            maskClosable={false}
            destroyOnClose
            visible={batchVisible}
            onCancel={() => {
              setState({ batchFile: [], batchVisible: false });
            }}
            onOk={() => {
              handleBatchSearchSubmit();
            }}
          >
            <Form>
              <Form.Item label="文件导入" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                <Upload
                  accept=".xls,.xlsx"
                  beforeUpload={(file) => {
                    setState({ batchFile: [file] });
                    return false;
                  }}
                  onRemove={(file) => {
                    setState({ batchFile: [] });
                    return false;
                  }}
                  fileList={batchFile}
                >
                  <Button>
                    <Icon type="vertical-align-top" />
                    选择文件
                  </Button>
                </Upload>
                {!batchFile?.length && <span>未选择文件</span>}
                <div>注：每个文件不能超过200个号码。</div>
                <div>
                  <Button
                    type="link"
                    onClick={() => {
                      handleTempDown();
                    }}
                    style={{ paddingLeft: 0 }}
                  >
                    {' '}
                    模板下载{' '}
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </Modal>
        )}
        {/* 详情 */}
        {infoVisible && (
          <Details
            visible={infoVisible}
            details={info}
            onClose={() => {
              setInfo({});
              setInfoVisible(false);
            }}
            columns={columns}
          />
        )}
      </Card>
    </Fragment>
  );
};
export default connect(({ fraudPreventionManage, userInfoSearch, loading }) => ({
  fraudPreventionManage,
  userInfoSearch,
  tableLoading: loading.effects['userInfoSearch/findTableData'],
}))(Form.create()(FraudPreventionLableManage));
