import React, { useReducer, useEffect, Fragment } from 'react';
import { Card, Form, Row, Col, Input, Button, DatePicker } from 'antd';
import { connect } from 'dryad';
import { exportFile } from '@/utils/utils';
import AddSearchModal from './AddSearchModal';
import { onEnterPage } from '@/utils/openTab';
import StandardTable from '@/components/StandardTable';
import { Licensee, useLicensee } from 'ponshine';

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}
const initialState = {};
const FraudPreventionLableManage = (props) => {
  const {
    form: { getFieldDecorator, getFieldsValue, resetFields },
    dispatch,
  } = props;
  const [state, setState] = useReducer(reducer, initialState);
  const {
    searchValue = {},
    pagination = {},
    dataSource = [],
    total = 0,
    selectedRows = [],
    visible = false,
  } = state;
  useEffect(() => {
    initTable();
    const unlistenHistory = onEnterPage(props, () => {
      initTable();
    });
    return () => {
      if (unlistenHistory) unlistenHistory();
    };
  }, []);
  const show = useLicensee('useInfoBatchSearch_getSourceResultFile')


  const columns = [
    {
      dataIndex: 'id',
      key: 'id',
      title: 'ID',
      ellipsis: true,
      width: 200,
      align: 'center',
    },
    {
      dataIndex: 'importFileName',
      key: 'importFileName',
      title: '导入文件',
      ellipsis: true,
      width: 200,
      align: 'center',
      render: (text, recored) => {
        return (
          <a
            onClick={() => {
              show&&handleExport(recored, '1');
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      dataIndex: 'queryTime',
      key: 'queryTime',
      title: '查询时间',
      ellipsis: true,
      width: 200,
      align: 'center',
    },
    {
      dataIndex: 'outcomeFileName',
      key: 'outcomeFileName',
      title: '结果文件',
      ellipsis: true,
      width: 200,
      align: 'center',
      render: (text, recored) => {
        return (
          <a
            onClick={() => {
              handleExport(recored, '2');
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      dataIndex: 'queryStatus',
      key: 'queryStatus',
      title: '处理状态',
      ellipsis: true,
      width: 200,
      align: 'center',
    },
  ];
  const setVisible = () => {
    setState({ visible: !visible });
  };
  const initTable = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setState({ pagination: { current: pageNum, pageSize } });
    const time = props.time;
    const param = {
      currentPage: pageNum,
      pageSize,
      ...props,
      importTimeBegin: time && time[0] ? time[0].format('YYYYMMDD000000') : undefined,
      importTimeEnd: time && time[1] ? time[1].format('YYYYMMDD235959') : undefined,
    };
    delete param.time;
    dispatch({
      type: 'useInfoBatchSearch/findTableData',
      payload: param,
      callback: (res) => {
        if (res && res.data) {
          setState({ dataSource: res.data.items || [], total: res.data.totalNum || 0 });
        } else {
          setState({ dataSource: [], total: 0 });
        }
      },
    });
  };

  const reloadTable = () => {
    initTable();
    setState({ searchValue: {}, selectedRows: [], shutdownSubTagList: [], shutdownTagList: [] });
    resetFields();
  };

  const handleSearch = () => {
    const values = getFieldsValue();
    setState({ searchValue: { ...values } });
    initTable(values);
  };
  const handleSelectRows = (rows) => {
    setState({
      selectedRows: rows,
    });
  };

  const handlePaginationTable = (pagination) => {
    const { current, pageSize } = pagination;
    setState({ selectedRows: [] });
    initTable({ pageNum: current, pageSize, ...searchValue });
  };
  const handleExport = (record, fileType) => {
    exportFile({
      urlAPi: '/api/userInfo/getSourceResultFile',
      // title: '防欺诈关停标签管理',
      params: {
        id: record.id,
        fileType: fileType,
      },
      decode: true,
      method: 'GET',
      mime: 'xlsx',
      // isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };

  return (
    <Fragment>
      <Card style={{ width: '100%' }}>
        <Licensee license="useInfoBatchSearch_getBatchUserInformation">
          <Form {...formItemLayout}>
            <Row>
              <Col span={7}>
                <Form.Item label="查询文件名称">
                  {getFieldDecorator('fileName')(
                    <Input style={{ width: '100%' }} placeholder="请输入文件名称" allowClear />,
                  )}
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="查询日期">
                  {getFieldDecorator('time')(
                    <RangePicker
                      allowClear
                      format="YYYY-MM-DD"
                      placeholder={['开始时间', '结束时间']}
                      style={{ width: '100%' }}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={7} style={{ marginTop: '4px' }}>
                <Button
                  type="primary"
                  onClick={() => {
                    handleSearch();
                  }}
                  style={{ margin: '0 16px' }}
                >
                  查看查询结果
                </Button>
              </Col>
            </Row>
          </Form>
        </Licensee>

        <Row style={{ marginBottom: 8 }}>
          <Licensee license="useInfoBatchSearch_newBatchUserInformationFile">

            <Button
              onClick={() => {
                setState({ visible: true });
              }}
            >
              新增查询
            </Button>
          </Licensee>
        </Row>
        <StandardTable
          rowKey="id"
          columns={columns}
          showSelectCount={false}
          rowSelectionProps={false}
          data={{
            list: dataSource || [],
            pagination: {
              total: total | 0,
              showSizeChanger: true,
              showQuickJumper: true,
              ...pagination,
            },
          }}
          scroll={{
            x: 1000,
          }}
          onChange={handlePaginationTable}
        />
        <AddSearchModal visible={visible} setVisible={setVisible} handleSearch={handleSearch} />
      </Card>
    </Fragment>
  );
};
export default connect(({ fraudPreventionManage, useInfoBatchSearch }) => ({
  fraudPreventionManage,
  useInfoBatchSearch,
}))(Form.create()(FraudPreventionLableManage));
