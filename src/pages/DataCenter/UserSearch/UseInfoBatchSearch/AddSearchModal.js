import React, { useReducer, useEffect, useState } from 'react';
import { Modal, Upload, message, Button, Icon, Checkbox, Row, Col } from 'antd';
import request from '@/utils/request';
const CheckboxGroup = Checkbox.Group;

function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}

const options = [
  // {
  //   label: '手机号码',
  //   value: 'phoneNum',
  // },

  {
    label: '本地网',
    value: 'localNetwork',
  },

  {
    label: '营业区',
    value: 'businessArea',
  },
  {
    label: '入网时间',
    value: 'internetAccessTime',
  },
  {
    label: '渠道类型',
    value: 'channelType',
  },
  {
    label: '受理网点',
    value: 'acceptancePoint',
  },
  {
    label: '受理工号',
    value: 'acceptanceWorkId',
  },
  {
    label: '销售品',
    value: 'saleCategory',
  },
  {
    label: '销售品价格',
    value: 'salePrice',
  },
  {
    label: '是否校园卡',
    value: 'whetherCampus',
  },
  {
    label: '是否开通国漫',
    value: 'whetherOpenChineseAnime',
  },
  {
    label: '是否一号双终端',
    value: 'whetherOneDualTerminal',
  },
  {
    label: '客户类型',
    value: 'customerType',
  },
  {
    label: '使用人证件类型',
    value: 'certificatesType',
  },
  {
    label: '使用客户证件号码',
    value: 'certificatesNumber',
  },
  {
    label: '受理员工',
    value: 'acceptStaff',
  },
  {
    label: '产权客户',
    value: 'propertyCustomer',
  },
  {
    label: '产权客户地址',
    value: 'propertyCustomerAddress',
  },
  {
    label: '使用客户',
    value: 'usingCustomer',
  },
  {
    label: '使用客户地址',
    value: 'usingCustomerAddress',
  },
  {
    label: '号码标识',
    value: 'phoneNumTag',
  },
  {
    label: '号码状态',
    value: 'phoneNumState',
  },
  {
    label: '复机单号',
    value: 'resumeNumbers',
  },
  {
    label: '复机时间',
    value: 'resumeTime',
  },
  {
    label: '复机工号',
    value: 'resumeWorkId',
  },
  {
    label: '复机人',
    value: 'resumeOperator',
  },
  {
    label: '揽机人工号',
    value: 'grabMachineWorkId',
  },

  {
    label: '揽机人名称',
    value: 'grabMachineName',
  },
  {
    label: '揽机人组织',
    value: 'grabMachineOrganize',
  },
  {
    label: '揽机人组织渠道类型',
    value: 'grabMachineOrganizeChannelType',
  },
  {
    label: '甩单时间',
    value: 'throwOrderTime',
  },
  {
    label: '甩单工号',
    value: 'throwOrderWorkId',
  },
  {
    label: '甩单人',
    value: 'throwOrderOperator',
  },
  {
    label: '输单时间',
    value: 'inputOrderTime',
  },
  {
    label: '输单工号',
    value: 'inputOrderWorkId',
  },
  {
    label: '输单人',
    value: 'inputOrderOperator',
  },
  {
    label: '是否主副卡',
    value: 'masterCard',
  },
  {
    label: '用户装机地址',
    value: 'userInstallAddress',
  },
  {
    label: '产权证件类型',
    value: 'propertyCertificatesType',
  },
  {
    label: '产权证件号码',
    value: 'propertyCertificatesNumber',
  },
  {
    label: '产品类型',
    value: 'productType',
  },
];

const BatchUserStopModal = (props) => {
  const { visible, setVisible, type, handleSearch } = props;
  const [state, setState] = useReducer(reducer, {
    plainOptions: [...options],
  });
  const { checkedList = [], indeterminate = false, checkAll = true, plainOptions = [] } = state;
  const [fileList, setFileList] = useState([]);
  const [okLoading, setOkLoading] = useState(false);
  useEffect(() => {
    if (!visible) {
      setFileList([]);
    } else {
      setState({
        checkAll: true,
        checkedList: options.map((x) => x.value),
        indeterminate: false,
      });
    }
  }, [visible]);

  const handleCancel = () => {
    setVisible();
    setState({ checkedList: [] });
  };
  const uploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      const size = file.size / 1024 / 1024;
      if (tag === '.xlsx') {
        setFileList([file]);
        return false;
      }
      message.warning('文件类型错误');
    },
    fileList,
  };
  const templateDownload = () => {
    let url = '/api/template/getTemplate',
      filename = '用户信息查询模板.xlsx',
      data = { templateCode: 'batchUserInormation' };
    // if (type === 'stop') {
    //   filename = '停机批量导入模板.xlsx';
    //   url = '/api/hn/shutdown/downLoadTemplate';
    //   data = {};
    // }
    return new Promise((resolve, reject) => {
      request
        .get(url, {
          responseType: 'blob',
          useCSRFToken: false,
          getResponse: true,
          params: data,
        })
        .then(({ data, response = {} }) => {
          const name = filename;
          if (data) {
            if ('download' in document.createElement('a')) {
              // 非IE下载
              const elink = document.createElement('a');
              elink.download = name;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(data);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(data, name);
            }
          } else {
            reject();
            return;
          }
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  };
  const handleSubmit = async () => {
    if (fileList.length === 0) return message.warning('请选择上传文件！');
    let url = '/api/userInfo/newBatchUserInformationFile';
    // if (type === 'stop') {
    //   url = '/api/hn/shutdown/batchShutdown';
    // }
    const formData = new FormData();
    formData.append('file', fileList[0]);
    formData.append('customerAttributesList', ['phoneNum', ...checkedList]);
    setOkLoading(true);
    try {
      const response = await request(url, { method: 'POST', data: formData });
      setOkLoading(false);
      handleCancel();
      if (!response) {
        message.success('导入成功');
      } else {
        if (response && response.code) {
          if (response.code == 200) {
            message.success(response.message);
            handleSearch();
          } else if (response.code == 401) {
            Modal.confirm({
              title: '导出错误文件',
              content: '导入失败，点击【确定】导出错误文件',
              okText: '确定',
              cancelText: '取消',
              onOk: () => {
                let filename = `用户信息查询错误文件.xlsx`;
                url = '/api/userInfo/exportBatchUserInformationErrorFile';
                // if (type == 'stop') {
                //   url = '/api/hn/shutdown/downLoadBatchShutdownErrorFile';
                //   filename = `批量用户关停错误文件导出.xlsx`;
                // }
                commDownLoadErrFile({ url, filename });
              },
            });
          } else {
            message.error(response.message);
          }
          // return;
        }
      }
    } catch (e) {}
  };
  const commDownLoadErrFile = ({ filename = '', url = '' }) => {
    request
      .get(url, {
        responseType: 'blob',
        getResponse: true,
      })
      .then((res) => {
        // 处理返回的文件流
        if (res.data) {
          const blob = res.data;
          if ('download' in document.createElement('a')) {
            // 非IE下载
            const elink = document.createElement('a');
            elink.download = filename;
            elink.style.display = 'none';
            elink.href = URL.createObjectURL(blob);
            document.body.appendChild(elink);
            elink.click();
            URL.revokeObjectURL(elink.href); // 释放URL对象
            document.body.removeChild(elink);
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, filename);
          }
        } else {
          message('warning', '下载报告失败');
        }
      });
  };
  const onCheckAllChange = (e) => {
    setState({
      checkedList: e.target.checked ? plainOptions.map((x) => x.value) : [],
      indeterminate: false,
      checkAll: e.target.checked,
    });
  };
  const onChange = (checkedList) => {
    setState({
      checkedList,
      indeterminate: !!checkedList.length && checkedList.length < plainOptions.length,
      checkAll: checkedList.length === plainOptions.length,
    });
  };
  return (
    <Modal
      title="新增查询"
      destroyOnClose
      visible={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      okButtonProps={{
        loading: okLoading,
      }}
    >
      <div>
        <p style={{ margin: '12px 0' }}>1、请导入要查询的号码清单文件</p>
        <a style={{ marginBottom: '8px' }} onClick={templateDownload}>
          模板下载
        </a>
      </div>
      <div>
        <Upload {...uploadProps} accept=".xlsx">
          <Button>
            <Icon type="upload" /> 选择文件
          </Button>
          {uploadProps.fileList.length === 0 ? <span>未选择任何文件</span> : ''}
        </Upload>
      </div>
      <div style={{ margin: '16px 0' }}>*每个文件不超过1万条号码</div>
      <p style={{ margin: '8px 0' }}>2、请选择要查询的属性</p>
      <Row>
        <Col>客户属性：</Col>
        <Col>
          <div>
            <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
              全选
            </Checkbox>
            <br />
            <CheckboxGroup options={plainOptions} value={state.checkedList} onChange={onChange} />
          </div>
        </Col>
      </Row>
    </Modal>
  );
};
export default BatchUserStopModal;
