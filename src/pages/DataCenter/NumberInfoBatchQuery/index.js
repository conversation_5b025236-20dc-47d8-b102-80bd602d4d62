import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Table,
  DatePicker,
  Input,
  Button,
  Modal,
  Tooltip,
  message,
} from 'antd';
import request from 'ponshine-request';

import { exportFile } from '@/utils/utils';
import StandardTable from '@/components/StandardTable';
import BatchImportModal from './BatchImportModal';
const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form, reloadTable } = props;
  const { getFieldDecorator, getFieldValue, validateFields } = form;
  const [loading, setloading] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [importVisible, setImportVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [searchValue, setSearchValue] = useState({});

  useEffect(() => {
    getListDatas();
  }, []);

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchValue,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const getListDatas = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setloading(true);

    const params = {
      ...props,
      pageNum,
      pageSize,
    };
    request({
      url: '/api/hn/policeStationed/queryPhoneBatchQuery',
      method: 'POST',
      requestType: 'json',
      data: params,
    }).then((res) => {
      setloading(false);
      if (res?.code == 200) {
        setSearchValue(props);
        setListData({
          list: res?.data?.items || [],
          pagination: {
            total: res?.data?.totalNum || 0,
            current: pageNum || 1,
            pageSize: pageSize || 10,
          },
        });
      } else {
        message.error(res?.message);
        setListData({
          list: [],
          pagination: {
            total: 0,
            current: 1,
            pageSize: 10,
          },
        });
      }
    });
  };

  const handleSearch = () => {
    const values = form.getFieldsValue();
    getListDatas({
      ...values,
      time: undefined,
      beginTime: values?.time?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endTime: values?.time?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  let columns = [
    {
      title: 'ID',
      width: 100,
      dataIndex: 'id',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '导入文件',
      width: 220,
      dataIndex: 'importFileName',
      render: (text, record) => {
        return (
          <a
            onClick={() => {
              handleExport(record, '1');
            }}
          >
            {text}
          </a>
        );
      },
      align: 'center',
      ellipsis: true,
    },
    {
      title: '查询时间',
      width: 200,
      align: 'center',
      dataIndex: 'queryTime',
    },
    {
      title: '结果文件',
      width: 200,
      dataIndex: 'resultFileName',
      render: (text, record) => {
        if (text) {
          if (text.includes('.xlsx')) {
            return (
              <a
                onClick={() => {
                  handleExport(record, '2');
                }}
              >
                {text}
              </a>
            );
          } else {
            return text;
          }
        } else {
          return record.state == '查询中' ? '正在查询字段' : '--';
        }
      },
      align: 'center',
      ellipsis: true,
    },
    {
      title: '处理状态',
      width: 100,
      align: 'center',
      dataIndex: 'state',
      // render: (v) => {
      //   return ['未开始', '查询中', '查询完成', '查询失败']?.[v] || '--';
      // },
    },
  ];

  const handleExport = (record, fileType) => {
    exportFile({
      urlAPi: '/api/hn/policeStationed/downloadFile',
      params: {
        id: record.id,
        fileType: fileType,
      },
      decode: true,
      method: 'POST',
      requestType: 'form',
      // mime: 'xlsx',
    });
  };

  const onImport = (values, callback) => {
    setConfirmLoading(true);
    request({
      url: '/api/hn/policeStationed/addPhoneBatchQuery',
      method: 'POST',
      requestType: 'form',
      data: values,
    }).then((res) => {
      setConfirmLoading(false);
      if (res?.code == 200) {
        message.success(res.message || '导入成功');
        form.resetFields();
        getListDatas();
        hideImport();
      } else {
        if (res.code == 401) {
          callback();
        } else {
          message.error(res.message);
        }
      }
    });
  };

  const hideImport = () => {
    setImportVisible(false);
  };

  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Card>
          <Form layout="inline">
            <Form.Item label={'查询文件名称'}>
              {getFieldDecorator('fileName', {
                // rules: [{ required: true, message: '查询文件名称' }],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
            <Form.Item label={'查询日期'}>
              {getFieldDecorator('time', {
                // initialValue: [
                //   moment(moment(new Date()).startOf('day'), 'YYYY-MM-DD'),
                //   moment(moment(new Date()), 'YYYY-MM-DD'),
                // ],
              })(
                <RangePicker
                  style={{
                    width: '100%',
                  }}
                  allowClear
                  getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                />,
              )}
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                style={{ marginRight: 10, marginTop: 4 }}
                onClick={handleSearch}
              >
                查看查询结果
              </Button>
              <Button
                type="primary"
                style={{ marginRight: 10, marginTop: 4 }}
                ghost
                onClick={() => {
                  setImportVisible(true);
                }}
              >
                新增查询
              </Button>
            </Form.Item>
          </Form>
        </Card>
        <Card>
          <StandardTable
            columns={columns}
            locale={{ emptyText: '无号码属性查询记录' }}
            data={listData}
            onChange={handleTableChange}
            multiple={true}
            loading={loading}
            showSelectCount={false}
            rowSelectionProps={false}
            rowKey="id"
          />
          <BatchImportModal
            title="批量查询"
            visible={importVisible}
            onImport={onImport}
            onClose={hideImport}
            loading={confirmLoading}
            downTemplateUrl={`/api/template/getTemplate?templateCode=policeStationedQuery `}
            errorExportUrl="/api/hn/fraudPoliceNotificationReplay/downloadErrorExcel"
          />
        </Card>
      </div>
    </div>
  );
};
export default Form.create({})(Index);
