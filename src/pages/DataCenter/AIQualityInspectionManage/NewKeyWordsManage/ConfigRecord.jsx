import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col, Select } from 'antd';

import StandardTable from '@/components/StandardTable';
import ShrinkSearchForm from '@/components/ShrinkSearchForm';
import AddModal from './AddModal';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import { keywordOperateRecordQuery } from './services';

const Index = (props) => {
  const {
    form,
    form: { getFieldsValue, resetFields, getFieldDecorator, getFieldValue },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const serachParams = useRef();

  const [detailVisible, setDetailVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState({});

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await keywordOperateRecordQuery({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.dataList || [],
        pagination: {
          total: response.data.totalCount,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      setListData({
        list: [],
        pagination: {
          total: 0,
          current: pageNum,
          pageSize,
        },
      });
      message.error(response.msg);
    }
  };

  useEffect(() => {
    findTableDataPager();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { operateTime } = formValue;
    findTableDataPager({
      ...formValue,
      gmtCreateStartTime: operateTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      gmtCreateEndTime: operateTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      operateTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
  };

  const handleDetail = (record) => {
    setCurrentRow({ ...record, title: '详情' });
    setDetailVisible(true);
  };

  const columns = [
    {
      title: '类型',
      dataIndex: 'keywordType',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      align: 'center',
      width: 180,
      ellipsis: true,
    },

    {
      title: '时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '操作用户',
      dataIndex: 'username',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作内容',
      dataIndex: 'operateType',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (v) => {
        const obj = {
          1: '新增',
          2: '删除',
        };
        return obj?.[v] || '--';
      },
    },
  ];

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <ShrinkSearchForm
          colSpan={8}
          formList={[
            <CommonSelect
              form={form}
              formItemKey="keywordType"
              configType="keyWordType"
              formItemLabel="类型"
              valueKey="name"
            />,
            <Form.Item label="关键词">
              {getFieldDecorator('keywords')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>,

            <Form.Item label="时间">
              {getFieldDecorator('operateTime')(
                <DatePicker.RangePicker placeholder="请选择" allowClear format="YYYY-MM-DD" />,
              )}
            </Form.Item>,
          ]}
          optButton={
            <Fragment>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </Fragment>
          }
        />
      </Form>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
      />
      {detailVisible && (
        <AddModal
          visible={detailVisible}
          onReload={handleReset}
          onCancel={() => {
            currentRow?.id && setCurrentRow({});
            setDetailVisible(false);
          }}
          currentRow={currentRow}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
