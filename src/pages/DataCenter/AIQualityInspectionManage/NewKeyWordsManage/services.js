import request from 'ponshine-request';
const apiPrefix = '/api/biz/modularity/project1752737891113331';

// 查询关键词
export async function keywordQuery(params) {
  return request(`${apiPrefix}/keywordQuery`, {
    method: 'GET',
    params,
  });
}

// 查询类型
export async function getKeywordType(params) {
  return request(`${apiPrefix}/highRiskTypeQuery`, {
    method: 'GET',
    params,
  });
}
// 新增关键词
export async function keywordAdd(params) {
  return request(`${apiPrefix}/qualityInspection_addTextKeywords`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 校验重复关键词
export async function checkRepeatKeyword(params) {
  return request(`${apiPrefix}/qualityInspection_findDuplicateTextKeywords`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 删除关键词
export async function keywordDelete(params) {
  return request(`${apiPrefix}/keywordDelete`, {
    method: 'GET',
    params,
  });
}

// 关键词操作记录
export async function keywordOperateRecordQuery(params) {
  return request(`${apiPrefix}/keywordOperateRecordQuery`, {
    method: 'GET',
    params,
  });
}
