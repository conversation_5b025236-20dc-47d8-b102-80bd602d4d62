import React, { Fragment } from 'react';
import { Form, Input } from 'antd';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';

const FirstStep = ({ form, searchType }) => {
  return (
    <Fragment>
      <CommonSelect
        configType="keyWordType"
        form={form}
        formItemKey="keywordType"
        formItemLabel="类型"
        valueKey="name"
        rules={[{ required: true, message: '请选择类型' }]}
        initialValue={searchType}
      />
      <Form.Item label="关键词">
        {form.getFieldDecorator('keywords', {
          rules: [
            {
              required: true,
              message: '请输入关键词',
            },
          ],
        })(
          <Input.TextArea
            placeholder="
            关键词最多可添加100词;
            关键词间用换行符隔开关键词内用”&“;
            隔开不需区分大小写;
            示例：请领取奖品，恭喜&获奖"
            allowClear
            rows={10}
          />,
        )}
      </Form.Item>
    </Fragment>
  );
};

export default FirstStep;
