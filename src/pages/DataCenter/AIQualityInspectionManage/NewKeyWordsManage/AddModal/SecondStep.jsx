import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Divider, Tag } from 'antd';
import styles from '../index.less';

const SecondStep = forwardRef(({ initKeywordList = [], repeatKeywordList = [] }, ref) => {
  const [keywordList, setKeywordList] = useState(initKeywordList);

  const handleClose = (e, index) => {
    e.preventDefault();
    const newKeywordList = [...keywordList];
    newKeywordList.splice(index, 1);
    setKeywordList(newKeywordList);
  };

  useImperativeHandle(ref, () => ({
    getConfirmKeywordList: () => {
      return keywordList;
    },
  }));

  useEffect(() => {
    setKeywordList([...initKeywordList]);
  }, [initKeywordList]);

  return (
    <div className={styles.secondStep}>
      <h4>新增关键字</h4>
      {keywordList?.map((item, index) => (
        <Tag key={index} onClose={(e) => handleClose(e, index)} closable style={{ marginBottom: 6 }}>
          {item}
        </Tag>
      ))}
      <Divider />
      <h4>重复关键字(不添加)</h4>
      {repeatKeywordList?.map((item, index) => (
        <Tag key={index} style={{ marginBottom: 6 }}>{item}</Tag>
      ))}
    </div>
  );
});

export default SecondStep;
