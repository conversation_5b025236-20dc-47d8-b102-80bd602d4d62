import { Modal, Form, Input, message, Button } from 'antd';
import React, { useState, Fragment, useRef, useEffect } from 'react';
import FirstStep from './FirstStep';
import SecondStep from './SecondStep';
import _ from 'lodash';

import { keywordAdd, checkRepeatKeyword } from '../services';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
};

const AddModal = Form.create()(
  ({
    visible,
    onCancel,
    form,
    form: { validateFields, getFieldDecorator, resetFields },
    onReload,
    searchType,
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);
    const [repeatKeywordList, setRepeatKeywordList] = useState([]);
    const [keywordList, setKeywordList] = useState([]);

    const secondStepRef = useRef(null);

    const handleOk = async () => {
      const confirmKeywordList = secondStepRef?.current?.getConfirmKeywordList();
      if (confirmKeywordList?.length === 0) {
        message.error('没有能保存的关键词');
        return;
      }
      setConfirmLoading(true);
      const response = await keywordAdd({
        keywordsList: confirmKeywordList,
        keywordType: form.getFieldValue('keywordType'),
      });
      setConfirmLoading(false);
      if (response.status === 200) {
        message.success(response.msg);
        onCancel();
        onReload();
      } else {
        message.error(response.msg);
      }
    };

    const handleStep = (type) => {
      if (type === 'pre') {
        setCurrentStep(currentStep - 1);
      } else {
        validateFields(async (err, fieldsValue) => {
          if (err) return;
          const { keywordType, keywords } = form.getFieldsValue();

          const currentKeywordList = keywords
            ?.split('\n')
            ?.map((item) => item.trim())
            ?.filter((item) => item);

          if (currentKeywordList?.length > 100) {
            message.error('最多输入100个关键词');
            return;
          }

          // 调用接口，校验重复项
          await getRepeatKeywordList({
            keywordType,
            keywordsList: currentKeywordList,
          });
        });
      }
    };

    const getRepeatKeywordList = async (params) => {
      const response = await checkRepeatKeyword(params);
      if (response.status === 200) {
        const repeatKeywordList = response?.data?.sameList || [];
        const newKeywordList = _.difference(params?.keywordsList, repeatKeywordList);
        setRepeatKeywordList(repeatKeywordList);
        setKeywordList(newKeywordList);
        setCurrentStep(currentStep + 1);
      } else {
        message.error(response.msg);
      }
    };

    return (
      <Modal
        title="关键字新增"
        visible={visible}
        onOk={handleOk}
        onCancel={onCancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
        width={800}
        footer={
          <Fragment>
            <Button onClick={onCancel}>取消</Button>
            {currentStep === 1 && (
              <Fragment>
                <Button onClick={() => handleStep('pre')} type="primary">
                  上一步
                </Button>
                <Button onClick={handleOk} type="primary" loading={confirmLoading}>
                  确定
                </Button>
              </Fragment>
            )}
            {currentStep === 0 && (
              <Button onClick={() => handleStep('next')} type="primary">
                下一步
              </Button>
            )}
          </Fragment>
        }
      >
        <Form {...formItemLayout}>
          <div style={{ display: currentStep === 0 ? 'block' : 'none' }}>
            <FirstStep form={form} searchType={searchType} />
          </div>
          <div style={{ display: currentStep === 1 ? 'block' : 'none' }}>
            <SecondStep
              initKeywordList={keywordList}
              repeatKeywordList={repeatKeywordList}
              ref={secondStepRef}
            />
          </div>
        </Form>
      </Modal>
    );
  },
);

export default AddModal;
