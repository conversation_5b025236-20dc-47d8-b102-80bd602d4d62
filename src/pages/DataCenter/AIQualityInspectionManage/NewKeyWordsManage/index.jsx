import React, { Fragment, useEffect, useState, useRef } from 'react';
import {
  Button,
  Card,
  message,
  Form,
  Modal,
  Input,
  DatePicker,
  Row,
  Col,
  Select,
  Tag,
  Divider,
  Spin,
} from 'antd';
import CommonSelect from '@/components/CommonFormItem/CommonSelect';
import styles from './index.less';

import AddModal from './AddModal';
import request from 'ponshine-request';
import { router } from 'ponshine';

import { keywordQuery, getKeywordType, keywordDelete } from './services';

const Index = (props) => {
  const {
    form,
    form: { getFieldsValue, resetFields, getFieldDecorator, validateFields },
  } = props;

  const [addVisible, setAddVisible] = useState(false);

  const [tagData, setTagData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState({});

  const [keywordTypeList, setKeywordTypeList] = useState([]);

  const getKeywordTypeList = async () => {
    const response = await getKeywordType();
    setKeywordTypeList(response?.data?.typeList || []);
  };

  const getTagData = async (props) => {
    setLoading(true);
    const response = await keywordQuery(props);
    if (response.status === 200) {
      setSearchParams(props);
      setLoading(false);
      setTagData(response?.data?.dataList || []);
    } else {
      setTagData([]);
      message.error(response.msg);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      if (err) {
        return;
      }
      getTagData(values);
    });
  };

  const handleReload = () => {
    if (searchParams?.keywordType) {
      getTagData(searchParams);
    }
  };

  const handleReset = () => {
    resetFields();
    setSearchParams({});
    setTagData([]);
  };

  const handleAdd = () => {
    setAddVisible(true);
  };

  const handleDelete = (e, id) => {
    e.preventDefault();
    Modal.confirm({
      title: '提示',
      content: '是否删除该关键字',
      onOk: async () => {
        const response = await keywordDelete({ id });
        if (response.status === 200) {
          message.success(response.msg);
          handleReload();
        } else {
          message.error(response.msg);
        }
      },
    });
  };

  useEffect(() => {
    getKeywordTypeList();
  }, []);

  return (
    <div className={styles.newKeyWordsManage}>
      <Card>
        <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
          <Row gutter={[24]}>
            <Col span={6}>
              <CommonSelect
                form={form}
                formItemKey="keywordType"
                configType="keyWordType"
                valueKey="name"
                formItemLabel="类型"
                rules={[{ required: true, message: '请选择类型' }]}
              />
            </Col>

            <Col span={6}>
              <Form.Item label="关键词">
                {getFieldDecorator('keywords')(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'right' }}>
                <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                  查询
                </Button>
                <Button style={{ marginRight: 16 }} onClick={handleReset}>
                  重置
                </Button>

                <Button type="primary" style={{ marginRight: 16 }} onClick={handleAdd}>
                  新增
                </Button>
                <Button
                  style={{ marginRight: 16 }}
                  onClick={() => {
                    router.push('/dataCenter/AIQualityInspectionManage/keyWordsManageRecord');
                  }}
                >
                  配置记录
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Divider />
        <Spin spinning={loading}>
          {tagData?.map((ele) => (
            <Tag key={ele.id} onClose={(e) => handleDelete(e, ele.id)} closable>
              {ele.keywords}
            </Tag>
          ))}
        </Spin>

        {addVisible && (
          <AddModal
            visible={addVisible}
            onReload={handleReload}
            onCancel={() => {
              setAddVisible(false);
            }}
            searchType={searchParams?.keywordType}
          />
        )}
      </Card>
    </div>
  );
};

export default Form.create()(Index);
