import React, { useRef, useState, useEffect } from 'react';
import { Form, Row, Col, Input, Select, message, But<PERSON>, Card } from 'antd';
import StandardTable from '@/components/StandardTable';
import { selectSignedOrderQuery } from './services';

const Index = ({ form, form: { getFieldDecorator, getFieldsValue } }) => {
  const [loading, setLoading] = useState(false);

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const serachParams = useRef({});

  let columns = [
    {
      title: '号码',
      dataIndex: 'phoneNum',
      width: 100,
      ellipsis: true,
    },

    {
      title: '签约类型',
      dataIndex: 'signType',
      width: 120,
      ellipsis: true,
    },

    {
      title: '签约状态',
      dataIndex: 'signState',
      width: 120,
      ellipsis: true,
      render: (v) => {
        const obj = {
          1: '待签入',
          2: '已签入',
          3: '签入失败',
          4: '待签出',
          5: '已签出',
          6: '签出失败',
        };
        return obj?.[v] || '--';
      },
    },
    {
      title: '签入时间',
      dataIndex: 'signInTime',
      width: 160,
      ellipsis: true,
    },
    {
      title: '签入原因',
      dataIndex: 'signInReason',
      width: 120,
      ellipsis: true,
    },
    {
      title: '签出时间',
      dataIndex: 'signOutTime',
      width: 160,
      ellipsis: true,
    },
    {
      title: '签出原因',
      dataIndex: 'signOutReason',
      width: 120,
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectSignedOrderQuery({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.status === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.dataList || [],
        pagination: {
          total: response?.data?.totalCount || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      setListData({
        list: [],
        pagination: {
          total: 0,
          current: 1,
          pageSize,
        },
      });
      message.error(response.msg);
    }
  };

  const handleSearch = () => {
    const formValues = getFieldsValue();
    getListDatas(formValues);
  };

  const handleReset = () => {
    form.resetFields();
    getListDatas();
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  useEffect(() => {
    getListDatas();
  }, []);

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="签约类型">
              {getFieldDecorator('signType')(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  {['主叫签约', '被叫签约'].map((item) => (
                    <Select.Option value={item}>{item}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>

          <Col span={12} style={{ textAlign: 'right' }}>
            <Button onClick={handleReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>
          </Col>
        </Row>
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </Card>
  );
};

export default Form.create()(Index);
