import React, { useRef, useState, useEffect } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, But<PERSON>, Card } from 'antd';
import LocalNetworkFormItem from '@/components/CommonFormItem/LocalNetworkFormItem';
import request from 'ponshine-request';
import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { v4 as uuidv4 } from 'uuid';
import { exportFile } from '@/utils/utils';
import moment from 'moment';

const { RangePicker } = DatePicker;

const Index = ({ form, form: { getFieldDecorator, validateFields } }) => {
  const initDate = [moment(), moment()];
  const initParams = {
    gmtCreateStart: initDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
    gmtCreateEnd: initDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
  };
  const localNetworkRef = useRef();
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [serachParams, setSearchParams] = useState({});

  let columns = [
    {
      title: '批次号',
      width: 100,
      dataIndex: 'batchId',
      ellipsis: true,
    },
    {
      title: '号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },

    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '接收时间',
      width: 120,
      dataIndex: 'acceptTime',
      ellipsis: true,
    },
    {
      title: '操作',
      width: 120,
      dataIndex: 'operateType',
      ellipsis: true,
    },
    // {
    //   title: '操作类型',
    //   width: 120,
    //   dataIndex: 'signType',
    //   ellipsis: true,
    // },
    {
      title: '类型',
      width: 120,
      dataIndex: 'phoneType',
      ellipsis: true,
    },

    {
      title: '操作结果',
      width: 100,
      dataIndex: 'operateResult',
      ellipsis: true,
    },
    {
      title: '操作信息',
      width: 100,
      dataIndex: 'operateInfo',
      ellipsis: true,
    },
    {
      title: '回调结果',
      width: 100,
      dataIndex: 'callbackResult',
      ellipsis: true,
    },
    {
      title: '回调信息',
      width: 100,
      dataIndex: 'callbackInfo',
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 180,
      dataIndex: 'operateTime',
      ellipsis: true,
    },
    {
      title: '签约类型',
      width: 120,
      dataIndex: 'signType',
      ellipsis: true,
    },
    {
      title: '签入签出原因',
      width: 120,
      dataIndex: 'modelType',
      ellipsis: true,
    },
    {
      title: '模型检出时间',
      width: 180,
      dataIndex: 'modelCheckTime',
      ellipsis: true,
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items?.map((item) => ({
          ...item,
          uuid: uuidv4(),
        })),
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      const { jsTime } = values;
      getListDatas({
        ...values,
        gmtCreateStart: jsTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
        gmtCreateEnd: jsTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
        jsTime: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();

    getListDatas({ localNetwork: localNetworkRef.current.getInitialValue() });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/external/exportDataCenterPhoneNum',
      decode: true,
      params: serachParams,
      method: 'POST',
    });
  };

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <LocalNetworkFormItem
              form={form}
              getListDatas={getListDatas}
              cref={localNetworkRef}
              // otherInitParams={initParams}
              defaultSearch={false}
            />
          </Col>
          <Col span={6}>
            <Form.Item label="接受时间">
              {getFieldDecorator('jsTime', {
                // initialValue: initDate,
              })(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作">
              {getFieldDecorator('operateType')(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  <Select.Option value={'签入'}>签入</Select.Option>
                  <Select.Option value={'签出'}>签出</Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作结果">
              {getFieldDecorator('operateResult')(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  <Select.Option value={'成功'}>成功</Select.Option>
                  <Select.Option value={'失败'}>失败</Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="回调结果">
              {getFieldDecorator('callbackResult')(
                <Select
                  placeholder="请选择"
                  allowClear
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  <Select.Option value={'成功'}>成功</Select.Option>
                  <Select.Option value={'失败'}>失败</Select.Option>
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="批次号">
              {getFieldDecorator('batchId')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={6} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>
            <Button onClick={onReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Button type="primary" onClick={handleExport}>
              导出
            </Button>
          </Col>
        </Row>
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="uuid"
        scroll={{
          x: 1000,
        }}
      />
    </Card>
  );
};

export default Form.create()(Index);
