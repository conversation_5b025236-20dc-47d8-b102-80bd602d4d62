import React from 'react';
import { Card, Tabs } from 'antd';
import SignInMoveOutManage from './SignInMoveOutManage';
import SMSActivelySignIn from './SMSActivelySignIn';
import SignInNumberManage from './SignInNumberManage';
import SignInOrderManage from './SignInOrderManage';
const { TabPane } = Tabs;

const Index = () => {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab="签入签出管理" key="1">
          <SignInMoveOutManage />
        </TabPane>
        <TabPane tab="活跃签入短信查询" key="2">
          <SMSActivelySignIn />
        </TabPane>
        <TabPane tab="签约号码管理" key="3">
          <SignInNumberManage />
        </TabPane>
        <TabPane tab="签约订单管理" key="4">
          <SignInOrderManage />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default Index;
