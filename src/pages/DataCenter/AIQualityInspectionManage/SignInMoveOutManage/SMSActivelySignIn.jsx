import React, { useRef, useState } from 'react';
import { Form, Row, Col, DatePicker, Input, Select, message, But<PERSON>, Card } from 'antd';
import StandardTable from '@/components/StandardTable';
import { selectSMSActivelySignIn } from './services';

import moment from 'moment';

const { RangePicker } = DatePicker;

const Index = ({ form, form: { getFieldDecorator, validateFields, getFieldsValue } }) => {
  const [loading, setLoading] = useState(false);

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const serachParams = useRef({});

  let columns = [
    {
      title: '告警号码',
      width: 100,
      dataIndex: 'phoneNum',
      ellipsis: true,
    },

    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '发送时间',
      width: 120,
      dataIndex: 'sendTime',
      ellipsis: true,
    },

    {
      title: '短信发送状态',
      width: 120,
      dataIndex: 'ifSend',
      ellipsis: true,
      render: (v) => {
        return v === 1 ? '已发送' : '未发送';
      },
    },
  ];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectSMSActivelySignIn({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      serachParams.current = props;
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum || 0,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    const formValues = getFieldsValue();
    const { phoneNum, sendTime } = formValues;

    if (!phoneNum && !sendTime) {
      message.warning('请至少选择号码或发送时间其中一项');
      return;
    }

    getListDatas({
      ...formValues,
      sendTimeStart: sendTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      sendTimeEnd: sendTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      sendTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  return (
    <Card>
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ marginBottom: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="发送时间">
              {getFieldDecorator('sendTime', {
                // initialValue: initDate,
              })(<RangePicker format={'YYYY-MM-DD'} />)}
            </Form.Item>
          </Col>

          <Col span={12} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
              查询
            </Button>
          </Col>
        </Row>
      </Form>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </Card>
  );
};

export default Form.create()(Index);
