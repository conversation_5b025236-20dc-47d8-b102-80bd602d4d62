import request from 'ponshine-request';
const apiPrefix = '/api/biz/modularity/project1752737891113331';

// 列表
export async function selectPage(params) {
  return request(`/api/hn/external/pageDataCenterPhoneNum`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 活跃签入短信查询
export async function selectSMSActivelySignIn(params) {
  return request(`/api/hn/external/pageAiMsgSendRecord`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 签约号码管理
export async function selectContractNumberQuery(params) {
  return request(`${apiPrefix}/contractNumberQuery`, {
    method: 'GET',
    params,
  });
}
// 签约订单管理
export async function selectSignedOrderQuery(params) {
  return request(`${apiPrefix}/signedOrderQuery`, {
    method: 'GET',
    params,
  });
}
