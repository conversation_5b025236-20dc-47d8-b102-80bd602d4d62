import { Modal, Form, Input, message } from 'antd';
import React, { useState } from 'react';
import request from 'ponshine-request';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const AddModal = Form.create()(
  ({ visible, cancel, form: { validateFields, getFieldDecorator, resetFields }, onReload }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const handleOk = () => {
      validateFields(async (err, fieldsValue) => {
        if (err) return;
        setConfirmLoading(true);
        const response = await request('/api/hn/aiKeyword/addKeyword', {
          data: fieldsValue,
          method: 'POST',
          requestType: 'form',
        });
        setConfirmLoading(false);
        if (response.code === 200) {
          message.success(response.message);
          cancel();
          onReload();
        } else {
          message.error(response.message);
        }
      });
    };

    return (
      <Modal
        title="关键字新增"
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        maskClosable={false}
        confirmLoading={confirmLoading}
      >
        <Form {...formItemLayout}>
          <Form.Item label="关键字">
            {getFieldDecorator('keyword', {
              rules: [
                {
                  required: true,
                  message: '请输入关键字',
                },
                {
                  max: 100,
                  message: '最多输入100个字符',
                },
              ],
            })(<Input placeholder="请输入" allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default AddModal;
