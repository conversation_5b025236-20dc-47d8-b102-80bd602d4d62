import React, { Fragment, useEffect, useState, useRef } from 'react';
import { Button, Card, message, Form, Modal, Input, DatePicker, Row, Col } from 'antd';

import StandardTable from '@/components/StandardTable';
import AddModal from './AddModal';

const { RangePicker } = DatePicker;

import { Licensee } from 'ponshine';
import request from 'ponshine-request';
import { exportFile } from '@/utils/utils';

const Index = (props) => {
  const {
    form: { getFieldsValue, resetFields, getFieldDecorator },
  } = props;

  const [selectedRows, setSelectedRows] = useState([]);
  const [addVisible, setAddVisible] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});

  const columns = [
    {
      title: '关键字',
      dataIndex: 'keyword',
      align: 'center',
      width: '30%',
      ellipsis: true,
    },
    {
      title: '操作人',
      dataIndex: 'inputUserName',
      align: 'center',
      width: '30%',
      ellipsis: true,
    },
    {
      title: '操作时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      width: '30%',
      ellipsis: true,
    },
  ];

  const findTableDataPager = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/aiKeyword/pageKeyword', {
      data: { pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };
  useEffect(() => {
    findTableDataPager();
  }, []);

  const handleTableChange = (pagination) => {
    findTableDataPager({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const handleSearch = () => {
    const formValue = getFieldsValue();
    const { optTime } = formValue;
    findTableDataPager({
      ...formValue,
      gmtCreateStart: optTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
      gmtCreateEnd: optTime?.[1]?.format('YYYY-MM-DD 23:59:59'),
      optTime: undefined,
    });
  };

  const handleReset = () => {
    resetFields();
    findTableDataPager();
    setSelectedRows([]);
  };

  const handleAdd = () => {
    setAddVisible(true);
  };

  const onSelectChange = (_selectRows) => {
    setSelectedRows(_selectRows);
  };

  const handleDelete = () => {
    if (!selectedRows?.length) {
      return message.info('请选择数据后删除');
    }
    Modal.confirm({
      title: '提示',
      content: '是否确认删除选中关键字',
      onOk: async () => {
        const response = await request('/api/hn/aiKeyword/deleteByIdList', {
          data: { idList: selectedRows?.map((ele) => ele.id)?.join() },
          method: 'POST',
          requestType: 'form',
        });
        if (response.code === 200) {
          message.success(response.message);
          handleReset();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/aiKeyword/exportKeyword',
      decode: true,
      params: { ...serachParams, pageNum: 1, pageSize: 1 }, //pageNum 和pageSize只为满足接口参数要求，没实际意义
      method: 'POST',
    });
  };

  return (
    <Card>
      <Form wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
        <Row gutter={[24]}>
          <Col span={6}>
            <Form.Item label="关键字">
              {getFieldDecorator('keyword')(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="操作时间">
              {getFieldDecorator('optTime')(
                <RangePicker allowClear format="YYYY-MM-DD" style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'right' }}>
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                查询
              </Button>
              <Button style={{ marginRight: 16 }} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleExport}>
                数据导出
              </Button>
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleAdd}>
                新建
              </Button>
              <Button type="danger" onClick={handleDelete} disabled={!selectedRows?.length}>
                删除
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <StandardTable
        // showSelectCount={false}
        // rowSelectionProps={false}
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        rowKey="id"
        loading={loading}
        onSelectRow={onSelectChange}
        selectedRows={selectedRows}
        otherRowSelectionProps={{
          columnWidth: '5%',
        }}
      />
      {addVisible && (
        <AddModal
          visible={addVisible}
          onReload={handleReset}
          cancel={() => {
            setAddVisible(false);
          }}
        />
      )}
    </Card>
  );
};

export default Form.create()(Index);
