import React, { useMemo, useState, useEffect, Fragment } from 'react';
import { Card, Form, Row, Col, Select, Input, Button, Tooltip, message } from 'antd';

import { exportFile } from '@/utils/utils';
import StandardTable from '@/components/StandardTable';
import AddBlock from './components/addBlock';
import ExportApprove from '@/components/ExportApprove';

import request from 'ponshine-request';
import { Licensee } from 'ponshine';
import moment from 'moment';

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldValue, setFieldsValue, validateFields } = form;
  const [pageType, setPageType] = useState('init');
  const formItemLayout = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 18,
    },
  };
  const [listData, setListData] = useState({});
  const [visible, setvisible] = useState(false);
  const [FormData, setFormData] = useState({});
  const [row, setRow] = useState({});

  const [loading, setloading] = useState(false);
  const [sortedInfo, setSortedInfo] = useState({});

  const handleTableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
    const newData = { ...listData };
    newData.pagination.current = pagination?.current || 1;
    newData.pagination.pageSize = pagination?.pageSize || 10;
    setListData(newData);
  };
  const getListDatas = (pagination) => {
    validateFields((error, values) => {
      if (error) return;
      setloading(true);

      setSortedInfo({});

      request({
        url: '/api/hn/terminalInfo/queryTerminalInformation',
        method: 'POST',
        requestType: 'json',
        data: {
          queryType: values?.queryType,
          param: values?.param,
        },
      }).then((res) => {
        setloading(false);
        if (res?.code == 200) {
          setListData({
            list: res?.data || [],
            pagination: {
              current: pagination?.current || 1,
              pageSize: pagination?.pageSize || 10,
              total: res?.data?.length || 0,
            },
          });
        } else {
          message.error(res?.message);
          setListData({
            list: [],
            pagination: {
              current: 1,
              pageSize: 10,
              total: 0,
            },
          });
        }
      });
    });
  };
  const onExport = () => {
    const values = form.getFieldsValue();
    exportFile({
      urlAPi: '/api/hn/terminalInfo/exportTerminalInformation',
      decode: true,
      params: { queryType: values?.queryType, param: values?.param },
      method: 'POST',
    });
  };
  const detailColumns = [
    { dataIndex: 'localNetwork', title: '本地网', width: 70 },
    { dataIndex: 'accNbr', title: '号码', width: 100 },
    { dataIndex: 'mktResInstNbr', title: 'MEID', width: 125 },
    { dataIndex: 'mktResInstNbrType', title: 'MEID类型', width: 85 },
    { dataIndex: 'regImei1', title: 'IMEI1', width: 125 },
    { dataIndex: 'regImei1Type', title: 'IMEI1类型', width: 85 },
    { dataIndex: 'regImei2', title: 'IMEI2', width: 125 },
    { dataIndex: 'regImei2Type', title: 'IMEI2类型', width: 85 },
    { dataIndex: 'regBrandName', title: '品牌', width: 80 },
    { dataIndex: 'regTermModel', title: '终端型号', width: 80 },
    { dataIndex: 'minRegTime', title: '最早注册时间', width: 170 },
    { dataIndex: 'lastRegTime', title: '最近注册时间', width: 170 },
  ];
  let columns = [
    {
      title: '本地网',
      width: 80,
      ellipsis: true,
      dataIndex: 'localNetwork',
      align: 'center',
    },
    {
      title: '号码',
      width: 80,
      dataIndex: 'accNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'accNbr' && sortedInfo.order,
    },
    {
      title: 'MEID',
      width: 100,
      dataIndex: 'mktResInstNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'mktResInstNbr' && sortedInfo.order,
      render: (v, row) => {
        const t = v || '--';
        return (
          <Tooltip title={t} placement="topLeft">
            <a
              onClick={() => {
                setPageType('addRowBlock');
                setvisible(true);
                setRow({
                  meiType: 'meid',
                  mei: v,
                });
              }}
            >
              {t}
            </a>
          </Tooltip>
        );
      },
    },
    {
      title: 'MEID类型',
      align: 'center',
      width: 80,
      dataIndex: 'mktResInstNbrType',
    },
    {
      title: 'IMEI1',
      width: 100,
      dataIndex: 'regImei1',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'regImei1' && sortedInfo.order,
      render: (v, row) => {
        const t = v || '--';
        return (
          <Tooltip title={t} placement="topLeft">
            <a
              onClick={() => {
                setPageType('addRowBlock');
                setvisible(true);
                setRow({
                  meiType: 'mei1',
                  mei: v,
                });
              }}
            >
              {t}
            </a>
          </Tooltip>
        );
      },
    },
    {
      title: 'IMEI1类型',
      width: 80,
      dataIndex: 'regImei1Type',
      align: 'center',
    },
    {
      title: 'IMEI2',
      width: 100,
      dataIndex: 'regImei2',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'regImei2' && sortedInfo.order,
      render: (v) => {
        const t = v || '--';
        return (
          <Tooltip title={t} placement="topLeft">
            <a
              onClick={() => {
                setPageType('addRowBlock');
                setvisible(true);
                setRow({
                  meiType: 'mei2',
                  mei: v,
                });
              }}
            >
              {t}
            </a>
          </Tooltip>
        );
      },
    },
    {
      title: 'IMEI2类型',
      width: 80,
      dataIndex: 'regImei2Type',
      align: 'center',
    },
    {
      title: '品牌',
      width: 100,
      dataIndex: 'regBrandName',
      align: 'center',
    },
    {
      title: '终端型号',
      width: 80,
      dataIndex: 'regTermModel',
      align: 'center',
    },
    {
      title: '最早注册时间',
      width: 200,
      dataIndex: 'minRegTime',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'minRegTime' && sortedInfo.order,
    },
    {
      title: '最近注册时间',
      width: 200,
      dataIndex: 'lastRegTime',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'lastRegTime' && sortedInfo.order,
    },
  ];

  const getCurrentPageData = useMemo(() => {
    let currentData = [...(listData.list || [])];

    if (sortedInfo.columnKey && sortedInfo.order) {
      currentData.sort((a, b) => {
        const order = sortedInfo.order === 'ascend' ? 1 : -1;

        switch (sortedInfo.columnKey) {
          case 'accNbr':
            return order * (Number(a.accNbr || '0') - Number(b.accNbr || '0'));
          case 'mktResInstNbr':
            return order * (Number(a.mktResInstNbr || '0') - Number(b.mktResInstNbr || '0'));
          case 'regImei1':
            return order * (Number(a.regImei1 || '0') - Number(b.regImei1 || '0'));
          case 'regImei2':
            return order * (Number(a.regImei2 || '0') - Number(b.regImei2 || '0'));
          case 'minRegTime':
            return order * (moment(a.minRegTime).valueOf() - moment(b.minRegTime).valueOf());
          case 'lastRegTime':
            return order * (moment(a.lastRegTime).valueOf() - moment(b.lastRegTime).valueOf());
          default:
            return 0;
        }
      });
    }

    const { current = 1, pageSize = 10 } = listData.pagination || {};
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return currentData.slice(start, end);
  }, [listData.list, listData.pagination, sortedInfo]);

  useEffect(() => {
    setFieldsValue({
      param: undefined,
    });
  }, [getFieldValue('queryType')]);
  useEffect(() => {
    if (!visible) {
      setRow({});
    }
  }, [visible]);

  return (
    <div style={{ position: 'relative' }}>
      {
        <div id="create_pdf">
          <Card>
            <Row>
              <Form labelCol={{ span: 6 }} wrapperCol={{ span: 17 }} layout={'horizontal'}>
                <Col span={6}>
                  <Form.Item label={'查询类型'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('queryType', {
                      initialValue: '1',
                      rules: [{ required: true, message: '查询类型为必选' }],
                    })(
                      <Select
                        placeholder="请选择"
                        size="default"
                        allowClear={true}
                        autoFocus={false}
                        showArrow={true}
                        showSearch={false}
                        getPopupContainer={(triggerNode) => triggerNode.parentElement}
                      >
                        <Select.Option key={'1'} value={'1'}>
                          号码
                        </Select.Option>
                        <Select.Option key={'2'} value={'2'}>
                          终端串码
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item wrapperCol={{ span: 23 }} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('param', {
                      rules:
                        props.form.getFieldValue('queryType') == '2'
                          ? [
                              { required: true, message: '模糊查询至少包含14位' },
                              { min: 14, message: '模糊查询至少包含14位' },
                            ]
                          : [{ required: true, message: '查询条件必填' }],
                    })(
                      <Input
                        disabled={false}
                        size="default"
                        allowClear={true}
                        placeholder="请输入"
                      />,
                    )}
                  </Form.Item>
                </Col>
              </Form>

              <Col align="left" span={14}>
                <Button
                  type="primary"
                  style={{ marginRight: 10, marginTop: 4 }}
                  onClick={getListDatas}
                >
                  搜索
                </Button>
                <Licensee license="oneBreakBlack">
                  <Button
                    type="primary"
                    style={{ marginRight: 10, marginTop: 4 }}
                    onClick={() => {
                      validateFields((error, values) => {
                        if (error) return;
                        setPageType('addBlock');
                        setvisible(true);
                        setFormData(values);
                      });
                    }}
                  >
                    一键加黑
                  </Button>
                </Licensee>
                {/* <Button type="primary" style={{ marginTop: 4 }} onClick={onExport}>
                  数据导出
                </Button> */}
                <ExportApprove
                  exportParams={{
                    urlAPi: '/api/hn/terminalInfo/exportTerminalInformation',
                    decode: true,
                    params: form.getFieldsValue(),
                    method: 'POST',
                  }}
                  moduleTile="终端管理"
                />
              </Col>
            </Row>
          </Card>
          <Card>
            <StandardTable
              columns={columns}
              detailColumns={detailColumns.map((item) => {
                return {
                  ...item,
                  key: item?.dataIndex,
                  ellipsis: true,
                  width: item.width,
                  align: 'center',
                  render: columns?.find((ele) => item.dataIndex === ele.dataIndex)?.render,
                  sorter: columns?.find((ele) => item.dataIndex === ele.dataIndex)?.sorter,
                  sortOrder: columns?.find((ele) => item.dataIndex === ele.dataIndex)?.sortOrder,
                };
              })}
              data={{
                list: getCurrentPageData,
                pagination: listData.pagination,
              }}
              onChange={handleTableChange}
              tools={true}
              multiple={true}
              loading={loading}
              showSelectCount={false}
              rowSelectionProps={false}
              rowKey="id"
            />
          </Card>
          {visible && (
            <div>
              <AddBlock
                search={getListDatas}
                meiType={row?.meiType}
                mei={row?.mei}
                FormData={FormData}
                setPageType={setPageType}
                visible={visible}
                pageType={pageType}
                setvisible={setvisible}
              />
            </div>
          )}
        </div>
      }
    </div>
  );
};
export default Form.create({})(Index);
