import React, { useEffect, useState } from 'react';
import {
  Card,
  Button,
  Row,
  Tooltip,
  Form,
  Select,
  Input,
  Divider,
  Col,
  Modal,
  message,
} from 'antd';
import StandardTable from '@/components/StandardTable';
import request from 'ponshine-request';
import { exportFile } from '@/utils/utils';
function addBlock(props) {
  const { setPageType, pageType, form, meiType, mei, visible, setvisible, FormData, search } =
    props;
  const { validateFields, getFieldDecorator, getFieldsValue } = form;
  useEffect(() => {
    if (visible) {
      setselectedRows([]);
    }
  }, [visible]);
  const [selectedRows, setselectedRows] = useState([]);
  const handleSelectRows = (newSelectedRows) => {
    setselectedRows(newSelectedRows);
  };
  let columns = [
    {
      title: '号码',
      width: 80,
      dataIndex: 'accNbr',

      align: 'center',
      ellipsis: true,
    },
    {
      title: 'MEID',
      width: 90,
      dataIndex: 'mktResInstNbr',

      align: 'center',
      ellipsis: true,
    },

    {
      title: 'IMEI1',
      width: 90,
      dataIndex: 'regImei1',
      align: 'center',

      ellipsis: true,
    },

    {
      title: 'IMEI2',
      width: 90,
      dataIndex: 'regImei2',
      align: 'center',

      ellipsis: true,
    },
    {
      title: '最早注册时间',
      width: 90,
      dataIndex: 'minRegTime',
      align: 'center',

      ellipsis: true,
    },
    {
      title: '最近注册时间',
      width: 90,
      dataIndex: 'lastRegTime',
      align: 'center',

      ellipsis: true,
    },
  ];

  const [listData, setListData] = useState({});
  const handleTableChange = (pagination) => {
    getListDatas({
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };
  useEffect(() => {
    getListDatas();
  }, []);
  const getListDatas = (pagination) => {
    const data =
      pageType == 'addBlock'
        ? {
            param: FormData?.param,
            queryType: FormData?.queryType,
          }
        : {
            meiType: meiType,
            mei: mei,
          };
    request({
      url:
        pageType == 'addBlock'
          ? '/api/hn/terminalInfo/queryTerminalInformation'
          : '/api/hn/terminalInfo/queryTerminalInfoDetails',
      method: 'POST',
      requestType: 'json',
      data: data,
    }).then((res) => {
      if (res?.code == 200) {
        setListData({
          list: res?.data || [],
          pagination: {
            ...pagination,
            total: res?.data?.length || 0,
          },
        });
      } else {
        setListData({
          list: [],
          pagination: {
            ...pagination,
            total: 0,
          },
        });
      }
    });
  };
  const onSubmit = () => {
    validateFields((error, values) => {
      if (error) return;
      const data = {
        blackReason: values?.blackReason,
        riskLevel: values?.riskLevel,
        list: selectedRows.map((item) => {
          return {
            mktResInstNbr: item?.mktResInstNbr,
            regImei1: item?.regImei1,
            regImei2: item?.regImei2,
          };
        }),
      };
      request({
        url: '/api/hn/terminalInfo/oneBreakBlack',
        method: 'POST',
        requestType: 'json',
        data: data,
      }).then((res) => {
        if (res?.code == 200) {
          setvisible(false);
          search();
          message.success(res?.message);
          if (res?.data?.hasFail) {
            Modal.confirm({
              title: '提示',
              content: '存在错误数据，请点击下载！',
              onOk: () => {
                exportFile({
                  urlAPi: '/api/hn/terminalInfo/exportOneBreakBlackError',
                  decode: true,
                  params: {},
                  method: 'POST',
                });
              },
            });
          }
        } else {
          message.error(res?.message);
        }
      });
    });
  };
  return (
    <div>
      <Modal
        title={pageType == 'addBlock' ? '一键加黑' : '终端加黑'}
        visible={visible}
        width={1000}
        onCancel={() => {
          setvisible(false);
        }}
        footer={
          <>
            <Button
              onClick={() => {
                setvisible(false);
              }}
            >
              取消
            </Button>
            <Button
              disabled={selectedRows?.length == 0}
              type="primary"
              style={{ marginLeft: 4 }}
              onClick={() => {
                onSubmit();
              }}
            >
              确定
            </Button>
          </>
        }
      >
        <Row>
          <Button
            type="primary"
            style={{ marginBottom: 16 }}
            onClick={() => {
              exportFile({
                urlAPi:
                  pageType == 'addBlock'
                    ? '/api/hn/terminalInfo/exportTerminalInformation'
                    : '/api/hn/terminalInfo/exportTerminalInfoDetails',
                decode: true,
                params:
                  pageType == 'addBlock'
                    ? {
                        param: FormData?.param,
                        queryType: FormData?.queryType,
                      }
                    : { meiType, mei },
                method: 'POST',
              });
            }}
          >
            导出
          </Button>
        </Row>
        <Row>
          <StandardTable
            // isNeedAutoWidth={true}
            columns={
              pageType == 'addBlock'
                ? [
                    {
                      title: '本地网',
                      width: 60,
                      dataIndex: 'localNetwork',
                      render: (v) => {
                        const t = v || '--';
                        return (
                          <Tooltip title={t} placement="topLeft">
                            <span>{t}</span>
                          </Tooltip>
                        );
                      },
                      align: 'center',
                      ellipsis: true,
                    },
                    ...columns,
                  ]
                : columns
            }
            data={listData}
            onChange={handleTableChange}
            multiple={true}
            loading={false}
            selectedRows={selectedRows}
            onSelectRow={handleSelectRows}
            rowKey="id"
            scroll={{
              // x: 1000,
              y: 500,
            }}
          />
        </Row>
        <Row>
          <Form>
            <Form.Item label={'风险级别'} labelCol={{ span: 3 }} wrapperCol={{ span: 7 }}>
              {getFieldDecorator('riskLevel', {
                initialValue: '1',
                rules: [{ required: true, message: '风险级别必选' }],
              })(
                <Select
                  mode=""
                  size="default"
                  allowClear={true}
                  autoFocus={false}
                  showArrow={true}
                  showSearch={false}
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                >
                  <Select.Option key={'1'} value={'1'}>
                    高风险
                  </Select.Option>
                  <Select.Option key={'0'} value={'0'}>
                    低风险
                  </Select.Option>
                </Select>,
              )}
            </Form.Item>
            <Form.Item label={'加黑原因'} labelCol={{ span: 3 }} wrapperCol={{ span: 7 }}>
              {getFieldDecorator('blackReason', {
                rules: [
                  { required: true, message: '加黑原因必填' },
                  { max: 200, message: '文本，限制200字以内' },
                ],
              })(<Input placeholder=""></Input>)}
            </Form.Item>
          </Form>
          <div style={{ color: '#ff0000' }}>
            注：录入原因必须包含时间、关联号码、描述，缺乏关键信息将不予审批通过，样例如: 7.6
            XXXXXXXXXXX外呼核验涉诈
          </div>
        </Row>
      </Modal>
    </div>
  );
}
export default Form.create()(addBlock);
