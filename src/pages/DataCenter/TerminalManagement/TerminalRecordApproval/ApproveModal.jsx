import React, { useState } from 'react';
import { Modal, Form, Select, Input } from 'antd';
import request from 'ponshine-request';

const ApproveModal = ({
  form: {
    getFieldDecorator,
    getFieldsValue,
    getFieldValue,
    setFieldsValue,
    resetFields,
    validateFields,
  },
  onOk,
  onCancel,
  visible,
}) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const handleOk = () => {
    validateFields((error, values) => {
      if (error) return;
      setConfirmLoading(true);
      onOk(values, () => {
        setConfirmLoading(false);
      });
    });
  };

  const handleChange = (v) => {
    setFieldsValue({
      approvalOpinion: v,
    });
  };

  return (
    <Modal
      title={'终端名单审批'}
      visible={visible}
      width={700}
      onOk={handleOk}
      onCancel={onCancel}
      okText={'确认'}
      cancelText="取消"
      afterClose={() => {
        resetFields();
      }}
      maskClosable={false}
      confirmLoading={confirmLoading}
    >
      <div>
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 15 }}>
          <Form.Item label={'审批结果'}>
            {getFieldDecorator('approvalResult', {
              rules: [
                {
                  required: true,
                  message: '请选择审批结果',
                },
              ],
            })(
              <Select placeholder="请选择" onChange={handleChange}>
                {['审批通过', '审批驳回'].map((ele) => (
                  <Select.Option value={ele} key={ele}>
                    {ele}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label={'审批意见'}>
            {getFieldDecorator('approvalOpinion', {
              initialValue: getFieldValue('approvalResult'),
              rules: [
                {
                  required: true,
                  message: '请选择审批意见',
                },
              ],
            })(<Input.TextArea placeholder="请填写"></Input.TextArea>)}
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default Form.create()(ApproveModal);
