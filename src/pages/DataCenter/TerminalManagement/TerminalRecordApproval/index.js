import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import { Card, Form, Row, Col, DatePicker, Input, Button, Modal, message, Select } from 'antd';
import { Licensee, useLicensee } from 'ponshine';
import 'antd/dist/antd.css';
import { exportFile, getReloadTableWithFilterPageNum } from '@/utils/utils';

import StandardTable from '@/components/VirtualStandardTable';
import ApproveModal from './ApproveModal';
import { VTComponents } from 'virtualizedtableforantd';

const { RangePicker } = DatePicker;
import request from 'ponshine-request';

import { getLocalNetwork } from '@/services/common';

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const Index = (props) => {
  const { form } = props;
  const {
    getFieldDecorator,
    getFieldsValue,
    getFieldValue,
    setFieldsValue,
    resetFields,
    validateFields,
  } = form;
  const searchParams = useRef();
  const [data, setdata] = useState({});
  const [loading, setloading] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [approveVisible, setApproveVisible] = useState(false);
  const [terminalTagData, setTerminalTagData] = useState([]);
  const localNetworkData = useRef();
  const initialParams = useRef();

  let columns = [
    {
      title: '终端串号',
      width: 140,
      dataIndex: 'mei',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端类型',
      // width: 100,
      dataIndex: 'terminalType',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端标签',
      // width: 100,
      dataIndex: 'terminalTag',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '状态',
      // width: 100,
      dataIndex: 'approvalState',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '变更类型',
      // width: 100,
      dataIndex: 'changeType',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端关联号码数',
      // width: 110,
      dataIndex: 'relatedPhoneCountString',
      align: 'center',
      ellipsis: true,
      render: (text) => {
        return (
          <div style={Number(text) >= 200 ? { background: 'red', color: '#fff' } : {}}>{text}</div>
        );
      },
    },
    {
      title: '录入原因',
      width: 150,
      dataIndex: 'reason',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请人',
      // width: 100,
      dataIndex: 'inputUserName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
      align: 'center',
      ellipsis: true,
    },
  ];

  useEffect(() => {
    getLocalNetworkData();
  }, []);

  const getLocalNetworkData = async () => {
    const response = await getLocalNetwork();
    if (response.code === 200) {
      localNetworkData.current = response?.data?.map((ele) => ele.name) || [];
      if (response?.data?.length === 1) {
        initialParams.current = {
          terminalType: '2',
          terminalTag: localNetworkData?.current?.[0],
        };
        setTerminalTagData(localNetworkData?.current);
        getListDatas(initialParams.current);
      } else {
        getListDatas();
      }
    } else {
      localNetworkData.current = [];
      message.error(response?.message);
    }
  };
  const onSelectRow = (selectedRows) => {
    setSelectedRows(selectedRows);
  };

  const getListDatas = ({ pageNum = 1, pageSize = 200, ...props } = {}) => {
    setloading(true);
    const params = {
      pageNum,
      pageSize,
      ...props,
    };
    request('/api/hn/terminal/workOrder/pageTerminalWorkOrder', {
      method: 'POST',
      data: params,
      requestType: 'json',
    })
      .then((res) => {
        if (res?.code == 200) {
          searchParams.current = params;
          setdata({
            list: res?.data?.items || [],
            pagination: {
              pageSize,
              current: pageNum || 1,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setdata({
            list: [],
            pagination: {
              total: 0,
            },
          });
        }
      })
      .finally(() => {
        setloading(false);
      });
  };

  const handleSearch = () => {
    const values = getFieldsValue();
    const { date } = values;
    getListDatas({
      ...values,
      startTime: date?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endTime: date?.[1]?.format('YYYY-MM-DD 23:59:59'),
      date: undefined,
    });
  };

  const handleReset = () => {
    if (!initialParams.current) {
      setTerminalTagData([]);
    }
    resetFields();
    getListDatas(initialParams.current || {});
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams.current,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/hn/terminal/workOrder/exportTerminalWorkOrder',
      decode: true,
      params: { ...searchParams.current, pageNum: 1, pageSize: 200 },
      method: 'POST',
    });
  };

  const handleChangeTerminalType = (v) => {
    setFieldsValue({
      terminalTag: undefined,
    });
    if (v) {
      if (v === '1') {
        setTerminalTagData(['集团', '省内']);
      } else {
        setTerminalTagData(localNetworkData?.current);
      }
    } else {
      setTerminalTagData([]);
    }
  };

  const handleApprove = () => {
    if (!selectedRows.length) {
      message.warning('至少勾选一项');
      return;
    }
    if (!selectedRows.every((ele) => ele.terminalType === selectedRows[0]?.terminalType)) {
      return message.warning('请选择同一种终端类型审批！');
    }
    setApproveVisible(true);
  };

  const handleApproveOk = (formValues, callback) => {
    request('/api/hn/terminal/workOrder/approveTerminal', {
      method: 'POST',
      data: {
        idList: selectedRows.map((item) => item?.id),
        ...formValues,
      },
      requestType: 'json',
    }).then((res) => {
      if (res?.code == 200) {
        setApproveVisible();
        reloadWidthParams();
        callback && callback();
        message.success(res?.message);
      } else {
        message.error(res?.message);
      }
    });
  };

  const reloadWidthParams = () => {
    const {
      list,
      pagination: { total, pageSize, current },
    } = data;
    getListDatas({
      ...searchParams.current,
      pageNum: getReloadTableWithFilterPageNum({
        total,
        pageSize,
        current,
        listLength: list.length,
        selectedRowsLength: selectedRows?.length,
      }),
    });
    setSelectedRows([]);
  };
  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Card>
          <Row>
            <Col>
              <Form layout={'horizontal'} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                <Col span={6}>
                  <Form.Item label={'终端串号'}>
                    {getFieldDecorator('mei')(<Input placeholder="请输入" allowClear />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'终端类型'}>
                    {getFieldDecorator('terminalType', {
                      initialValue: initialParams?.current?.terminalType,
                    })(
                      <Select
                        allowClear
                        placeholder="请选择"
                        onChange={handleChangeTerminalType}
                        disabled={localNetworkData?.current?.length > 1 ? false : true}
                      >
                        <Select.Option value={'1'} key={1}>
                          黑终端
                        </Select.Option>
                        <Select.Option value={'2'} key={2}>
                          白终端
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'终端标签'}>
                    {getFieldDecorator('terminalTag', {
                      initialValue: initialParams?.current?.terminalTag,
                    })(
                      <Select
                        placeholder="请选择"
                        disabled={localNetworkData?.current?.length > 1 ? false : true}
                        allowClear
                      >
                        {terminalTagData?.map((ele, index) => (
                          <Select.Option value={ele} key={index}>
                            {ele}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item label={'变更类型'}>
                    {getFieldDecorator('changeType')(
                      <Select allowClear placeholder="请选择">
                        <Select.Option value={'1'} key={1}>
                          新增
                        </Select.Option>
                        <Select.Option value={'2'} key={2}>
                          删除
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'申请人'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('inputUserName')(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'申请时间'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('date', {
                      // initialValue: [
                      //   moment(moment(new Date()).startOf('day'), 'YYYY-MM-DD'),
                      //   moment(moment(new Date()), 'YYYY-MM-DD'),
                      // ],
                    })(
                      <RangePicker
                        style={{
                          width: '100%',
                        }}
                        allowClear
                        getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      />,
                    )}
                  </Form.Item>
                </Col>
              </Form>
            </Col>
            <Col align="right" span={12}>
              <Licensee license="workOrder_page">
                <Button type={'primary'} style={{ marginRight: 10 }} onClick={handleSearch}>
                  查询
                </Button>
                <Button style={{ marginRight: 10 }} onClick={handleReset}>
                  重置
                </Button>
              </Licensee>

              <Licensee license="workOrder_approval">
                <Button type={'primary'} style={{ marginRight: 10 }} onClick={handleApprove}>
                  审批
                </Button>
              </Licensee>
              <Licensee license="workOrder_export">
                <Button
                  type={'primary'}
                  onClick={() => {
                    handleExport();
                  }}
                >
                  数据导出
                </Button>
              </Licensee>
            </Col>
          </Row>
        </Card>
        <Card>
          <StandardTable
            scroll={{ y: 600 }}
            columns={columns}
            data={{
              list: data?.list || [],
              pagination: {
                ...data?.pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                defaultPageSize: 200,
                pageSizeOptions: ['10', '20', '30', '50', '200', '1000'],
              },
            }}
            onChange={handleTableChange}
            loading={loading}
            showSelectCount={true}
            rowSelectionProps={true}
            selectedRows={selectedRows}
            rowKey="id"
            onSelectRow={onSelectRow}
            components={VTComponents({
              id: 1001,
            })}
          />
        </Card>

        {approveVisible && (
          <ApproveModal
            visible={approveVisible}
            onOk={handleApproveOk}
            onCancel={() => {
              setApproveVisible(false);
            }}
          />
        )}
      </div>
    </div>
  );
};
export default Form.create({})(Index);
