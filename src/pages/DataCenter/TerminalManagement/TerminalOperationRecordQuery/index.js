import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Table,
  DatePicker,
  Input,
  Button,
  Modal,
  Tooltip,
  message,
  Icon,
  Select,
} from 'antd';
import { Licensee, useLicensee } from 'ponshine';
import 'antd/dist/antd.css';
import { exportFile } from '@/utils/utils';

// import StandardTable from "./StandardTable";
import StandardTable from '@/components/StandardTable';
import ExportApprove from '@/components/ExportApprove';
import moment from 'moment';
const { RangePicker } = DatePicker;
import styles from './index.less';
// import request from "umi-request";
import request from 'ponshine-request';

const terminalTagStr =
  '长沙本地网、岳阳本地网、株洲本地网、湘潭本地网、娄底本地网、郴州本地网、永州本地网、益阳本地网、怀化本地网、邵阳本地网、吉首本地网、衡阳本地网、张家界本地网、常德本地网';
const terminalTagArr = terminalTagStr.split('、');
const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const formItemLayout = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 18,
    },
  };
  useEffect(() => {
    getListDatas();
  }, []);
  const [selectedRows, setSelectedRows] = useState([]);
  const onSelectRow = (selectedRows) => {
    setSelectedRows(selectedRows);
  };
  const [data, setdata] = useState({});
  const [loading, setloading] = useState(false);
  const getListDatas = (
    pagination = {
      pageNum: 1,
      pageSize: 10,
    },
  ) => {
    setloading(true);
    const values = form.getFieldsValue();
    let data = {
      ...values,
      startTime:
        values?.startTime && values?.startTime[0]
          ? values?.startTime[0].format('YYYY-MM-DD 00:00:00')
          : undefined,
      endTime:
        values?.startTime && values?.startTime[1]
          ? values?.startTime[1].format('YYYY-MM-DD 23:59:59')
          : undefined,
      ...pagination,
    };
    request('/api/hn/terminal/workOrder/pageTerminalLog', {
      method: 'POST',
      data,
      requestType: 'json',
    })
      .then((res) => {
        if (res?.code == 200) {
          setdata({
            list: res?.data?.items || [],
            pagination: {
              ...pagination,
              current: pagination?.pageNum || 1,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setdata({
            list: [],
            pagination: {
              ...pagination,
              current: pagination?.pageNum || 1,
              total: 0,
            },
          });
        }
      })
      .finally(() => {
        setloading(false);
      });
  };
  function handleDelete(row) {
    if (selectedRows.length == 0) {
      message.error('提示至少勾选一个数据');
      return;
    }
    setdeleteVisible(true);
  }
  const handleTableChange = (pagination) => {
    getListDatas({
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };
  const [VisibleknVmkzhLMf, setVisibleknVmkzhLMf] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [deleteVisible, setdeleteVisible] = useState(false);
  const relationClickknVmkzhLMf = () => {
    setAddModalVisible(true);
    // setVisibleknVmkzhLMf(true);
    // setuploadTitle('信息上传');
  };
  let columns = [
    {
      title: '终端串号',
      width: 130,
      dataIndex: 'mei',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端类型',
      width: 70,
      dataIndex: 'terminalType',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端标签',
      width: 70,
      dataIndex: 'terminalTag',
      ellipsis: true,

      align: 'center',
    },
    {
      title: '风险级别',
      width: 70,
      dataIndex: 'riskLevel',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '状态',
      width: 70,
      dataIndex: 'approvalState',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '变更类型',
      width: 65,
      dataIndex: 'changeType',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端关联号码数',
      width: 110,
      dataIndex: 'relatedPhoneCountString',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '原因',
      width: 55,
      dataIndex: 'reason',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '审批人',
      width: 65,
      dataIndex: 'approvalUserName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '审批意见',
      width: 75,
      dataIndex: 'approvalOpinion',
      align: 'center',

      ellipsis: true,
    },
    {
      title: '审批时间',
      width: 135,
      dataIndex: 'approvalTime',
      align: 'center',

      ellipsis: true,
    },

    {
      title: '申请人',
      width: 60,
      dataIndex: 'inputUserName',
      align: 'center',

      ellipsis: true,
    },
    {
      title: '申请时间',
      width: 140,
      dataIndex: 'createTime',
      align: 'center',

      ellipsis: true,
    },
  ];
  columns = [...columns];
  const [record, setRecord] = useState({});
  const [uploadTitle, setuploadTitle] = useState('');
  useEffect(() => {
    if (!VisibleknVmkzhLMf) {
      setRecord({});
    }
  }, [VisibleknVmkzhLMf]);
  // const lists = [{"id":1,"name":"关键词","type":"类型","time":"2022-01-11","title":"XXXXXXX","level":"一级","upLoadMan":"IKUN","upLoadTime":"2020-1-1"}];
  const handleExport = () => {
    const values = form.getFieldsValue();
    let newdata = {
      ...values,
      startTime:
        values?.startTime && values?.startTime[0]
          ? values?.startTime[0].format('YYYY-MM-DD 00:00:00')
          : undefined,
      endTime:
        values?.startTime && values?.startTime[1]
          ? values?.startTime[1].format('YYYY-MM-DD 23:59:59')
          : undefined,
      pageNum: data?.pageNum || 1,
      pageSize: data?.pageSize || 10,
    };
    exportFile({
      urlAPi: '/api/hn/terminal/workOrder/exportTerminalLog',
      decode: true,
      params: newdata,
      method: 'POST',
      ErrorInfo: true,
    });
  };
  const getInfo = (row) => {
    request('/api/hn/document/getDetailById', {
      method: 'POST',
      data: {
        id: row.id,
      },
      requestType: 'form',
    }).then((res) => {
      if (res?.code == 200) {
        setRecord({ ...row, ...res.data });
        setVisibleknVmkzhLMf(true);
      } else {
        setRecord({});
        message.error(res?.message);
      }
    });
  };
  let x = 0;
  columns.forEach((item) => {
    x += item.width;
  });
  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Card>
          <Row>
            <Col>
              <Form layout={'horizontal'} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                <Col span={6}>
                  <Form.Item label={'终端串号'}>
                    {getFieldDecorator('mei')(<Input placeholder="请输入" allowClear />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'状态'}>
                    {getFieldDecorator('approvalState')(
                      <Select allowClear placeholder="请选择">
                        <Select.Option value={'1'} key={1}>
                          待审批
                        </Select.Option>
                        <Select.Option value={'2'} key={2}>
                          审批通过
                        </Select.Option>
                        <Select.Option value={'3'} key={3}>
                          审批驳回
                        </Select.Option>
                        <Select.Option value={'4'} key={4}>
                          待地市审核员审批
                        </Select.Option>
                        <Select.Option value={'5'} key={5}>
                          待省公司审批
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'终端类型'}>
                    {getFieldDecorator('terminalType')(
                      <Select allowClear placeholder="请选择">
                        <Select.Option value={'1'} key={1}>
                          黑终端
                        </Select.Option>
                        <Select.Option value={'2'} key={2}>
                          白终端
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'终端标签'}>
                    {getFieldDecorator('terminalTag')(
                      <Select allowClear placeholder="请选择">
                        {(getFieldsValue().terminalType != '1' &&
                        getFieldsValue().terminalType != '2'
                          ? []
                          : getFieldsValue().terminalType == '1'
                          ? ['集团', '省内']
                          : terminalTagArr
                        ).map((item) => {
                          return <Select.Option value={item}>{item}</Select.Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'变更类型'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('changeType')(
                      <Select allowClear placeholder="请选择">
                        <Select.Option value={'1'} key={1}>
                          新增
                        </Select.Option>
                        <Select.Option value={'2'} key={2}>
                          删除
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'申请人'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('inputUserName')(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'申请时间'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('startTime', {
                      // initialValue: [
                      //   moment(moment(new Date()).startOf('day'), 'YYYY-MM-DD'),
                      //   moment(moment(new Date()), 'YYYY-MM-DD'),
                      // ],
                    })(
                      <RangePicker
                        style={{
                          width: '100%',
                        }}
                        allowClear
                        getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      />,
                    )}
                  </Form.Item>
                </Col>
              </Form>
            </Col>
            <Col align="right" span={6}>
              <Form.Item style={{ marginBottom: 0 }}>
                <Licensee license="terminalLog_page">
                  <Button
                    type={'primary'}
                    style={{ marginRight: 10 }}
                    onClick={() => {
                      getListDatas();
                    }}
                  >
                    查询
                  </Button>
                  <Button
                    style={{ marginRight: 10 }}
                    onClick={() => {
                      form.resetFields();
                      getListDatas();
                    }}
                  >
                    重置
                  </Button>
                </Licensee>
                <Licensee license="terminalLog_export">
                  {/* <Button
                    type={'primary'}
                    onClick={() => {
                      handleExport();
                    }}
                  >
                    数据导出
                  </Button> */}
                  <ExportApprove
                    exportParams={{
                      urlAPi: '/api/hn/terminal/workOrder/exportTerminalLog',
                      decode: true,
                      params: {
                        ...form.getFieldsValue(),
                        startTime: form
                          ?.getFieldsValue()
                          ?.startTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
                        endTime: form
                          ?.getFieldsValue()
                          ?.startTime?.[1]?.format('YYYY-MM-DD 00:00:00'),
                        pageNum: data?.pageNum || 1,
                        pageSize: data?.pageSize || 10,
                      },
                      method: 'POST',
                    }}
                    moduleTile="终端管理"
                  />
                </Licensee>
              </Form.Item>
            </Col>
          </Row>
        </Card>
        <Card>
          <div className={styles.standarTable}>
            <StandardTable
              scroll={{ x }}
              columns={columns}
              data={data}
              onChange={handleTableChange}
              multiple={true}
              loading={loading}
              showSelectCount={true}
              rowSelectionProps={true}
              selectedRows={selectedRows}
              rowKey="id"
              onSelectRow={onSelectRow}
            />
          </div>
        </Card>
      </div>
    </div>
  );
};
export default Form.create({})(Index);
