import React, { useEffect, useState } from 'react';
import {
  Input,
  TextArea,
  Select,
  Modal,
  Row,
  Form,
  Col,
  Upload,
  Button,
  Icon,
  message,
} from 'antd';
import FileViewer from 'react-file-viewer';
import request from 'ponshine-request';
import styles from '../index.less';
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const acceptTypes = [
  '.csv',
  '.xls',
  '.xlsx',
  '.jpg',
  '.png',
  '.bmp',
  '.jpeg',
  '.rar',
  '.zip',
  '.7z',
  '.doc',
  '.docx',
  '.txt',
  '.pdf',
  '.ppt',
  '.pptx',
];
const type = {
  1: '一级',
  2: '二级',
  3: '三级',
};
const Index = (props) => {
  const {
    visible,
    modalValues,
    setVisible,
    form,
    uploadTitle,
    record,
    getListDatas,
    terminalTagArr,
  } = props;
  const { getFieldDecorator } = form;
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    setfileList(
      (record?.fileList || []).map((item) => {
        return {
          uid: item?.id,
          name: item?.fileName,
          status: 'done',
          url: item?.filePath,
          id: item?.id,
        };
      }),
    );
  }, [record]);
  useEffect(() => {
    getLevelByUser();
  }, []);
  const [fileList, setfileList] = useState([]);

  const handleSubmit = () => {
    form.validateFields((err, values) => {
      if (err) return;
      setConfirmLoading(true);
      request('/api/hn/terminal/white/singleAddWhiteInformation', {
        method: 'POST',
        data: { ...values },
        requestType: 'json',
      })
        .then((res) => {
          if (res?.code == 200) {
            setVisible();
            getListDatas();
            message.success(res?.message);
          } else {
            message.error(res?.message);
          }
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };
  const [LevelByUser, setLevelByUser] = useState([]);
  const getLevelByUser = () => {
    request('/api/hn/document/getLevelByUser', {
      method: 'get',
    }).then((res) => {
      if (res?.code == 200) {
        setLevelByUser(res?.data || []);
      } else {
        setLevelByUser([]);
      }
    });
  };
  return (
    <>
      {visible && (
        <Modal
          title={'白终端单个新增'}
          visible={visible}
          width={800}
          onOk={() => {
            handleSubmit();
          }}
          onCancel={() => {
            setVisible();
          }}
          okText={'确认添加'}
          cancelText="取消"
          maskClosable={false}
          confirmLoading={confirmLoading}
        >
          <div className={styles.formStyles}>
            <Form {...formItemLayout}>
              <Row gutter={[24, 8]}>
                <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }} layout={'horizontal'}>
                  <Col span={12}>
                    <Form.Item label={'终端串号'} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                      {getFieldDecorator('mei', {
                        rules: [
                          {
                            required: true,
                            message: '终端串号格式不正确，请重新录入',
                          },
                          {
                            pattern: /^[^\u4e00-\u9fa5]{14,15}$/,
                            message: '终端串号格式不正确，请重新录入',
                          },
                        ],
                      })(<Input allowClear placeholder="请填写" />)}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={'终端标签'}
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                      allowClear
                      placeholder="请选择"
                    >
                      {getFieldDecorator('terminalTag', {
                        rules: [
                          {
                            required: true,
                            message: '风险级别为必选',
                          },
                        ],
                      })(
                        <Select allowClear placeholder="请选择">
                          {terminalTagArr.map((item) => {
                            return (
                              <Select.Option value={item} key={item}>
                                {item}
                              </Select.Option>
                            );
                          })}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <div className={styles.level}>
                      <Form.Item label={'原因'} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                        {getFieldDecorator('whiteReason', {
                          rules: [
                            {
                              max: 200,
                              message: '文本，限制200字以内',
                            },
                          ],
                        })(<Input.TextArea placeholder="请填写"></Input.TextArea>)}
                      </Form.Item>
                    </div>
                  </Col>
                </Form>
              </Row>
            </Form>
          </div>
        </Modal>
      )}
    </>
  );
};
export default Form.create()(Index);
