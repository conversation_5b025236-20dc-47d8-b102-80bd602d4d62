import React, { useEffect, useState } from 'react';
import {
  Input,
  TextArea,
  Select,
  Modal,
  Row,
  Form,
  Col,
  Upload,
  Button,
  Icon,
  message,
} from 'antd';
import FileViewer from 'react-file-viewer';
import request from 'ponshine-request';
import styles from '../index.less';
import { exportFile } from '@/utils/utils';
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const acceptTypes = ['.csv', '.xls', '.xlsx'];
const type = {
  1: '一级',
  2: '二级',
  3: '三级',
};
const Index = (props) => {
  const {
    visible,
    modalValues,
    setVisible,
    form,
    uploadTitle,
    record,
    getListDatas,
    terminalTagArr,
  } = props;
  const { getFieldDecorator } = form;
  const [fileSearchVisible, setfileSearchVisible] = useState(false);
  const [fileInfo, setfileInfo] = useState({});
  const [txtData, settxtData] = useState('');
  const [Loading, setLoading] = useState(false);
  const [ErrorLoading, setErrorLoading] = useState(false);
  useEffect(() => {
    if (!visible) {
      setLoading(false);
      setErrorLoading(false);
    }
  }, [visible]);
  const [RemoveList, setRemoveList] = useState([]);
  useEffect(() => {
    setfileList(
      (record?.fileList || []).map((item) => {
        return {
          uid: item?.id,
          name: item?.fileName,
          status: 'done',
          url: item?.filePath,
          id: item?.id,
        };
      }),
    );
  }, [record]);
  useEffect(() => {
    getLevelByUser();
  }, []);
  const [fileList, setfileList] = useState([]);

  const handleSubmit = () => {
    form.validateFields((err, values) => {
      if (err) return;
      const formData = new FormData();
      for (let key in values) {
        if (key == 'file') {
          formData.append('file', fileList[0]);
        } else {
          formData.append(key, values[key]);
        }
      }
      setLoading(true);
      request('/api/hn/terminal/white/batchChangeWhiteInformation', {
        method: 'POST',
        data: formData,
        requestType: 'form',
      }).then((res) => {
        setLoading(false);
        if (res?.code == 200) {
          setVisible();
          getListDatas();
          message.success(res?.message);
        } else {
          message.error(res?.message);
        }
        if (res?.data?.hasFail) {
          Modal.confirm({
            content: `已成功录入${res?.data?.successCount}个，失败${res?.data?.failCount}个，失败原因请下载文件，失败原因清单`,
            onOk: () => {
              exportFile({
                urlAPi: '/api/hn/terminal/white/downloadWhiteTerminalErrorFile',
                decode: true,
                method: 'POST',
              });
            },
          });
        }
      });
    });
  };
  const [LevelByUser, setLevelByUser] = useState([]);
  const getLevelByUser = () => {
    request('/api/hn/document/getLevelByUser', {
      method: 'get',
    }).then((res) => {
      if (res?.code == 200) {
        setLevelByUser(res?.data || []);
      } else {
        setLevelByUser([]);
      }
    });
  };
  const upLoadProps = {
    accept: '.csv,.xls,.xlsx',
    multiple: true,
    beforeUpload: (file, list) => {
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      if (
        acceptTypes.some((item) => {
          return item == tag;
        })
      ) {
        setfileList([file]);
        return false;
      }
      message.warning('文件上传格式不正确，文件上传类型为:.csv,.xls,.xlsx');
      return false;
    },
    onRemove: (file) => {
      Modal.confirm({
        title: '提示',
        content: '请确认是否删除该文件',
        onOk: () => {
          const index = fileList.indexOf(file);
          const newFileList = fileList.slice();
          newFileList.splice(index, 1);
          setfileList(newFileList);
        },
      });
    },

    // onDownload: (file) => {
    //   Modal.confirm({
    //     title: '提示',
    //     content: '请确认是否下载该文件',
    //     onOk: () => {
    //       window.open(`/api/hn/document/downloadFileById?fileId=${file?.id}`);
    //     },
    //   });
    // },
    showUploadList: {
      showRemoveIcon: true,
    },
    fileList,
  };
  return (
    <>
      {visible && (
        <Modal
          title={'批量白终端信息变更'}
          visible={visible}
          width={800}
          onOk={() => {
            handleSubmit();
          }}
          onCancel={() => {
            setVisible();
          }}
          confirmLoading={Loading}
          okText={'确认'}
          cancelText="取消"
        >
          <div className={styles.formStyles}>
            <Form {...formItemLayout}>
              <Row gutter={[24, 8]}>
                <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }} layout={'horizontal'}>
                  <Col span={12}>
                    <Form.Item label={'变更类型'} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                      {getFieldDecorator('changeType', {
                        rules: [
                          {
                            required: true,
                            message: '变更类型必选',
                          },
                        ],
                      })(
                        <Select allowClear placeholder="请选择">
                          <Select.Option key={'新增'} value={'1'}>
                            新增
                          </Select.Option>
                          <Select.Option key={'删除'} value={'2'}>
                            删除
                          </Select.Option>
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={'终端标签'}
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                      allowClear
                      placeholder="请选择"
                    >
                      {getFieldDecorator('terminalTag', {
                        rules: [
                          {
                            required: true,
                            message: '终端标签为必选',
                          },
                        ],
                      })(
                        <Select allowClear placeholder="请选择">
                          {terminalTagArr.map((item) => {
                            return (
                              <Select.Option value={item} key={item}>
                                {item}
                              </Select.Option>
                            );
                          })}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <div style={{ marginLeft: 39 }}>
                      <Form.Item labelCol={{ span: 4 }} wrapperCol={{ span: 14 }}>
                        {getFieldDecorator('file')(
                          <Upload
                            disabled={uploadTitle == '详情'}
                            {...upLoadProps}
                            fileList={fileList}
                          >
                            <Button disabled={uploadTitle == '详情'}>
                              <Icon type="upload" /> 选择文件
                            </Button>
                            未选择任何数据
                          </Upload>,
                        )}
                        <div style={{ color: 'rgba(0,0,0,0.5)' }}>
                          *每个文件不超过&nbsp;<span style={{ color: '#000' }}>1000条</span>
                        </div>
                        <a
                          onClick={() => {
                            window.open(
                              '/api/template/getTemplate?templateCode=terminalBatchImport',
                            );
                          }}
                        >
                          模板下载
                        </a>
                      </Form.Item>
                    </div>
                  </Col>
                </Form>
              </Row>
            </Form>
            <div style={{color:'#ff0000'}}>
            注：录入原因必须包含时间、关联号码、描述，缺乏关键信息将不予审批通过，样例如: 7.6 XXXXXXXXXXX外呼核验涉诈
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};
export default Form.create()(Index);
