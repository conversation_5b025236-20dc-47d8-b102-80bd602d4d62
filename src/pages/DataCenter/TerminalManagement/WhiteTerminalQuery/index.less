.flieContent {
  width: 100%;
  height: calc(100vh - 140px);

  .react-grid-Grid {
    height: calc(100vh - 140px);
  }

  .pg-viewer-wrapper {
    .pg-viewer {
      .pdf-viewer-container {
        .pdf-viewer {
          .pdf-canvas {
            canvas {
              margin: 0 auto;
            }
          }
        }
      }
    }
  }
}

.standarTable {
  :global {
    .ant-table-body-inner::-webkit-scrollbar {
      display: none;
    }
  }
}

.level {
  :global {
    .ant-form-item-children {
      display: flex;
    }
  }

  a {
    margin-left: 13px;
    width: 100px;
  }
}

.downloadIcon {
  position: absolute;
  right: 206px;
  top: 51px;
  display: flex;
  flex-direction: column;

  :global {
    .anticon-download {
      height: 22px;
      margin-top: 4px;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .anticon-delete {
      height: 22px;
      margin-top: 4px;
      margin-bottom: 4px;
      font-size: 14px;
    }
  }
}

.formStyles {
  .TextArea {
    :global {
      .ant-input {
        height: 170px;
      }
    }
  }
}
