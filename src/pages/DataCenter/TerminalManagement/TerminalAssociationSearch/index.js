import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Table,
  DatePicker,
  Input,
  Button,
  Modal,
  Tooltip,
  message,
} from 'antd';

import { exportFile } from '@/utils/utils';
import StandardTable from '@/components/StandardTable';
import ExportApprove from '@/components/ExportApprove';

import moment from 'moment';
const { RangePicker } = DatePicker;
import request from 'ponshine-request';

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldValue, validateFields } = form;
  const [sortedInfo, setSortedInfo] = useState({});

  const formItemLayout = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 18,
    },
  };
  const [loading, setloading] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const handleTableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
    const newData = { ...listData };
    newData.pagination.current = pagination?.current || 1;
    newData.pagination.pageSize = pagination?.pageSize || 10;
    setListData(newData);
  };

  const onExport = () => {
    exportFile({
      urlAPi: '/api/hn/terminalInfo/exportAssociativeQueries',
      decode: true,
      params: { phone: getFieldValue('phone') },
      method: 'POST',
    });
  };
  const getListDatas = (pagination) => {
    validateFields((error, values) => {
      if (error) return;
      setloading(true);
      setSortedInfo({});
      request({
        url: '/api/hn/terminalInfo/queryAssociativeQueries',
        method: 'POST',
        requestType: 'json',
        data: {
          phone: values?.phone,
        },
      }).then((res) => {
        setloading(false);
        if (res?.code == 200) {
          setListData({
            list: res?.data || [],
            pagination: {
              current: pagination?.current || 1,
              pageSize: pagination?.pageSize || 10,
              total: res?.data?.length || 0,
            },
          });
        } else {
          message.error(res?.message);
          setListData({
            list: [],
            pagination: {
              current: pagination?.current || 1,
              pageSize: pagination?.pageSize || 10,
              total: res?.data?.length || 0,
            },
          });
        }
      });
    });
  };
  const onReset = () => {
    form.resetFields();
    getListDatas && getListDatas();
  };
  const detailColumns = [
    { dataIndex: 'localNetwork', title: '本地网', width: 70 },
    { dataIndex: 'accNbr', title: '号码', width: 100 },
    { dataIndex: 'mktResInstNbr', title: 'MEID', width: 125 },
    { dataIndex: 'mktResInstNbrType', title: 'MEID类型', width: 85 },
    { dataIndex: 'regImei1', title: 'IMEI1', width: 125 },
    { dataIndex: 'regImei1Type', title: 'IMEI1类型', width: 85 },
    { dataIndex: 'regImei2', title: 'IMEI2', width: 125 },
    { dataIndex: 'regImei2Type', title: 'IMEI2类型', width: 85 },
    { dataIndex: 'regBrandName', title: '品牌', width: 80 },
    { dataIndex: 'regTermModel', title: '终端型号', width: 80 },
    { dataIndex: 'minRegTime', title: '最早注册时间', width: 170 },
    { dataIndex: 'lastRegTime', title: '最近注册时间', width: 170 },
  ];
  let columns = [
    {
      title: '本地网',
      width: 80,
      dataIndex: 'localNetwork',
      ellipsis: true,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '号码',
      width: 80,
      dataIndex: 'accNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'accNbr' && sortedInfo.order,
    },
    {
      title: 'MEID',
      width: 100,
      dataIndex: 'mktResInstNbr',
      align: 'center',
      ellipsis: true,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'mktResInstNbr' && sortedInfo.order,
    },
    {
      title: 'MEID类型',
      width: 80,
      dataIndex: 'mktResInstNbrType',
      align: 'center',
    },
    {
      title: 'IMEI1',
      width: 100,
      ellipsis: true,
      dataIndex: 'regImei1',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'regImei1' && sortedInfo.order,
    },
    {
      title: 'IMEI1类型',
      width: 80,
      dataIndex: 'regImei1Type',
      align: 'center',
    },
    {
      title: 'IMEI2',
      width: 100,
      ellipsis: true,
      dataIndex: 'regImei2',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'regImei2' && sortedInfo.order,
    },
    {
      title: 'IMEI2类型',
      width: 80,
      dataIndex: 'regImei2Type',
      align: 'center',
    },
    {
      title: '终端品牌',
      width: 80,
      ellipsis: true,
      dataIndex: 'regBrandName',
      align: 'center',
    },
    {
      title: '终端型号',
      width: 80,
      ellipsis: true,
      dataIndex: 'regTermModel',
      align: 'center',
    },
    {
      title: '最早注册时间',
      width: 180,
      dataIndex: 'minRegTime',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'minRegTime' && sortedInfo.order,
    },
    {
      title: '最近注册时间',
      width: 180,
      dataIndex: 'lastRegTime',
      align: 'center',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'lastRegTime' && sortedInfo.order,
    },
    {
      title: '关联号码',
      width: 200,
      dataIndex: 'associatedNumber',
      align: 'center',
    },
  ];
  columns = [
    ...columns,
    // {
    //   title: '操作',
    //   dataIndex: 'operate',
    //   key: 'operate',
    //   fixed: 'right',
    //   align: 'center',
    //   width: 350,
    //   render: () => {
    //     return (
    //       <>
    //         <Button type="link" style={{ marginRight: 10 }} onClick={null}>
    //           编辑
    //         </Button>
    //         <Button type="link" style={{ marginRight: 10 }} onClick={null}>
    //           删除
    //         </Button>
    //         <Button type="link" style={{ marginRight: 10 }} onClick={null}>
    //           详情
    //         </Button>
    //       </>
    //     );
    //   },
    // },
  ];
  // const lists = [{"id":1,"name":"关键词","type":"类型","time":"2022-01-11","localNetwork":"ikun","accNbr":"ikun","mktResInstNbr":"ikun","mktResInstNbrType":"ikun","regImei1":"ikun","regImei1Type":"ikun","regImei2":"ikun","regImei2Type":"ikun","regBrandName":"ikun","regTermModel":"ikun","minRegTime":"ikun","lastRegTime":"ikun","undefined":"[object Object]ikun"}];

  const getCurrentPageData = useMemo(() => {
    let currentData = [...(listData.list || [])];

    if (sortedInfo.columnKey && sortedInfo.order) {
      currentData.sort((a, b) => {
        const order = sortedInfo.order === 'ascend' ? 1 : -1;

        switch (sortedInfo.columnKey) {
          case 'accNbr':
            return order * (Number(a.accNbr || '0') - Number(b.accNbr || '0'));
          case 'mktResInstNbr':
            return order * (Number(a.mktResInstNbr || '0') - Number(b.mktResInstNbr || '0'));
          case 'regImei1':
            return order * (Number(a.regImei1 || '0') - Number(b.regImei1 || '0'));
          case 'regImei2':
            return order * (Number(a.regImei2 || '0') - Number(b.regImei2 || '0'));
          case 'minRegTime':
            return order * (moment(a.minRegTime).valueOf() - moment(b.minRegTime).valueOf());
          case 'lastRegTime':
            return order * (moment(a.lastRegTime).valueOf() - moment(b.lastRegTime).valueOf());
          default:
            return 0;
        }
      });
    }

    const { current = 1, pageSize = 10 } = listData.pagination || {};
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return currentData.slice(start, end);
  }, [listData.list, listData.pagination, sortedInfo]);

  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Card>
          <Row>
            <Col>
              <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
                <Col span={8}>
                  <Form.Item label={'号码'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('phone', {
                      rules: [{ required: true, message: '号码必填' }],
                    })(<Input placeholder="请输入" allowClear />)}
                  </Form.Item>
                </Col>
              </Form>
            </Col>
            <Col align="left" span={16}>
              <Button
                type="primary"
                style={{ marginRight: 10, marginTop: 4 }}
                onClick={getListDatas}
              >
                搜索
              </Button>
              {/* <Button type="primary" style={{ marginRight: 10, marginTop: 4 }} onClick={onExport}>
                数据导出
              </Button> */}
              <ExportApprove
                exportParams={{
                  urlAPi: '/api/hn/terminalInfo/exportAssociativeQueries',
                  decode: true,
                  params: { phone: getFieldValue('phone') },
                  method: 'POST',
                }}
                moduleTile="终端管理"
              />
            </Col>
          </Row>
        </Card>
        <Card>
          <StandardTable
            columns={columns}
            detailColumns={detailColumns.map((item) => {
              return {
                ...item,
                key: item?.dataIndex,
                ellipsis: true,
                width: item.width,
                align: 'center',
                render: (v) => {
                  const t = v || '--';
                  return (
                    <Tooltip title={t} placement="topLeft">
                      {t}
                    </Tooltip>
                  );
                },
                sorter: columns?.find((ele) => item.dataIndex === ele.dataIndex)?.sorter,
                sortOrder: columns?.find((ele) => item.dataIndex === ele.dataIndex)?.sortOrder,
              };
            })}
            data={{
              list: getCurrentPageData,
              pagination: listData.pagination,
            }}
            tools={true}
            onChange={handleTableChange}
            multiple={true}
            loading={loading}
            showSelectCount={false}
            rowSelectionProps={false}
            rowKey="id"
            scroll={{
              x: 1000,
              y: 500,
            }}
          />
        </Card>
      </div>
    </div>
  );
};
export default Form.create({})(Index);
