import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Table,
  DatePicker,
  Input,
  Button,
  Modal,
  Tooltip,
  message,
} from 'antd';
import request from 'ponshine-request';

import { exportFile } from '@/utils/utils';
import StandardTable from '@/components/StandardTable';
import BatchImportModal from './BatchImportModal';
import BatchImportBlackModal from './BatchImportBlackModal';
import { Licensee, useLicensee } from 'ponshine';
const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form, reloadTable } = props;
  const { getFieldDecorator, getFieldValue, validateFields } = form;
  const [loading, setloading] = useState(false);
  const [listData, setListData] = useState({
    total: 0,
    list: [],
    pageNum: 1,
    pageSize: 10,
  });
  const [importVisible, setImportVisible] = useState(false);
  const [importBlackVisible, setImportBlackVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [searchValue, setSearchValue] = useState({});

  useEffect(() => {
    getListDatas()
  }, [])

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchValue,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const getListDatas = ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setloading(true);

    const params = {
      ...props,
      pageNum,
      pageSize,
    }
    request({
      url: '/api/hn/terminalInfo/pageTerminalBatchQuery',
      method: 'POST',
      requestType: 'json',
      data: params,
    }).then((res) => {
      setloading(false);
      setSearchValue(params)
      if (res?.code == 200) {
        setListData({
          total: res?.data?.totalNum || 0,
          list: res?.data.items || [],
          pageNum: pageNum || 1,
          pageSize: pageSize || 10,
        });
      } else {
        message.error(res?.message);
        setListData({
          total: 0,
          list: [],
          pageNum: 1,
          pageSize: 10,
        });
      }
    });
  };

  const handleSearch = () => {
    const values = form.getFieldsValue();
    getListDatas({
      ...values,
      time: undefined,
      startTimeBegin: values?.time?.[0]?.format('YYYY-MM-DD 00:00:00'),
      startTimeEnd: values?.time?.[1]?.format('YYYY-MM-DD 23:59:59'),
    })
  }

  const onReset = () => {
    form.resetFields();
    getListDatas && getListDatas();
  };

  let columns = [
    {
      title: 'ID',
      width: 100,
      dataIndex: 'id',
     
      align: 'center',
      ellipsis: true,
    },
    {
      title: '导入文件',
      width: 220,
      dataIndex: 'queryFileName',
      render: (text, record) => {
        return (
          <a
            onClick={() => {
              handleExport(record, '1');
            }}
          >
            {text}
          </a>
        );
      },
      align: 'center',
      ellipsis: true,
    },
    {
      title: '查询时间',
      width: 200,
      align: 'center',
      dataIndex: 'startTime',
     
    },
    {
      title: '结果文件',
      width: 200,
      dataIndex: 'resultFileName',
      render: (text, record) => {
        if (text) {
          if(text.includes('.xlsx')) {
            return (
              <a
                onClick={() => {
                  handleExport(record, '2');
                }}
              >
                {text}
              </a>
            );
          }else {
            return text
          }
          
        } else {

          return record.taskStatus == 1 ?'正在查询字段' : '--';
        }
      },
      align: 'center',
      ellipsis: true,
    },
    {
      title: '处理状态',
      width: 100,
      align: 'center',
      dataIndex: 'taskStatus',
      render: (v) => {
        return ['未开始', '查询中', '查询完成', '查询失败']?.[v] || '--';
      },
    },
  ];

  
  const handleExport = (record, fileType) => {
    exportFile({
      urlAPi: '/api/hn/terminalInfo/getSourceResultFile',
      params: {
        id: record.id,
        fileType: fileType,
      },
      decode: true,
      method: 'GET',
      mime: 'xlsx',
    });
  };

  const onImport = (values, callback) => {
    setConfirmLoading(true);
    request({
      url: '/api/hn/terminalInfo/createQueryTask',
      method: 'POST',
      requestType: 'json',
      data: values,
    }).then((res) => {
      setConfirmLoading(false);
      if (res?.code == 200) {
        message.success(res.message || '导入成功');
        form.resetFields()
        getListDatas();
        hideImport();
      } else {
        if (res.code == 401) {
          callback();
        } else {
          message.error(res.message);
        }
      }
    });
  };

  const hideImport = () => {
    setImportVisible(false);
  };
  const onImportBlack = (values, callback) => {
    setConfirmLoading(true);
    request({
      url: '/api/hn/terminalInfo/createAutoBlackTask',
      method: 'POST',
      requestType: 'json',
      data: values,
    }).then((res) => {
      setConfirmLoading(false);
      if (res?.code == 200) {
        message.success(res.message || '导入成功');
        form.resetFields()
        getListDatas();
        hideBlackImport();
      } else {
        if (res.code == 401) {
          callback();
        } else {
          message.error(res.message);
        }
      }
    });
  };
  const hideBlackImport = () => {
    setImportBlackVisible(false);
  };

  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Card>
          <Form layout="inline">
            <Form.Item label={'查询文件名称'}>
              {getFieldDecorator('fileName', {
                // rules: [{ required: true, message: '查询文件名称' }],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
            <Form.Item label={'查询日期'}>
              {getFieldDecorator('time', {
                // initialValue: [
                //   moment(moment(new Date()).startOf('day'), 'YYYY-MM-DD'),
                //   moment(moment(new Date()), 'YYYY-MM-DD'),
                // ],
              })(
                <RangePicker
                  style={{
                    width: '100%',
                  }}
                  allowClear
                  getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                />,
              )}
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                style={{ marginRight: 10, marginTop: 4 }}
                onClick={handleSearch}
              >
                查看查询结果
              </Button>
              <Button
              type="primary"
              style={{ marginRight: 10, marginTop: 4 }}
              ghost
              onClick={() => {
                setImportVisible(true);
              }}
            >
              新增查询
            </Button>
            <Licensee license='autoBlackTerminal' fallback={null}>
              <Button
              type="danger"
              style={{ marginRight: 10, marginTop: 4 }}
              ghost
              onClick={() => {
                setImportBlackVisible(true);
              }}
            >
              一键加黑串码
            </Button>
            </Licensee>
            </Form.Item>
          </Form>

         
  
        </Card>
        <Card>
          <StandardTable
            columns={columns}
            locale={{ emptyText: '无号码属性查询记录' }}
            data={{
              list: listData.list,
              pagination: {
                total: listData.total,
                showSizeChanger: true,
                showQuickJumper: true,
                defaultPageSize: 20,
                current: listData.pageNum,
                pageSize: listData.pageSize,
                showTotal: (total, range) => {
                  return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
                },
              },
            }}
            onChange={handleTableChange}
            multiple={true}
            loading={loading}
            showSelectCount={false}
            rowSelectionProps={false}
            rowKey="id"
          />
          <BatchImportModal
            title="批量查询"
            visible={importVisible}
            onImport={onImport}
            onClose={hideImport}
            loading={confirmLoading}
            downTemplateUrl={`template/getTemplate`}
            errorExportUrl="/api/hn/fraudPoliceNotificationReplay/downloadErrorExcel"
          />
          {
            importBlackVisible &&   <BatchImportBlackModal
            title="批量查询"
            visible={importBlackVisible}
            onImport={onImportBlack}
            onClose={hideBlackImport}
            loading={confirmLoading}
            downTemplateUrl={`template/getTemplate`}
            errorExportUrl="/api/hn/fraudPoliceNotificationReplay/downloadErrorExcel"
          />
          }
        
        </Card>
      </div>
    </div>
  );
};
export default Form.create({})(Index);
