import React, { useState } from 'react';
import { Modal, Upload, Button, Icon, Row, Col, message, Form, Select } from 'antd';
import styles from './index.less';
import { basePath } from '@/services/basePath';
import { exportFile } from '@/utils/utils';

const BatchImportModal = (props) => {
  const {
    title,
    visible,
    onImport,
    onClose,
    downTemplateUrl,
    errorExportUrl,
    loading,
    tipsText,
    form,
  } = props;
  const { getFieldDecorator } = form;
  const [fileList, setFileList] = useState([]);
  const [downloadVisible, setDownloadVisible] = useState(false);

  const okHandle = () => {
    form.validateFields((err, values) => {
      if (err) return;
      if (fileList.length) {
        const fileData = new FormData();
        fileData.append('file', fileList[0]);
        fileData.append('queryType', values.queryType);
        onImport(fileData, () => {
          setDownloadVisible(true);
        });
      } else {
        message.warning('请选择文件');
      }
    });
  };

  const download = (url) => {
    exportFile({
      urlAPi: url,
      decode: true,
      method: 'GET',
    });
    setDownloadVisible(false);
    onClose();
  };

  const hideDownModal = () => {
    setDownloadVisible(false);
  };

  // 模板下载
  const handleDownload = () => {
    try {
      window.open(`${basePath}${downTemplateUrl}?templateCode=terminalBatchQuery`);
    } catch (e) {
      console.log(e);
    }
  };

  const uploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      if (tag === '.xlsx' || tag === '.xls') {
        setFileList([file]);
        return false;
      } else {
        message.warning('文件格式不正确');
      }
    },
    fileList,
  };

  return (
    <>
      <Modal
        title={title}
        visible={visible}
        onCancel={onClose}
        destroyOnClose
        afterClose={() => {
          setFileList([]);
        }}
        maskClosable={false}
        footer={[
          <Button key="1" onClick={onClose}>
            取消
          </Button>,
          <Button key="2" type="primary" onClick={okHandle} loading={loading}>
            确认添加
          </Button>,
        ]}
      >
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col span={24} style={{ marginLeft: 10 }}>
            <Form.Item
              label={'查询方式'}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 14 }}
              layout={'horizontal'}
            >
              {getFieldDecorator('queryType', {
                rules: [{ required: true, message: '请选择查询方式' }],
              })(
                <Select style={{ width: 200 }} placeholder="请选择">
                  {['号码关联查询', '按号码查询终端信息', '按终端查询终端信息'].map(
                    (ele, index) => (
                      <Select.Option value={index + 1}>{ele}</Select.Option>
                    ),
                  )}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={24} style={{ marginLeft: 10 }}>
            <div className={styles.uploadPanel}>
              <Upload {...uploadProps} accept=".xlsx,.xls">
                <Button>
                  <Icon type="upload" /> 选择文件
                </Button>
                {uploadProps.fileList.length === 0 ? '  未选择任何文件' : ''}
              </Upload>
            </div>
            <div className={styles.tips}>{tipsText ? tipsText : '*每个文件不超过50个'}</div>
            <a className={styles.download} onClick={handleDownload}>
              模板下载
            </a>
          </Col>
        </Row>
      </Modal>
      {downloadVisible && (
        <Modal
          visible={downloadVisible}
          title="下载错误文件"
          destroyOnClose
          maskClosable={false}
          onCancel={hideDownModal}
          footer={[
            <Button onClick={hideDownModal} key="1">
              取消
            </Button>,
            <Button type="primary" onClick={() => download(errorExportUrl)} key="2">
              确认下载
            </Button>,
          ]}
        >
          <div className={styles.confirmModalContent}>
            <span>导入的文件内容有误，请下载错误文件</span>
          </div>
        </Modal>
      )}
    </>
  );
};

export default Form.create()(BatchImportModal);
