import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Table,
  DatePicker,
  Input,
  Button,
  Modal,
  Tooltip,
  message,
  Icon,
  Select,
} from 'antd';
import { Licensee, useLicensee } from 'ponshine';
import 'antd/dist/antd.css';
import { exportFile } from '@/utils/utils';

// import StandardTable from "./StandardTable";
import StandardTable from '@/components/StandardTable';
import BtnCallbackComponentknVmkzhLMf from './components/upModal';
import moment from 'moment';
const { RangePicker } = DatePicker;
import styles from './index.less';
// import request from "umi-request";
import request from 'ponshine-request';
import AddModal from './components/addModal';
import ExportApprove from '@/components/ExportApprove';

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator } = form;

  const formItemLayout = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 18,
    },
  };
  useEffect(() => {
    getListDatas();
  }, []);
  const [selectedRows, setSelectedRows] = useState([]);
  const onSelectRow = (selectedRows) => {
    setSelectedRows(selectedRows);
  };
  const [data, setdata] = useState({});
  const [loading, setloading] = useState(false);
  const getListDatas = (
    pagination = {
      pageNum: 1,
      pageSize: 10,
    },
  ) => {
    setloading(true);
    const values = form.getFieldsValue();
    let data = {
      ...values,
      startTime:
        values?.startTime && values?.startTime[0]
          ? values?.startTime[0].format('YYYY-MM-DD 00:00:00')
          : undefined,
      endTime:
        values?.startTime && values?.startTime[1]
          ? values?.startTime[1].format('YYYY-MM-DD 23:59:59')
          : undefined,
      ...pagination,
    };
    request('/api/hn/blackTerminal/pageBlackTerminal', {
      method: 'POST',
      data,
      requestType: 'json',
    })
      .then((res) => {
        if (res?.code == 200) {
          setdata({
            list: res?.data?.items || [],
            pagination: {
              ...pagination,
              current: pagination?.pageNum || 1,
              total: res?.data?.totalNum || 0,
            },
          });
        } else {
          setdata({
            list: [],
            pagination: {
              ...pagination,
              current: pagination?.pageNum || 1,
              total: 0,
            },
          });
        }
      })
      .finally(() => {
        setloading(false);
      });
  };
  function handleDelete(row) {
    if (selectedRows.length == 0) {
      message.error('提示至少勾选一个数据');
      return;
    }
    setdeleteVisible(true);
  }
  const handleTableChange = (pagination) => {
    getListDatas({
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };
  const [VisibleknVmkzhLMf, setVisibleknVmkzhLMf] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [deleteVisible, setdeleteVisible] = useState(false);
  const relationClickknVmkzhLMf = () => {
    setAddModalVisible(true);
    // setVisibleknVmkzhLMf(true);
    // setuploadTitle('信息上传');
  };
  let columns = [
    {
      title: '终端串号',
      width: 140,
      dataIndex: 'mei',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端标签',
      width: 70,
      dataIndex: 'terminalTag',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '风险级别',
      width: 70,
      dataIndex: 'riskLevel',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '原因',
      width: 140,
      dataIndex: 'blackReason',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '录入时间',
      width: 140,
      dataIndex: 'gmtCreate',
      align: 'center',
    },
    {
      title: '录入人',
      width: 70,
      dataIndex: 'inputUserName',
      align: 'center',
    },
  ];
  columns = [...columns];
  const [record, setRecord] = useState({});
  const [uploadTitle, setuploadTitle] = useState('');
  useEffect(() => {
    if (!VisibleknVmkzhLMf) {
      setRecord({});
    }
  }, [VisibleknVmkzhLMf]);
  // const lists = [{"id":1,"name":"关键词","type":"类型","time":"2022-01-11","title":"XXXXXXX","level":"一级","upLoadMan":"IKUN","upLoadTime":"2020-1-1"}];
  const handleExport = () => {
    const values = form.getFieldsValue();
    let newdata = {
      ...values,
      startTime:
        values?.startTime && values?.startTime[0]
          ? values?.startTime[0].format('YYYY-MM-DD 00:00:00')
          : undefined,
      endTime:
        values?.startTime && values?.startTime[1]
          ? values?.startTime[1].format('YYYY-MM-DD 23:59:59')
          : undefined,
      pageNum: data?.pageNum || 1,
      pageSize: data?.pageSize || 10,
    };
    exportFile({
      urlAPi: '/api/hn/blackTerminal/exportBlackTerminal',
      decode: true,
      params: newdata,
      method: 'POST',
    });
  };
  const getInfo = (row) => {
    request('/api/hn/document/getDetailById', {
      method: 'POST',
      data: {
        id: row.id,
      },
      requestType: 'form',
    }).then((res) => {
      if (res?.code == 200) {
        setRecord({ ...row, ...res.data });
        setVisibleknVmkzhLMf(true);
      } else {
        setRecord({});
        message.error(res?.message);
      }
    });
  };
  let x = 0;
  columns.forEach((item) => {
    x += item.width;
  });
  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Card>
          <Row>
            <Col>
              <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} layout={'horizontal'}>
                <Col span={6}>
                  <Form.Item label={'终端串号'}>
                    {getFieldDecorator('mei')(<Input placeholder="请输入" allowClear />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'终端标签'}>
                    {getFieldDecorator('terminalTag')(
                      <Select allowClear placeholder="请选择">
                        <Select.Option value="省内">省内</Select.Option>
                        <Select.Option value="集团">集团</Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'原因'}>
                    {getFieldDecorator('blackReason')(<Input allowClear placeholder="模糊查询" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'录入人'}>
                    {getFieldDecorator('inputUserName')(<Input allowClear placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={'录入时间'} style={{ marginBottom: 0 }}>
                    {getFieldDecorator('startTime', {
                      // initialValue: [
                      //   moment(moment(new Date()).startOf('day'), 'YYYY-MM-DD'),
                      //   moment(moment(new Date()), 'YYYY-MM-DD'),
                      // ],
                    })(
                      <RangePicker
                        style={{
                          width: '100%',
                        }}
                        allowClear
                        getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      />,
                    )}
                  </Form.Item>
                </Col>
              </Form>
            </Col>

            <Col align="right" span={18}>
              <Form.Item style={{ marginBottom: 0 }}>
                <Licensee license="blackTerminal_page">
                  <Button
                    type={'primary'}
                    style={{ marginRight: 10 }}
                    onClick={() => {
                      getListDatas();
                    }}
                  >
                    查询
                  </Button>
                  <Button
                    style={{ marginRight: 10 }}
                    onClick={() => {
                      form.resetFields();
                      getListDatas();
                    }}
                  >
                    重置
                  </Button>
                </Licensee>
                <Licensee license="blackTerminal_export">
                  {/* <Button
                    type={'primary'}
                    style={{ marginRight: 10 }}
                    onClick={() => {
                      handleExport();
                    }}
                  >
                    数据导出
                  </Button> */}
                  <ExportApprove
                    buttonStyle={{ marginRight: 10 }}
                    exportParams={{
                      urlAPi: '/api/hn/blackTerminal/exportBlackTerminal',
                      decode: true,
                      params: {
                        ...form.getFieldsValue(),
                        startTime: form
                          ?.getFieldsValue()
                          ?.startTime?.[0]?.format('YYYY-MM-DD 00:00:00'),
                        endTime: form
                          ?.getFieldsValue()
                          ?.startTime?.[1]?.format('YYYY-MM-DD 23:59:59'),

                        pageNum: data?.pageNum || 1,
                        pageSize: data?.pageSize || 10,
                      },
                      method: 'POST',
                    }}
                    moduleTile="终端管理"
                  />
                </Licensee>
                <Licensee license="blackTerminal_singleAdd">
                  <Button
                    type={'primary'}
                    style={{ marginRight: 10 }}
                    onClick={() => {
                      relationClickknVmkzhLMf();
                    }}
                  >
                    <Icon type="plus" />
                    新建
                  </Button>
                </Licensee>
                <Licensee license="blackTerminal_batchChange">
                  <Button
                    type={'primary'}
                    style={{ marginRight: 10 }}
                    onClick={() => {
                      setVisibleknVmkzhLMf(true);
                    }}
                  >
                    黑终端批量变更
                  </Button>
                </Licensee>
                <Licensee license="blackTerminal_delete">
                  <Button
                    type={'primary'}
                    onClick={() => {
                      handleDelete();
                    }}
                  >
                    <Icon type="delete" /> 删除
                  </Button>
                </Licensee>
              </Form.Item>
            </Col>

            {/* <Col align="right" span={24}>
              <Licensee license="document_query">
                <Button
                  type={'primary'}
                  style={{ marginRight: 10 }}
                  onClick={() => {
                    getListDatas();
                  }}
                >
                  查询
                </Button>
                <Button
                  style={{ marginRight: 10 }}
                  onClick={() => {
                    form.resetFields();
                    getListDatas();
                  }}
                >
                  重置
                </Button>
              </Licensee>
              <Licensee license="document_export">
                <Button
                  type={'primary'}
                  style={{ marginRight: 10 }}
                  onClick={() => {
                    handleExport();
                  }}
                >
                  数据导出
                </Button>
              </Licensee>
              <Licensee license="document_add">
                <Button
                  type={'primary'}
                  style={{ marginRight: 10 }}
                  onClick={() => {
                    relationClickknVmkzhLMf();
                  }}
                >
                  + 新建
                </Button>
              </Licensee>
              <Licensee license="document_delete">
                <Button
                  disabled={selectedRows?.length == 0}
                  type={'primary'}
                  style={{ marginRight: 10 }}
                  onClick={() => {
                    handleDelete();
                  }}
                >
                  删除
                </Button>
              </Licensee>
            </Col> */}
          </Row>
        </Card>
        <Card>
          <div className={styles.standarTable}>
            <StandardTable
              // size="small"
              scroll={{ x }}
              columns={columns}
              data={{
                list: data?.list || [],
                pagination: {
                  ...data?.pagination,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  defaultPageSize: 20,
                  showTotal: (total, range) => {
                    return `当前显示记录：${range[0]}到${range[1]} 共计：${total}`;
                  },
                },
              }}
              onChange={handleTableChange}
              multiple={true}
              loading={loading}
              showSelectCount={true}
              rowSelectionProps={true}
              selectedRows={selectedRows}
              rowKey="id"
              onSelectRow={onSelectRow}
            />
          </div>
        </Card>
        {VisibleknVmkzhLMf && (
          <BtnCallbackComponentknVmkzhLMf
            visible={VisibleknVmkzhLMf}
            setVisible={setVisibleknVmkzhLMf}
            getListDatas={getListDatas}
            selectedRows={selectedRows}
          />
        )}
        {addModalVisible && (
          <AddModal
            setVisible={setAddModalVisible}
            getListDatas={getListDatas}
            visible={addModalVisible}
          ></AddModal>
        )}
        {deleteVisible && (
          <Modal
            title={'删除'}
            visible={deleteVisible}
            width={700}
            onOk={() => {
              props.form.validateFields((Error, value) => {
                if (Error) return;
                request('/api/hn/blackTerminal/batchDeleteBlackTerminal', {
                  method: 'POST',
                  data: {
                    idList: selectedRows.map((item) => item?.id),
                    deleteReason: value.deleteReason,
                  },
                  requestType: 'json',
                }).then((res) => {
                  if (res?.code == 200) {
                    setSelectedRows([]);
                    setdeleteVisible();
                    getListDatas();
                    message.success(res?.message);
                  } else {
                    message.error(res?.message);
                  }
                });
              });
            }}
            onCancel={() => {
              setdeleteVisible();
            }}
            okText={'确认'}
            cancelText="取消"
          >
            <div className={styles.formStyles}>
              <Form {...formItemLayout}>
                <Row gutter={[24, 8]}>
                  <Form layout={'horizontal'}>
                    <Col span={24}>
                      <div className={styles.level}>
                        <Form.Item
                          label={'删除原因'}
                          labelCol={{ span: 4 }}
                          wrapperCol={{ span: 18 }}
                        >
                          {getFieldDecorator('deleteReason', {
                            rules: [
                              {
                                required: true,
                                message: '提示原因必填',
                              },
                            ],
                          })(<Input.TextArea placeholder="请填写"></Input.TextArea>)}
                        </Form.Item>
                      </div>
                    </Col>
                  </Form>
                </Row>
              </Form>
            </div>
          </Modal>
        )}
      </div>
    </div>
  );
};
export default Form.create({})(Index);
