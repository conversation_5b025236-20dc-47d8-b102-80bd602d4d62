/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:26:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-07-04 14:08:39
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\WhiteListManage\BatchChange\index.jsx
 * @Description:
 */
import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import { withContext } from 'demasia-pro-layout';

import { connect } from 'dryad';
import { Card, Button, message, Modal, Upload, Icon, Input, Form, Select } from 'antd';
import { exportFile } from '@/utils/utils';
import request from '@/utils/request';

import { batchUploadFile } from '@/services/DataCenter/WhiteListManage/BatchChange';
import { Licensee, useLicensee } from 'ponshine';
const { Option } = Select;
const changeTypeList = [
  {
    value: 1,
    label: '新增',
  },
  {
    value: 2,
    label: '删除',
  },
];

const BatchChange = (props) => {
  const {
    batchChange: { tableData },
    tableLoading,
    dispatch,
    submitLoading,
    form,
    form: { getFieldDecorator },
  } = props;

  // useEffect(() => {
  //   // 当离开当前页面时， 清空表格
  //   if (!isTabInactive) {
  //     setFileName('');
  //     dispatch({
  //       type: 'batchChange/clearTableData',
  //     });
  //   }
  // }, [isTabInactive]);

  const formRef = useRef(null);

  const columns = [
    {
      title: '号码',
      dataIndex: 'phoneNum',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '号码归属',
      dataIndex: 'numberAttribution',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },
    {
      title: '变更类型',
      dataIndex: 'changeType',
      align: 'center',
     
      // render: (text) => changeTypeList?.find((ele) => ele.value === text)?.label || '--',
      width: 150,
      ellipsis: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireDate',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },

    {
      title: '客户标签',
      dataIndex: 'customerTag',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'customerSubTab',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },
    {
      title: '证件号',
      dataIndex: 'documentsNumber',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },

    {
      title: '原因',
      dataIndex: 'reason',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },

    {
      title: '入网时间',
      dataIndex: 'accessTime',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },

    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      align: 'center',
     
      width: 150,
      ellipsis: true,
    },
  ];

  const [fileList, setFileList] = useState([]);
  const [fileName, setFileName] = useState('');
  const [errorfileVisible, setErrorfileVisible] = useState(false);
  const [whiteListvisible, setwhiteListvisible] = useState(false);
  const [whiteList, setwhiteList] = useState([]);
  const [fileUploadInfo, setFileUploadInfo] = useState({});

  useEffect(() => {
    setFileName('');
    dispatch({
      type: 'batchChange/clearTableData',
    });
    getSystemConfigListByConfigType();
  }, []);

  const findTableDataPager = ({ currentPage = 1, pageSize = 10, ...props } = {}) => {
    dispatch({
      type: 'batchChange/findTableData',
      payload: {
        ...props,
        currentPage,
        pageSize,
      },
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({
      currentPage: current,
      pageSize,
    });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/template/getTemplate',
      title: '白名单导入模板',
      params: { templateCode: 'whitelist' },
      method: 'GET',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };

  // 上传文件
  const upLoadFile = async (formData, fileName) => {
    await batchUploadFile(formData)
      .then((res) => {
        if (res.code === 200) {
          setFileUploadInfo(res.data || {});
          if (res?.data?.hasFail) {
            setErrorfileVisible(true);
          } else {
            message.success(`录入成功${res?.data?.successCount}条，失败${res?.data?.failCount}条`);
          }
          if (res?.data?.approvalProfessional) {
            setwhiteListvisible(true);
          }
          findTableDataPager();
          setFileName(fileName);
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {});
  };

  const handleSubmit = () => {
    dispatch({
      type: 'batchChange/submitFileList',
      callback: (res) => {
        if (res.code === 200) {
          message.success(res.message);
          setFileName('');
          dispatch({
            type: 'batchChange/clearTableData',
          });
        } else if (res.code === 701) {
          setwhiteListvisible(true);
        } else {
          message.error(res.message);
        }
      },
    });
  };

  // 导出错误报告
  const handleDownloadErr = () => {
    setErrorfileVisible(false);
    exportFile({
      urlAPi: '/api/white/getWhiteTemplateErrorFile',
      title: '白名单批量变更导入失败原因',
      // params: { filePath: errFilePath },
      method: 'POST',
      mime: 'xlsx',
    });
  };
  const getSystemConfigListByConfigType = () => {
    request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
      method: 'GET',
      params: {
        configType: 'approval_professional_province',
      },
    }).then((res) => {
      if (res?.code == 200) {
        setwhiteList(res?.data || []);
      } else {
        setwhiteList([]);
      }
    });
  };
  const submitWhiteList = () => {
    request(`/api/white/approvalProfessionalUpdate`, {
      method: 'POST',
      params: {
        approvalProfessional: form.getFieldValue('approvalProfessional'),
      },
      requestType: 'form',
    }).then((res) => {
      if (res?.code == 200) {
        setwhiteListvisible(false);
        message.success(res?.message);
      } else {
        message.error(res?.message);
      }
    });
  };
  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Licensee license="whiteListBatchChange_getWhitePhoneWorkOrderTemplateData">
            <Upload
              // {...uploadProps}
              accept=".xlsx"
              showUploadList={false}
              customRequest={(info) => {
                const formData = new FormData();
                formData.append('file', info.file);
                // 手动上传
                upLoadFile(formData, info.file?.name);
              }}
            >
              <div style={{ display: 'flex' }}>
                <Input value={fileName || '  未选择任何文件'}></Input>
                <Button type="primary" style={{ marginRight: 16 }}>
                  模板导入
                </Button>
              </div>
            </Upload>
          </Licensee>
          {/* <Licensee license="whiteListBatchChange_exportWhiteTemplateDownload"> */}

          <a style={{ marginLeft: 24 }} onClick={handleExport}>
            白名单导入模板下载
          </a>
          {/* </Licensee> */}
        </div>
        <Licensee license="whiteListBatchChange_submitWhitePhoneWorkOrderTemplateData">
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={submitLoading}
            disabled={!tableData?.list?.length}
          >
            提交
          </Button>
        </Licensee>
      </div>
      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        loading={tableLoading}
        isNeedAutoWidth={true}
        scroll={{ x: 1200 }}
      />
      {/* 下载错误报告 errorfilepath */}
      <Modal
        title="异常提示"
        visible={errorfileVisible}
        width={800}
        maskClosable={false}
        onCancel={() => {
          // setErrorfilepathVisible(false);
          setErrorfileVisible(false);
        }}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        zIndex={31}
        afterClose={() => {
          // setErrFile('');
        }}
        onOk={() => handleDownloadErr()}
      >
        <span>
          <Icon
            type="exclamation-circle"
            style={{ marginRight: 5, color: 'orange', fontWeight: 'bolder' }}
          />
          录入成功{fileUploadInfo?.successCount}条，失败{fileUploadInfo?.failCount}
          条, 点击确认可下载错误数据和原因
        </span>
      </Modal>
      <Modal
        title="白名单审批"
        visible={whiteListvisible}
        width={800}
        maskClosable={false}
        onCancel={() => {
          // setErrorfilepathVisible(false);
          setwhiteListvisible(false);
        }}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        afterClose={() => {
          // setErrFile('');
        }}
        onOk={() => submitWhiteList()}
        zIndex={30}
      >
        <div>
          本地网今日累记提交白名单超过500个号码，需市公司审批完成后再升级省公司审批，请选择省公司审批专业
        </div>
        <div>
          <Form labelCol={{ span: 8 }} wrapperCol={{ span: 10 }}>
            <Form.Item label={'省公司审批专业'}>
              {getFieldDecorator('approvalProfessional')(
                <Select placeholder="请选择" allowClear>
                  {(whiteList || [])?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.value}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </Card>
  );
};

const config = [['tabKey', 'isTabInactive', 'activeKey', 'isTab']];

export default connect(({ batchChange, loading }) => ({
  batchChange,
  tableLoading: loading.effects['batchChange/findTableData'],
  submitLoading: loading.effects['batchChange/submitFileList'],
}))(Form.create()(BatchChange));
