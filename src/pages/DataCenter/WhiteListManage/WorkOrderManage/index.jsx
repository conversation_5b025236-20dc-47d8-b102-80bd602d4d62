/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:26:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-14 14:40:57
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\WhiteListManage\WorkOrderManage\index.jsx
 * @Description:
 */
import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import SearchFrom from './SearchFrom';
import ExportApprove from '@/components/ExportApprove';

import { connect } from 'dryad';
import { Card, Button, message, Modal } from 'antd';
import { exportFile } from '@/utils/utils';
import {
  getCustomerInfoList,
  getCorrelationModelList,
  getNumberAttributionList,
  getSearchItemList,
} from '../../common';
import { Licensee, useLicensee } from 'ponshine';

// const statusList = [
//   { value: 1, label: '待审批' },
//   { value: 2, label: '审批通过' },
//   { value: 3, label: '审批驳回' },
//   { value: 4, label: '政企自动同步' },
//   { value: 5, label: '模型自动同步' },
// ];

// const changeTypeList = [
//   {
//     value: 1,
//     label: '新增',
//   },
//   {
//     value: 2,
//     label: '删除',
//   },
// ];

const WorkOrderManage = (props) => {
  const {
    workOrderManage: { tableData },
    tableLoading,
    dispatch,
  } = props;
  const formRef = useRef(null);

  const columns = [
    {
      title: '号码',
      dataIndex: 'phoneNum',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '号码归属',
      dataIndex: 'numberAttribution',
      align: 'center',

      width: 150,
      ellipsis: true,
    },

    {
      title: '状态',
      dataIndex: 'state',
      align: 'center',

      // render: (text) => statusList?.find((ele) => ele.value === text)?.label || '--',
      width: 150,
      ellipsis: true,
    },
    {
      title: '变更类型',
      dataIndex: 'changeType',
      align: 'center',

      // render: (text) => changeTypeList?.find((ele) => ele.value === text)?.label || '--',
      width: 150,
      ellipsis: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireDate',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '客户标签',
      dataIndex: 'customerTag',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'customerSubTab',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '关联模型',
      dataIndex: 'associationModel',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '证件号',
      dataIndex: 'documentsNumber',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '入网时间',
      dataIndex: 'accessTime',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '产权客户',
      dataIndex: 'propertyCustomer',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '产权证件号码',
      dataIndex: 'propertyCertificatesNumber',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '审批人',
      dataIndex: 'approver',
      align: 'center',

      width: 150,
      ellipsis: true,
    },

    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '审批时间',
      dataIndex: 'approvalTime',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '审批意见',
      dataIndex: 'approvalOpinions',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '省公司审批人',
      dataIndex: 'provincialApprover',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '省公司审批时间',
      dataIndex: 'provincialApprovalTime',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '省公司审批意见',
      dataIndex: 'provincialApprovalOpinions',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '工单号',
      dataIndex: 'workOrderId',
      align: 'center',

      width: 200,
      ellipsis: true,
    },
  ];

  const [searchParams, setSearchParams] = useState({});
  const [customerTagList, setCustomerTagList] = useState([]);
  const [numberAttributionList, setNumberAttributionList] = useState([]);
  const [correlationModelList, setCorrelationModelList] = useState([]);
  const [statusList, setStatusList] = useState([]);
  const [changeTypeList, setChangeTypeList] = useState([]);

  useEffect(() => {
    getCustomerInfoList({
      rosterType: 1,
      type: 2,
      callback: (data) => {
        setCustomerTagList(data);
      },
    });
    getCorrelationModelList((data) => {
      setCorrelationModelList(data);
    });
    getNumberAttributionList((data) => {
      setNumberAttributionList(data);
    });
    getSearchItemList('state_white_work', (data) => {
      setStatusList(data);
    });
    getSearchItemList('changeType', (data) => {
      setChangeTypeList(data);
    });

    // findTableDataPager();
  }, []);

  const findTableDataPager = ({ currentPage = 1, pageSize = 10, ...props } = {}) => {
    dispatch({
      type: 'workOrderManage/findTableData',
      payload: {
        ...props,
        currentPage,
        pageSize,
      },
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleSearch = () => {
    const { getFieldsValue } = formRef.current?.form;
    const formValue = getFieldsValue();
    const { applyTime } = formValue;
    findTableDataPager({
      ...formValue,
      applyStartTime: applyTime?.[0]?.format('YYYYMMDD000000'),
      applyEndTime: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
    setSearchParams({
      ...formValue,
      applyStartTime: applyTime?.[0]?.format('YYYYMMDD000000'),
      applyEndTime: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({
      ...searchParams,
      currentPage: current,
      pageSize,
    });
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/white/exportWhiteWorkOrderManage',
      title: '白名单工单管理导出',
      params: searchParams,
      method: 'POST',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };

  const handleClearSearch = () => {
    const {
      clearCustomerSubTagList,
      form: { resetFields },
    } = formRef.current;
    resetFields();
    findTableDataPager();
    clearCustomerSubTagList();
    setSearchParams({});
  };

  return (
    <Card>
      <Licensee license="whiteListWorkOrderManage_getWhiteWorkOrderManage">
        <SearchFrom
          wrappedComponentRef={formRef}
          customerTagList={customerTagList}
          change={getCustomerInfoList}
          numberAttributionList={numberAttributionList}
          correlationModelList={correlationModelList}
          statusList={statusList}
          changeTypeList={changeTypeList}
        >
          <div style={{ display: 'flex', justifyContent: 'end', marginBottom: 16 }}>
            <Licensee license="whiteListWorkOrderManage_getWhiteWorkOrderManage">
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                查询
              </Button>
              <Button style={{ marginRight: 16 }} onClick={handleClearSearch}>
                重置
              </Button>
            </Licensee>
            <Licensee license="whiteListWorkOrderManage_exportWhiteWorkOrderManage">
              {/* <Button type="primary" style={{ marginRight: 16 }} onClick={handleExport}>
            批量导出
          </Button> */}
              <ExportApprove
                buttonText="批量导出"
                exportParams={{
                  urlAPi: '/api/white/exportWhiteWorkOrderManage',
                  title: '白名单工单管理导出',
                  params: searchParams,
                  method: 'POST',
                  mime: 'xlsx',
                  isDate: true,
                  currentDateFormate: 'YYYYMMDD',
                }}
                moduleTile="白名单管理"
                // 是否校验商业秘密电子文件相关
                isVerifyhEncryption={true}
                disabledExport={!tableData?.list?.length}
              />
            </Licensee>
          </div>
        </SearchFrom>
      </Licensee>

      <StandardTable
        showSelectCount={false}
        rowSelectionProps={false}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        loading={tableLoading}
        scroll={{ x: 1200 }}
        isNeedAutoWidth={true}
      />
    </Card>
  );
};

export default connect(({ workOrderManage, loading }) => ({
  workOrderManage,
  tableLoading: loading.effects['workOrderManage/findTableData'],
}))(WorkOrderManage);
