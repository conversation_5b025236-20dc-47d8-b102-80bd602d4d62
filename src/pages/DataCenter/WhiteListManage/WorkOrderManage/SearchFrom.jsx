/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-16 15:08:38
 * @LastEditors: zhaoxiaowen
 * @LastEditTime: 2022-10-10 12:19:28
 * @FilePath: \hunanfanzha\src\pages\DataCenter\WhiteListManage\WorkOrderManage\SearchFrom.jsx
 * @Description:
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Form, Row, Col, Input, Select, Button, DatePicker } from 'antd';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const SearchFrom = Form.create()(
  forwardRef((props, ref) => {
    const {
      form,
      form: { getFieldDecorator, setFieldsValue },
      change,
      numberAttributionList,
      correlationModelList,
      statusList,
      changeTypeList,
      customerTagList,
      children,
    } = props;

    const [customerSubTagList, setCustomerSubTagList] = useState([]);

    useImperativeHandle(ref, () => ({
      form: form,
      clearCustomerSubTagList: () => {
        setCustomerSubTagList([]);
      },
    }));

    const handleChange = (value) => {
      setFieldsValue({ customerSubTab: undefined });
      if (!value) {
        return setCustomerSubTagList([]);
      }
      change({
        rosterType: 1,
        rosterTge: value,
        type: 1,
        callback: (data) => {
          setCustomerSubTagList(data);
        },
      });
    };
    return (
      <Form {...formItemLayout}>
        <Row gutter={[24]}>
          <Col span={6}>
            <FormItem label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态">
              {getFieldDecorator('state')(
                <Select placeholder="请选择" allowClear>
                  {statusList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="号码归属">
              {getFieldDecorator('numberAttribution')(
                <Select placeholder="请选择" allowClear>
                  {numberAttributionList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="客户标签">
              {getFieldDecorator('customerTag')(
                <Select placeholder="请选择" onChange={handleChange} allowClear>
                  {customerTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="客户子标签">
              {getFieldDecorator('customerSubTab')(
                <Select placeholder="请选择" allowClear>
                  {customerSubTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="申请时间">
              {getFieldDecorator('applyTime')(
                <RangePicker
                  allowClear
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="关联模型">
              {getFieldDecorator('associationModel')(
                <Select placeholder="请选择" allowClear>
                  {correlationModelList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="客户名称">
              {getFieldDecorator('customerName')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="申请人">
              {getFieldDecorator('applicant')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="变更类型">
              {getFieldDecorator('changeType')(
                <Select placeholder="请选择" allowClear>
                  {changeTypeList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="工单号">
              {getFieldDecorator('workOrderId')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          {children && <Col span={6}>{children}</Col>}
        </Row>
      </Form>
    );
  }),
);

export default SearchFrom;
