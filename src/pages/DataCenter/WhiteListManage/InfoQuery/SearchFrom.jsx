/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-16 15:08:38
 * @LastEditors: zhaoxiaowen
 * @LastEditTime: 2022-09-26 17:10:15
 * @FilePath: \hunanfanzha\src\pages\DataCenter\WhiteListManage\InfoQuery\SearchFrom.jsx
 * @Description:
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Form, Row, Col, Input, Select, Button, DatePicker, message } from 'antd';
import request from 'ponshine-request';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const SearchFrom = Form.create()(
  forwardRef((props, ref) => {
    const {
      form,
      form: { getFieldDecorator, setFieldsValue },
      change,
      numberAttributionList,
      correlationModelList,
      statusList,
      customerTagList,
    } = props;

    const [customerSubTagList, setCustomerSubTagList] = useState([]);
    const [whiteListTypeData, setWhiteListTypeData] = useState([]);

    useImperativeHandle(ref, () => ({
      form: form,
      clearCustomerSubTagList: () => {
        setCustomerSubTagList([]);
      },
    }));

    const handleChange = (value) => {
      setFieldsValue({ customerSubTab: undefined });
      if (!value) {
        return setCustomerSubTagList([]);
      }
      change({
        rosterType: 1,
        rosterTge: value,
        type: 2,
        callback: (data) => {
          setCustomerSubTagList(data);
        },
      });
    };

    const getWhiteListType = async () => {
      const response = await request('/api/white/getWhiteTypeByRole', {
        method: 'GET',
      });
      if (response.code === 200) {
        setWhiteListTypeData(response?.data || []);
      } else {
        message.error(response.message);
      }
    };

    useEffect(() => {
      getWhiteListType();
    }, []);

    return (
      <Form {...formItemLayout}>
        <Row gutter={[24]}>
          <Col span={6}>
            <FormItem label="号码">
              {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态">
              {getFieldDecorator('state')(
                <Select placeholder="请选择" allowClear>
                  {statusList.map((ele) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="客户标签">
              {getFieldDecorator('customerTag')(
                <Select placeholder="请选择" onChange={handleChange} allowClear>
                  {customerTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="客户子标签">
              {getFieldDecorator('customerSubTab')(
                <Select placeholder="请选择" allowClear>
                  {customerSubTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={6}>
            <FormItem label="关联模型">
              {getFieldDecorator('associationModel')(
                <Select placeholder="请选择" allowClear>
                  {correlationModelList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={ele.value}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="证件号">
              {getFieldDecorator('documentsNumber')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="开始时间">
              {getFieldDecorator('startTime')(
                <RangePicker
                  allowClear
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="到期时间">
              {getFieldDecorator('expireTime')(
                <RangePicker
                  allowClear
                  format="YYYY-MM-DD"
                  placeholder={['开始时间', '结束时间']}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="客户名称">
              {getFieldDecorator('customerName')(<Input placeholder="请输入" allowClear />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="号码归属">
              {getFieldDecorator('numberAttribution')(
                <Select placeholder="请选择" allowClear>
                  {numberAttributionList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="白名单类型">
              {getFieldDecorator('type')(
                <Select placeholder="请选择" allowClear>
                  {whiteListTypeData.map((ele, index) => (
                    <Select.Option value={ele} key={index + 1}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="是否已生效">
              {getFieldDecorator('bigState')(
                <Select placeholder="请选择" allowClear>
                  <Select.Option value={'是'} key={'1'}>
                    是
                  </Select.Option>
                  <Select.Option value={'否'} key={'2'}>
                    否
                  </Select.Option>
                </Select>,
              )}
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }),
);

export default SearchFrom;
