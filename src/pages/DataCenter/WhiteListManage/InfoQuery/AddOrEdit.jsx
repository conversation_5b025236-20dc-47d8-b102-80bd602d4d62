import { Modal, Form, Input, Select, DatePicker, Row, Col } from 'antd';
import React, { useEffect, useState } from 'react';
import moment from 'moment';

import { getCustomerInfoList } from '../../common';
import request from '@/utils/request';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const FormItem = Form.Item;
export default Form.create()((props) => {
  const {
    visible,
    form: { getFieldDecorator, validateFields, resetFields, setFieldsValue },
    currentRow = {},
    confimAdd,
    cancel,
    change,
  } = props;

  const [customerTagList, setCustomerTagList] = useState([]);
  const [customerSubTagList, setCustomerSubTagList] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      getCustomerInfoList({
        rosterType: 1,
        type: 1,
        callback: (data) => {
          setCustomerTagList(data);
        },
      });
      if (currentRow?.id) {
        getCustomerInfoList({
          rosterType: 1,
          rosterTge: currentRow?.customerTag,
          type: 1,
          callback: (data) => {
            setCustomerSubTagList(data);
          },
        });
      }
    }
  }, [visible]);

  const handleChange = (value) => {
    setFieldsValue({ customerSubTab: undefined });
    if (!value) {
      return setCustomerSubTagList([]);
    }

    change({
      rosterType: 1,
      rosterTge: value,
      type: 1,
      callback: (data) => {
        setCustomerSubTagList(data);
      },
    });
  };

  const handleOk = () => {
    validateFields((err, fieldsValue) => {
      if (!err) {
        setConfirmLoading(true);
        confimAdd({ ...fieldsValue, id: currentRow?.id || undefined }, () => {
          setConfirmLoading(false);
        });
      }
    });
  };
  const [netWorkList, setNetWorkList] = useState([]);

  const getOrganizationByUser = () => {
    request(`/api/hn/systemConfig/getOrganizationByUser`, {
      method: 'GET',
    }).then((res) => {
      if (res && res.code == '200' && res.data) {
        setNetWorkList(res.data || []);
      } else {
        setNetWorkList([]);
      }
    });
  };

  useEffect(() => {
    if (currentRow?.id) {
      return false;
    } else {
      getOrganizationByUser();
    }
  }, []);

  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };

  return (
    <Modal
      title={`白名单单个${currentRow?.id ? '修改' : '新增'}`}
      width={700}
      visible={visible}
      onOk={handleOk}
      okText="确认添加"
      afterClose={() => {
        resetFields();
      }}
      onCancel={cancel}
      confirmLoading={confirmLoading}
      maskClosable={false}
    >
      <Form {...formItemLayout}>
        <Row>
          <Col span={12}>
            <FormItem label="号码">
              {getFieldDecorator('phoneNum', {
                initialValue: currentRow?.phoneNum,
                rules: [
                  {
                    required: true,
                    message: '请输入手机号或者固话',
                  },
                  {
                    pattern: /^[0-9]+$/,
                    message: '只能输入数字',
                  },
                ],
              })(<Input placeholder="请输入" allowClear disabled={currentRow?.id} />)}
            </FormItem>
          </Col>
          {!currentRow.id && (
            <Col span={12}>
              <Form.Item label="本地网">
                {getFieldDecorator('numberAttribution', {
                  rules: [
                    {
                      required: true,
                      message: '请选择本地网',
                    },
                  ],
                })(
                  <Select placeholder="请选择" allowClear>
                    {netWorkList.map((item) => (
                      <Option value={item.value}>{item.name}</Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          )}

          <Col span={12}>
            <FormItem label="到期时间">
              {getFieldDecorator('time', {
                initialValue: currentRow?.expireDate
                  ? moment(currentRow?.expireDate, 'YYYY-MM-DD')
                  : undefined,
                rules: [
                  {
                    required: true,
                    message: '请选择到期时间',
                  },
                ],
              })(<DatePicker style={{ width: '100%' }} disabledDate={disabledDate} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="客户标签">
              {getFieldDecorator('customerTag', {
                initialValue: currentRow?.customerTag,
                rules: [
                  {
                    required: true,
                    message: '请选择客户标签',
                  },
                ],
              })(
                <Select placeholder="请选择" onChange={handleChange}>
                  {customerTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label="客户子标签">
              {getFieldDecorator('customerSubTab', {
                initialValue: currentRow?.customerSubTab,
                rules: [
                  {
                    required: true,
                    message: '请选择客户子标签',
                  },
                ],
              })(
                <Select placeholder="请选择">
                  {customerSubTagList?.map((ele, index) => (
                    <Select.Option value={ele} key={index}>
                      {ele}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={24}>
            <FormItem label="原因" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
              {getFieldDecorator('reason', {
                initialValue: currentRow?.reason,
                rules: [
                  {
                    required: true,
                    message: '请输入原因',
                  },
                ],
              })(<TextArea placeholder="请输入" />)}
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
});
