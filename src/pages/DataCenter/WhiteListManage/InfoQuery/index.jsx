/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:26:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-14 14:33:02
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\WhiteListManage\InfoQuery\index.jsx
 * @Description:
 */
import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/StandardTable';
import SearchFrom from './SearchFrom';
import AddOrEdit from './AddOrEdit';
import Delete from './Delete';
import { connect } from 'dryad';
import { Card, Button, message, Modal } from 'antd';
import { exportFile } from '@/utils/utils';
import {
  getCustomerInfoList,
  getCorrelationModelList,
  getNumberAttributionList,
  getSearchItemList,
} from '../../common';
import { Licensee, useLicensee } from 'ponshine';
import { Tooltip } from 'demasia-pro-layout';
import ExportApprove from '@/components/ExportApprove';

const InfoQuery = (props) => {
  const {
    whiteListInfoQuery: { tableData },
    tableLoading,
    dispatch,
  } = props;
  const formRef = useRef(null);

  const columns = [
    {
      title: '号码',
      dataIndex: 'phoneNum',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '号码归属',
      dataIndex: 'numberAttribution',
      align: 'center',
      width: 150,
      ellipsis: true,
    },

    {
      title: '状态',
      dataIndex: 'state',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireDate',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '白名单类型',
      dataIndex: 'type',
      align: 'center',
      width: 150,
      ellipsis: true,
    },

    {
      title: '客户标签',
      dataIndex: 'customerTag',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'customerSubTab',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '关联模型',
      dataIndex: 'associationModel',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '证件号',
      dataIndex: 'documentsNumber',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '入网时间',
      dataIndex: 'accessTime',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '开始时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '是否生效',
      dataIndex: 'bigState',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
  ];

  const [selectedRows, setSelectedRows] = useState([]);
  const [visible, setVisible] = useState(false);
  const [deleteVisible, setDeleteVisible] = useState(false);

  const [searchParams, setSearchParams] = useState({});
  const [customerTagList, setCustomerTagList] = useState([]);
  const [numberAttributionList, setNumberAttributionList] = useState([]);
  const [correlationModelList, setCorrelationModelList] = useState([]);
  const [statusList, setStatusList] = useState([]);

  useEffect(() => {
    getCustomerInfoList({
      rosterType: 1,
      type: 2,
      callback: (data) => {
        setCustomerTagList(data);
      },
    });
    getCorrelationModelList((data) => {
      setCorrelationModelList(data);
    });
    getNumberAttributionList((data) => {
      setNumberAttributionList(data);
    });
    getSearchItemList('state_white', (data) => {
      setStatusList(data);
    });
    // findTableDataPager();
  }, []);

  const findTableDataPager = ({ currentPage = 1, pageSize = 10, ...props } = {}) => {
    dispatch({
      type: 'whiteListInfoQuery/findTableData',
      payload: {
        ...props,
        currentPage,
        pageSize,
      },
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleSearch = () => {
    const {
      form: { getFieldsValue },
    } = formRef.current;
    const formValue = getFieldsValue();
    const { startTime, expireTime } = formValue;
    findTableDataPager({
      ...formValue,
      gmtCreateStart: startTime?.[0]?.format('YYYYMMDD000000'),
      gmtCreateEnd: startTime?.[1]?.format('YYYYMMDD235959'),
      expireDateStart: expireTime?.[0]?.format('YYYY-MM-DD'),
      expireDateEnd: expireTime?.[1]?.format('YYYY-MM-DD'),
      expireTime: undefined,
      startTime: undefined,
    });
    setSelectedRows([]);
    setSearchParams({
      ...formValue,
      gmtCreateStart: startTime?.[0]?.format('YYYYMMDD000000'),
      gmtCreateEnd: startTime?.[1]?.format('YYYYMMDD235959'),
      expireDateStart: expireTime?.[0]?.format('YYYY-MM-DD'),
      expireDateEnd: expireTime?.[1]?.format('YYYY-MM-DD'),
      expireTime: undefined,
      startTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({
      currentPage: current,
      pageSize,
      ...searchParams,
    });
    setSelectedRows([]);
  };

  const handleSelectRows = (selectedRows) => {
    setSelectedRows(selectedRows);
  };

  const handleConfirm = (params, callback) => {
    dispatch({
      type: `whiteListInfoQuery/${params?.id ? 'updateWhiteInfo' : 'addWhiteInfo'}`,
      payload: { ...params, expireDate: params?.time?.format('YYYYMMDDHHmmss'), time: undefined },
      callback: (res) => {
        callback();
        if (res.code === 200) {
          message.success(res.message);
          reload();
          setVisible(false);
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const reload = () => {
    const {
      form: { resetFields },
      clearCustomerSubTagList,
    } = formRef.current;
    resetFields();
    setSearchParams({});
    findTableDataPager();
    setSelectedRows([]);
    clearCustomerSubTagList();
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/white/exportWhiteInformation',
      title: '白名单信息导出',
      params: searchParams,
      method: 'POST',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
    });
  };

  const handleDelete = (formValue) => {
    dispatch({
      type: 'whiteListInfoQuery/deleteWhiteInfo',
      payload: {
        ...formValue,
        ids: selectedRows?.map((ele) => ele.id),
      },
      callback: (res) => {
        if (res.code === 200) {
          message.success(res.message);
          setDeleteVisible(false);
          reload();
        } else {
          message.error(res.message);
        }
      },
    });
  };
  const filterListStr =
    '号码,号码归属,状态,到期时间,白名单类型,客户标签,客户子标签,关联模型,原因,客户名称,证件号,入网时间,开始时间,是否生效,操作';
  const filterList = filterListStr.split(',');
  let newfilterList = [];
  columns.forEach((item) => {
    if (
      filterList.some((items) => {
        return items == item.title;
      })
    ) {
      newfilterList.push({ dataIndex: item.dataIndex });
    }
  });
  return (
    <Card>
      <Licensee license="whiteListInfoQuery_getWhiteInformation">
        <SearchFrom
          wrappedComponentRef={formRef}
          customerTagList={customerTagList}
          change={getCustomerInfoList}
          numberAttributionList={numberAttributionList}
          correlationModelList={correlationModelList}
          statusList={statusList}
        />
      </Licensee>
      <div style={{ display: 'flex', justifyContent: 'end', marginBottom: 16 }}>
        <Licensee license="whiteListInfoQuery_getWhiteInformation">
          <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
            查询
          </Button>
          <Button style={{ marginRight: 16 }} onClick={reload}>
            重置
          </Button>
        </Licensee>
        <Licensee license="whiteListInfoQuery_exportWhiteInformation">
          {/* <Button type="primary" style={{ marginRight: 16 }} onClick={handleExport}>
            数据导出
          </Button> */}
          <ExportApprove
            buttonStyle={{ marginRight: 16 }}
            exportParams={{
              urlAPi: '/api/white/exportWhiteInformation',
              title: '白名单信息导出',
              params: searchParams,
              method: 'POST',
              mime: 'xlsx',
              isDate: true,
              currentDateFormate: 'YYYYMMDD',
            }}
            moduleTile="白名单管理"
            // 是否校验商业秘密电子文件相关
            isVerifyhEncryption={true}
            disabledExport={!tableData?.list?.length}
          />
        </Licensee>
        <Licensee license="whiteListInfoQuery_addWhiteInformation">
          <Button
            type="primary"
            icon="plus"
            style={{ marginRight: 16 }}
            onClick={() => {
              setSelectedRows([]);
              setVisible(true);
            }}
          >
            新建
          </Button>
        </Licensee>
        <Licensee license="whiteListInfoQuery_updateWhiteInformation">
          <Button
            type="primary"
            icon="edit"
            disabled={selectedRows?.length !== 1}
            style={{ marginRight: 16 }}
            onClick={() => {
              if (selectedRows.find((v) => v.type !== '手工白名单'))
                return message.error('仅手工白名单可进行编辑！');
              setVisible(true);
            }}
          >
            编辑
          </Button>
        </Licensee>
        <Licensee license="whiteListInfoQuery_deleteWhiteInformation">
          <Button
            type="danger"
            icon="delete"
            disabled={selectedRows?.length < 1}
            onClick={() => {
              if (selectedRows.find((v) => v.type !== '手工白名单'))
                return message.error('仅手工白名单可进行删除！');
              setDeleteVisible(true);
            }}
          >
            删除
          </Button>
        </Licensee>
      </div>
      <StandardTable
        // showSelectCount={false}
        detailColumns={columns.map((item) => {
          return {
            ...item,
            key: item?.dataIndex,
            ellipsis: true,
            width: 80,
            align: 'center',

            // render: (text) => {
            //   return <Tooltip title={text}>{text || '--'}</Tooltip>;
            // },
          };
        })}
        tools={true}
        float="right"
        columns={newfilterList}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        onSelectRow={handleSelectRows}
        selectedRows={selectedRows}
        loading={tableLoading}
        scroll={{ x: 1200 }}
        isNeedAutoWidth={true}
      />
      <AddOrEdit
        visible={visible}
        confimAdd={handleConfirm}
        cancel={() => {
          setVisible(false);
          selectedRows.length && setSelectedRows([]);
        }}
        currentRow={selectedRows?.[0] || {}}
        change={getCustomerInfoList}
      />
      <Delete
        visible={deleteVisible}
        confirm={handleDelete}
        cancel={() => {
          setDeleteVisible(false);
        }}
      />
    </Card>
  );
};

export default connect(({ whiteListInfoQuery, loading }) => ({
  whiteListInfoQuery,
  tableLoading: loading.effects['whiteListInfoQuery/findTableData'],
}))(InfoQuery);
