/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-21 17:20:57
 * @LastEditors: zhao<PERSON><PERSON>en
 * @LastEditTime: 2022-09-23 13:49:34
 * @FilePath: \hunanfanzha\src\pages\DataCenter\WhiteListManage\InfoQuery\Delete.jsx
 * @Description:
 */
import React from 'react';
import { Modal, Form, Input } from 'antd';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const Delete = Form.create()(
  ({ visible, form: { validateFields, getFieldDecorator, resetFields }, confirm, cancel }) => {
    const handleOk = () => {
      validateFields((err, fieldsValue) => {
        if (!err) {
          confirm(fieldsValue);
        }
      });
    };
    return (
      <Modal
        title="白名单删除"
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
      >
        <Form {...formItemLayout}>
          <Form.Item label="删除原因">
            {getFieldDecorator('reason', {
              rules: [
                {
                  required: true,
                  message: '请输入原因',
                },
              ],
            })(<TextArea placeholder="请输入" />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default Delete;
