/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-20 15:26:00
 * @LastEditors: zxw
 * @LastEditTime: 2023-08-15 10:28:06
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\WhiteListManage\WorkOrderExamine\index.jsx
 * @Description:
 */
import React, { useRef, Fragment, useState, useEffect } from 'react';
import StandardTable from '@/components/VirtualStandardTable';
import { VTComponents } from 'virtualizedtableforantd';

import SearchFrom from './SearchFrom';
import Approve from './Approve';
import ApproveFile from './ApproveFile';

import { connect } from 'dryad';
import { Card, Button, message, Modal } from 'antd';
import { exportFile } from '@/utils/utils';

import {
  getCustomerInfoList,
  getCorrelationModelList,
  getNumberAttributionList,
  getSearchItemList,
} from '../../common';
import { Licensee, useLicensee } from 'ponshine';

const WorkOrderExamine = (props) => {
  const {
    workOrderExamine: { tableData },
    tableLoading,
    dispatch,
  } = props;
  const formRef = useRef(null);

  const columns = [
    {
      title: '号码',
      dataIndex: 'phoneNum',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '审批专业',
      dataIndex: 'approvalProfessional',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '号码归属',
      dataIndex: 'numberAttribution',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '产权客户',
      dataIndex: 'propertyCustomer',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '产权证件号码',
      dataIndex: 'propertyCertificatesNumber',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '变更类型',
      dataIndex: 'changeType',
      align: 'center',

      // render: (text) => changeTypeList?.find((ele) => ele.value === text)?.label || '--',
      width: 150,
      ellipsis: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireDate',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '客户标签',
      dataIndex: 'customerTag',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '客户子标签',
      dataIndex: 'customerSubTab',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      align: 'center',

      width: 150,
      ellipsis: true,
    },

    {
      title: '客户名称',
      dataIndex: 'customerName',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '证件号',
      dataIndex: 'documentsNumber',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '入网时间',
      dataIndex: 'accessTime',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      align: 'center',

      width: 150,
      ellipsis: true,
    },

    {
      title: '工单号',
      dataIndex: 'workOrderId',
      align: 'center',

      width: 150,
      ellipsis: true,
    },
  ];

  const [searchParams, setSearchParams] = useState({});
  const [customerTagList, setCustomerTagList] = useState([]);
  const [numberAttributionList, setNumberAttributionList] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [approveVisible, setApproveVisible] = useState(false);
  const [apporveFileVisible, setApporveFileVisible] = useState(false);
  const [changeTypeList, setChangeTypeList] = useState([]);
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    getCustomerInfoList({
      rosterType: 1,
      type: 2,
      callback: (data) => {
        setCustomerTagList(data);
      },
    });

    getNumberAttributionList((data) => {
      setNumberAttributionList(data);
    });
    getSearchItemList('changeType', (data) => {
      setChangeTypeList(data);
    });
    findTableDataPager();
  }, []);

  const findTableDataPager = ({ currentPage = 1, pageSize = 10, ...props } = {}) => {
    dispatch({
      type: 'workOrderExamine/findTableData',
      payload: {
        ...props,
        currentPage,
        pageSize,
      },
      callback: (response) => {
        if (response.code === 200) {
          response.message !== '操作成功' && message.info(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleSearch = () => {
    const {
      form: { getFieldsValue },
    } = formRef.current;
    const formValue = getFieldsValue();
    const { applyTime } = formValue;
    findTableDataPager({
      ...formValue,
      applyStartTime: applyTime?.[0]?.format('YYYYMMDD000000'),
      applyEndTime: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
    setSearchParams({
      ...formValue,
      applyStartTime: applyTime?.[0]?.format('YYYYMMDD000000'),
      applyEndTime: applyTime?.[1]?.format('YYYYMMDD235959'),
      applyTime: undefined,
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    findTableDataPager({
      currentPage: current,
      pageSize,
      ...searchParams,
    });
  };

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/white/exportWhiteWorkOrderApproval',
      title: '白名单工单审批导出',
      params: searchParams,
      method: 'POST',
      mime: 'xlsx',
      isDate: true,
      currentDateFormate: 'YYYYMMDD',
      isVerifyhEncryption: true,

      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const handleSelectRows = (selectedRows) => {
    setSelectedRows(selectedRows);
  };

  const handleBatchApprove = () => {};

  const handleApprove = () => {
    // 勾选到非待审批项时提示“存在号码已审批，请重新选择”
    setApproveVisible(true);
  };

  const confirmApprove = (formValue, callback) => {
    dispatch({
      type: 'workOrderExamine/whiteListApprove',
      payload: {
        ...formValue,
        ids: selectedRows?.map((ele) => ele.id),
      },
      callback: (res) => {
        callback();
        if (res.code === 200) {
          message.success(res.message);
          setApproveVisible(false);
          reload();
        } else {
          message.error(res.message);
        }
      },
    });
  };

  const reload = () => {
    const {
      form: { resetFields },
      clearCustomerSubTagList,
    } = formRef.current;
    resetFields();
    setSearchParams({});
    findTableDataPager();
    setSelectedRows([]);
    clearCustomerSubTagList();
  };

  return (
    <Card>
      <Licensee license="whiteListWorkOrderExamine_getWhiteWorkOrderApproval">
        <SearchFrom
          wrappedComponentRef={formRef}
          customerTagList={customerTagList}
          change={getCustomerInfoList}
          numberAttributionList={numberAttributionList}
          changeTypeList={changeTypeList}
        >
          <div style={{ display: 'flex', justifyContent: 'end', marginBottom: 16 }}>
            <Licensee license="whiteListWorkOrderExamine_getWhiteWorkOrderApproval">
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                查询
              </Button>
              <Button style={{ marginRight: 16 }} onClick={reload}>
                重置
              </Button>
            </Licensee>
            <Licensee license="whiteListWorkOrderExamine_singleApprovalWhiteWorkOrderApproval">
              <Button
                type="primary"
                style={{ marginRight: 16 }}
                disabled={!selectedRows?.length}
                onClick={handleApprove}
              >
                审批
              </Button>
            </Licensee>
            <Licensee license="whiteListWorkOrderExamine_batchApprovalWhiteWorkOrderApproval">
              <Button
                type="primary"
                style={{ marginRight: 16 }}
                onClick={() => {
                  setApporveFileVisible(true);
                }}
              >
                批量提交审核结果
              </Button>
            </Licensee>
            <Licensee license="whiteListWorkOrderExamine_exportWhiteWorkOrderApproval">
              <Button
                type="primary"
                onClick={handleExport}
                loading={exportLoading}
                disabled={!tableData?.list?.length}
              >
                批量导出
              </Button>
            </Licensee>
          </div>
        </SearchFrom>
      </Licensee>

      <StandardTable
        // showSelectCount={false}
        columns={columns}
        data={tableData}
        onChange={handleTableChange}
        rowKey="id"
        loading={tableLoading}
        scroll={{ x: 1200, y: 600 }}
        onSelectRow={handleSelectRows}
        selectedRows={selectedRows}
        isNeedAutoWidth={true}
        components={VTComponents({
          id: 1735021351979,
        })}
      />
      <Approve
        visible={approveVisible}
        confirm={confirmApprove}
        cancel={() => {
          setApproveVisible(false);
        }}
      />
      <ApproveFile
        visible={apporveFileVisible}
        cancel={() => {
          setApporveFileVisible(false);
        }}
        reload={reload}
      />
    </Card>
  );
};

export default connect(({ workOrderExamine, loading }) => ({
  workOrderExamine,
  tableLoading: loading.effects['workOrderExamine/findTableData'],
}))(WorkOrderExamine);
