/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-22 13:57:59
 * @LastEditors: zhao<PERSON><PERSON>en
 * @LastEditTime: 2022-09-23 15:01:21
 * @FilePath: \hunanfanzha\src\pages\DataCenter\WhiteListManage\WorkOrderExamine\Approve.jsx
 * @Description:
 */
import { Modal, Form, Select, Input } from 'antd';
import React, { useEffect, useState } from 'react';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const approveType = [
  { value: 1, label: '审批通过' },
  { value: 2, label: '审批驳回' },
];

const Approve = Form.create()(
  ({
    visible,
    cancel,
    confirm,
    form: { validateFields, getFieldDecorator, resetFields, setFieldsValue },
  }) => {
    const [confirmLoading, setConfirmLoading] = useState(false);
    const handleOk = () => {
      validateFields((err, fieldsValue) => {
        if (!err) {
          setConfirmLoading(true);
          confirm(fieldsValue, () => {
            setConfirmLoading(false);
          });
        }
      });
    };

    const handleChange = (value) => {
      if (!value) return;
      setFieldsValue({ approvalOpinions: approveType?.find((ele) => ele.value === value)?.label });
    };

    return (
      <Modal
        title="白名单审批"
        visible={visible}
        onOk={handleOk}
        onCancel={cancel}
        afterClose={() => {
          resetFields();
        }}
        confirmLoading={confirmLoading}
        maskClosable={false}
      >
        <Form {...formItemLayout}>
          <Form.Item label="审批结果">
            {getFieldDecorator('approvalResult', {
              rules: [
                {
                  required: true,
                  message: '请选择审批结果',
                },
              ],
            })(
              <Select placeholder="请选择" onChange={handleChange}>
                {approveType.map((ele) => (
                  <Select.Option value={ele.value} key={ele.value}>
                    {ele.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="审批意见">
            {getFieldDecorator('approvalOpinions', {
              rules: [
                {
                  required: true,
                  message: '请输入审批意见',
                },
              ],
            })(<TextArea placeholder="请输入" allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default Approve;
