/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-09-22 14:25:03
 * @LastEditors: zhaoxiaowen
 * @LastEditTime: 2022-09-26 14:23:43
 * @FilePath: \hunanfanzha\src\pages\DataCenter\WhiteListManage\WorkOrderExamine\ApproveFile.jsx
 * @Description:
 */
import React, { Fragment, useState } from 'react';
import { Modal, Upload, Icon, Button, message } from 'antd';

import { exportFile } from '@/utils/utils';
import request from '@/utils/request';

const BatchApporve = ({ visible, reload, cancel }) => {
  const [importLoading, setImportLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [errModalTip, setErrModalTip] = useState('');
  const [errFilePath, setErrFilePath] = useState('');
  const [errorfileVisible, setErrorfileVisible] = useState(false);

  const uploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      const tag = file.name.substring(file.name.lastIndexOf('.'));
      const size = file.size / 1024 / 1024;
      if (tag === '.xlsx') {
        setFileList([file]);
        return false;
      }
      message.warning('文件类型错误');

      // if (size > 1) {
      //   message.destroy();
      //   message.warning('文件大小不能超过1M');
      //   return false;
      // }
    },
    onChange: (info) => {
      // let fileList = info.fileList;
      // const str = String(fileList[0]?.name)
      // if (str.split(".")[0].length > 10) {
      //   fileList[0].name = fileList[0].name.substring(0, 10) + '***.' + str.split(".")[1]
      //   // const file = new File([fileList[0]],"aa",{type:info.file.type||".xls"})
      //   setFileList(fileList)
      // }
    },
    fileList,
  };

  const okHandleImport = () => {
    if (fileList.length) {
      const file = new FormData();
      file.append('file', fileList[0]);

      setImportLoading(true);
      request({
        url: '/api/white/batchApprovalWhiteWorkOrderApproval',
        method: 'POST',
        data: file,
        getResponse: true,
      })
        .then((res) => {
          const response = res.data;
          if (response.code === 200) {
            if (response?.data?.hasFail) {
              setErrorfileVisible(true);
            } else {
              message.success(response.message);
              cancel();
              reload();
            }
          } else {
            message.error(response.message);
          }
        })
        .finally(() => {
          setImportLoading(false);
        });
    } else if (!fileList.length) {
      return message.warning('请添加导入的文件!');
    } else {
      message.destroy();
      setFileList([]);
    }
  };

  const handleDownload = () => {
    exportFile({
      urlAPi: '/api/template/getTemplate',
      title: '白名单批量审批模板',
      params: { templateCode: 'whiteApproval' },
      method: 'GET',
      mime: 'xlsx',
    });
  };

  // 导出错误报告
  const handleDownloadErr = () => {
    setErrorfileVisible(false);
    exportFile({
      urlAPi: '/api/white/batchApprovalWhiteWorkOrderApprovalError',
      title: '白名单批量审批错误数据',
      method: 'POST',
      mime: 'xlsx',
    });
  };

  return (
    <Fragment>
      <Modal
        confirmLoading={importLoading}
        title="提交审核结果"
        visible={visible}
        maskClosable={false}
        width={500}
        onOk={() => okHandleImport()}
        onCancel={cancel}
        destroyOnClose
        okText="确认提交"
        cancelText="取消"
        afterClose={() => {
          setFileList([]);
        }}
      >
        <Upload {...uploadProps} accept=".xlsx">
          <Button>
            <Icon type="upload" /> 选择文件
          </Button>
          {uploadProps.fileList.length === 0 ? '  未选择任何文件' : ''}
        </Upload>
        <p style={{ color: 'rgba(0,0,0,0.5)', marginTop: 6, marginBottom: 16 }}>
          *每个文件不可超过1万条号码
        </p>
        <a onClick={handleDownload}>模板下载</a>
      </Modal>
      {/* 下载错误报告 errorfilepath */}
      <Modal
        title="异常提示"
        visible={errorfileVisible}
        width={800}
        maskClosable={false}
        onCancel={() => {
          setErrorfileVisible(false);
          setImportLoading(false);
          cancel();
        }}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        onOk={() => handleDownloadErr()}
      >
        <span>
          <Icon
            type="exclamation-circle"
            style={{ marginRight: 5, color: 'orange', fontWeight: 'bolder' }}
          />
          导入数据存在错误，点击确认可下载错误数据
        </span>
      </Modal>
    </Fragment>
  );
};

export default BatchApporve;
