import React, { useState, useEffect } from 'react';
import { Card, Form, DatePicker, Button, message, Tooltip } from 'antd';
import styles from './index.less';

import moment from 'moment';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { exportFile } from '@/utils/utils';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator, getFieldsValue } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState({});
  const [exportLoading, setExportLoading] = useState(false);
  const initDate = [moment().subtract(14, 'days'), moment().subtract(1, 'days')];

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);

    const params = {
      ...props,
      timeStart: props.timeStart || initDate?.[0]?.format('YYYY-MM-DD 00:00:00'),
      timeEnd: props.timeEnd || initDate?.[1]?.format('YYYY-MM-DD 23:59:59'),
    };
    const response = await selectPage({
      pageNum,
      pageSize,
      ...params,
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...params });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response?.data?.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas();
  }, []);

  const handleSearch = () => {
    const values = getFieldsValue();
    const { time } = values;

    getListDatas({
      ...values,
      time: undefined,
      timeStart: time?.[0]?.format('YYYY-MM-DD 00:00:00'),
      timeEnd: time?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...searchParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas();
  };

  let columns = [
    {
      title: '数据分析时间',
      width: 140,
      dataIndex: 'analysisTime',
      ellipsis: true,
    },
    {
      title: '告警日期',
      dataIndex: 'alarmTime',
      width: 140,
      ellipsis: true,
    },
    {
      title: '主叫号码',
      width: 100,
      dataIndex: 'callingPhone',
      ellipsis: true,
    },
    {
      title: '主叫归属地',
      width: 100,
      dataIndex: 'callingArea',
      ellipsis: true,
    },
    // {
    //   title: '主叫业务类型',
    //   width: 100,
    //   dataIndex: 'callingBusinessType',
    //   ellipsis: true,
    // },
    // {
    //   title: '被叫号码',
    //   width: 120,
    //   dataIndex: 'calledPhone',
    //   ellipsis: true,
    // },
    // {
    //   title: '被叫归属地',
    //   width: 100,
    //   dataIndex: 'calledArea',
    //   ellipsis: true,
    // },
    // {
    //   title: '被叫业务类型',
    //   width: 100,
    //   dataIndex: 'calledBusinessType',
    //   ellipsis: true,
    // },
    // {
    //   title: '发送频次',
    //   width: 100,
    //   dataIndex: 'sendCount',
    //   ellipsis: true,
    // },
    {
      title: '短信内容',
      width: 250,
      dataIndex: 'msgContent',
      render: (v) => {
        return (
          <Tooltip title={v} placement="topLeft">
            <div className={styles.msgContent}>{v}</div>
          </Tooltip>
        );
      },
    },

    {
      title: '数据类型',
      width: 100,
      dataIndex: 'dataType',
      ellipsis: true,
    },
    {
      title: '告警类型',
      width: 100,
      dataIndex: 'alarmType',
      ellipsis: true,
    },
    {
      title: '涉诈类型',
      width: 100,
      dataIndex: 'fraudType',
      ellipsis: true,
    },
  ];

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/hn/mms/exportMmsInfo',
      decode: true,
      method: 'POST',
      requestType: 'json',
      params: searchParams,
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  return (
    <Card>
      <Form layout="inline">
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Form.Item label="查询时间">
            {getFieldDecorator('time', {
              initialValue: initDate,
            })(<RangePicker format={'YYYY-MM-DD'} allowClear={false} />)}
          </Form.Item>
          <Form.Item style={{marginRight: 0}}>
            <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
              查询
            </Button>
            <Button onClick={onReset} style={{ marginRight: 10 }}>
              重置
            </Button>
            <Button
              type="primary"
              onClick={handleExport}
              loading={exportLoading}
              disabled={!listData?.list?.length}
            >
              数据导出
            </Button>
          </Form.Item>
        </div>
      </Form>

      <StandardTable
        columns={columns}
        showSelectCount={false}
        rowSelectionProps={false}
        data={listData}
        onChange={handleTableChange}
        loading={loading}
        rowKey="id"
        scroll={{
          x: 1000,
        }}
      />
    </Card>
  );
};
export default Form.create({})(Index);
