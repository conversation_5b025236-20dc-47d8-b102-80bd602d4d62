import React, { useEffect, useState } from 'react';
import { Card, message } from 'antd';
import BarChart from '@/components/Chart/BarChart';
import DateChoose from '../DateChoose';

import request from 'ponshine-request';

export default function index() {
  const data = {
    涉诈短彩号码量: [
      {
        value: 100,
        name: '2024/01/01',
      },
      {
        value: 200,
        name: '2024/01/02',
      },
    ],
    涉诈短彩数据量: [
      {
        value: 400,
        name: '2024/01/01',
      },
      {
        value: 300,
        name: '2024/01/02',
      },
    ],
  };

  const [listData, setListData] = useState({});
  const [loading, setLoading] = useState(false);

  const getData = async (timeDim) => {
    setLoading(true);
    const response = await request(`/api/hn/mms/getMmsHistogram`, {
      method: 'POST',
      data: {
        timeDim: timeDim || 1,
      },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const handleChangeDate = (e) => {
    getData(e.target.value);
  };

  return (
    <Card title="各地市涉诈短彩数据情况">
      <DateChoose onChange={handleChangeDate} />
      <BarChart
        xData={listData?.[Object.keys(listData)[0]]?.map((ele) => ele?.name)}
        yData={Object.keys(listData)?.map((ele) => {
          return {
            name: ele,
            data: listData[ele].map((ele) => ele?.value),
          };
        })}
        legendData={Object.keys(listData)}
        height={300}
        width="100%"
        loading={loading}
      />
    </Card>
  );
}
