import { Row, Col } from 'antd';
import React, { useEffect, useState } from 'react';
import TotalInfoBox from './components/TotalInfoBox';
import one from '../../imgs/one.png';
import two from '../../imgs/two.png';
import three from '../../imgs/three.png';
import four from '../../imgs/four.png';
import _ from 'lodash';
import request from 'ponshine-request';

export default function index() {
  const totalInfoList = [
    // {
    //   rate: '10',
    //   rateDes: '同比前一天',
    //   img: one,
    //   flag: undefined,
    //   name: '涉诈短彩总量',
    // },
    {
      rate: '-10',
      rateDes: '同比前一天',
      img: two,

      flag: undefined,
      name: '涉诈短彩手机号码量',
    },
    {
      rate: '10',
      rateDes: '同比前一天',
      img: three,

      flag: undefined,
      name: '涉诈短彩拦截量',
    },
    {
      rate: '-10',
      rateDes: '同比前一天',
      img: four,

      flag: undefined,
      name: '涉诈短彩告警量',
    },
  ];
  const [list, setList] = useState(totalInfoList);

  const getData = async () => {
    let newList = [...list];
    const response = await request('/api/hn/mms/getTop', {
      method: 'POST',
    });
    if (response.code === 200) {
      response?.data?.forEach((ele, index) => {
        Object.assign(newList[index], ele);
      });
    }
    setList(newList);
  };

  useEffect(() => {
    getData();
  }, []);
  return (
    <Row gutter={[16, 16]}>
      {(list.length ? list : totalInfoList)?.map((ele, index) => (
        <Col span={8} key={index}>
          <TotalInfoBox {...ele} />
        </Col>
      ))}
    </Row>
  );
}
