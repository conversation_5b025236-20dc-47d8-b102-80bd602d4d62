import React from 'react';
import { Icon } from 'antd';
import styles from './index.less';
import { formatNumber } from '@/utils/utils';

export default function index(props) {
  const { percent, rateDes, img, value = 0, flag, countDate, name } = props;
  return (
    <div className={styles.totalInfoBox}>
      <img src={img} />
      <div className={styles.totalInfoBox_rightBox}>
        <div className={styles.totalInfoBox_rightBox_title}>
          {countDate ?? ''} {name}
        </div>
        <strong>{formatNumber(String(value))}</strong>
        <div className={styles.totalInfoBox_rightBox_rate}>
          <span style={{ color: flag === 2 ? '#52c41a' : '#f5222d' }}>
            <Icon type={flag === 2 ? 'caret-down' : 'caret-up'} />
          </span>
          {percent ? percent + '%' : '--'}

          <span className={styles.totalInfoBox_rightBox_rateDes}>{rateDes}</span>
        </div>
      </div>
    </div>
  );
}
