import React, { useEffect, useState } from 'react';
import { Card, message } from 'antd';
import LineChart from '@/components/Chart/LineChart';

import request from 'ponshine-request';

export default function index() {
  const data = {
    涉诈短彩号码量: [
      {
        value: 100,
        countDate: '2024/01/01',
      },
      {
        value: 200,
        countDate: '2024/01/01',
      },
    ],
    涉诈短彩数据量: [
      {
        value: 400,
        countDate: '2024/01/01',
      },
      {
        value: 300,
        countDate: '2024/01/01',
      },
    ],
  };

  const [listData, setListData] = useState({});
  const [loading, setLoading] = useState(false);

  const getData = async () => {
    setLoading(true);
    const response = await request(`/api/hn/mms/getMmsLineChart`, {
      method: 'POST',
      requestType: 'form',
      data: { timeDim: 14 },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(response?.data || {});
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <Card title="近14天全省涉诈短彩数据情况">
      <LineChart
        xData={listData?.[Object.keys(listData)[0]]?.map((ele) => ele?.countDate)}
        yData={Object.keys(listData)?.map((ele) => {
          return {
            name: ele,
            data: listData[ele].map((ele) => ele.value),
          };
        })}
        legendData={Object.keys(listData)}
        height={300}
        width="100%"
        loading={loading}
        isMutipleYAxisIndex={true}
      />
    </Card>
  );
}
