import React, { useEffect, useState } from 'react';
import <PERSON><PERSON><PERSON> from '@/components/Chart/PieChart';
import { Card, message } from 'antd';
import request from 'ponshine-request';
import DateChoose from '../DateChoose';

export default function FraudDataPieChart({ cardTitle, isNeedDateChoose = true, queryType }) {
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getData = async (timeDim) => {
    setLoading(true);
    const response = await request('/api/hn/mms/getMmsPieChart', {
      method: 'POST',
      requestType: 'form',
      data: {
        queryType,
        timeDim: isNeedDateChoose ? timeDim || 1 : undefined,
      },
    });
    setLoading(false);

    if (response.code === 200) {
      setListData(
        response?.data?.map((ele) => {
          return {
            value: ele.value,
            name: ele.name,
            percent: ele.percent,
          };
        }) || [],
      );
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const handleChangeDate = (e) => {
    getData(e.target.value);
  };

  return (
    <Card title={cardTitle} style={{ marginTop: 8 }}>
      {isNeedDateChoose && <DateChoose onChange={handleChangeDate} />}
      <PieChart data={listData} height={210} loading={loading} />
    </Card>
  );
}
