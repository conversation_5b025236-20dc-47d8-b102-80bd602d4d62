import { Card, Row, Col } from 'antd';
import React from 'react';
import TotalInfoRow from './components/TotalInfoRow';
import WholeProvinceFraudData from './components/WholeProvinceFraudData';
import PrefecturalMunicipalFraudData from './components/PrefecturalMunicipalFraudData';
import FraudDataPieChart from './components/FraudDataPieChart';

export default function index() {
  return (
    <Card>
      <TotalInfoRow />
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <WholeProvinceFraudData />
        </Col>
        <Col span={12}>
          <PrefecturalMunicipalFraudData />
        </Col>
        <Col span={12}>
          <FraudDataPieChart cardTitle="全省涉诈短彩告警类型号码量情况" queryType="alarmCount" />
        </Col>
        {/* <Col span={8}>
          <FraudDataPieChart cardTitle="全省涉诈短彩类型数据量情况" queryType="typeMmsCount" />
        </Col> */}
        <Col span={12}>
          <FraudDataPieChart cardTitle="全省涉诈短彩类型号码量情况" queryType="typePhoneCount" />
        </Col>
      </Row>
    </Card>
  );
}
