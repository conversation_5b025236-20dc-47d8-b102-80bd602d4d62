import React, { memo, useEffect, useState } from 'react';
import { Card, Form, Select, DatePicker, Button, Row, Col, Input, Modal, message } from 'antd';
import StandardTable from '@/components/StandardTable';
const { RangePicker } = DatePicker;
import request from 'ponshine-request';
import moment from 'moment';
import { Licensee } from 'ponshine';

const queryTypeList = [
  {
    label: '主叫号码',
    value: 'calling',
  },
  {
    label: '被叫号码',
    value: 'called',
  },
];

const Index = (props) => {
  const {
    form: { getFieldDecorator, getFieldValue, validateFields, resetFields, setFieldsValue },
    location: { query },
  } = props;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});
  const [startTime, setStartTime] = useState();

  const findTableData = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await request('/api/hn/outboundResult/pageOutboundResult', {
      data: { pageNum, pageSize, ...props },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    validateFields((err, values) => {
      if (err) return;
      findTableData({
        ...values,
        startDate: values?.time?.[0].format('YYYY-MM-DD 00:00:00'),
        endDate: values?.time?.[1].format('YYYY-MM-DD 23:59:59'),
        time: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    findTableData({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleDetail = async (r) => {
    const response = await request('/api/hn/outboundResult/getCallContent', {
      params: { detailId: r.id },
      method: 'GET',
    });
    if (response.code === 200) {
      Modal.info({
        title: '详情',
        content: response.data,
        okText: '关闭',
      });
    } else {
      message.error(response.message);
    }
  };

  let columns = [
    {
      title: '主叫号码',
      width: 110,
      dataIndex: 'callingPhoneNum',
      ellipsis: true,
    },
    {
      title: '被叫号码',
      width: 120,
      dataIndex: 'calledPhoneNum',
      ellipsis: true,
    },
    {
      title: '外呼时间',
      width: 160,
      dataIndex: 'callTime',
      ellipsis: true,
    },
    {
      title: '外呼结果',
      width: 140,
      dataIndex: 'outboundResult',
      ellipsis: true,
    },
    {
      title: '详情',
      width: 100,
      dataIndex: 'detail',
      ellipsis: true,
      render: (v, r) => (
        <Licensee license="getOutboundCallContent">
          <a onClick={() => handleDetail(r)}>详情</a>
        </Licensee>
      ),
    },
  ];

  const handleChangeType = () => {
    resetFields('phoneNum');
  };

  const disabledDate = (current) => {
    if (!startTime) return current > moment().endOf('day');

    return (
      current > moment(startTime).add(6, 'month') ||
      current < moment(startTime).subtract(6, 'month') ||
      current > moment().endOf('day')
    );
  };

  useEffect(() => {
    if (query?.phoneNum) {
      const date = moment(query.countDate);
      setFieldsValue({
        phoneNum: query.phoneNum,
        time: [date, date],
      });
      handleSearch();
    } else {
      resetFields();
      setListData({
        list: [],
        pagination: false,
      });
    }
  }, [query]);
  return (
    <Card>
      <Form wrapperCol={{ span: 16 }} labelCol={{ span: 8 }}>
        <Row>
          <Col span={8}>
            <Form.Item
              label=""
              wrapperCol={{ span: 24 }}
              style={{ width: '30%', display: 'inline-block' }}
            >
              {getFieldDecorator('queryType', {
                initialValue: 'calling',
              })(
                <Select placeholder="请选择" allowClear={false} onChange={handleChangeType}>
                  {queryTypeList?.map((ele, index) => (
                    <Select.Option value={ele.value} key={index}>
                      {ele.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item
              label=""
              wrapperCol={{ span: 24 }}
              style={{ width: '70%', display: 'inline-block' }}
            >
              {getFieldDecorator('phoneNum', {
                rules: [
                  {
                    required: true,
                    message: `请输入${
                      queryTypeList?.find((ele) => ele.value === getFieldValue('queryType'))?.label
                    }`,
                  },
                  {
                    pattern: /^\d{11}$/,
                    message: `请输入11位数字`,
                  },
                ],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="外呼日期">
              {getFieldDecorator('time', {
                // initialValue: '外呼日期',
                rules: [{ required: true, message: `请选择外呼日期` }],
              })(
                <RangePicker
                  allowClear
                  format={'YYYY-MM-DD'}
                  placeholder={'请选择'}
                  disabledDate={disabledDate}
                  onCalendarChange={(dates) => {
                    if (dates && dates.length === 1) {
                      setStartTime(dates[0]);
                    } else if (dates && dates.length === 2) {
                      setStartTime();
                    }
                  }}
                  onOpenChange={(status) => {
                    setStartTime();
                  }}
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Licensee license="pageOutboundDetail">
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
            </Licensee>
          </Col>
        </Row>
      </Form>
      <StandardTable
        columns={columns}
        data={listData}
        onChange={handleTableChange}
        multiple={true}
        loading={loading}
        showSelectCount={false}
        rowSelectionProps={false}
        rowKey="id"
      />
    </Card>
  );
};

export default Form.create()(Index);
