import React, { memo, useEffect, useState } from 'react';
import { Card, Form, Select, DatePicker, Button, Row, Col, Input, message } from 'antd';
import StandardTable from '@/components/StandardTable';
import OutCallTypeTrend from './OutCallTypeTrend';
import AutoPlayTable from './AutoPlayTable';
import CommonTitle from '@/components/CommonTitle';
import moment from 'moment';
import { getOutCallNumberData, getOutCallTypeTrend } from './services';
import { getSystemConfigListByConfigType } from '@/services/common';

const Index = (props) => {
  const {
    form: { getFieldDecorator, getFieldValue, getFieldsValue, setFieldsValue },
  } = props;

  const defaultTime = moment().subtract(1, 'day');

  const [listData, setListData] = useState([]);
  const [trendData, setTrendData] = useState({});
  const [listDataLoading, setListDataLoading] = useState(false);
  const [trendDataLoading, setTrendDataLoading] = useState(false);
  const [currentDate, setCurrentDate] = useState(defaultTime.format('YYYY-MM-DD'));
  const [isRefresh, setIsRefresh] = useState(true);
  const [localNetworkList, setLocalNetworkList] = useState([]);
  const [searchParams, setSearchParams] = useState({});

  // 获取外呼号码数据
  const findOutCallNumberData = async (props) => {
    setListDataLoading(true);
    const response = await getOutCallNumberData(props);
    setListDataLoading(false);
    if (response.code === 200) {
      setListData(
        response?.data?.map((ele, index) => {
          return {
            ...ele,
            id: index,
          };
        }) || [],
      );
    } else {
      message.error(response.message);
    }
  };

  let columns = [
    {
      title: '外呼分类',
      width: 110,
      dataIndex: 'outboundType',
      ellipsis: true,
    },
    {
      title: '外呼号码数',
      width: 120,
      dataIndex: 'typeCount',
      ellipsis: true,
    },
  ];

  // 获取外呼分类趋势
  const findOutCallTypeTrend = async (params) => {
    setTrendDataLoading(true);
    const response = await getOutCallTypeTrend(params);
    setTrendDataLoading(false);
    if (response.code === 200) {
      setTrendData(response?.data || {});
    } else {
      message.error(response?.message);
    }
  };

  const getlocalNetworkList = async () => {
    const response = await getSystemConfigListByConfigType({ configType: 'organization' });
    if (response.code === 200) {
      setLocalNetworkList(response?.data || []);
    } else {
      message.error(response?.message);
    }
  };
  const getYData = (data) => {
    return Object?.keys(data)?.map((ele) => {
      return {
        name: ele,
        type: 'line',
        data: data?.[ele]?.map((ele) => ele.typeCount),
      };
    });
  };

  const getXData = (data) => {
    return data?.[Object?.keys(data)?.[0]]?.map((ele) => ele.countDate);
  };

  useEffect(() => {
    const params = { countDate: defaultTime?.format('YYYY-MM-DD') };
    findOutCallNumberData(params);
    findOutCallTypeTrend(params);
    getlocalNetworkList();
  }, []);

  const disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };

  const handleSearch = () => {
    const params = { countDate: getFieldValue('time')?.format('YYYY-MM-DD') };
    findOutCallNumberData(params);
    findOutCallTypeTrend(params);
    setCurrentDate(getFieldValue('time')?.format('YYYY-MM-DD'));
    setIsRefresh(!isRefresh);
    setFieldsValue({
      localNetwork: undefined,
      phoneNum: undefined,
    });
    setSearchParams({});
  };

  const handleSearchNumberDetail = () => {
    setSearchParams({
      ...getFieldsValue(),
      countDate: currentDate,
    });
  };

  return (
    <Card>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16 }}>
          <Form.Item label="外呼日期">
            {getFieldDecorator('time', {
              initialValue: defaultTime,
              rules: [{ required: true, message: `请选择外呼日期` }],
            })(
              <DatePicker
                format={'YYYY-MM-DD'}
                placeholder={'请选择'}
                disabledDate={disabledDate}
                allowClear={false}
              />,
            )}
          </Form.Item>
          <Form.Item label="">
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
          </Form.Item>
        </div>

        <Row gutter={[16, 16]}>
          <Col span={8}>
            <StandardTable
              columns={columns}
              data={{
                list: listData,
                pagination: false,
              }}
              loading={listDataLoading}
              showSelectCount={false}
              rowSelectionProps={false}
              rowKey="id"
            />
          </Col>
          <Col span={16}>
            <OutCallTypeTrend
              xData={getXData(trendData)}
              seriesData={getYData(trendData)}
              loading={trendDataLoading}
            />
          </Col>
        </Row>
        <div>
          <Form.Item label="本地网">
            {getFieldDecorator('localNetwork')(
              <Select placeholder="请选择" allowClear style={{ width: 200 }}>
                {localNetworkList?.map((ele) => (
                  <Select.Option value={ele.name} key={ele.value}>
                    {ele.name}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="业务号码">
            {getFieldDecorator('phoneNum')(<Input placeholder="请输入" allowClear />)}
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={handleSearchNumberDetail}>
              查询
            </Button>
          </Form.Item>
        </div>
      </Form>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <CommonTitle title="诈骗号码详单" />
          <AutoPlayTable
            queryType="left"
            countDate={currentDate}
            isRefresh={isRefresh}
            searchParams={searchParams}
          />
        </Col>
        <Col span={12}>
          <CommonTitle title="非诈骗号码详单" />

          <AutoPlayTable
            queryType="right"
            countDate={currentDate}
            isRefresh={isRefresh}
            searchParams={searchParams}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default Form.create()(Index);
