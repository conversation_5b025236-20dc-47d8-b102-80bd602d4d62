import React, { memo, useEffect } from 'react';
import ReactEcharts from 'echarts-for-react';

import { getOutCallTypeTrend } from './services';
import { Spin } from 'antd';

const OutCallTypeTrend = memo(({ xData = [], seriesData, height = 300, loading = false }) => {
  const options = {
    title: {
      text: '近15天外呼分类趋势',
      right: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      //   data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
      top: 30,
    },
    grid: {
      left: '6%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData,
      axisLabel: {
        // width: 100,
        rotate: 30,
        fontSize: 12,
        overflow: 'truncate',
      },
    },
    yAxis: {
      type: 'value',
    },
    series: seriesData,
  };
  return (
    <Spin spinning={loading}>
      <ReactEcharts option={options} style={{ width: '100%', height: height }} />
    </Spin>
  );
});

export default OutCallTypeTrend;
