import React, { useEffect, useRef, useState } from 'react';
import { Card, Tooltip, message } from 'antd';
import { withContext } from 'demasia-pro-layout';

import StandardTable from '@/components/StandardTable';

const RealTimeSituation = (props) => {
  const { tabKey, activeKey, columns, data, className, loading } = props;

  const scrollTimer = useRef();

  const autoScroll = (data) => {
    let v = document.querySelector(`.${className} .ant-table-body`);
    if (data?.length > 5) {
      // 5可以根据各自的列表高度替换
      scrollTimer.current = setInterval(() => {
        // v.scrollTop += row.clientHeight;
        // v.scrollTop += 100;
        v.scrollTop++;
        if (Math.ceil(v.scrollTop) >= parseFloat(v.scrollHeight - v.clientHeight)) {
          // 滚动条到底后重新开始
          v.scrollTop = 0;
          
        }
      }, 50);
    }
  };

  useEffect(() => {
    return () => {
      clearInterval(scrollTimer.current);
    };
  }, []);

  useEffect(() => {
    if (
      tabKey.includes('/dataCenter/outCallCheck/statisticalAnalysis') &&
      activeKey.includes('/dataCenter/outCallCheck/statisticalAnalysis')
    ) {
      autoScroll(data);
    } else {
      clearInterval(scrollTimer.current);
    }
  }, [activeKey]);

  useEffect(() => {
    autoScroll(data);
  }, [JSON.stringify(data)]);

  return (
    <div
      className={className}
      onMouseEnter={() => {
        clearInterval(scrollTimer.current);
      }}
      onMouseLeave={() => {
        autoScroll(data);
      }}
    >
      <StandardTable
        loading={loading}
        columns={columns}
        showSelectCount={false}
        rowSelection={null}
        rowSelectionProps={false}
        tableAlert={false}
        rowKey="userAccount"
        size="small"
        data={{
          list: data,
          pagination: false,
        }}
        scroll={{
          y: 376,
          scrollToFirstRowOnChange: true,
        }}
      />
    </div>
  );
};

let config = [['activeKey', 'tabKey']];

export default withContext(...config)((props) => {
  return <RealTimeSituation {...props} />;
});
