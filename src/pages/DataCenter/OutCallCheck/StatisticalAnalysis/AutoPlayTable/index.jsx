import React, { useEffect, useState, useRef } from 'react';
import request from 'ponshine-request';
import { message, Table } from 'antd';
import styles from './index.less';
import { withContext } from 'demasia-pro-layout';
import { router } from 'ponshine';

const AutoPlayTable = ({ countDate, queryType, activeKey, tabKey, isRefresh, searchParams }) => {
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const scrollTimer = useRef();

  useEffect(() => {
    findTableData();
    // return () => {
    //   clearInterval(scrollTimer.current);
    // };
  }, [countDate, isRefresh, searchParams]);

  // useEffect(() => {
  //   autoPlay();
  // }, [JSON.stringify(listData)]);

  // useEffect(() => {
  //   if (
  //     tabKey.includes('/dataCenter/outCallCheck/statisticalAnalysis') &&
  //     activeKey.includes('/dataCenter/outCallCheck/statisticalAnalysis')
  //   ) {
  //     autoPlay();
  //   } else {
  //     clearInterval(scrollTimer.current);
  //   }
  // }, [activeKey]);

  const autoPlay = () => {
    clearInterval(scrollTimer.current);
    const { current, total, pageSize } = listData?.pagination || {};
    const totalPages = Math.ceil(total / pageSize);
    if (totalPages > 1) {
      let nextPage = 1;
      if (current < totalPages) {
        nextPage = current + 1;
      } else {
        nextPage = 1;
      }
      scrollTimer.current = setInterval(() => {
        findTableData({ pageNum: nextPage });
      }, 3000);
    }
  };

  const findTableData = async ({ pageNum = 1, pageSize = 10 } = {}, callback) => {
    setLoading(true);
    const response = await request('/api/hn/outboundResult/pagePartData', {
      data: { pageNum, pageSize, queryType, ...searchParams, countDate },
      method: 'POST',
      requestType: 'json',
    });
    setLoading(false);
    if (response.code === 200) {
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleTableChange = (pagination) => {
    findTableData({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleJump = (v) => {
    router.push({
      pathname: '/dataCenter/outCallCheck/detailDataQuery',
      query: {
        phoneNum: v,
        countDate,
      },
    });
  };

  let columns = [
    {
      title: '外呼时间',
      dataIndex: 'outboundTime',
      key: 'outboundTime',
      width: 120,
      ellipsis: true,
      align: 'center',
    },
    {
      title: '业务号码',
      dataIndex: 'calling',
      width: 80,
      key: 'calling',
      ellipsis: true,
      align: 'center',
      render: (v) => <a onClick={() => handleJump(v)}>{v}</a>,
    },
    {
      title: '本地网',
      dataIndex: 'localNetwork',
      width: 80,
      key: 'localNetwork',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '外呼分类',
      dataIndex: 'outboundType',
      width: 80,
      key: 'outboundType',
      ellipsis: true,
      align: 'center',
    },
  ];

  return (
    <div className={styles.tableBox}>
      <Table
        columns={columns}
        dataSource={listData?.list || []}
        pagination={listData?.pagination || {}}
        size={'small'}
        onChange={handleTableChange}
        loading={loading}
      />
    </div>
  );
};

let config = [['activeKey', 'tabKey']];

export default withContext(...config)((props) => {
  return <AutoPlayTable {...props} />;
});
