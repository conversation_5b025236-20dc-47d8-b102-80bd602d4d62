import React, { useMemo, useState, useEffect, Fragment } from 'react';
import { Card, Form, Row, Col, DatePicker, Select, Input, Button, Tooltip, message } from 'antd';

import StandardTable from '@/components/StandardTable';
import { selectPage } from './services';
import { exportFile, renderToolTip } from '@/utils/utils';
import { aesDecode } from '@/utils/EncryptDecryp';

import moment from 'moment';
import { render } from 'react-dom';
const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({ pageNum, pageSize, ...props });
    setLoading(false);
    if (response.code === 200) {
      response.message !== '操作成功' && message.info(response.message);
      setSearchParams({ ...props });
      setListData({
        list: response?.data?.items || [],
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    form.validateFields((err, values) => {
      if (err) return;
      getListDatas({
        ...values,
        startTime: values?.time?.[0].format('YYYY-MM-DD HH:mm:ss'),
        endTime: values?.time?.[1].format('YYYY-MM-DD HH:mm:ss'),
        time: undefined,
      });
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current || pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    setListData({
      list: [],
      pagination: {},
    });
  };

  const handleChangeType = () => {
    form.setFieldsValue({ text: '' });
  };

  const handleExport = () => {
    form.validateFields((err, values) => {
      if (err) return;
      setExportLoading(true);
      exportFile({
        urlAPi: '/api/hn/policeStationed/exportPhoneInfo',
        method: 'POST',
        requestType: 'form',
        title: '公安驻场号码信息查询结果',
        mime: 'xlsx',
        params: { ...serachParams },
        isVerifyhEncryption: true,
        callback: () => {
          setExportLoading(false);
        },
      });
    });
  };

  let columns = [
    {
      title: '号码',
      width: 110,
      dataIndex: 'phone',
      ellipsis: true,
      render: (v) => renderToolTip(v ? aesDecode(v) : ''),
    },
    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '身份证',
      width: 160,
      dataIndex: 'idCard',
      ellipsis: true,
      render: (v) => renderToolTip(v ? aesDecode(v) : ''),
    },
    {
      title: '卡码',
      width: 160,
      dataIndex: 'cardNum',
      ellipsis: true,
    },
    {
      title: 'MEID',
      width: 140,
      dataIndex: 'meid',
      ellipsis: true,
    },
    {
      title: 'IMEI1',
      width: 140,
      dataIndex: 'imei1',
      ellipsis: true,
    },
    {
      title: 'IMEI2',
      width: 120,
      dataIndex: 'imei2',
      ellipsis: true,
    },
    {
      title: '品牌',
      width: 120,
      dataIndex: 'brand',
      ellipsis: true,
    },
    {
      title: '终端型号',
      width: 120,
      dataIndex: 'terminalModel',
      ellipsis: true,
    },
    {
      title: '最早注册时间',
      width: 140,
      dataIndex: 'earliestRegisterTime',
      ellipsis: true,
    },
    {
      title: '最近注册时间',
      width: 140,
      dataIndex: 'lastRegisterTime',
      ellipsis: true,
    },
  ];

  const conditionTypeList = [
    {
      key: '1',
      label: '号码',
      rules: [
        {
          required: true,
          message: '请输入号码',
        },
        {
          pattern: /^\d{11}$/,
          message: `请输入11位整数`,
        },
      ],
    },
    {
      key: '2',
      label: '身份证',
      rules: [
        {
          required: true,
          message: '请输入身份证',
        },
        {
          pattern:
            /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/,
          message: `请输入18位正确的身份证号码`,
        },
      ],
    },
    {
      key: '3',
      label: '卡码',
      rules: [
        {
          required: true,
          message: '请输入卡码',
        },
        {
          pattern: /^\d+$/,
          message: `请输入数字`,
        },
      ],
    },
    {
      key: '4',
      label: '串码',
      rules: [
        {
          required: true,
          message: '请输入串码',
        },
        {
          min: 14,
          message: `最少输入14个字符`,
        },
        {
          max: 15,
          message: `最多输入15个字符`,
        },
      ],
    },
  ];

  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Card>
          <Row>
            <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout={'horizontal'}>
              <Col span={6}>
                <Form.Item label={'查询类型'} style={{ marginRight: 8 }}>
                  {getFieldDecorator('type', {
                    initialValue: '1',
                    rules: [{ required: true, message: '请选择查询类型' }],
                  })(
                    <Select
                      mode=""
                      size="default"
                      allowClear={true}
                      autoFocus={false}
                      showArrow={true}
                      showSearch={false}
                      getPopupContainer={(triggerNode) => triggerNode.parentElement}
                      placeholder="请选择"
                      onChange={handleChangeType}
                    >
                      {conditionTypeList?.map((ele, index) => (
                        <Select.Option value={ele.key} key={ele.key}>
                          {ele.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                  {getFieldDecorator('text', {
                    rules:
                      conditionTypeList?.find((ele) => ele.key === form.getFieldValue('type'))
                        ?.rules || [],
                  })(
                    <Input
                      disabled={false}
                      size="default"
                      allowClear={true}
                      placeholder="请输入"
                    />,
                  )}
                </Form.Item>
              </Col>

              <Col align="right" span={14} style={{ textAlign: 'right' }}>
                <Form.Item wrapperCol={{ span: 24 }}>
                  <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                    查询
                  </Button>
                  <Button type="" style={{ marginRight: 10 }} onClick={onReset}>
                    重置
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleExport}
                    loading={exportLoading}
                    disabled={!listData?.list?.length}
                  >
                    导出
                  </Button>
                </Form.Item>
              </Col>
            </Form>
          </Row>

          <StandardTable
            columns={columns}
            data={listData}
            onChange={handleTableChange}
            multiple={true}
            loading={loading}
            showSelectCount={false}
            rowSelectionProps={false}
            rowKey="id"
            scroll={{
              x: 1000,
              // y: 500,
            }}
          />
        </Card>
      </div>
    </div>
  );
};
export default Form.create({})(Index);
