.infoCardBox {
    padding: 16px;
    border: 1px solid #e8e8e8;
    cursor: pointer;
    .basicInfo {
        border-radius: 12px;
        border: 2px solid #f2f2f2;
        padding: 16px;
        position: relative;
        .currentPayPeriod {
            position: absolute;
            top: -21px;
            right: 9px;
            background: #1890ff;
            padding: 0 16px;
            color: #fff;
        }
        &.disable {
            border-color: #dcdcdd;
            background: #eeeeee;
        }
        &.active {
            border-color: rgb(64, 169, 255);
            box-shadow: 0px 0px 0px 5px rgba(64, 169, 255, 0.2);
        }
    }
   
}
