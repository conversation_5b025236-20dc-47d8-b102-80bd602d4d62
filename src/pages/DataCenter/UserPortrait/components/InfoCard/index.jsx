import React, { useState } from 'react';
import { Descriptions, Tag } from 'antd';
import moment from 'moment';
import TagBox from '../TagBox';

import styles from './index.less';

export default function index({
  currentIndex,
  index,
  phoneState,
  blackList,
  blueList,
  redList,
  greenList,
}) {
  return (
    <div className={styles.infoCardBox}>
      <div
        className={`${styles.basicInfo} ${phoneState === '0' && styles.disable} ${
          currentIndex === index && styles.active
        }`}
      >
        <div className={styles.currentPayPeriod}>
          当前数据账期：{moment().subtract('3', 'days').format('YYYYMMDD')}{' '}
        </div>
        <Descriptions column={6}>
          {blackList.map((ele, index) => (
            <Descriptions.Item label={ele?.split?.('：')[0]}>
              {ele.split('：')[1]}
            </Descriptions.Item>
          ))}
        </Descriptions>

        <TagBox list={redList} color="#f50" />
        <TagBox list={blueList} color="#2db7f5" />
        <TagBox list={greenList} color="#87d068" />
      </div>
    </div>
  );
}
