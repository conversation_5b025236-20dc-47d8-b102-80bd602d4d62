/*
 * @Author: zxw
 * @Date: 2023-11-09 16:35:05
 * @LastEditors: zxw
 * @LastEditTime: 2023-11-14 14:41:27
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\UserPortrait\components\TagInfo\index.jsx
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { Collapse, Descriptions } from 'antd';
import styles from './index.less';
const { Panel } = Collapse;

export default function index({ data = [], type, onJump }) {
  const [activeKey, setActiveKey] = useState([]);

  // 改变折叠面板状态
  const handleChange = (keys) => {
    setActiveKey(keys);
  };

  // 查询数据改变时，修改默认展开的折叠面板
  useEffect(() => {
    setActiveKey(data.map((ele) => ele.key));
  }, [JSON.stringify(data)]);

  return (
    <div className={styles.tagInfoBox}>
      <Collapse activeKey={activeKey} onChange={handleChange}>
        {data?.map((ele) => (
          <Panel header={ele.label} key={ele.key}>
            {ele.children?.map((item, i) => (
              <Descriptions column={6} title={item.label} key={item.key} layout="vertical">
                {item.children.map((cur, j) => (
                  <Descriptions.Item label={cur.label} key={cur.key}>
                    {type === '号码' && cur.label === '身份证信息' ? (
                      <a onClick={() => onJump(cur?.value)}>点击跳转该身份证下的卡号</a>
                    ) : cur.value || cur.value === 0 ? (
                      cur.value
                    ) : (
                      '--'
                    )}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            ))}
          </Panel>
        ))}
      </Collapse>
    </div>
  );
}
