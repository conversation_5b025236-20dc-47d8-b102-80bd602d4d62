/*
 * @Author: zxw
 * @Date: 2023-11-09 15:53:13
 * @LastEditors: zxw
 * @LastEditTime: 2023-11-16 14:38:03
 * @FilePath: \newHunanfanzha\src\pages\DataCenter\UserPortrait\components\FilterBox\index.jsx
 * @Description:
 */
import React, { useState, Fragment, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Checkbox, Button } from 'antd';

import styles from './index.less';

// 获取默认选中
const getDefaultChecked = (data) => {
  let arr = [];
  data.forEach((ele) => {
    ele.children.forEach((item) => {
      if (item.checked) {
        arr.push(item.key);
      }
    });
  });
  return arr;
};

// 获取所有的key
const getAllKey = (data) => {
  let arr = [];
  data.forEach((ele) => {
    ele.children.forEach((item) => {
      arr.push(item.key);
    });
  });
  return arr;
};

const Index = forwardRef(({ data, onSearch, currentIndex, cref }, ref) => {
  const defaultChecked = getDefaultChecked(data);
  const allKey = getAllKey(data);
  const [indeterminate, setIndeterminate] = useState(true);
  const [checkAll, setCheckAll] = useState(false);
  const [checkedList, setCheckedList] = useState(defaultChecked);

  // 全选或全不选
  const onCheckAllChange = (e) => {
    let value = e.target.checked;
    setIndeterminate(false);
    setCheckAll(value);
    if (value) {
      setCheckedList(allKey);
    } else {
      setCheckedList([]);
    }
  };

  // 改变可选项
  const handleChangeGroup = (checkedValues) => {
    setCheckedList(checkedValues);
    setIndeterminate(!!checkedValues.length && checkedValues.length < allKey.length);
    setCheckAll(checkedValues.length === allKey.length);
  };

  // 查询
  const handleSearch = (checkedArr) => {
    let newCheckList = checkedArr?.length ? checkedArr : checkedList;
    let defaultArr = data.filter((ele) =>
      ele.children.some((item) => newCheckList?.includes(item.key)),
    );
    let newTagList = defaultArr.map((ele) => {
      return {
        ...ele,
        children: ele.children.filter((item) => newCheckList.includes(item.key)),
      };
    });
    onSearch(newTagList);
  };

  // 重置
  const handleReset = () => {
    setCheckedList(defaultChecked);
    setCheckAll(false);
    setIndeterminate(true);
    handleSearch(defaultChecked);
  };

  useImperativeHandle(cref, () => ({
    handleReset: handleReset,
  }));

  // 卡片切换时，重置选中项
  useEffect(() => {
    handleReset();
  }, [currentIndex]);

  return (
    <div style={{ marginTop: 16 }}>
      <div style={{ borderBottom: '1px solid #E9E9E9', marginBottom: 16 }}>
        <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
          全选
        </Checkbox>
      </div>
      <div>
        <Checkbox.Group onChange={handleChangeGroup} value={checkedList}>
          {data.map((ele) => (
            <div style={{ marginBottom: 16 }}>
              <strong className={styles.checkLableBox}>{ele.label}：</strong>
              {ele.children.map((item, i) => (
                <Checkbox value={item.key}>{item.label}</Checkbox>
              ))}
            </div>
          ))}
        </Checkbox.Group>
      </div>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={() => handleSearch()} style={{ marginRight: 16 }}>
          查询
        </Button>
        <Button style={{ marginRight: 16 }} onClick={handleReset}>
          重置
        </Button>
        {/* <Button>下载</Button> */}
      </div>
    </div>
  );
});

export default Index;
