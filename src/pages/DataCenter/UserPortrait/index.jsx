import React, { useRef, useState } from 'react';
import { Button, Card, Form, Input, Select, message, Tabs, Spin, Empty } from 'antd';

import { findDataByIdCardNum, findDataByPhoneNum } from './services';

const { TabPane } = Tabs;
import InfoCard from './components/InfoCard';
import FilterBox from './components/FilterBox';
import TagInfo from './components/TagInfo';

const Index = ({
  form: {
    getFieldDecorator,
    validateFields,
    resetFields,
    getFieldValue,
    getFieldsValue,
    setFieldsValue,
  },
}) => {
  const [cardData, setCardData] = useState([]);
  const [currentTagList, setCurrentTagList] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(false);

  const filterRef = useRef();

  // 切换号码或者身份证
  const handleChange = () => {
    setFieldsValue({
      searchValue: '',
    });
    setCardData([]);
    setCurrentTagList([]);
    setCurrentIndex(0);
  };

  // 搜索
  const handleSearch = () => {
    validateFields(async (err, values) => {
      if (err) return;
      findCardData(values);
    });
  };

  // 查询数据
  const findCardData = async (values) => {
    setLoading(true);
    let response;
    if (values.info === '身份证') {
      response = await findDataByIdCardNum({ idCardNum: values.searchValue });
    } else {
      response = await findDataByPhoneNum({ phoneNum: values.searchValue });
    }
    setLoading(false);
    if (response.code === 200) {
      setCardData(response.data || []);
      setCurrentIndex(0);
      setCurrentTagList(response?.data?.[0]?.tagList || []);
      filterRef?.current?.handleReset();
      if (!response.data) {
        message.info(response.message);
      }
    } else {
      clearData();
      message.error(response.message);
    }
  };

  // 清空已渲染数据
  const clearData = () => {
    setCardData([]);
    setCurrentIndex(0);
    setCurrentTagList([]);
    filterRef?.current?.handleReset();
  };

  // 搜索选中项
  const handleSearchTagInfo = (list) => {
    setCurrentTagList(list);
  };

  // 切换卡片
  const handleClick = (index) => {
    if (index !== currentIndex) {
      setCurrentIndex(index);
      setCurrentTagList(cardData[index].tagList);
    }
  };

  // 跳转至身份证页面
  const handleJump = (value) => {
    setFieldsValue({ info: '身份证', searchValue: value });
    findCardData(getFieldsValue());
  };

  return (
    <Card>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="人员信息">
          {getFieldDecorator('info', {
            initialValue: '号码',
          })(
            <Select
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              placeholder="请选择"
              onChange={handleChange}
              style={{ width: 120 }}
            >
              <Select.Option value="号码">号码</Select.Option>
              <Select.Option value="身份证">身份证</Select.Option>
            </Select>,
          )}
        </Form.Item>
        <Form.Item>
          {getFieldDecorator('searchValue', {
            rules: [
              { required: true, message: `请输入${getFieldValue('info')}` },
              {
                pattern:
                  getFieldValue('info') === '身份证'
                    ? /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/
                    : /^\d{11}$/,
                message: `请输入正确的${getFieldValue('info')}`,
              },
            ],
          })(<Input placeholder="请输入" allowClear />)}
        </Form.Item>
        <Form.Item>
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Tabs type="card">
        <TabPane tab={`单个${getFieldValue('info')}查询`} key="1" style={{ minHeight: 50 }}>
          <Spin spinning={loading}>
            {getFieldValue('info') === '身份证' && cardData?.[0]?.tip && (
              <h4>
                <strong>{cardData?.[0]?.tip}</strong>
              </h4>
            )}
            {cardData?.map((ele, index) => (
              <div key={index} onClick={() => handleClick(index)}>
                <InfoCard {...ele} currentIndex={currentIndex} index={index} />
              </div>
            ))}
            {cardData?.[currentIndex]?.tagList?.length && (
              <FilterBox
                cref={filterRef}
                data={cardData?.[currentIndex]?.tagList || []}
                onSearch={handleSearchTagInfo}
                currentIndex={currentIndex}
              />
            )}
            <TagInfo data={currentTagList} type={getFieldValue('info')} onJump={handleJump} />
          </Spin>
          {!cardData?.length && <Empty />}
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default Form.create()(Index);
