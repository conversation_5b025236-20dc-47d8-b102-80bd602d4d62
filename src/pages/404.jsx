import { Button, Result } from 'antd';
import { withSilence } from 'demasia-pro-layout';
import { router } from 'ponshine'; // eslint-disable-next-line import/no-extraneous-dependencies
import { formatMessage } from 'ponshine-plugin-react/locale';
const NoFoundPage = () => (
  <Result
    status="404"
    title={formatMessage({
      id: 'auth.404.title',
      defaultMessage: '404',
    })}
    subTitle={formatMessage({
      id: 'auth.404.sub-title',
      defaultMessage: 'Sorry, the page you visited does not exist.',
    })}
    extra={
      <Button type="primary" onClick={() => router.push('/')}>
        {formatMessage({
          id: 'auth.404.back-home',
          defaultMessage: 'Back Home',
        })}
      </Button>
    }
  />
);

export default withSilence(NoFoundPage);
