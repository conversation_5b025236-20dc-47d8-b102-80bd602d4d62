import { Button, Result } from 'antd';
import { withSilence } from 'demasia-pro-layout';
import { router } from 'ponshine'; // eslint-disable-next-line import/no-extraneous-dependencies
import { formatMessage } from 'ponshine-plugin-react/locale';
const NoPermissionPage = () => (
  <Result
    status="403"
    title={formatMessage({
      id: 'auth.403.title',
      defaultMessage: '403',
    })}
    subTitle={formatMessage({
      id: 'auth.403.sub-title',
      defaultMessage: 'Sorry, you are not authorized to access this page.',
    })}
    extra={
      <Button type="primary" onClick={() => router.push('/user/login')}>
        {formatMessage({
          id: 'auth.403.go-login',
          defaultMessage: 'Go Login',
        })}
      </Button>
    }
  />
);

export default withSilence(NoPermissionPage);
