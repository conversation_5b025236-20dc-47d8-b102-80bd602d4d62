import React, { useMemo, useState, useEffect, Fragment } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Modal,
} from 'antd';

import StandardTable from '@/components/StandardTable';
import BatchImportModal from '@/components/BatchImport';
import { selectPage, deleteData, batchImportStuInfo } from './services';
import { exportFile } from '@/utils/utils';
import { Licensee } from 'ponshine';

const { RangePicker } = DatePicker;

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator } = form;

  const [listData, setListData] = useState({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});
  const [visible, setVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState({});
  const [importVisible, setImportVisible] = useState(false);
  const [importDeleteVisible, setImportDeleteVisible] = useState(false);
  const [imporLoading, setImporLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);

    const response = await selectPage({
      pageNum,
      pageSize,
      ...props,
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  useEffect(() => {
    getListDatas();
  }, []);

  const handleSearch = () => {
    form.validateFields((err, values) => {
      getListDatas(values);
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    setSelectedRows([]);
    getListDatas();
  };

  let columns = [
    {
      title: '白名单IP',
      width: 120,
      dataIndex: 'whiteIp',
      ellipsis: true,
    },
    {
      title: '加白原因',
      width: 100,
      dataIndex: 'whiteReason',
      ellipsis: true,
    },
    {
      title: '操作人',
      width: 100,
      dataIndex: 'operatorName',
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 100,
      dataIndex: 'operateTime',
      ellipsis: true,
    },
  ];

  const handleBatchDelete = () => {
    if (!selectedRows.length) return message.info('请选择要删除的数据！');
    Modal.confirm({
      title: '白名单IP信息删除',
      content: '请确认是否删除？',
      cancelText: '取消',
      okText: '确定',
      onOk: async () => {
        const response = await deleteData({ idList: selectedRows.map((ele) => ele.id) });
        if (response.code === 200) {
          onReset();
          message.success(response.message || '操作成功');
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleSelectRow = (rows) => {
    setSelectedRows(rows);
  };

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/hn/whiteLoginIp/exportWhiteLoginIp',
      decode: true,
      method: 'POST',
      params: { ...serachParams },
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Form layout="inline" style={{ marginBottom: 16 }}>
          <Row>
            <Col span={6}>
              <Form.Item label="白名单IP">
                {getFieldDecorator('whiteIp')(<Input allowClear={true} placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={18} style={{ textAlign: 'right' }}>
              <Button style={{ marginRight: 10 }} onClick={onReset}>
                重置
              </Button>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Licensee license="accountBindIPManage_whiteImport">
                <Button
                  type="primary"
                  style={{ marginRight: 10 }}
                  onClick={() => {
                    setImportVisible(true);
                  }}
                >
                  批量导入
                </Button>
              </Licensee>
              <Licensee license="accountBindIPManage_whiteDelete">
                <Button
                  type="danger"
                  onClick={() => handleBatchDelete()}
                  style={{ marginRight: 10 }}
                >
                  删除
                </Button>
              </Licensee>
              <Licensee license="accountBindIPManage_whiteExport">
                <Button
                  type=""
                  onClick={handleExport}
                  loading={exportLoading}
                  disabled={!listData?.list?.length}
                >
                  导出
                </Button>
              </Licensee>
            </Col>
          </Row>
        </Form>

        <StandardTable
          columns={columns}
          data={listData}
          onChange={handleTableChange}
          loading={loading}
          selectedRows={selectedRows}
          onSelectRow={handleSelectRow}
          rowKey="id"
          scroll={{
            x: 1000,
            y: 500,
          }}
        />

        {/* 批量导入 */}
        <BatchImportModal
          title="批量白名单IP信息导入"
          tipsText="*每个文件不超过1000条"
          visible={importVisible}
          onClose={() => {
            setImportVisible(false);
          }}
          loading={imporLoading}
          errorExportUrl={'/api/hn/whiteLoginIp/exportErrorMsg'}
          downTemplateUrl={`/api/template/getTemplate?templateCode=whiteLoginIpImport`}
          importRequest={batchImportStuInfo}
          reload={() => {
            onReset();
          }}
          closeModal={() => {
            setImportVisible(false);
          }}
        />
      </div>
    </div>
  );
};
export default Form.create({})(Index);
