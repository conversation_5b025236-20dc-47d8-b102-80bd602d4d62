import request from 'ponshine-request';

// 查询
export async function selectPage(params) {
  return request(`/api/hn/whiteLoginIp/pageWhiteLoginIp`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 删除
export async function deleteData(params) {
  return request(`/api/hn/whiteLoginIp/deleteWhiteLoginIp`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 批量导入
export async function batchImportStuInfo(params) {
  return request(`/api/hn/whiteLoginIp/importWhiteLoginIp`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
