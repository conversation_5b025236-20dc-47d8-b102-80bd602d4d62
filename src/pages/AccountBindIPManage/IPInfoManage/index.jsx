import React, { useMemo, useState, useEffect, Fragment, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Tooltip,
  message,
  Icon,
  Modal,
} from 'antd';

import StandardTable from '@/components/StandardTable';
import LocalNetworkFormItem from './components/LocalNetworkFormItem';
import { selectPage, resetBind, getLocalNetwork } from './services';
import { exportFile } from '@/utils/utils';
import { Licensee } from 'ponshine';

const Index = (props) => {
  const { form } = props;
  const { getFieldDecorator } = form;

  const [listData, setListData] = useState({
    list: [
      {
        realName: '1232',
      },
    ],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [loading, setLoading] = useState(false);
  const [serachParams, setSearchParams] = useState({});

  const [exportLoading, setExportLoading] = useState(false);
  const [networkList, setNetworkList] = useState([]);
  const localNetworkRef = useRef();

  const getListDatas = async ({ pageNum = 1, pageSize = 10, ...props } = {}) => {
    setLoading(true);
    const response = await selectPage({
      pageNum,
      pageSize,
      ...props,
    });
    setLoading(false);
    if (response.code === 200) {
      setSearchParams(props);
      setListData({
        list: response.data.items,
        pagination: {
          total: response.data.totalNum,
          current: pageNum,
          pageSize,
        },
      });
    } else {
      message.error(response.message);
    }
  };

  const handleSearch = () => {
    form.validateFields((err, values) => {
      getListDatas(values);
    });
  };

  const handleTableChange = (pagination) => {
    getListDatas({
      ...serachParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const onReset = () => {
    form.resetFields();
    getListDatas({ companyId: localNetworkRef.current.getInitialValue() });
  };

  const findLocalNetworkList = async () => {
    const response = await getLocalNetwork();
    if (response.code === 200) {
      setNetworkList(response?.data || []);
    } else {
      message.error(response.message);
    }
  };

  let columns = [
    {
      title: '账号',
      width: 120,
      dataIndex: 'username',
      ellipsis: true,
    },
    {
      title: '姓名',
      width: 100,
      dataIndex: 'realName',
      ellipsis: true,
    },
    {
      title: '本地网',
      width: 120,
      dataIndex: 'localNetwork',
      ellipsis: true,
    },
    {
      title: '绑定IP地址',
      width: 140,
      dataIndex: 'ipList',
      ellipsis: true,
    },
    {
      title: '已绑定个数',
      width: 100,
      dataIndex: 'ipCount',
      ellipsis: true,
    },
    {
      title: '操作人',
      width: 120,
      dataIndex: 'operatorName',
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 140,
      dataIndex: 'operateTime',
      ellipsis: true,
    },
    {
      title: '操作',
      width: 100,
      dataIndex: 'opt',
      ellipsis: true,
      render: (v, r) => {
        return (
          <Licensee license="accountBindIPManage_reset">
            <a disabled={!r?.ipCount} onClick={() => handleResetBind(r)}>
              重置绑定
            </a>
          </Licensee>
        );
      },
    },
  ];

  const handleResetBind = (r) => {
    Modal.confirm({
      title: '重置账号绑定IP地址',
      content: '请确认是否重置账号绑定IP地址?',
      onOk: async () => {
        const response = await resetBind({ userId: r.userId });
        if (response.code === 200) {
          onReset();
          message.success(response.message);
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleExport = () => {
    setExportLoading(true);
    exportFile({
      urlAPi: '/api/hn/userBindIp/exportUserBindIp',
      decode: true,
      method: 'POST',
      params: { ...serachParams },
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  return (
    <div style={{ position: 'relative' }}>
      <div id="create_pdf">
        <Form
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          layout={'horizontal'}
          style={{ marginBottom: 16 }}
        >
          <Row>
            <Col span={6}>
              <Form.Item label="账号">
                {getFieldDecorator('username')(<Input allowClear={true} placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="姓名">
                {getFieldDecorator('realName')(<Input allowClear={true} placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <LocalNetworkFormItem
                form={form}
                getListDatas={getListDatas}
                cref={localNetworkRef}
              />
            </Col>
            <Col span={6}>
              <Form.Item label="绑定IP地址">
                {getFieldDecorator('ip')(<Input allowClear={true} placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6} offset={18} style={{ textAlign: 'right' }}>
              <Button style={{ marginRight: 10 }} onClick={onReset}>
                重置
              </Button>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleSearch}>
                查询
              </Button>
              <Licensee license="accountBindIPManage_export">
                <Button
                  type=""
                  onClick={handleExport}
                  loading={exportLoading}
                  disabled={!listData?.list?.length}
                >
                  批量导出
                </Button>
              </Licensee>
            </Col>
          </Row>
        </Form>

        <StandardTable
          showSelectCount={false}
          rowSelectionProps={false}
          columns={columns}
          data={listData}
          onChange={handleTableChange}
          loading={loading}
          rowKey="userId"
          scroll={{
            x: 1000,
            y: 500,
          }}
        />
      </div>
    </div>
  );
};
export default Form.create({})(Index);
