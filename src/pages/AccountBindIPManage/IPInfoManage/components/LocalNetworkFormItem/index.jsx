import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Form, Select, message } from 'antd';
import { getLocalNetwork } from './services';

const Index = forwardRef((props, ref) => {
  const {
    form: { getFieldDecorator },
    getListDatas,
    cref,
  } = props;
  const [localNetworkList, setLocalNetworkList] = useState([]);

  const getInitialValue = (data) => {
    if (data?.length === 1) {
      return data?.[0]?.id;
    } else {
      return undefined;
    }
  };

  // 获取本地网和是否电渠下拉
  const getSearchList = async () => {
    const response = await getLocalNetwork();
    if (response.code === 200) {
      setLocalNetworkList(response?.data || []);
      getListDatas({
        companyId: getInitialValue(response?.data || []),
      });
    } else {
      getListDatas();
      message.error(response.message);
    }
  };

  useEffect(() => {
    getSearchList();
  }, []);

  useImperativeHandle(cref, () => ({
    getInitialValue: () => {
      return getInitialValue(localNetworkList);
    },
  }));

  return (
    <Form.Item label="本地网">
      {getFieldDecorator('companyId', {
        initialValue: getInitialValue(localNetworkList),
      })(
        <Select placeholder="请选择" allowClear={!Boolean(getInitialValue(localNetworkList))}>
          {localNetworkList?.map((ele, index) => (
            <Select.Option value={ele.id} key={ele.id}>
              {ele.name}
            </Select.Option>
          ))}
        </Select>,
      )}
    </Form.Item>
  );
});

export default Index;
