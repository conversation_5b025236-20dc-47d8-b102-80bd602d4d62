import request from 'ponshine-request';

// 查询
export async function selectPage(params) {
  return request(`/api/hn/userBindIp/pageUserBindIp`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
}

// 重置绑定
export async function resetBind(params) {
  return request(`/api/hn/userBindIp/resetBindIp`, {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}

// 获取本地网
export async function getLocalNetwork(params) {
  return request(`/api/hn/systemConfig/getOrganizationByUser`, {
    method: 'GET',
    params,
  });
}
