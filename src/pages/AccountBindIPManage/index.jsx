import React from 'react';
import { Card, Tabs } from 'antd';
import { Licensee } from 'ponshine';
import IPInfoManage from './IPInfoManage';
import IPWhiteListManage from './IPWhiteListManage';
import NoAuth from '../NoAuth';

const { TabPane } = Tabs;

export default function index() {
  return (
    <Card>
      <Tabs defaultActiveKey="1">
        <TabPane tab="账号绑定IP信息管理" key="1">
          <Licensee license="accountBindIPManage_page" fallback={<NoAuth />}>
            <IPInfoManage />
          </Licensee>
        </TabPane>

        <TabPane tab="IP白名单管理" key="2">
          <Licensee license="accountBindIPManage_whitePage" fallback={<NoAuth />}>
            <IPWhiteListManage />
          </Licensee>
        </TabPane>
      </Tabs>
    </Card>
  );
}
