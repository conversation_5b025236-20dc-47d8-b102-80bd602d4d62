import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, message, Row, Col, Select, Tooltip } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import styles from './index.less';
import PermissionModal from './components/PermissionModal';
import { exportFile } from '@/utils/utils';
import { setPermissionData } from './services';

const Index = (props) => {
  const {
    form,
    form: { getFieldDecorator },
  } = props;
  // 列表加载状态
  const [tableLoading, setTableLoading] = useState(false);
  // 列表数据
  const [tableData, setTableData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  // 列表选中项
  // 查询条件
  const [searchParams, setSearchParams] = useState({});
  // 本地网
  const [netWorkList, setNetWorkList] = useState([]);

  const getNetWorkList = () => {
    request(`/api/hn/systemConfig/getSystemConfigListByConfigType`, {
      method: 'get',
      params: {
        configType: 'organization',
      },
    }).then((res) => {
      if (res.code == 200) {
        setNetWorkList(res?.data || []);
      }
    });
  };

  useEffect(() => {
    getNetWorkList();
    handleSearch();
  }, []);
  // 表格配置项
  const columns = [
    {
      title: '账号',
      dataIndex: 'username',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: '姓名',
      dataIndex: 'realName',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '角色',
      dataIndex: 'roleName',
      align: 'center',
      ellipsis: true,
      width: 130,
    },
    {
      title: '本地网',
      dataIndex: 'companyName',
      align: 'center',
      ellipsis: true,
      width: 110,
    },
    {
      title: '所属部门',
      dataIndex: 'departmentName',
      align: 'center',
      ellipsis: true,
      width: 100,
      render:(text)=>{
        return text||'--'
      }
    },
    {
      title: '权限',
      dataIndex: 'permissionName',
      align: 'center',
      ellipsis: true,
      width: 190,
      render:(text)=>{
        return text?<Tooltip title={text} placement='topLeft'><span title=''>{text}</span></Tooltip>:'--'
      }
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      align: 'center',
      ellipsis: true,
      width: 100,
      render:(text)=>{
        return text||'--'
      }
    },
    {
      title: '操作时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      ellipsis: true,
      width: 160,
      render:(text)=>{
        return text||'--'
      }
    },
    {
      title: '操作',
      dataIndex: 'actions',
      align: 'center',
      // width: 0,
      fixed: 'right',
      render: (_, r) => {
        return (
          <Button
            title={'权限配置'}
            size="small"
            type="link"
            icon="setting"
            onClick={() => {
              setConfigUserId(r.userId);
            }}
          />
        );
      },
    },
  ];
  // 列表查询
  const handleSearch = (current = 1, pageSize = 10, searchParams) => {
    const formData = searchParams || form.getFieldsValue() || {};
    const params = {
      ...formData,
      pageNum: current,
      pageSize: pageSize,
    };
    setTableLoading(true);
    request('/api/hn/userAccess/pageUserAccess', {
      method: 'POST',
      data: {
        ...params,
      },
      requestType: 'json',
    })
      .then((res) => {
        if (res.code == 200) {
          setTableData({
            list: res.data?.items || [],
            total: res.data?.totalNum || 0,
          });
          setPagination({
            current: current,
            pageSize: pageSize,
          });
          setSearchParams({
            ...params,
          });
        } else {
          message.error(res.message);
          setTableData({
            list: [],
            total: 0,
          });
          setPagination({
            current: 1,
            pageSize: 10,
          });
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  };
  // 重置列表查询
  const handleReset = () => {
    form.resetFields();
    handleSearch();
  };
  // 列表改变分页
  const handleTableChange = (p) => {
    handleSearch(p.current, p.pageSize, searchParams);
  };

  const [configUserId, setConfigUserId] = useState();

  // 设置权限数据
  const handleSetPermissionData = async (keys) => {
    try {
      await setPermissionData({
        userId: configUserId,
        nodeIds: keys,
      });
      message.success('设置成功！');
      handleReset();
      setConfigUserId(undefined);
    } catch {
      message.error('设置失败！');
    }
  };

  const [exportLoading, setExportLoading] = useState(false);

  //导出
  const handleExport = () => {
    setExportLoading(true)
    exportFile({
      urlAPi: '/api/hn/userAccess/exportUserAccess',
      params: { ...searchParams },
      method: 'POST',
      decode: true,
      callback: () => {
        setExportLoading(false);
      },
    });
  };

  return (
    <Card>
      <Form className={styles.myStylesForm}>
        <Row gutter={24}>
          <Col span={6}>
            <Form.Item label="账号">
              {getFieldDecorator('userName', { initialValue: undefined })(
                <Input placeholder="请输入" allowClear style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="姓名">
              {getFieldDecorator('realName', { initialValue: undefined })(
                <Input placeholder="请输入" allowClear style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="本地网">
              {getFieldDecorator('companyId')(
                <Select placeholder="请输入" allowClear>
                  {netWorkList.map((item) => (
                    <Option value={item.id}>{item.name}</Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="权限">
              {getFieldDecorator('permissionName', { initialValue: undefined })(
                <Input placeholder="请输入" allowClear style={{ width: '100%' }} />,
              )}
            </Form.Item>
          </Col>
          <Col span={24} style={{ textAlign: 'right', marginBottom: 12 }}>
            <Button
              type="primary"
              onClick={() => {
                handleSearch();
              }}
            >
              查询
            </Button>
            <Button
              onClick={() => {
                handleReset();
              }}
              style={{ marginLeft: 12 }}
            >
              重置
            </Button>
            <Button
              type="primary"
              onClick={() => {
                handleExport();
              }}
              loading={exportLoading}
              style={{ marginLeft: 12 }}
            >
              批量导出
            </Button>
          </Col>
        </Row>
      </Form>
      <div>
        <StandardTable
          data={{
            list: tableData.list,
            pagination: {
              total: tableData.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          }}
          columns={columns}
          scroll={{ x: 1100 }}
          onChange={handleTableChange}
          rowSelectionProps={false}
          showSelectCount={false}
          rowKey="userId"
          loading={tableLoading}
        />
      </div>
      <PermissionModal
        visible={!!configUserId}
        userId={configUserId}
        onSetPermissionData={handleSetPermissionData}
        onClose={() => setConfigUserId(undefined)}
      />
    </Card>
  );
};

export default Form.create()(Index);
