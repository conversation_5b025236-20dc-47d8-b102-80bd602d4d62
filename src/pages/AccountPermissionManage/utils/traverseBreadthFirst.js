/* eslint-disable func-names */
export default function traverseBreadthFirst(rootNode, options) {
  const mergedOptions = {
    subnodesAccessor:
      options?.subnodesAccessor ??
      function (node) {
        return node.children;
      },
    userdataAccessor:
      options?.userdataAccessor ??
      function (_node, userdata) {
        return userdata;
      },
    onNode: options?.onNode ?? function () {},
    userdata: options.userdata,
  };
  const queue = [];
  queue.push([rootNode, options.userdata, null]);

  while (queue.length > 0) {
    const [node, data, parent] = queue.shift();
    mergedOptions.onNode(node, data, parent);
    const subnodeData = mergedOptions.userdataAccessor(node, data);
    const subnodes = mergedOptions.subnodesAccessor(node); // eslint-disable-next-line no-restricted-syntax

    for (const n of subnodes) {
      queue.push([n, subnodeData, node]);
    }
  }

  return rootNode;
}
