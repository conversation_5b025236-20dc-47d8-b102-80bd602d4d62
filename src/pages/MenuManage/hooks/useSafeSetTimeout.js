import { useCallback, useLayoutEffect, useRef } from 'react';

function useSafeSetTimeout() {
  const timeoutIdsRef = useRef([]);
  useLayoutEffect(() => {
    return () => {
      timeoutIdsRef.current.forEach((timeoutId) => {
        clearTimeout(timeoutId);
      });
    };
  }, []);
  const safeSetTimeout = useCallback((callback, milliseconds, ...args) => {
    let isCleared = false;
    const timeoutId = setTimeout(
      () => {
        try {
          callback();
        } catch (e) {
          throw e;
        } finally {
          isCleared = true;
          const index = timeoutIdsRef.current.indexOf(timeoutId);

          if (index !== -1) {
            timeoutIdsRef.current.splice(index, 1);
          }
        }
      },
      milliseconds,
      ...args,
    );
    timeoutIdsRef.current.push(timeoutId);
    return () => {
      if (!isCleared) {
        clearTimeout(timeoutId);
      }
    };
  }, []);
  return safeSetTimeout;
}

export default useSafeSetTimeout;
