import cloneDeep from 'lodash/cloneDeep';
import { useCallback, useEffect, useRef, useState } from 'react';
import stringNaturalCompare from 'string-natural-compare';
import { getInterfacePermissions } from '../services';

const useInterfacePermissions = (rowKey, nodeId) => {
  const ref = useRef({
    nodeId,
    rowKey,
  });
  ref.current = {
    nodeId,
    rowKey,
  };
  const requestToken = useRef(0);
  const [loading, setLoading] = useState(false);
  const selectedRef = useRef([]);
  const unselectedRef = useRef([]);
  const selectedKeysRef = useRef([]);
  const unselectedKeysRef = useRef([]);
  const allRef = useRef([]);
  const initSelectedRef = useRef([]);
  const initUnselectedRef = useRef([]);
  const initSelectedKeysRef = useRef([]);
  const initUnselectedKeysRef = useRef([]);
  const sortFn = useCallback(
    (a, b) => stringNaturalCompare(`${a?.[ref.current.rowKey]}`, `${b?.[ref.current.rowKey]}`),
    [],
  );
  const queryFn = useCallback(async () => {
    requestToken.current += 1;
    const token = requestToken.current;
    selectedRef.current = [];
    unselectedRef.current = [];
    selectedKeysRef.current = [];
    unselectedKeysRef.current = [];
    allRef.current = [];
    initSelectedRef.current = [];
    initUnselectedRef.current = [];
    initSelectedKeysRef.current = [];
    initUnselectedKeysRef.current = [];
    setLoading(true);

    try {
      const rs = await getInterfacePermissions({
        nodeId: ref.current.nodeId,
      });

      if (token === requestToken.current) {
        selectedRef.current = (rs?.selected || []).sort(sortFn);
        unselectedRef.current = (rs?.unselected || []).sort(sortFn);
        selectedKeysRef.current = selectedRef.current.map((v) => v?.[ref.current.rowKey]);
        unselectedKeysRef.current = unselectedRef.current.map((v) => v?.[ref.current.rowKey]);
        allRef.current = [...selectedRef.current, ...unselectedRef.current].sort(sortFn);
        initSelectedRef.current = cloneDeep(selectedRef.current);
        initUnselectedRef.current = cloneDeep(unselectedRef.current);
        initSelectedKeysRef.current = cloneDeep(selectedKeysRef.current);
        initUnselectedKeysRef.current = cloneDeep(unselectedKeysRef.current);
      }
    } catch (e) {
      console.error(e);
    } finally {
      if (token === requestToken.current) {
        setLoading(false);
      }
    }
  }, [sortFn]);
  const [, triggerUpdate] = useState(Date.now());
  const setKeys = useCallback((isSelectedKeys, newKeys) => {
    const newSelected = [];
    const newUnselected = [];
    const newSelectedKeys = [];
    const newUnselectedKeys = [];
    allRef.current.forEach((v) => {
      const key = v?.[ref.current.rowKey];

      if (newKeys.includes(key)) {
        if (isSelectedKeys) {
          newSelected.push(v);
          newSelectedKeys.push(key);
        } else {
          newUnselected.push(v);
          newUnselectedKeys.push(key);
        }
      } else {
        if (isSelectedKeys) {
          newUnselected.push(v);
          newUnselectedKeys.push(key);
        } else {
          newSelected.push(v);
          newSelectedKeys.push(key);
        }
      }
    });
    selectedRef.current = newSelected;
    unselectedRef.current = newUnselected;
    selectedKeysRef.current = newSelectedKeys;
    unselectedKeysRef.current = newUnselectedKeys;
    triggerUpdate((prevState) => {
      let nextState = Date.now();

      if (prevState === nextState) {
        nextState = -nextState;
      }

      return nextState;
    });
  }, []);
  const setSelectedKeys = useCallback(
    (newSelectedKeys) => {
      setKeys(true, newSelectedKeys);
    },
    [setKeys],
  );
  const setUnselectedKeys = useCallback(
    (newUnselectedKeys) => {
      setKeys(false, newUnselectedKeys);
    },
    [setKeys],
  );
  useEffect(() => {
    queryFn();
  }, [rowKey, nodeId, queryFn]);
  return {
    loading,
    selected: selectedRef.current,
    unselected: unselectedRef.current,
    selectedKeys: selectedKeysRef.current,
    unselectedKeys: unselectedKeysRef.current,
    all: allRef.current,
    initSelected: initSelectedRef.current,
    initUnselected: initUnselectedRef.current,
    initSelectedKeys: initSelectedKeysRef.current,
    initUnselectedKeys: initUnselectedKeysRef.current,
    setSelectedKeys,
    setUnselectedKeys,
  };
};

export default useInterfacePermissions;
