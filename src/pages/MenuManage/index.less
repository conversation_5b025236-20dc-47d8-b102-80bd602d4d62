@import (reference) '~antd/es/style/index.less';
@import (reference) '~antd/es/descriptions/style/index.less';
@import (reference) '~antd/es/drawer/style/index.less';
@import (reference) '~antd/es/transfer/style/index.less';
@import (reference) '~antd/es/table/style/index.less';
@import (reference) '~antd/es/spin/style/index.less';
@import (reference) '~antd/es/pagination/style/index.less';

.contextMenu {
  &:global(.react-contexify) {
    margin: 0;
    padding: 4px 0;
    text-align: left;
    background-color: @component-background;
    background-clip: padding-box;
    border-radius: @border-radius-base;
    outline: none;
    box-shadow: @box-shadow-base;
    -webkit-transform: translate3d(0, 0, 0);
  }

  &:global(.react-contexify .react-contexify__item.react-contexify__item--disabled) {
    opacity: 1;
  }

  &:global(.react-contexify .react-contexify__item > .react-contexify__item__content) {
    clear: both;
    margin: 0;
    padding: @dropdown-vertical-padding @control-padding-horizontal;
    color: @text-color;
    font-weight: normal;
    font-size: @dropdown-font-size;
    line-height: @dropdown-line-height;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s;
  }

  &:global(.react-contexify
      .react-contexify__item:not(.react-contexify__item--disabled)
      > .react-contexify__item__content) {
    color: @text-color;
  }

  &:global(.react-contexify
      .react-contexify__item:not(.react-contexify__item--disabled):hover
      > .react-contexify__item__content) {
    color: @text-color;
    background-color: @item-hover-bg;
  }

  &:global(.react-contexify
      .react-contexify__item.react-contexify__item--disabled
      > .react-contexify__item__content) {
    color: @disabled-color;
    cursor: not-allowed;
  }

  &:global(.react-contexify
      .react-contexify__item.react-contexify__item--disabled:hover
      > .react-contexify__item__content) {
    background-color: @component-background;
  }
}

.descriptionsCustom {
  :global {
    .@{descriptions-prefix-cls} {
      &-item {
        white-space: nowrap;

        &-label {
          display: table-cell;
          vertical-align: top;
        }

        &-content {
          display: table-cell;
          vertical-align: top;

          & > * {
            display: inline-block;
            max-height: 50px;
            overflow: auto;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
          }
        }
      }
    }
  }
}

.transferCustom {
  height: 100%;

  :global {
    .@{transfer-prefix-cls} {
      &-list {
        height: 100%;
      }

      &-list-body {
        height: 100%;
      }

      &-list-body-customize-wrapper {
        height: 100%;
      }
    }
  }
}

.tableCustom {
  height: 100%;

  :global {
    .@{table-prefix-cls} {
      flex: 1 1 0;
      height: 0;

      &-wrapper {
        height: 100%;
      }

      &-content {
        height: 100%;
      }

      &-scroll {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: flex-start;
        height: 100%;
      }

      &-header {
        flex: 0 0 auto;
      }

      &-body {
        flex: 1 1 0;
      }

      &-placeholder {
        flex: 1 1 100%;
      }
    }

    .@{spin-prefix-cls} {
      &-nested-loading {
        height: 100%;
      }

      &-container {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: flex-start;
        height: 100%;

        .@{pagination-prefix-cls} {
          flex: 0 0 auto;
          text-align: right;
        }
      }
    }
  }
}

.drawerTitleContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 60px;

  .drawerTitleButton {
    margin-left: 24px;
  }

  .drawerTitleTotal {
    margin-left: 12px;
  }
}

.drawerTitleButtonTooltipContentContainer {
  display: flex;
  flex-direction: column;
}

.bottomDrawerCustom {
  :global(.@{drawer-prefix-cls}) {
    &-content-wrapper {
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
    }

    &-wrapper-body {
      position: relative;
      display: flex;
      flex-direction: column;
    }

    &-header {
      flex: 0 0 auto;
    }

    &-body {
      position: relative;
      flex: 1 1 auto;
      overflow: auto;
    }
  }
}

.autoLinefeed {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}

.tableDisabled {
  pointer-events: none;
}

.searchContainer {
  padding: 8px;
}

.searchInput {
  display: block;
  width: 188px;
  margin-bottom: 8px;
}

.searchOkButton {
  width: 90px;
  margin-right: 8px;
}

.searchResetButton {
  width: 90px;
}

.searchHighlighter {
  padding: 0;
  background-color: @warning-color;
}

.loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.titleExtraButton {
  margin-left: 12px;
}
