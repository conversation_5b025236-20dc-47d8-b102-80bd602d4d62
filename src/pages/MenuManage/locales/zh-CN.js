export default {
  'menumanage.interfacePermissions.columns.name': '接口名',
  'menumanage.interfacePermissions.columns.method': '接口方法',
  'menumanage.interfacePermissions.columns.path': '接口路径',
  'menumanage.interfacePermissions.filter': '筛选',
  'menumanage.interfacePermissions.ok': '确定',
  'menumanage.interfacePermissions.ok.hint': '点击「确定」按钮后才能生效',
  'menumanage.interfacePermissions.reset': '重置',
  'menumanage.interfacePermissions.resetToInit.hint': '重置为初始的{initTotal}条',
  'menumanage.interfacePermissions.clear': '清空',
  'menumanage.interfacePermissions.clearFilterConditions': '清空筛选条件',
  'menumanage.interfacePermissions.targetInterfaceList': '目标接口列表',
  'menumanage.interfacePermissions.sourceInterfaceList': '源接口列表',
  'menumanage.interfacePermissions.showTotal': '筛选：第{start}-{end}条/总共{total}条',
  'menumanage.interfacePermissions.itemUnit': '条',
  'menumanage.interfacePermissions.itemsUnit': '条',
  'menumanage.hint': '在节点上的右键菜单中选择您要对该节点的操作。',
  'menumanage.viewNode': '查看节点',
  'menumanage.addNode': '新增子节点',
  'menumanage.addNode.success.message': '新增成功！',
  'menumanage.addNode.error.message': '新增失败！',
  'menumanage.addNode.warning.message': '按钮下无法新增节点！',
  'menumanage.editNode': '编辑节点',
  'menumanage.editNode.success.message': '编辑成功！',
  'menumanage.editNode.error.message': '编辑失败！',
  'menumanage.editNode.warning.message': '该节点不可编辑！',
  'menumanage.deleteNode': '删除节点',
  'menumanage.deleteNode.success.message': '删除成功！',
  'menumanage.deleteNode.error.message': '删除失败！',
  'menumanage.deleteNode.warning.message': '该节点不可删除！',
  'menumanage.deleteNode.confirm': '确定删除选中的节点吗？',
  'menumanage.deleteNode.submit': '确定',
  'menumanage.deleteNode.cancel': '取消',
  'menumanage.nodeForm.parentNode': '父节点名称',
  'menumanage.nodeForm.topicNode': '菜单名称',
  'menumanage.nodeForm.topicNode.required': '该字段为必填字段！',
  'menumanage.nodeForm.topicNode.maxLength': '不能超过10个字！',
  'menumanage.nodeForm.topicNode.exist': '该节点已存在！',
  'menumanage.nodeForm.topicNode.placeholder': '请输入',
  'menumanage.nodeForm.type': '类别',
  'menumanage.nodeForm.type.menu': '菜单',
  'menumanage.nodeForm.type.button': '按钮',
  'menumanage.nodeForm.sort': '排序',
  'menumanage.nodeForm.sort.required': '该字段为必填字段！',
  'menumanage.nodeForm.sort.placeholder': '请输入',
  'menumanage.nodeForm.remark': '备注',
  'menumanage.nodeForm.remark.placeholder': '请输入',
  'menumanage.nodeForm.imageClassName': '样式',
  'menumanage.nodeForm.imageClassName.placeholder': '请输入',
  'menumanage.nodeForm.pageUrl': '页面链接',
  'menumanage.nodeForm.pageUrl.required': '该字段为必填字段！',
  'menumanage.nodeForm.pageUrl.noHound': '链接无需输入斜杠！',
  'menumanage.nodeForm.pageUrl.placeholder': '请输入',
  'menumanage.nodeForm.url': '按钮链接',
  'menumanage.nodeForm.url.required': '该字段为必填字段！',
  'menumanage.nodeForm.url.noHound': '链接无需输入斜杠！',
  'menumanage.nodeForm.url.exist': '该节点已存在！',
  'menumanage.nodeForm.url.placeholder': '请输入',
  'menumanage.nodeForm.submit': '确定',
  'menumanage.nodeForm.cancel': '取消',
  'menumanage.nodeForm.interfacePermissions': '接口权限',
  'menumanage.nodeForm.noInterfacePermissions': '无接口权限',
  'menumanage.nodeForm.interfacePermissions.viewDetail': '查看详情',
  'menumanage.nodeForm.interfacePermissions.edit': '编辑',
  'menumanage.nodeForm.interfacePermissions.viewInterfacePermissionsDetail': '查看接口权限详情',
  'menumanage.nodeForm.interfacePermissions.editInterfacePermissionsDetail': '编辑接口权限',
};
