export default {
  'menumanage.interfacePermissions.columns.name': 'Interface Name',
  'menumanage.interfacePermissions.columns.method': 'Interface Method',
  'menumanage.interfacePermissions.columns.path': 'Interface Path',
  'menumanage.interfacePermissions.filter': 'Filter',
  'menumanage.interfacePermissions.ok': 'OK',
  'menumanage.interfacePermissions.ok.hint': 'Click the 「OK」 button to take effect',
  'menumanage.interfacePermissions.reset': 'Reset',
  'menumanage.interfacePermissions.resetToInit.hint': 'Reset to initial {initTotal} items',
  'menumanage.interfacePermissions.clear': 'Clear',
  'menumanage.interfacePermissions.clearFilterConditions': 'Clear Filter Conditions',
  'menumanage.interfacePermissions.targetInterfaceList': 'Target Interface List',
  'menumanage.interfacePermissions.sourceInterfaceList': 'Source Interface List',
  'menumanage.interfacePermissions.showTotal': 'filter: {start}-{end} of {total} items',
  'menumanage.interfacePermissions.itemUnit': 'item',
  'menumanage.interfacePermissions.itemsUnit': 'items',
  'menumanage.hint':
    'Select the operation you want to do on the node in the right-click menu on the node.',
  'menumanage.viewNode': 'View node',
  'menumanage.addNode': 'Add child node',
  'menumanage.addNode.success.message': 'Add successfully!',
  'menumanage.addNode.error.message': 'Add failed!',
  'menumanage.addNode.warning.message': 'No node can be added under the button!',
  'menumanage.editNode': 'Edit node',
  'menumanage.editNode.success.message': 'Edit successfully!',
  'menumanage.editNode.error.message': 'Edit failed!',
  'menumanage.editNode.warning.message': 'This node is not editable!',
  'menumanage.deleteNode': 'Delete node',
  'menumanage.deleteNode.success.message': 'Delete successfully!',
  'menumanage.deleteNode.error.message': 'Deletion failed!',
  'menumanage.deleteNode.warning.message': 'This node cannot be deleted!',
  'menumanage.deleteNode.confirm': 'Are you sure you want to delete the selected node?',
  'menumanage.deleteNode.submit': 'OK',
  'menumanage.deleteNode.cancel': 'Cancel',
  'menumanage.nodeForm.parentNode': 'ParentNode',
  'menumanage.nodeForm.topicNode': 'Menu name',
  'menumanage.nodeForm.topicNode.required': 'This field is required!',
  'menumanage.nodeForm.topicNode.maxLength': 'Cannot exceed 10 characters!',
  'menumanage.nodeForm.topicNode.exist': 'The node already exists!',
  'menumanage.nodeForm.topicNode.placeholder': 'Please enter',
  'menumanage.nodeForm.type': 'Category',
  'menumanage.nodeForm.type.menu': 'Menu',
  'menumanage.nodeForm.type.button': 'Button',
  'menumanage.nodeForm.sort': 'Sort',
  'menumanage.nodeForm.sort.required': 'This field is required!',
  'menumanage.nodeForm.sort.placeholder': 'Please enter',
  'menumanage.nodeForm.remark': 'Remarks',
  'menumanage.nodeForm.remark.placeholder': 'Please enter',
  'menumanage.nodeForm.imageClassName': 'Style',
  'menumanage.nodeForm.imageClassName.placeholder': 'Please enter',
  'menumanage.nodeForm.pageUrl': 'Page link',
  'menumanage.nodeForm.pageUrl.required': 'This field is required!',
  'menumanage.nodeForm.pageUrl.noHound': 'The link does not need to enter a slash!',
  'menumanage.nodeForm.pageUrl.placeholder': 'Please enter',
  'menumanage.nodeForm.url': 'Button link',
  'menumanage.nodeForm.url.required': 'This field is required!',
  'menumanage.nodeForm.url.noHound': 'The link does not need to enter a slash!',
  'menumanage.nodeForm.url.exist': 'The node already exists!',
  'menumanage.nodeForm.url.placeholder': 'Please enter',
  'menumanage.nodeForm.submit': 'OK',
  'menumanage.nodeForm.cancel': 'Cancel',
  'menumanage.nodeForm.interfacePermissions': 'Interface Permissions',
  'menumanage.nodeForm.noInterfacePermissions': 'No Interface Permissions',
  'menumanage.nodeForm.interfacePermissions.viewDetail': 'View Detail',
  'menumanage.nodeForm.interfacePermissions.edit': 'Edit',
  'menumanage.nodeForm.interfacePermissions.viewInterfacePermissionsDetail':
    'View Interface Permissions Detail',
  'menumanage.nodeForm.interfacePermissions.editInterfacePermissionsDetail':
    'Edit Interface Permissions',
};
