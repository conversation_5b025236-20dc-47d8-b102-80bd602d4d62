export default {
  'menumanage.interfacePermissions.columns.name': '接口名',
  'menumanage.interfacePermissions.columns.method': '接口方法',
  'menumanage.interfacePermissions.columns.path': '接口路徑',
  'menumanage.interfacePermissions.filter': '篩選',
  'menumanage.interfacePermissions.ok': '確定',
  'menumanage.interfacePermissions.ok.hint': '點擊「確定」按鈕後才能生效',
  'menumanage.interfacePermissions.reset': '重置',
  'menumanage.interfacePermissions.resetToInit.hint': '重置為初始的{initTotal}條',
  'menumanage.interfacePermissions.clear': '清空',
  'menumanage.interfacePermissions.clearFilterConditions': '清空篩選條件',
  'menumanage.interfacePermissions.targetInterfaceList': '目標接口列表',
  'menumanage.interfacePermissions.sourceInterfaceList': '源接口列表',
  'menumanage.interfacePermissions.showTotal': '篩選：第{start}-{end}條/總共{total}條',
  'menumanage.interfacePermissions.itemUnit': '條',
  'menumanage.interfacePermissions.itemsUnit': '條',
  'menumanage.hint': '在節點上的右鍵菜單中選擇您要對該節點的操作。',
  'menumanage.viewNode': '查看節點',
  'menumanage.addNode': '新增子節點',
  'menumanage.addNode.success.message': '新增成功！',
  'menumanage.addNode.error.message': '新增失敗！ ',
  'menumanage.addNode.warning.message': '按鈕下無法新增節點！',
  'menumanage.editNode': '編輯節點',
  'menumanage.editNode.success.message': '編輯成功！',
  'menumanage.editNode.error.message': '編輯失敗！',
  'menumanage.editNode.warning.message': '該節點不可編輯！',
  'menumanage.deleteNode': '刪除節點',
  'menumanage.deleteNode.success.message': '刪除成功！',
  'menumanage.deleteNode.error.message': '刪除失敗！',
  'menumanage.deleteNode.warning.message': '該節點不可刪除！',
  'menumanage.deleteNode.confirm': '確定刪除選中的節點嗎？',
  'menumanage.deleteNode.submit': '確定',
  'menumanage.deleteNode.cancel': '取消',
  'menumanage.nodeForm.parentNode': '父節點名稱',
  'menumanage.nodeForm.topicNode': '菜單權限名稱',
  'menumanage.nodeForm.topicNode.required': '該字段為必填字段！',
  'menumanage.nodeForm.topicNode.maxLength': '不能超過10個字！',
  'menumanage.nodeForm.topicNode.exist': '該節點已存在！',
  'menumanage.nodeForm.topicNode.placeholder': '請輸入',
  'menumanage.nodeForm.type': '類別',
  'menumanage.nodeForm.type.menu': '菜單',
  'menumanage.nodeForm.type.button': '按鈕',
  'menumanage.nodeForm.sort': '排序',
  'menumanage.nodeForm.sort.required': '該字段為必填字段！',
  'menumanage.nodeForm.sort.placeholder': '請輸入',
  'menumanage.nodeForm.remark': '備註',
  'menumanage.nodeForm.remark.placeholder': '請輸入',
  'menumanage.nodeForm.imageClassName': '樣式',
  'menumanage.nodeForm.imageClassName.placeholder': '請輸入',
  'menumanage.nodeForm.pageUrl': '頁面鏈接',
  'menumanage.nodeForm.pageUrl.required': '該字段為必填字段！',
  'menumanage.nodeForm.pageUrl.noHound': '鏈接無需輸入斜杠！',
  'menumanage.nodeForm.pageUrl.placeholder': '請輸入',
  'menumanage.nodeForm.url': '按鈕鏈接',
  'menumanage.nodeForm.url.required': '該字段為必填字段！',
  'menumanage.nodeForm.url.noHound': '鏈接無需輸入斜杠！',
  'menumanage.nodeForm.url.exist': '該節點已存在！',
  'menumanage.nodeForm.url.placeholder': '請輸入',
  'menumanage.nodeForm.submit': '確定',
  'menumanage.nodeForm.cancel': '取消',
  'menumanage.nodeForm.interfacePermissions': '接口權限',
  'menumanage.nodeForm.noInterfacePermissions': '無接口權限',
  'menumanage.nodeForm.interfacePermissions.viewDetail': '查看詳情',
  'menumanage.nodeForm.interfacePermissions.edit': '編輯',
  'menumanage.nodeForm.interfacePermissions.viewInterfacePermissionsDetail': '查看接口權限詳情',
  'menumanage.nodeForm.interfacePermissions.editInterfacePermissionsDetail': '編輯接口權限',
};
