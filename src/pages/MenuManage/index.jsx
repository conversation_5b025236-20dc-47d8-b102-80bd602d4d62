import { <PERSON><PERSON>, Card, message, Modal, Skeleton, Tree } from 'antd';
import { withSilence } from 'demasia-pro-layout';
import { formatMessage } from 'ponshine-plugin-react/locale';
import React from 'react';
import { contextMenu, Item, Menu } from 'react-contexify';
import 'react-contexify/dist/ReactContexify.min.css';
import NodeDetailModal from './components/NodeDetailModal';
import NodeModal from './components/NodeModal';
import { addNode, deleteNode, editNode, getMenus, getNodeDetail } from './services';
import traverseBreadthFirst from './utils/traverseBreadthFirst';
import styles from './index.less';
export default withSilence((props) => {
  const [menuData, setMenuData] = React.useState();
  const [selectedKeys] = React.useState([]);
  const [expandedKeys, setExpandedKeys] = React.useState(['-1']);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [nodeLoading, setNodeLoading] = React.useState(false);
  const [detailModalVisible, setDetailModalVisible] = React.useState(false);
  const [detailLoading, setDetailLoading] = React.useState(false);
  const [opType, setOpType] = React.useState();
  const [parentNode, setParentNode] = React.useState();
  const [topicNode, setTopicNode] = React.useState();
  const [loading, setLoading] = React.useState(true);

  const loadMenuData = async () => {
    try {
      setLoading(true);
      const response = await getMenus();
      traverseBreadthFirst(response?.[0], {
        onNode: (node) => {
          // eslint-disable-next-line no-param-reassign
          node.title = node.text; // eslint-disable-next-line no-param-reassign

          node.key = String(node.id); // eslint-disable-next-line no-param-reassign

          node.disabled = node.id === -1; // node.selectable = node.id !== -1;
        },
        userdata: undefined,
      });
      setMenuData(response);
    } finally {
      setLoading(false);
    }
  };

  const handleAddNode = async (values) => {
    const response = await addNode(values);

    if (response.state === 'SUCCESS') {
      message.success(
        response.message ||
          formatMessage({
            id: 'menumanage.addNode.success.message',
          }),
      );
      setOpType(undefined);
      setModalVisible(false);
      loadMenuData();
    } else {
      message.error(
        response.message ||
          formatMessage({
            id: 'menumanage.addNode.error.message',
          }),
      );
    }
  };

  const handleUpdateNode = async (values) => {
    try {
      await editNode(values);
      message.success(
        formatMessage({
          id: 'menumanage.editNode.success.message',
        }),
      );
      setOpType(undefined);
      setModalVisible(false);
      loadMenuData();
    } catch {
      message.error(
        formatMessage({
          id: 'menumanage.editNode.error.message',
        }),
      );
    }
  };

  const handleClickAddNode = async (node) => {
    setNodeLoading(true);
    setModalVisible(true);
    const response = await getNodeDetail({
      id: node.props.id,
    });

    if (response.type === 'button') {
      message.warning(
        formatMessage({
          id: 'menumanage.addNode.warning.message',
        }),
      );
      return;
    }

    setParentNode(response);
    setOpType('add');
    setModalVisible(true);
    setNodeLoading(false);
  };

  const handleClickUpdateNode = async (node) => {
    const nodeId = node.props.id;
    const { parentId } = node.props;

    if (nodeId === -1) {
      message.warning(
        formatMessage({
          id: 'menumanage.editNode.warning.message',
        }),
      );
      return;
    }

    setNodeLoading(true);
    setModalVisible(true);
    const [editTopicNode, editParentNode] = await Promise.all([
      await getNodeDetail({
        id: nodeId,
      }),
      await getNodeDetail({
        id: parentId,
      }),
    ]);
    setTopicNode(editTopicNode);
    setParentNode(editParentNode);
    setOpType('edit');
    setNodeLoading(false);
  };

  const handleClickDeleteNode = (node) => {
    const nodeId = node.props.id;

    if (nodeId === -1) {
      message.warning(
        formatMessage({
          id: 'menumanage.deleteNode.warning.message',
        }),
      );
      return;
    }

    Modal.confirm({
      maskClosable: false,
      title: formatMessage({
        id: 'menumanage.deleteNode',
      }),
      content: formatMessage({
        id: 'menumanage.deleteNode.confirm',
      }),
      okText: formatMessage({
        id: 'menumanage.deleteNode.submit',
      }),
      cancelText: formatMessage({
        id: 'menumanage.deleteNode.cancel',
      }),
      onOk: async () => {
        await deleteNode({
          id: nodeId,
        });
        message.success(
          formatMessage({
            id: 'menumanage.deleteNode.success.message',
          }),
        );
        loadMenuData();
      },
    });
  };

  const handleClickNodeDetail = async (node) => {
    setDetailModalVisible(true);
    setDetailLoading(true);
    const nodeId = node.props.id;
    const { parentId } = node.props;
    const [detailTopicNode, detailParentNode] = await Promise.all([
      await getNodeDetail({
        id: nodeId,
      }),
      await getNodeDetail({
        id: parentId,
      }),
    ]);
    setTopicNode(detailTopicNode);
    setParentNode(detailParentNode);
    setOpType('view');
    setDetailLoading(false);
  };

  React.useEffect(() => {
    loadMenuData();
  }, []);
  return (
    <Card>
      <Alert
        message={formatMessage({
          id: 'menumanage.hint',
        })}
        type="info"
      />
      <Skeleton loading={loading}>
        <Tree
          showLine
          selectable={false}
          selectedKeys={selectedKeys}
          expandedKeys={expandedKeys}
          onExpand={(keys) => setExpandedKeys(keys)}
          defaultExpandedKeys={expandedKeys}
          treeData={menuData}
          onRightClick={(info) => {
            contextMenu.show({
              id: 'context-menu',
              event: info.event,
              props: {
                node: info.node,
              },
            });
          }}
        />
        <Menu animation="flip" className={styles.contextMenu} id="context-menu">
          <Item
            onClick={({ props: menuProps }) => {
              const { node } = menuProps;
              handleClickNodeDetail(node);
            }}
          >
            {formatMessage({
              id: 'menumanage.viewNode',
            })}
          </Item>
          <Item
            onClick={({ props: menuProps }) => {
              const { node } = menuProps;
              handleClickAddNode(node);
            }}
          >
            {formatMessage({
              id: 'menumanage.addNode',
            })}
          </Item>
          <Item
            onClick={({ props: menuProps }) => {
              const { node } = menuProps;
              handleClickUpdateNode(node);
            }}
          >
            {formatMessage({
              id: 'menumanage.editNode',
            })}
          </Item>
          <Item
            onClick={({ props: menuProps }) => {
              const { node } = menuProps;
              handleClickDeleteNode(node);
            }}
          >
            {formatMessage({
              id: 'menumanage.deleteNode',
            })}
          </Item>
        </Menu>
        <NodeModal
          locale={props.locale}
          loading={nodeLoading}
          visible={modalVisible}
          opType={opType}
          parentNode={parentNode}
          topicNode={topicNode}
          onAddNode={handleAddNode}
          onUpdateNode={handleUpdateNode}
          onClose={() => {
            setModalVisible(false);
          }}
          afterClose={() => {
            setParentNode(undefined);
            setTopicNode(undefined);
          }}
        />
        <NodeDetailModal
          locale={props.locale}
          loading={detailLoading}
          visible={detailModalVisible}
          opType={opType}
          parentNode={parentNode}
          topicNode={topicNode}
          onClose={() => {
            setDetailModalVisible(false);
          }}
          afterClose={() => {
            setParentNode(undefined);
            setTopicNode(undefined);
          }}
        />
      </Skeleton>
    </Card>
  );
});
