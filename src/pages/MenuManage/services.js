import request from '@/utils/request';
export function getMenus() {
  return request('/api/node/allMenus', {
    method: 'POST',
    requestType: 'form',
  });
} // 验证菜单结点名称是否已存在

export function isMenuNameTaken(params) {
  return request('/api/node/existsSystemNode', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function isUrlTaken(params) {
  return request('/api/node/existsUrl', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function getNodeDetail(params) {
  return request('/api/node/findSystemNodeDetail', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function addNode(params) {
  return request('/api/node/addSystemNode', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function editNode(params) {
  return request('/api/node/editSystemNode', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function deleteNode(params) {
  return request('/api/node/deleteSystemNode', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function getInterfacePermissions(params) {
  return request('/api/node/permissions', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
