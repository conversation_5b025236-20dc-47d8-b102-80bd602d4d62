import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import NodeForm from './NodeForm';

const mapTitle = (opType) => {
  // @ts-ignore
  return {
    add: formatMessage({
      id: 'menumanage.addNode',
    }),
    edit: formatMessage({
      id: 'menumanage.editNode',
    }), // @ts-ignore
  }[opType];
};

export default class extends React.Component {
  formRef = null;
  state = {};
  handleOk = () => {
    if (!this.formRef) {
      return;
    }

    const { opType, topicNode, parentNode, onAddNode, onUpdateNode } = this.props;
    const { form } = this.formRef.props;
    form.validateFields((error, values) => {
      if (error) {
        return;
      }

      if (opType === 'add') {
        onAddNode?.({
          ...values,
          sort: Number(values.sort ?? ''),
          pageUrl: `${parentNode?.pageUrl ?? ''}/${values.pageUrl}`,
          pid: parentNode?.nodeId,
          level: parentNode?.levels && parentNode?.levels + 1,
        });
      }

      if (opType === 'edit') {
        const { nodeId, pid, nodeText, remark, type, url, permissions } = topicNode;
        onUpdateNode?.({
          nodeId,
          pid,
          nodeText,
          remark,
          type,
          url,
          permissions,
          ...values,
          pageUrl: `${parentNode?.pageUrl ?? ''}/${values.pageUrl}`,
        });
      } // nodeId 节点id 编辑时
      // pid 父节点id
      // level 父节点level + 1
    });
  };

  render() {
    const { visible, opType, parentNode, topicNode, onClose, afterClose, loading, locale } =
      this.props;
    return (
      // @ts-ignore
      <Modal
        centered
        maskClosable={false}
        destroyOnClose
        title={mapTitle(opType)}
        visible={visible}
        width={600}
        onCancel={onClose}
        footer={[
          <Button key="cancel" type="default" onClick={onClose}>
            {formatMessage({
              id: 'menumanage.nodeForm.cancel',
            })}
          </Button>,
          <Button key="submit" type="primary" onClick={this.handleOk}>
            {formatMessage({
              id: 'menumanage.nodeForm.submit',
            })}
          </Button>,
        ].filter(Boolean)}
        afterClose={afterClose}
        bodyStyle={{
          height: '550px',
          overflow: 'auto',
        }}
      >
        {/* @ts-ignore */}
        <Spin spinning={loading}>
          <NodeForm
            locale={locale}
            opType={opType}
            nodeId={topicNode?.nodeId}
            parentNode={parentNode}
            preloadValues={topicNode}
            wrappedComponentRef={(form) => {
              this.formRef = form;
            }}
          />
        </Spin>
      </Modal>
    );
  }
}
