import React, { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { Button, Drawer, Icon, Input, Spin, Table, Tag, Tooltip, Transfer } from 'antd';
import classNames from 'classnames';
import difference from 'lodash/difference';
import { formatMessage, FormattedMessage } from 'ponshine-plugin-react/locale';
import Highlighter from 'react-highlight-words';
import stringNaturalCompare from 'string-natural-compare';
import useInterfacePermissions from '../hooks/useInterfacePermissions';
import useSafeSetTimeout from '../hooks/useSafeSetTimeout';
import styles from '../index.less';

const TableView = ({
  disabled,
  rowKey,
  rowSelection,
  columns,
  onItemSelect,
  selectedKeys,
  onTableFilterChange,
  dataSource,
}) => (
  <Table
    className={classNames(styles.tableCustom, disabled && styles.tableDisabled)}
    rowKey={rowKey}
    rowSelection={rowSelection}
    columns={columns}
    pagination={{
      showSizeChanger: true,
      showTotal: (total, [start, end]) =>
        formatMessage(
          {
            id: 'menumanage.interfacePermissions.showTotal',
          },
          {
            start,
            end,
            total,
          },
        ),
    }}
    bordered={false}
    scroll={{
      y: true,
    }} // @ts-ignore
    dataSource={dataSource}
    size="small" // @ts-ignore
    onRow={({ key, disabled: itemDisabled }) => ({
      // key 会经过 rowKey 计算获得
      onClick: () => {
        if (itemDisabled || disabled) return;
        onItemSelect?.(key, !selectedKeys?.includes(key));
      },
    })}
    onChange={(pagination, filters) => {
      onTableFilterChange?.(filters);
    }}
  />
); // Customize Table Transfer

const TableTransfer = ({
  leftColumns,
  rightColumns,
  rowKey,
  listStyle,
  leftTitleExtra,
  rightTitleExtra,
  onLeftTableFilterChange,
  onRightTableFilterChange,
  ...restProps
}) => (
  <Transfer
    {...restProps}
    rowKey={rowKey}
    showSelectAll={false}
    locale={{
      itemUnit: <FormattedMessage id="menumanage.interfacePermissions.itemUnit" />,
      itemsUnit: <FormattedMessage id="menumanage.interfacePermissions.itemsUnit" />,
    }}
    titles={[
      // @ts-ignore
      <>
        <FormattedMessage id="menumanage.interfacePermissions.sourceInterfaceList" />
        {leftTitleExtra}
      </>, // @ts-ignore
      <>
        <FormattedMessage id="menumanage.interfacePermissions.targetInterfaceList" />
        {rightTitleExtra}
      </>,
    ]}
    listStyle={listStyle}
    className={styles.transferCustom}
  >
    {({
      direction,
      filteredItems,
      onItemSelectAll,
      onItemSelect,
      selectedKeys: listSelectedKeys,
      disabled: listDisabled,
    }) => {
      const columns = direction === 'left' ? leftColumns : rightColumns; // key 会经过 rowKey 计算获得

      const rowSelection = {
        getCheckboxProps: (item) => ({
          disabled: listDisabled || item.disabled,
        }),

        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows
            .filter((item) => !item.disabled) // @ts-ignore
            .map(({ key }) => key);
          const diffKeys = selected
            ? difference(treeSelectedKeys, listSelectedKeys)
            : difference(listSelectedKeys, treeSelectedKeys);
          onItemSelectAll(diffKeys, selected);
        },

        // @ts-ignore
        onSelect({ key }, selected) {
          onItemSelect(key, selected);
        },

        selectedRowKeys: listSelectedKeys,
      };
      return (
        <TableView
          disabled={listDisabled}
          rowKey={rowKey}
          rowSelection={rowSelection}
          columns={columns}
          onItemSelect={onItemSelect}
          selectedKeys={listSelectedKeys}
          onTableFilterChange={
            direction === 'left' ? onLeftTableFilterChange : onRightTableFilterChange
          }
          dataSource={filteredItems}
        />
      );
    }}
  </Transfer>
);

const InterfacePermissionsDrawer = (props) => {
  const safeSetTimeout = useSafeSetTimeout();
  const rowKey = 'id';
  const rowKeyFn = useCallback((record) => record?.[rowKey], [rowKey]);
  const storeSelectedKeysRef = useRef(null);
  const {
    loading,
    selected,
    unselected,
    selectedKeys,
    unselectedKeys,
    all,
    initSelected,
    // initUnselected,
    initSelectedKeys,
    // initUnselectedKeys,
    setSelectedKeys, // setUnselectedKeys,
  } = useInterfacePermissions(rowKey, props.nodeId);
  const selectedKeysRef = useRef(selectedKeys);
  selectedKeysRef.current = selectedKeys;
  useLayoutEffect(() => {
    if (!loading && props.visible) {
      storeSelectedKeysRef.current = selectedKeysRef.current;
    } else {
      storeSelectedKeysRef.current = null;
    }
  }, [loading, props.visible]);
  const defaultStateRef = useRef({
    search: {
      left: {
        path: {
          title: formatMessage({
            id: 'menumanage.interfacePermissions.columns.path',
          }),
          searchText: '',
          searching: false,
          inputRef: useRef(null),
          confirm: null,
          clearFilters: null,
        },
        // method 采用组件自带的多选方式进行筛选
        method: {
          searchValue: null,
        },
        name: {
          title: formatMessage({
            id: 'menumanage.interfacePermissions.columns.name',
          }),
          searchText: '',
          searching: false,
          inputRef: useRef(null),
          confirm: null,
          clearFilters: null,
        },
      },
      right: {
        path: {
          title: formatMessage({
            id: 'menumanage.interfacePermissions.columns.path',
          }),
          searchText: '',
          searching: false,
          inputRef: useRef(null),
          confirm: null,
          clearFilters: null,
        },
        // method 采用组件自带的多选方式进行筛选
        method: {
          searchValue: null,
        },
        name: {
          title: formatMessage({
            id: 'menumanage.interfacePermissions.columns.name',
          }),
          searchText: '',
          searching: false,
          inputRef: useRef(null),
          confirm: null,
          clearFilters: null,
        },
      },
    },
  });
  const [state, setState] = useState({ ...defaultStateRef.current });
  const stateRef = useRef(state);
  stateRef.current = state;
  const handleReset = useCallback((direction, clearFilters, dataIndex) => {
    clearFilters();
    const nextState = {
      ...stateRef.current,
      search: {
        ...stateRef.current.search,
        [direction]: {
          ...stateRef.current.search[direction],
          [dataIndex]: {
            ...stateRef.current.search[direction]?.[dataIndex],
            searching: false,
            searchText: '',
          },
        },
      },
    };
    stateRef.current = nextState;
    setState(nextState);
  }, []);
  const handleSearch = useCallback(
    (direction, selectedKeysParam, confirm, clearFilters, dataIndex) => {
      const text = `${selectedKeysParam[0] || ''}`;

      if (text) {
        confirm();
        const nextState = {
          ...stateRef.current,
          search: {
            ...stateRef.current.search,
            [direction]: {
              ...stateRef.current.search[direction],
              [dataIndex]: {
                ...stateRef.current.search[direction]?.[dataIndex],
                searching: true,
                searchText: text,
              },
            },
          },
        };
        stateRef.current = nextState;
        setState(nextState);
      } else {
        handleReset(direction, clearFilters, dataIndex);
      }
    },
    [handleReset],
  );
  const getColumnSearchProps = useCallback(
    (direction, dataIndex) => ({
      filterDropdown: ({
        setSelectedKeys: setSelectedKeysParam,
        selectedKeys: selectedKeysParam,
        confirm,
        clearFilters,
      }) => {
        if (stateRef.current.search[direction]?.[dataIndex]) {
          stateRef.current.search[direction][dataIndex].confirm = confirm;
          stateRef.current.search[direction][dataIndex].clearFilters = clearFilters;
        }

        return (
          <div className={styles.searchContainer}>
            <Input
              ref={stateRef.current.search[direction]?.[dataIndex]?.inputRef}
              placeholder={`${formatMessage({
                id: 'menumanage.interfacePermissions.filter',
              })} ${stateRef.current.search[direction]?.[dataIndex]?.title}`}
              value={`${selectedKeysParam?.[0] || ''}`}
              onChange={(e) =>
                setSelectedKeysParam?.(`${e.target.value || ''}` ? [`${e.target.value || ''}`] : [])
              }
              onPressEnter={() =>
                handleSearch(direction, selectedKeysParam, confirm, clearFilters, dataIndex)
              }
              className={styles.searchInput}
            />
            <Button
              type="link"
              onClick={() =>
                handleSearch(direction, selectedKeysParam, confirm, clearFilters, dataIndex)
              }
              size="small"
              className={styles.searchOkButton}
            >
              {formatMessage({
                id: 'menumanage.interfacePermissions.ok',
              })}
            </Button>
            <Button
              type="link"
              onClick={() => handleReset(direction, clearFilters, dataIndex)}
              size="small"
              className={styles.searchResetButton}
            >
              {formatMessage({
                id: 'menumanage.interfacePermissions.reset',
              })}
            </Button>
          </div>
        );
      },
      // @ts-ignore
      filterIcon: <Icon type="search" />,
      onFilter: (value, record) =>
        record[dataIndex]?.toString().toLowerCase().includes(value.toLowerCase()),
      onFilterDropdownVisibleChange: (visible) => {
        if (visible) {
          safeSetTimeout(() =>
            stateRef.current.search[direction]?.[dataIndex]?.inputRef?.current?.select?.(),
          );
        } else if (
          stateRef.current.search[direction]?.[dataIndex]?.searchText !==
          stateRef.current.search[direction]?.[dataIndex]?.inputRef?.current?.input?.value
        ) {
          safeSetTimeout(() =>
            handleSearch(
              direction,
              [stateRef.current.search[direction]?.[dataIndex]?.inputRef?.current?.input?.value],
              stateRef.current.search[direction]?.[dataIndex]?.confirm,
              stateRef.current.search[direction]?.[dataIndex]?.clearFilters,
              dataIndex,
            ),
          );
        }
      },
      render: (text) => (
        <span className={styles.autoLinefeed}>
          {stateRef.current.search[direction]?.[dataIndex]?.searching ? (
            <Highlighter
              highlightClassName={styles.searchHighlighter}
              searchWords={[stateRef.current.search[direction]?.[dataIndex]?.searchText]}
              autoEscape
              textToHighlight={text?.toString() || ''}
            />
          ) : (
            text
          )}
        </span>
      ),
    }),
    [handleReset, handleSearch, safeSetTimeout],
  );
  const methods = useMemo(
    () => ['GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'CONNECT', 'OPTIONS', 'TRACE', 'PATCH'],
    [],
  );
  const methodsStr = useMemo(() => methods.join(','), [methods]);
  const getColumnMethodProps = useCallback(
    (direction) => ({
      filters: methods.map((v) => ({
        text: v,
        value: v,
      })),
      filteredValue:
        Array.isArray(stateRef.current.search[direction]?.method?.searchValue) &&
        stateRef.current.search[direction].method.searchValue.length > 0
          ? stateRef.current.search[direction]?.method?.searchValue
          : null,
      onFilter: (value, record) =>
        !record?.method || `${record?.method}`.toLowerCase() === `${value}`.toLowerCase(),
      render: (method) => (
        <Tag className={styles.autoLinefeed}>
          {Array.isArray(stateRef.current.search[direction]?.method?.searchValue) &&
          stateRef.current.search[direction].method.searchValue.length > 0 ? (
            <Highlighter
              highlightClassName={styles.searchHighlighter}
              searchWords={stateRef.current.search[direction].method.searchValue}
              autoEscape
              textToHighlight={(method || methodsStr)?.toString() || ''}
            />
          ) : (
            method || methodsStr
          )}
        </Tag>
      ),
    }),
    [methods, methodsStr],
  );
  const getColumns = useCallback(
    (direction) => [
      {
        width: '33%',
        dataIndex: 'path',
        title: <FormattedMessage id="menumanage.interfacePermissions.columns.path" />,
        sorter: (a, b) => stringNaturalCompare(`${a?.path}`, `${b?.path}`),
        sortDirections: ['ascend', 'descend'],
        ...getColumnSearchProps(direction, 'path'),
      },
      {
        width: '33%',
        dataIndex: 'method',
        title: <FormattedMessage id="menumanage.interfacePermissions.columns.method" />,
        sorter: (a, b) =>
          stringNaturalCompare(`${a?.method || methodsStr}`, `${b?.method || methodsStr}`),
        sortDirections: ['ascend', 'descend'],
        ...getColumnMethodProps(direction),
      },
      {
        width: '33%',
        dataIndex: 'name',
        title: <FormattedMessage id="menumanage.interfacePermissions.columns.name" />,
        sorter: (a, b) => stringNaturalCompare(`${a?.name}`, `${b?.name}`),
        sortDirections: ['ascend', 'descend'],
        ...getColumnSearchProps(direction, 'name'),
      },
    ],
    [methodsStr, getColumnSearchProps, getColumnMethodProps],
  );
  const titleExtraClearFilterOnClick = useCallback(
    (direction) => {
      const nextState = {
        ...stateRef.current,
        search: {
          ...stateRef.current.search,
          [direction]: {
            ...stateRef.current.search[direction],
            path: { ...stateRef.current.search[direction].path, searchText: '', searching: false },
            method: { ...stateRef.current.search[direction].method, searchValue: null },
            name: { ...stateRef.current.search[direction].name, searchText: '', searching: false },
          },
        },
      };
      stateRef.current = nextState;

      if (stateRef.current.search[direction]?.path?.inputRef?.current?.input) {
        stateRef.current.search[direction].path.inputRef.current.input.value = '';
      }

      if (stateRef.current.search[direction]?.name?.inputRef?.current?.input) {
        stateRef.current.search[direction].name.inputRef.current.input.value = '';
      }

      safeSetTimeout(
        () =>
          typeof stateRef.current.search[direction].path.clearFilters === 'function' &&
          stateRef.current.search[direction].path.clearFilters(),
      );
      safeSetTimeout(
        () =>
          typeof stateRef.current.search[direction].name.clearFilters === 'function' &&
          stateRef.current.search[direction].name.clearFilters(),
      );
      setState(nextState);
    },
    [safeSetTimeout],
  );
  const leftTitleExtraRef = useRef(
    <Button
      className={styles.titleExtraButton}
      type="link"
      size="small"
      onClick={() => {
        titleExtraClearFilterOnClick('left');
      }}
    >
      <FormattedMessage id="menumanage.interfacePermissions.clearFilterConditions" />
    </Button>,
  );
  const rightTitleExtraRef = useRef(
    <Button
      className={styles.titleExtraButton}
      type="link"
      size="small"
      onClick={() => {
        titleExtraClearFilterOnClick('right');
      }}
    >
      <FormattedMessage id="menumanage.interfacePermissions.clearFilterConditions" />
    </Button>,
  );
  const onTableFilterChangeRef = useRef((direction, filters) => {
    // 只需要处理 method 字段就行，因为其它字段在自定义时已经处理好了
    const { method } = filters || {};

    if (
      JSON.stringify((stateRef.current.search[direction]?.method?.searchValue || []).sort()) !==
      JSON.stringify((method || []).sort())
    ) {
      const nextState = {
        ...stateRef.current,
        search: {
          ...stateRef.current.search,
          [direction]: {
            ...stateRef.current.search[direction],
            method: {
              ...stateRef.current.search[direction]?.method,
              searchValue: (method || []).sort(),
            },
          },
        },
      };
      stateRef.current = nextState;
      setState(nextState);
    }
  });
  const onLeftTableFilterChangeRef = useRef((filters) => {
    onTableFilterChangeRef.current('left', filters);
  });
  const onRightTableFilterChangeRef = useRef((filters) => {
    onTableFilterChangeRef.current('right', filters);
  });
  const leftColumns = useMemo(
    () => getColumns('left'),
    [
      getColumns,
      JSON.stringify(props.locale),
      JSON.stringify(stateRef.current.search.left?.method?.searchValue),
    ],
  );
  const rightColumns = useMemo(
    () => getColumns('right'),
    [
      getColumns,
      JSON.stringify(props.locale),
      JSON.stringify(stateRef.current.search.right?.method?.searchValue),
    ],
  );
  const onChange = useCallback(
    (nextTargetKeys) => {
      setSelectedKeys(nextTargetKeys);
    },
    [setSelectedKeys],
  );
  useLayoutEffect(() => {
    if (!props.visible) {
      const nextState = { ...defaultStateRef.current };
      stateRef.current = nextState;
      setState(nextState);
    }
  }, [props.visible]);
  return (
    <Drawer
      title={
        <div className={styles.drawerTitleContainer}>
          <span>
            {props.title}
            {props.opType && ['view'].includes(props.opType) ? (
              <span className={styles.drawerTitleTotal}>
                {selectedKeys.length}
                {selectedKeys.length > 1
                  ? formatMessage({
                      id: 'menumanage.interfacePermissions.itemsUnit',
                    })
                  : formatMessage({
                      id: 'menumanage.interfacePermissions.itemUnit',
                    })}
              </span>
            ) : null}
          </span>
          {props.opType && ['edit', 'add'].includes(props.opType) ? (
            <span>
              <Tooltip
                arrowPointAtCenter
                placement="bottom"
                title={<FormattedMessage id="menumanage.interfacePermissions.ok.hint" />}
              >
                <Button
                  loading={loading}
                  type="primary"
                  className={styles.drawerTitleButton}
                  onClick={() => {
                    props.onOk?.({
                      selected,
                      unselected,
                      selectedKeys,
                      unselectedKeys,
                    });
                    props.onClose();
                  }}
                >
                  <FormattedMessage id="menumanage.interfacePermissions.ok" />
                </Button>
              </Tooltip>
              <Tooltip
                arrowPointAtCenter
                placement="bottom"
                title={<FormattedMessage id="menumanage.interfacePermissions.ok.hint" />}
              >
                <Button
                  disabled={selectedKeys.length === 0}
                  loading={loading}
                  type="primary"
                  className={styles.drawerTitleButton}
                  onClick={() => {
                    setSelectedKeys([]);
                  }}
                >
                  <FormattedMessage id="menumanage.interfacePermissions.clear" />
                </Button>
              </Tooltip>
              {initSelectedKeys.length > 0 ? (
                <Tooltip
                  arrowPointAtCenter
                  placement="bottom"
                  title={
                    <span className={styles.drawerTitleButtonTooltipContentContainer}>
                      <FormattedMessage
                        id="menumanage.interfacePermissions.resetToInit.hint"
                        values={{
                          initTotal: initSelectedKeys.length,
                        }}
                      />
                      <FormattedMessage id="menumanage.interfacePermissions.ok.hint" />
                    </span>
                  }
                >
                  <Button
                    loading={loading}
                    type="primary"
                    className={styles.drawerTitleButton}
                    onClick={() => {
                      setSelectedKeys(initSelectedKeys);
                    }}
                  >
                    <FormattedMessage id="menumanage.interfacePermissions.reset" />
                  </Button>
                </Tooltip>
              ) : null}
            </span>
          ) : (
            rightTitleExtraRef.current
          )}
        </div>
      }
      visible={props.visible}
      placement="bottom"
      onClose={() => {
        if (storeSelectedKeysRef.current) {
          setSelectedKeys(storeSelectedKeysRef.current);
        }

        props.onClose();
      }}
      maskClosable={false}
      destroyOnClose
      className={styles.bottomDrawerCustom}
    >
      {props.opType && ['edit', 'add'].includes(props.opType) ? (
        <TableTransfer
          dataSource={all}
          rowKey={rowKeyFn}
          targetKeys={selectedKeys}
          onChange={onChange}
          leftColumns={leftColumns}
          rightColumns={rightColumns}
          leftTitleExtra={leftTitleExtraRef.current}
          rightTitleExtra={rightTitleExtraRef.current}
          onLeftTableFilterChange={onLeftTableFilterChangeRef.current}
          onRightTableFilterChange={onRightTableFilterChangeRef.current}
          listStyle={{
            width: '50%',
          }}
        />
      ) : (
        <TableView
          rowKey={rowKey}
          columns={rightColumns}
          onTableFilterChange={onRightTableFilterChangeRef.current}
          dataSource={initSelected}
        />
      )}
      {loading ? <Spin spinning={loading} className={styles.loading} /> : null}
    </Drawer>
  );
};

export default InterfacePermissionsDrawer;
