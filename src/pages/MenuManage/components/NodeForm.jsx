import React from 'react';
import { But<PERSON>, Card, Form, Input, Radio, Skeleton } from 'antd';
import { isMenuNameTaken, isUrlTaken } from '../services';
import { formatMessage } from 'ponshine-plugin-react/locale';
import convertInterfacePermissions from '../utils/convertInterfacePermissions';
import InterfacePermissionsDrawer from './InterfacePermissionsDrawer';
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const AddNodeForm = Form.create()(
  class extends React.Component {
    state = {
      drawerVisible: false,
      permissions: null,
    };

    render() {
      const { form, preloadValues, parentNode, nodeId, disabled, locale, opType } = this.props;
      const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
      const initialValues = preloadValues;
      let permissions = initialValues?.permissions;

      if (typeof this.state.permissions === 'string') {
        permissions = this.state.permissions;
      }

      return (
        <>
          <Form
            labelCol={{
              span: 5,
            }}
            wrapperCol={{
              span: 17,
            }}
          >
            <FormItem
              label={formatMessage({
                id: 'menumanage.nodeForm.parentNode',
              })}
            >
              <Input value={parentNode?.nodeText} disabled />
            </FormItem>
            <FormItem
              label={formatMessage({
                id: 'menumanage.nodeForm.topicNode',
              })}
            >
              {getFieldDecorator('nodeText', {
                validateFirst: true,
                initialValue: initialValues?.nodeText ?? '',
                rules: [
                  {
                    required: true,
                    message: formatMessage({
                      id: 'menumanage.nodeForm.topicNode.required',
                    }),
                  },
                  {
                    max: 10,
                    message: formatMessage({
                      id: 'menumanage.nodeForm.topicNode.maxLength',
                    }),
                  },
                  {
                    validator: (rule, value, callback) => {
                      if (value) {
                        isMenuNameTaken({
                          pid: parentNode?.nodeId,
                          nodeText: value,
                          nodeId,
                        }).then((response) => {
                          if (response === true) {
                            callback(
                              formatMessage({
                                id: 'menumanage.nodeForm.topicNode.exist',
                              }),
                            );
                          } else {
                            callback();
                          }
                        });
                      }

                      callback();
                    },
                  },
                ],
              })(
                <Input
                  disabled={disabled}
                  placeholder={
                    disabled
                      ? ''
                      : formatMessage({
                          id: 'menumanage.nodeForm.topicNode.placeholder',
                        })
                  }
                />,
              )}
            </FormItem>
            <FormItem
              label={formatMessage({
                id: 'menumanage.nodeForm.type',
              })}
            >
              {form.getFieldDecorator('type', {
                initialValue: initialValues?.type ?? 'menu',
              })(
                <RadioGroup disabled={disabled}>
                  <Radio value="menu">
                    {formatMessage({
                      id: 'menumanage.nodeForm.type.menu',
                    })}
                  </Radio>
                  <Radio value="button">
                    {formatMessage({
                      id: 'menumanage.nodeForm.type.button',
                    })}
                  </Radio>
                </RadioGroup>,
              )}
            </FormItem>
            {getFieldValue('type') === 'menu' && (
              <FormItem
                label={formatMessage({
                  id: 'menumanage.nodeForm.sort',
                })}
              >
                {getFieldDecorator('sort', {
                  initialValue: initialValues?.sort ?? '',
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'menumanage.nodeForm.sort.required',
                      }),
                    },
                  ],
                })(
                  <Input
                    disabled={disabled}
                    placeholder={
                      disabled
                        ? ''
                        : formatMessage({
                            id: 'menumanage.nodeForm.sort.placeholder',
                          })
                    }
                  />,
                )}
              </FormItem>
            )}
            <FormItem
              label={formatMessage({
                id: 'menumanage.nodeForm.remark',
              })}
            >
              {getFieldDecorator('remark', {
                initialValue: initialValues?.remark ?? '',
              })(
                <Input
                  disabled={disabled}
                  placeholder={
                    disabled
                      ? ''
                      : formatMessage({
                          id: 'menumanage.nodeForm.remark.placeholder',
                        })
                  }
                />,
              )}
            </FormItem>
            {getFieldValue('type') === 'menu' && (
              <FormItem
                label={formatMessage({
                  id: 'menumanage.nodeForm.imageClassName',
                })}
              >
                {getFieldDecorator('imageClassName', {
                  initialValue: initialValues?.imageClassName ?? '',
                })(
                  <Input
                    disabled={disabled}
                    placeholder={
                      disabled
                        ? ''
                        : formatMessage({
                            id: 'menumanage.nodeForm.imageClassName.placeholder',
                          })
                    }
                  />,
                )}
              </FormItem>
            )}
            {getFieldValue('type') === 'menu' ? (
              <FormItem
                label={formatMessage({
                  id: 'menumanage.nodeForm.pageUrl',
                })}
              >
                {getFieldDecorator('pageUrl', {
                  validateFirst: true,
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'menumanage.nodeForm.pageUrl.required',
                      }),
                    },
                    {
                      validator: (rule, value, callback) => {
                        if (/\//.test(value)) {
                          callback(
                            formatMessage({
                              id: 'menumanage.nodeForm.pageUrl.noHound',
                            }),
                          );
                        } else {
                          callback();
                        }
                      },
                    },
                  ],
                  initialValue:
                    initialValues?.pageUrl?.replace(`${parentNode?.pageUrl}/`, '') ?? '',
                })(
                  <Input
                    disabled={disabled}
                    addonBefore={`${parentNode?.pageUrl}/`}
                    placeholder={
                      disabled
                        ? ''
                        : formatMessage({
                            id: 'menumanage.nodeForm.pageUrl.placeholder',
                          })
                    }
                  />,
                )}
              </FormItem>
            ) : (
              <FormItem
                label={formatMessage({
                  id: 'menumanage.nodeForm.url',
                })}
              >
                {getFieldDecorator('url', {
                  validateFirst: true,
                  rules: [
                    {
                      required: true,
                      message: formatMessage({
                        id: 'menumanage.nodeForm.url.required',
                      }),
                    },
                    {
                      validator: (rule, value, callback) => {
                        if (value) {
                          isUrlTaken({
                            url: value,
                          }).then((response) => {
                            if (response === true) {
                              callback(
                                formatMessage({
                                  id: 'menumanage.nodeForm.url.exist',
                                }),
                              );
                            } else if (/\//.test(value)) {
                              callback(
                                formatMessage({
                                  id: 'menumanage.nodeForm.url.noHound',
                                }),
                              );
                            } else {
                              callback();
                            }
                          });
                        }

                        callback();
                      },
                    },
                  ],
                  initialValue: initialValues?.url ?? '',
                })(
                  <Input
                    disabled={disabled}
                    placeholder={
                      disabled
                        ? ''
                        : formatMessage({
                            id: 'menumanage.nodeForm.url.placeholder',
                          })
                    }
                  />,
                )}
              </FormItem>
            )}
            <FormItem
              style={{
                display: 'none',
              }}
            >
              {getFieldDecorator('permissions', {
                initialValue: initialValues?.permissions ?? '',
              })(<Input />)}
            </FormItem>
          </Form>
          <Card
            title={
              <span
                title={formatMessage({
                  id: 'menumanage.nodeForm.interfacePermissions',
                })}
              >
                {formatMessage({
                  id: 'menumanage.nodeForm.interfacePermissions',
                })}
              </span>
            }
            extra={
              <Button
                type="primary"
                onClick={() =>
                  this.setState((prevState) => ({ ...prevState, drawerVisible: true }))
                }
              >
                {formatMessage({
                  id: 'menumanage.nodeForm.interfacePermissions.edit',
                })}
              </Button>
            }
            bodyStyle={{
              maxHeight: 100,
              overflow: 'auto',
            }}
          >
            {permissions ||
              formatMessage({
                id: 'menumanage.nodeForm.noInterfacePermissions',
              })}
          </Card>
          {opType === 'edit' && typeof initialValues?.nodeId !== 'number' ? (
            <Skeleton />
          ) : (
            <InterfacePermissionsDrawer
              title={formatMessage({
                id: 'menumanage.nodeForm.interfacePermissions.editInterfacePermissionsDetail',
              })}
              locale={locale}
              visible={this.state.drawerVisible}
              nodeId={initialValues?.nodeId}
              onClose={() => {
                this.setState((prevState) => ({ ...prevState, drawerVisible: false }));
              }}
              opType={opType}
              onOk={({ selected }) => {
                const nextPermissions = convertInterfacePermissions(selected);
                setFieldsValue({
                  permissions: nextPermissions,
                });
                this.setState((prevState) => ({ ...prevState, permissions: nextPermissions }));
              }}
            />
          )}
        </>
      );
    }
  },
);
export default AddNodeForm;
