import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Descriptions, Modal, Spin } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
import InterfacePermissionsDrawer from './InterfacePermissionsDrawer';
import styles from '../index.less';
export default class extends React.PureComponent {
  state = {
    drawerVisible: false,
  };

  render() {
    const { visible, opType, parentNode, topicNode, onClose, afterClose, loading, locale } =
      this.props;
    return (
      <>
        {/* @ts-ignore */}
        <Modal
          centered
          maskClosable={false}
          destroyOnClose
          title={formatMessage({
            id: 'menumanage.viewNode',
          })}
          visible={visible}
          width={500}
          onCancel={() => {
            this.setState({
              drawerVisible: false,
            });
            onClose?.();
          }}
          footer={null}
          afterClose={() => {
            this.setState({
              drawerVisible: false,
            });
            afterClose?.();
          }}
          bodyStyle={{
            height: 400,
            overflow: 'auto',
          }}
        >
          {/* @ts-ignore */}
          <Spin spinning={loading}>
            <Descriptions
              className={styles.descriptionsCustom}
              style={{
                width: 300,
                margin: '0 auto',
              }}
              column={1}
            >
              <Descriptions.Item
                label={formatMessage({
                  id: 'menumanage.nodeForm.parentNode',
                })}
              >
                <span>{parentNode?.nodeText}</span>
              </Descriptions.Item>
              <Descriptions.Item
                label={formatMessage({
                  id: 'menumanage.nodeForm.topicNode',
                })}
              >
                <span>{topicNode?.nodeText}</span>
              </Descriptions.Item>
              <Descriptions.Item
                label={formatMessage({
                  id: 'menumanage.nodeForm.type',
                })}
              >
                <span>
                  {
                    {
                      menu: formatMessage({
                        id: 'menumanage.nodeForm.type.menu',
                      }),
                      button: formatMessage({
                        id: 'menumanage.nodeForm.type.button',
                      }),
                    }[topicNode?.type ?? '']
                  }
                </span>
              </Descriptions.Item>
              <Descriptions.Item
                label={formatMessage({
                  id: 'menumanage.nodeForm.sort',
                })}
              >
                <span>{parentNode?.sort}</span>
              </Descriptions.Item>
              <Descriptions.Item
                label={formatMessage({
                  id: 'menumanage.nodeForm.remark',
                })}
              >
                <span>{parentNode?.remark}</span>
              </Descriptions.Item>
              {topicNode?.type === 'menu' && (
                <Descriptions.Item
                  label={formatMessage({
                    id: 'menumanage.nodeForm.imageClassName',
                  })}
                >
                  <span>{parentNode?.imageClassName}</span>
                </Descriptions.Item>
              )}

              {topicNode?.type === 'menu' ? (
                <Descriptions.Item
                  label={formatMessage({
                    id: 'menumanage.nodeForm.pageUrl',
                  })}
                >
                  <span>{topicNode?.pageUrl}</span>
                </Descriptions.Item>
              ) : (
                <Descriptions.Item
                  label={formatMessage({
                    id: 'menumanage.nodeForm.url',
                  })}
                >
                  <span>{topicNode?.url}</span>
                </Descriptions.Item>
              )}
            </Descriptions>
            <Card
              title={
                <span
                  title={formatMessage({
                    id: 'menumanage.nodeForm.interfacePermissions',
                  })}
                >
                  {formatMessage({
                    id: 'menumanage.nodeForm.interfacePermissions',
                  })}
                </span>
              }
              extra={
                <Button
                  type="primary"
                  disabled={!topicNode?.permissions || typeof topicNode?.nodeId !== 'number'}
                  onClick={() =>
                    this.setState({
                      drawerVisible: true,
                    })
                  }
                >
                  {formatMessage({
                    id: 'menumanage.nodeForm.interfacePermissions.viewDetail',
                  })}
                </Button>
              }
              bodyStyle={{
                maxHeight: 100,
                overflow: 'auto',
              }}
            >
              {topicNode?.permissions ||
                formatMessage({
                  id: 'menumanage.nodeForm.noInterfacePermissions',
                })}
            </Card>
          </Spin>
          {typeof topicNode?.nodeId === 'number' ? (
            <InterfacePermissionsDrawer
              title={formatMessage({
                id: 'menumanage.nodeForm.interfacePermissions.viewInterfacePermissionsDetail',
              })}
              locale={locale}
              visible={this.state.drawerVisible}
              nodeId={topicNode?.nodeId}
              onClose={() => {
                this.setState({
                  drawerVisible: false,
                });
              }}
              opType={opType}
            />
          ) : null}
        </Modal>
      </>
    );
  }
}
