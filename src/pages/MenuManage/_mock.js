let globalMock = null;

try {
  delete require.cache[require.resolve('../../mock')]; // eslint-disable-next-line global-require

  globalMock = require('../../mock'); // eslint-disable-next-line no-empty
} catch (e) {}

export default {
  ...globalMock,
  'POST /api/node/allMenus': [
    {
      id: -1,
      text: '资源菜单',
      children: [
        {
          id: 1,
          text: '首页',
          children: [],
          parentId: -1,
        },
        {
          id: 2,
          text: '扩展属性',
          children: [
            {
              id: 202,
              text: '扩展属性管理',
              children: [],
              parentId: 2,
            },
            {
              id: 201,
              text: '扩展属性组管理',
              children: [
                {
                  id: 20101,
                  text: '新增',
                  children: [],
                  parentId: 201,
                },
                {
                  id: 20102,
                  text: '修改',
                  children: [],
                  parentId: 201,
                },
                {
                  id: 20103,
                  text: '删除',
                  children: [],
                  parentId: 201,
                },
              ],
              parentId: 2,
            },
          ],
          parentId: -1,
        },
        {
          id: 3,
          text: '系统管理',
          children: [
            {
              id: 304,
              text: '门户面板配置',
              children: [],
              parentId: 3,
            },
            {
              id: 305,
              text: '组织架构',
              children: [],
              parentId: 3,
            },
            {
              id: 301,
              text: '用户管理',
              children: [
                {
                  id: 30101,
                  text: '查询用户',
                  children: [],
                  parentId: 301,
                },
                {
                  id: 30102,
                  text: '新增用户',
                  children: [],
                  parentId: 301,
                },
                {
                  id: 30103,
                  text: '禁用用户',
                  children: [],
                  parentId: 301,
                },
                {
                  id: 30106,
                  text: '设置用户组',
                  children: [],
                  parentId: 301,
                },
                {
                  id: 30104,
                  text: '修改用户',
                  children: [],
                  parentId: 301,
                },
                {
                  id: 30105,
                  text: '删除用户',
                  children: [],
                  parentId: 301,
                },
                {
                  id: 30316,
                  text: '导入',
                  children: [],
                  parentId: 301,
                },
              ],
              parentId: 3,
            },
            {
              id: 302,
              text: '用户组管理',
              children: [
                {
                  id: 30201,
                  text: '新增用户组',
                  children: [],
                  parentId: 302,
                },
                {
                  id: 30202,
                  text: '编辑用户组',
                  children: [],
                  parentId: 302,
                },
                {
                  id: 30203,
                  text: '删除用户组',
                  children: [],
                  parentId: 302,
                },
                {
                  id: 30204,
                  text: '搜索用户组',
                  children: [],
                  parentId: 302,
                },
                {
                  id: 30205,
                  text: '权限分配',
                  children: [],
                  parentId: 302,
                },
                {
                  id: 30206,
                  text: '查看修改数据权限',
                  children: [],
                  parentId: 302,
                },
                {
                  id: 30207,
                  text: '设置扩展属性显示',
                  children: [],
                  parentId: 302,
                },
              ],
              parentId: 3,
            },
            {
              id: 303,
              text: '操作日志管理',
              children: [
                {
                  id: 30301,
                  text: '删除',
                  children: [],
                  parentId: 303,
                },
                {
                  id: 30302,
                  text: '查询',
                  children: [],
                  parentId: 303,
                },
              ],
              parentId: 3,
            },
            {
              id: 30306,
              text: '菜单管理',
              children: [
                {
                  id: 30313,
                  text: '删除节点',
                  children: [],
                  parentId: 30306,
                },
                {
                  id: 30314,
                  text: '修改节点',
                  children: [],
                  parentId: 30306,
                },
                {
                  id: 30311,
                  text: '新增节点',
                  children: [],
                  parentId: 30306,
                },
                {
                  id: 30312,
                  text: '节点详细',
                  children: [],
                  parentId: 30306,
                },
              ],
              parentId: 3,
            },
          ],
          parentId: -1,
        },
      ],
      parentId: null,
    },
  ],
  'POST /api/node/existsUrl': (req, res) => {
    res.json(false);
  },
  'POST /api/node/existsSystemNode': (req, res) => {
    res.json(false);
  },
  'POST /api/node/editSystemNode': (req, res) => {
    res.send('success');
  },
  'POST /api/node/deleteSystemNode': (req, res) => {
    res.send('success');
  },
  'POST /api/node/addSystemNode': (req, res) => {
    res.json({
      data: null,
      ext: null,
      message: '添加成功',
      state: 'SUCCESS',
      success: true,
    });
  },
  'POST /api/node/findSystemNodeDetail': (req, res) => {
    const permissions =
      '/api/user/saveUser,/api/user/mergeUser,/api/user/delUser,/api/user/updateRoleByUserId,/api/user/resetPassword,/api/user/editUserInfo,/api/user/findUserPager,/api/user/findUserDetail';

    if (req.body.pid === '3') {
      res.json({
        nodeId: 3,
        nodeText: '系统管理',
        title: null,
        state: 1,
        pid: 0,
        levels: 1,
        sort: 5,
        remark: '主要系统管理操作',
        type: 'menu',
        isHaveLeaft: null,
        url: null,
        componentId: null,
        imageClassName: 'bars',
        pageUrl: '/sysManage',
        permissions,
        childs: null,
      });
    }

    if (req.body.pid === '30306') {
      res.json({
        nodeId: 30306,
        nodeText: '菜单管理',
        title: '菜单管理',
        state: 1,
        pid: 3,
        levels: 2,
        sort: 5,
        remark: '菜单管理',
        type: 'menu',
        isHaveLeaft: null,
        url: 'menusManage',
        componentId: null,
        imageClassName: null,
        pageUrl: '/sysManage/menusManage',
        permissions,
        childs: null,
      });
    }

    if (req.body.pid === '30313') {
      res.json({
        nodeId: 30313,
        nodeText: '删除节点',
        title: '删除节点',
        state: 1,
        pid: 30306,
        levels: 3,
        sort: 1,
        remark: '删除节点',
        type: 'button',
        isHaveLeaft: null,
        url: 'delSystemNodeBtn',
        componentId: null,
        imageClassName: null,
        pageUrl: null,
        permissions: null,
        childs: null,
      });
    }

    res.json({
      nodeId: 30306,
      nodeText: '菜单管理',
      title: '菜单管理',
      state: 1,
      pid: 3,
      levels: 2,
      sort: 5,
      remark: '菜单管理',
      type: 'menu',
      isHaveLeaft: null,
      url: 'menusManage',
      componentId: null,
      imageClassName: null,
      pageUrl: '/sysManage/menusManage',
      permissions,
      childs: null,
    });
  },
  'POST /api/node/permissions': (req, res) => {
    const randomArr = [
      {
        id: 5,
        name: '测试2',
        path: '/api/test/2',
        method: 'POST',
      },
      {
        id: 4,
        name: '测试1',
        path: '/api/test/1',
        method: null,
      },
      {
        id: 6,
        name: '测试3',
        path: '/api/test/3',
        method: 'POST',
      },
      {
        id: 7,
        name: '测试4',
        path: '/api/test/4',
        method: 'POST',
      },
      {
        id: 8,
        name: '测试5',
        path: '/api/test/5',
        method: 'POST',
      },
      {
        id: 9,
        name: '测试6',
        path: '/api/test/6',
        method: 'POST',
      },
      {
        id: 10,
        name: '测试7',
        path: '/api/test/7',
        method: 'POST',
      },
      {
        id: 11,
        name: '测试8',
        path: '/api/test/8',
        method: 'POST',
      },
      {
        id: 12,
        name: '测试9',
        path: '/api/test/9',
        method: 'POST',
      },
    ];
    const isAppendToUnselected = Date.now() % 2 === 1;
    res.json({
      unselected: [
        {
          id: 0,
          name: null,
          path: '/api/role/saveRole',
          method: 'POST',
        },
        {
          id: 1,
          name: null,
          path: '/api/user/backPassword',
          method: 'POST',
        },
        {
          id: 2,
          name: '修改密码',
          path: '/api/user/changePassword',
          method: null,
        },
        {
          id: 3,
          name: null,
          path: '/api/dict/allowAccessIPAddress',
          method: 'POST',
        },
        ...(isAppendToUnselected ? randomArr : []),
      ],
      selected: [...(!isAppendToUnselected ? randomArr : [])],
    });
  },
};
