export default {
  'usergroupmanage.columns.name': 'User group name',
  'usergroupmanage.columns.remark': 'Description',
  'usergroupmanage.columns.createTime': 'Create Time',
  'usergroupmanage.columns.creator': 'Creator',
  'usergroupmanage.columns.modifyTime': 'Modify Time',
  'usergroupmanage.columns.modifier': 'Modifier',
  'usergroupmanage.columns.action': 'Action',
  'usergroupmanage.showTotal': '{start}-{end} of {total} items',
  'usergroupmanage.set': 'Permission assignment',
  'usergroupmanage.set.success.message': 'Set successfully!',
  'usergroupmanage.set.error.message': 'Set failed! ',
  'usergroupmanage.set.warning.message': 'Please select permission to operate!',
  'usergroupmanage.add': 'Add',
  'usergroupmanage.add.title': 'New user group',
  'usergroupmanage.add.success.message': 'Add successfully!',
  'usergroupmanage.add.error.message': 'Add failed!',
  'usergroupmanage.edit': 'Edit',
  'usergroupmanage.edit.title': 'Edit user group',
  'usergroupmanage.edit.success.message': 'Edit successfully!',
  'usergroupmanage.edit.error.message': 'Edit failed!',
  'usergroupmanage.delete': 'Delete',
  'usergroupmanage.delete.title': 'Delete user group',
  'usergroupmanage.delete.confirm': 'Are you sure you want to delete the selected user group?',
  'usergroupmanage.delete.success.message': 'Delete successfully!',
  'usergroupmanage.delete.error.message': 'Delete failed',
  'usergroupmanage.delete.error.description':
    'There are still users in the user group {group} and cannot be deleted!',
  'usergroupmanage.delete.notFinish.message': 'Partially deleted successfully',
  'usergroupmanage.delete.notFinish.description':
    'User group {group} still has users and cannot be deleted!',
  'usergroupmanage.modal.name': 'User group name',
  'usergroupmanage.modal.name.required': 'This field is required',
  'usergroupmanage.modal.name.maxLength': 'The maximum length does not exceed 15!',
  'usergroupmanage.modal.name.placeholder': 'Please enter',
  'usergroupmanage.modal.remark': 'Description',
  'usergroupmanage.modal.remark.maxLength': 'The maximum length does not exceed 15!',
  'usergroupmanage.modal.remark.placeholder': 'Please enter',
};
