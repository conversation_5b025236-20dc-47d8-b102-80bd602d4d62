import React from 'react';
import ProTable from 'demasia-pro-table';
import { withSilence } from 'demasia-pro-layout';
import { Card, Button, Modal, message, notification } from 'antd';
import UserGroupModal from './components/UserGroupModal';
import PermissionModal from './components/PermissionModal';
import { getUserGroups, deleteUserGroups, saveUserGroup, setPermissionData } from './services';
import { formatMessage } from 'ponshine-plugin-react/locale';

const request = async (rawParams, sort) => {
  try {
    const { current, pageSize, ...other } = rawParams;
    const sortKeys = Object.keys(sort);
    const params = {
      ...other,
      page: current - 1,
      size: pageSize,
      sort:
        sortKeys.length === 0
          ? rawParams.sort
          : sortKeys.map((key) => `${key},${sort[key].replace('end', '')}`),
    };
    const response = await getUserGroups(params);
    return Promise.resolve({
      data: response.content,
      success: true,
      total: response.totalElements,
    });
  } catch {
    return Promise.resolve({
      data: [],
      success: false,
      total: 0,
    });
  }
};

const adminId = 1;

const UserGroupManage = () => {
  const [selectedKeys, setSelectedKeys] = React.useState([]);
  const [selectedRows, setSelectedRows] = React.useState([]);
  const [opType, setOpType] = React.useState();
  const [targetUserGroup, setTargetUserGroup] = React.useState();
  const [configUserGroupId, setConfigUserGroupId] = React.useState(); // const [ modalVisibility, dispatch ] = React.useReducer((state, action) => {
  // })

  const ref = React.useRef();

  const handleDelete = async (rows) => {
    Modal.confirm({
      maskClosable: false,
      content: formatMessage({
        id: 'usergroupmanage.delete.confirm',
      }),
      title: formatMessage({
        id: 'usergroupmanage.delete.title',
      }),
      onOk: async () => {
        const response = await deleteUserGroups({
          ids: rows.map((i) => i.id),
        });

        if (response.state === 'SUCCESS') {
          if (response.data) {
            const group = response.data
              .split(',')
              .map((i) => rows.find((j) => Number(i) === j.id)?.name)
              .join(',');
            notification.success({
              message: formatMessage({
                id: 'usergroupmanage.delete.notFinish.message',
              }),
              description: formatMessage(
                {
                  id: 'usergroupmanage.delete.notFinish.description',
                },
                {
                  group,
                },
              ),
            });
          } else {
            message.success(
              formatMessage({
                id: 'usergroupmanage.delete.success.message',
              }),
            );
          }

          ref.current.reload();
          setSelectedKeys([]);
        } else if (response.data) {
          const group = response.data
            .split(',')
            .map((i) => rows.find((j) => Number(i) === j.id)?.name)
            .join(',');
          notification.warning({
            message: formatMessage({
              id: 'usergroupmanage.delete.error.message',
            }),
            description: formatMessage(
              {
                id: 'usergroupmanage.delete.error.description',
              },
              {
                group,
              },
            ),
          });
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const columns = [
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usergroupmanage.columns.name',
      }),
      dataIndex: 'name',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usergroupmanage.columns.remark',
      }),
      dataIndex: 'remark',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usergroupmanage.columns.createTime',
      }),
      dataIndex: 'createTime',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usergroupmanage.columns.creator',
      }),
      dataIndex: 'creator',
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usergroupmanage.columns.modifyTime',
      }),
      dataIndex: 'modifyTime',
      hideInSearch: true,
      valueType: 'dateTime',
      sorter: true,
    },
    {
      ellipsis: true,
      align: 'center',
      title: formatMessage({
        id: 'usergroupmanage.columns.modifier',
      }),
      dataIndex: 'modifier',
    },
    {
      title: formatMessage({
        id: 'usergroupmanage.columns.action',
      }),
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        return [
          <Button
            title={formatMessage({
              id: 'usergroupmanage.set',
            })}
            size="small"
            type="link"
            icon="setting"
            disabled={record.id === adminId}
            onClick={() => {
              setConfigUserGroupId(record.id);
            }}
          />,
          <Button
            title={formatMessage({
              id: 'usergroupmanage.edit',
            })}
            size="small"
            type="link"
            icon="edit"
            disabled={record.id === adminId}
            onClick={() => {
              setTargetUserGroup(record);
              setOpType('edit');
            }}
          />,
          <Button
            title={formatMessage({
              id: 'usergroupmanage.delete',
            })}
            size="small"
            type="link"
            icon="delete"
            disabled={record.id === adminId}
            onClick={() => handleDelete([record])}
          />,
        ];
      },
    },
  ];

  const handleAddUserGroup = async (values) => {
    try {
      const response = await saveUserGroup(values);

      if (response.state === 'SUCCESS') {
        message.success(
          response?.message ||
            formatMessage({
              id: 'usergroupmanage.add.success.message',
            }),
        );
        setOpType(undefined);
        ref.current?.reload();
        return Promise.resolve();
      }

      message.error(
        response.message ||
          formatMessage({
            id: 'usergroupmanage.add.error.message',
          }),
      );
      return Promise.reject();
    } catch {
      message.error(
        formatMessage({
          id: 'usergroupmanage.add.error.message',
        }),
      );
      return Promise.reject();
    }
  };

  const handleUpdateUserGroup = async (values) => {
    try {
      const { id } = targetUserGroup;
      const response = await saveUserGroup({ ...values, id });

      if (response.state === 'SUCCESS') {
        message.success(
          response?.message ||
            formatMessage({
              id: 'usergroupmanage.edit.success.message',
            }),
        );
        setOpType(undefined);
        ref.current?.reload();
        return Promise.resolve();
      }

      message.error(
        response?.message ||
          formatMessage({
            id: 'usergroupmanage.edit.error.message',
          }),
      );
      return Promise.reject();
    } catch {
      message.error(
        formatMessage({
          id: 'usergroupmanage.edit.error.message',
        }),
      );
      return Promise.reject();
    }
  };

  const handleSetPermissionData = async (keys) => {
    try {
      await setPermissionData({
        roleId: configUserGroupId,
        nodeIds: keys,
      });
      message.success(
        formatMessage({
          id: 'usergroupmanage.set.success.message',
        }),
      );
      setConfigUserGroupId(undefined);
    } catch {
      message.error(
        formatMessage({
          id: 'usergroupmanage.set.error.message',
        }),
      );
    }
  };

  return (
    <Card>
      <ProTable // 隐藏修改表格size功能
        // size="small"
        // options={{ density: false }}
        scroll={{
          x: 'max-content',
        }}
        actionRef={ref}
        request={request}
        columns={columns}
        rowKey="id"
        params={{
          sort: 'modifyTime,desc',
        }}
        rowSelection={{
          getCheckboxProps: (record) => ({
            disabled: record.id === adminId,
          }),
          selectedRowKeys: selectedKeys,
          onChange: (keys, rows) => {
            setSelectedKeys(keys);
            setSelectedRows(rows);
          },
        }}
        pagination={{
          defaultCurrent: 1,
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ['5', '10', '15', '20'],
          showTotal: (total, [start, end]) =>
            formatMessage(
              {
                id: 'usergroupmanage.showTotal',
              },
              {
                start,
                end,
                total,
              },
            ),
        }}
        toolBarRender={() => [
          <Button
            key="add"
            icon="plus"
            type="primary"
            onClick={() => {
              setOpType('add');
            }}
          >
            {formatMessage({
              id: 'usergroupmanage.add',
            })}
          </Button>,
          <Button
            key="delete"
            icon="delete"
            type="danger"
            disabled={selectedKeys.length === 0}
            onClick={() => handleDelete(selectedRows)}
          >
            {formatMessage({
              id: 'usergroupmanage.delete',
            })}
          </Button>,
        ]}
      />
      <UserGroupModal
        visible={!!opType}
        opType={opType}
        userGroup={targetUserGroup}
        onAddUserGroup={handleAddUserGroup}
        onUpdateUserGroup={handleUpdateUserGroup}
        onClose={() => {
          setOpType(undefined);
        }}
        afterClose={() => {
          setTargetUserGroup(undefined);
        }}
      />
      <PermissionModal
        visible={!!configUserGroupId}
        userGroupId={configUserGroupId}
        onSetPermissionData={handleSetPermissionData}
        onClose={() => setConfigUserGroupId(undefined)}
      />
    </Card>
  );
};

export default withSilence(UserGroupManage);
