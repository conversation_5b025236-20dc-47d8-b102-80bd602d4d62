import request from '@/utils/request';
// 分页查询
export function getUserGroups(params) {
  return request('/api/role/findRolePager', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function deleteUserGroups(params) {
  return request('/api/role/delRoles', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function saveUserGroup(params) {
  return request('/api/role/saveRole', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function getPermissionData(params) {
  return request('/api/node/permissionTree', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
export function setPermissionData(params) {
  return request('/api/node/setSystemNodeByRoleId', {
    method: 'POST',
    data: params,
    requestType: 'form',
  });
}
