/* eslint-disable max-classes-per-file */
import React from 'react';
import { Form, Input, Modal } from 'antd';
import { formatMessage } from 'ponshine-plugin-react/locale';
const FormItem = Form.Item;
const UserGroupForm = Form.create()(
  // eslint-disable-next-line react/prefer-stateless-function
  class extends React.Component {
    render() {
      const { form, opType, initialValues } = this.props;
      return (
        <Form
          wrapperCol={{
            span: 16,
          }}
          labelCol={{
            span: 6,
          }}
        >
          <FormItem
            label={formatMessage({
              id: 'usergroupmanage.modal.name',
            })}
          >
            {form.getFieldDecorator('name', {
              initialValue: opType === 'add' ? '' : initialValues?.name,
              rules: [
                {
                  required: true,
                  message: formatMessage({
                    id: 'usergroupmanage.modal.name.required',
                  }),
                },
                {
                  max: 15,
                  message: formatMessage({
                    id: 'usergroupmanage.modal.name.maxLength',
                  }),
                },
              ],
            })(
              <Input
                placeholder={formatMessage({
                  id: 'usergroupmanage.modal.name.placeholder',
                })}
              />,
            )}
          </FormItem>
          <FormItem
            label={formatMessage({
              id: 'usergroupmanage.modal.remark',
            })}
          >
            {form.getFieldDecorator('remark', {
              initialValue: opType === 'add' ? '' : initialValues?.remark,
              rules: [
                {
                  max: 15,
                  message: formatMessage({
                    id: 'usergroupmanage.modal.remark.maxLength',
                  }),
                },
              ],
            })(
              <Input
                placeholder={formatMessage({
                  id: 'usergroupmanage.modal.remark.placeholder',
                })}
              />,
            )}
          </FormItem>
        </Form>
      );
    }
  },
);

class UserGroupModal extends React.Component {
  formRef = undefined;
  state = {
    loading: false,
  };
  handleOk = () => {
    const { form } = this.formRef.props;
    const { opType, onAddUserGroup, onUpdateUserGroup } = this.props;
    form.validateFields(async (error, values) => {
      if (error) {
        return;
      }

      try {
        this.setState({
          loading: true,
        });

        if (opType === 'add') {
          await onAddUserGroup?.(values);
        } else if (opType === 'edit') {
          await onUpdateUserGroup?.(values);
        }
      } finally {
        this.setState({
          loading: false,
        });
      }
    });
  };

  render() {
    const { opType, visible, onClose, userGroup, afterClose } = this.props;
    return (
      <Modal
        maskClosable={false}
        destroyOnClose
        title={
          opType === 'add'
            ? formatMessage({
                id: 'usergroupmanage.add.title',
              })
            : formatMessage({
                id: 'usergroupmanage.edit.title',
              })
        }
        visible={visible}
        onOk={this.handleOk}
        onCancel={() => onClose?.()}
        afterClose={afterClose}
        confirmLoading={this.state.loading}
        width={600}
      >
        <UserGroupForm
          wrappedComponentRef={(form) => {
            this.formRef = form;
          }}
          initialValues={userGroup}
          opType={opType}
        />
      </Modal>
    );
  }
}

export default UserGroupModal;
