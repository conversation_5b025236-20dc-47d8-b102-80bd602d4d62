import React from 'react';
import { Modal, Spin, Tree, message } from 'antd';
import { getPermissionData } from '../services';
import traverseBreadthFirst from '../utils/traverseBreadthFirst';
import { formatMessage } from 'ponshine-plugin-react/locale';

const PermissionModal = ({ userGroupId, visible, onSetPermissionData, onClose, afterClose }) => {
  const [treeData, setTreeData] = React.useState();
  const [loading, setLoading] = React.useState(false);
  const [checkedKeys, setCheckedKeys] = React.useState([]);

  const loadPermissionData = async (id) => {
    setLoading(true);

    try {
      const response = await getPermissionData({
        roleId: id,
      });
      const userdata = {
        checkedKeys: [],
      };
      traverseBreadthFirst(response[0], {
        subnodesAccessor: (node) => node.children,
        onNode: (node, userData) => {
          // eslint-disable-next-line no-param-reassign
          node.title = node?.text; // eslint-disable-next-line no-param-reassign

          node.key = String(node.id);

          if (node?.state?.selected) {
            userData.checkedKeys.push(node.key);
          }
        },
        userdata,
      });
      setTreeData(response[0]);
      setCheckedKeys(userdata.checkedKeys);
    } finally {
      setLoading(false);
    }
  };

  const handleOk = () => {
    const userdata = {
      allCheckedKeys: new Set(),
    };
    traverseBreadthFirst(treeData, {
      subnodesAccessor: (node) => node.children,
      onNode: (node, userData, parentNode) => {
        if (checkedKeys.includes(node.key)) {
          userData.allCheckedKeys.add(node.key);
          const parentKey = parentNode?.key;

          if (parentKey) {
            userData.allCheckedKeys.add(parentKey); // halfChecked
          }
        }
      },
      userdata,
    });
    const keys = [...userdata.allCheckedKeys].filter((x) => x !== '-1');

    if (keys.length > 0) {
      onSetPermissionData(keys);
    } else {
      message.warning(
        formatMessage({
          id: 'usergroupmanage.set.warning.message',
        }),
      );
    }
  };

  React.useEffect(() => {
    if (userGroupId) {
      loadPermissionData(userGroupId);
    }
  }, [userGroupId]);
  return (
    <Modal
      maskClosable={false}
      title={formatMessage({
        id: 'usergroupmanage.set',
      })}
      visible={visible}
      bodyStyle={{
        maxHeight: 500,
        overflow: 'auto',
      }}
      onOk={handleOk}
      onCancel={() => onClose?.()}
      destroyOnClose
      afterClose={afterClose}
    >
      {loading ? (
        <div
          style={{
            textAlign: 'center',
            marginTop: 20,
          }}
        >
          <Spin />
        </div>
      ) : (
        <Tree
          checkable
          treeData={treeData}
          defaultExpandedKeys={checkedKeys}
          checkedKeys={checkedKeys}
          onCheck={(keys) => {
            setCheckedKeys(keys);
          }}
        />
      )}
    </Modal>
  );
};

export default PermissionModal;
