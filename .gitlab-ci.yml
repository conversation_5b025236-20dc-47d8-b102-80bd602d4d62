deployJob:
  stage: deploy
  cache:
    paths:
      - node_modules
  variables:
    previewAlias: "Demasia Pro"
  script:
    - echo `yarn config get registry`
    - yarn install
    # - sed -i "s/export default {/export default {\n  publicPath:\ '.\/',/g" config/chore.ts
    - yarn run fetch:blocks
    - yarn run build
    - mkdir -p previewDeploy
    - cp -r dist/. previewDeploy
    - sh ~/previewDeploy.sh
    - sh ~/previewAlias.sh
  only:
    - master
